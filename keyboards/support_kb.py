from aiogram.types import Inline<PERSON>eyboardMarkup, InlineKeyboardButton
from utils.template_helpers import format_text
from utils.button_layout import should_use_single_row


def support_keyboard() -> InlineKeyboardMarkup:
    """Create a keyboard for support options."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "support_send_message"), "send_support_message"),
        (format_text("buttons", "support_faqs"), "show_faqs"),
        (format_text("buttons", "support_return"), "return_to_main"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


# Commented-out duplicate functions have been removed to clean up dead code.


def support_thread_reply_keyboard(user_id=None, thread_id=None) -> InlineKeyboardMarkup:
    MAX_PAYLOAD_LENGTH = 50
    callback_data = "reply_to_thread:none"

    if user_id:
        user_id_str = str(user_id)
        prefix = "reply_to_user:"
        if len(prefix) + len(user_id_str) > MAX_PAYLOAD_LENGTH:
            user_id_str = user_id_str[-(MAX_PAYLOAD_LENGTH - len(prefix)) :]
        callback_data = f"{prefix}{user_id_str}"

    if thread_id:
        thread_id_str = str(thread_id)
        prefix = "reply_to_thread:"
        if len(prefix) + len(thread_id_str) > MAX_PAYLOAD_LENGTH:
            thread_id_str = thread_id_str[-(MAX_PAYLOAD_LENGTH - len(prefix)) :]
        callback_data = f"{prefix}{thread_id_str}"

    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text(
                        "buttons", "support_reply"
                    ),  # You can replace this with "📝 Reply" if preferred
                    callback_data=callback_data,
                )
            ]
        ]
    )


def support_message_cancel_keyboard() -> InlineKeyboardMarkup:
    """Create a keyboard for canceling a support message."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "support_cancel"),
                    callback_data="contact_support",
                )
            ]
        ]
    )


def support_message_sent_keyboard() -> InlineKeyboardMarkup:
    """Create a keyboard after a support message is sent."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "support_back"), "contact_support"),
        (format_text("buttons", "support_return_main"), "return_to_main"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def support_message_retry_keyboard(thread_id=None) -> InlineKeyboardMarkup:
    """Create an enhanced keyboard to retry sending a support message.

    Args:
        thread_id: Optional thread ID for reply context

    Returns:
        InlineKeyboardMarkup with retry options
    """
    # Determine correct callback data based on context
    retry_callback_data = "send_support_message"

    if thread_id:
        # Ensure thread_id is a string
        thread_id_str = str(thread_id)

        # If thread_id is too long, use just the last 20 chars which should be unique enough
        # This keeps the callback data under Telegram's 64-byte limit
        if len(thread_id_str) > 20:
            thread_id_str = thread_id_str[-20:]

        # Create callback data for replying to an existing thread
        retry_callback_data = f"reply_to_support:{thread_id_str}"

    # Create a more helpful keyboard with additional options
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "support_try_again"), retry_callback_data),
        # (format_text("buttons", "support_contact_direct"), "show_contact_details"),
        (format_text("buttons", "support_view_faqs"), "show_faqs"),
        (format_text("buttons", "support_back_menu"), "contact_support"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)
