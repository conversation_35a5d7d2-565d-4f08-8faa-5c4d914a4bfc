from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from utils.template_helpers import format_text

__all__ = [
    "purchase_success_keyboard",
    "deposit_keyboard",
    "checkout_keyboard",
    "insufficient_balance_keyboard",
]


def purchase_success_keyboard(file_link: str = None) -> InlineKeyboardMarkup:
    """Create keyboard for successful purchase with download link."""
    if file_link and not (
        file_link.startswith("telegram:") or file_link.startswith("text_message:")
    ):
        return InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text=format_text("buttons", "purchase_download"), url=file_link
                    )
                ]
            ]
        )
    return None


def deposit_keyboard(amount: float, invoice_id: str) -> InlineKeyboardMarkup:
    """Create keyboard for deposit process."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_pay"),
                    callback_data=f"pay_deposit:{amount}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_verify"),
                    callback_data=f"verify_deposit:{invoice_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_cancel_deposit"),
                    callback_data="cancel_deposit",
                )
            ],
        ]
    )


def checkout_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for checkout."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_complete"),
                    callback_data="confirm_order",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_cancel_checkout"),
                    callback_data="cancel_checkout",
                )
            ],
        ]
    )


def insufficient_balance_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for insufficient balance."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_add_credit"),
                    callback_data="deposit_funds",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_return_cart"),
                    callback_data="view_cart",
                )
            ],
        ]
    )
