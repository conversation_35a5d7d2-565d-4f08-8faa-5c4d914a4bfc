from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from utils.template_helpers import format_text
from utils.button_layout import should_use_single_row


def main_menu_keyboard() -> InlineKeyboardMarkup:
    """Create the main menu keyboard for users."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "main_menu_wallet"), "view_balance"),
        (format_text("buttons", "main_menu_shop"), "browse_products"),
        (format_text("buttons", "main_menu_deposit"), "deposit_funds"),
        (format_text("buttons", "main_menu_history"), "view_orders"),
        (format_text("buttons", "main_menu_cart"), "view_cart"),
        (format_text("buttons", "main_menu_support"), "contact_support"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)
