from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from utils.template_helpers import format_text


def edit_balance_success_keyboard():
    """Keyboard shown after successful balance edit."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "user_management_edit_another"),
                    callback_data="edit_balance_menu",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "user_management_back"),
                    callback_data="admin_users",
                )
            ],
        ]
    )


def confirm_ban_keyboard(user_id):
    """Keyboard for confirming user ban."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "user_management_ban_yes"),
                    callback_data=f"confirm_ban:{user_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "user_management_ban_no"),
                    callback_data="admin_users",
                )
            ],
        ]
    )


def confirm_unban_keyboard(user_id):
    """Keyboard for confirming user unban."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "user_management_unban_yes"),
                    callback_data=f"confirm_unban:{user_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "user_management_unban_no"),
                    callback_data="admin_users",
                )
            ],
        ]
    )


def banned_user_details_keyboard(user_id):
    """Keyboard for banned user details."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "user_management_unban"),
                    callback_data=f"confirm_unban:{user_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "user_management_back_banned"),
                    callback_data="list_banned_users",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "user_management_back_users"),
                    callback_data="admin_users",
                )
            ],
        ]
    )


def ban_user_menu_keyboard():
    """Keyboard for ban user menu."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "user_management_cancel"),
                    callback_data="admin_users",
                )
            ]
        ]
    )


def user_profile_keyboard(user_id):
    """Keyboard for user profile with tabs."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="👤 Profile", callback_data=f"user_profile_tab:{user_id}"
                ),
                InlineKeyboardButton(
                    text="📊 Analytics", callback_data=f"user_analytics:{user_id}"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📦 Orders", callback_data=f"user_orders_tab:{user_id}"
                ),
                InlineKeyboardButton(
                    text="💰 Transactions",
                    callback_data=f"user_transactions_tab:{user_id}",
                ),
            ],
            [
                InlineKeyboardButton(
                    text="💳 Deposits", callback_data=f"user_deposits_tab:{user_id}"
                ),
                InlineKeyboardButton(
                    text="💵 Edit Balance", callback_data=f"edit_user_balance:{user_id}"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="📅 Timeline", callback_data=f"user_timeline:{user_id}"
                ),
                InlineKeyboardButton(
                    text="📊 Export Data", callback_data=f"export_user_data:{user_id}"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🚫 Ban User", callback_data=f"ban_user:{user_id}"
                ),
                InlineKeyboardButton(
                    text="🔙 Back to User Management", callback_data="admin_users"
                )
            ],
        ]
    )


def user_deposits_pagination_keyboard(user_id: int, current_page: int, total_pages: int, has_prev: bool, has_next: bool):
    """Create pagination keyboard for user deposits."""
    keyboard = []

    # Pagination row
    if total_pages > 1:
        pagination_row = []

        if has_prev:
            pagination_row.append(
                InlineKeyboardButton(
                    text="⬅️ Previous",
                    callback_data=f"user_deposits_page:{user_id}:{current_page-1}"
                )
            )

        # Page indicator
        pagination_row.append(
            InlineKeyboardButton(
                text=f"📄 {current_page}/{total_pages}",
                callback_data="noop"  # Non-functional button for display
            )
        )

        if has_next:
            pagination_row.append(
                InlineKeyboardButton(
                    text="Next ➡️",
                    callback_data=f"user_deposits_page:{user_id}:{current_page+1}"
                )
            )

        keyboard.append(pagination_row)

    # Back to profile button
    keyboard.append([
        InlineKeyboardButton(
            text="🔙 Back to Profile",
            callback_data=f"user_profile_tab:{user_id}"
        )
    ])

    return InlineKeyboardMarkup(inline_keyboard=keyboard)
