from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from database.operations import get_all_categories
from utils.button_layout import should_use_single_row
from utils.template_helpers import format_text


def shop_main_keyboard() -> InlineKeyboardMarkup:
    """Create the main shop keyboard."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "shop_browse_categories"),
                    callback_data="shop_categories",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "shop_all_products"),
                    callback_data="browse_products",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "shop_back_main"),
                    callback_data="return_to_main",
                )
            ],
        ]
    )


def shop_categories_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard with product categories."""
    from datetime import datetime

    # Get categories from database
    categories = get_all_categories()

    # Sort categories by creation time, newest first
    if categories:
        categories = sorted(
            categories,
            key=lambda x: (
                x.get("created_at", datetime.min)
                if x.get("created_at")
                else datetime.min
            ),
            reverse=True,  # This makes it newest first
        )

    # Create buttons for each category
    buttons = []
    category_buttons = []  # Temporary list to hold buttons for current row

    if categories:
        for category in categories:
            category_id = category.get("id") or category.get("_id")
            category_name = category.get("name", "Unknown Category")

            # Create the button with validated text
            button_text = format_text("buttons", "shop_category_prefix") + category_name
            button = InlineKeyboardButton(
                text=button_text,
                callback_data=f"shop_category:{category_id}",
            )

            # Check if the category name is long using our utility function
            if should_use_single_row(button_text):
                # Long name - add as its own row immediately
                buttons.append([button])
            else:
                # Short name - collect for potential pairing
                category_buttons.append(button)

                # When we have 2 buttons, add them as a row
                if len(category_buttons) == 2:
                    buttons.append(category_buttons)
                    category_buttons = []  # Reset for next pair

    # Add any remaining button (for odd number of short-named categories)
    if category_buttons:
        buttons.append(category_buttons)

    # Add All Products and Back buttons
    buttons.append(
        [
            InlineKeyboardButton(
                text=format_text("buttons", "shop_all_products"),
                callback_data="browse_products",
            )
        ]
    )
    buttons.append(
        [
            InlineKeyboardButton(
                text=format_text("buttons", "shop_back_shop"), callback_data="shop_main"
            )
        ]
    )

    return InlineKeyboardMarkup(inline_keyboard=buttons)


def product_detail_keyboard(product_id, price: float) -> InlineKeyboardMarkup:
    """Create keyboard for product details."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "shop_add_cart"),
                    callback_data=f"add_to_cart:{product_id}",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "shop_buy_now").replace(
                        "{price:.2f}", f"{price:.2f}"
                    ),
                    callback_data=f"buy_product:{product_id}",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "shop_back"),
                    callback_data="browse_products",
                )
            ],
        ]
    )


def no_products_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for when no products are available."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "shop_back_categories"),
                    callback_data="shop_categories",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "shop_browse_all"),
                    callback_data="browse_products",
                )
            ],
        ]
    )
