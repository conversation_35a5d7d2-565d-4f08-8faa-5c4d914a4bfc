from aiogram.types import Inline<PERSON>eyboardMarkup, InlineKeyboardButton
from utils.button_layout import should_use_single_row
from utils.template_helpers import format_text


def balance_keyboard() -> InlineKeyboardMarkup:
    """Create a keyboard for balance menu."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "balance_add_funds"),
                    callback_data="deposit_funds",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "balance_verify_payment"),
                    callback_data="verify_latest_payment",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "balance_transaction_history"),
                    callback_data="view_transactions",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "balance_main_menu"),
                    callback_data="return_to_main",
                )
            ],
        ]
    )


def deposit_keyboard() -> InlineKeyboardMarkup:
    """Create the deposit page keyboard."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_10"),
                    callback_data="select_amount:10",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_20"),
                    callback_data="select_amount:20",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_50"),
                    callback_data="select_amount:50",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_100"),
                    callback_data="select_amount:100",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_200"),
                    callback_data="select_amount:200",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_500"),
                    callback_data="select_amount:500",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_custom"),
                    callback_data="custom_amount",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_cancel"),
                    callback_data="cancel_deposit",
                )
            ],
        ]
    )


def transactions_keyboard() -> InlineKeyboardMarkup:
    """Create the transaction history keyboard."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "transactions_back_wallet"),
                    callback_data="view_balance",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "transactions_back_home"),
                    callback_data="return_to_main",
                )
            ],
        ]
    )


# Removed duplicate support_keyboard function - using the one from support_kb.py instead


def products_keyboard(products):
    """Create keyboard with product buttons."""
    inline_keyboard = []
    product_buttons = []  # Temporary list to hold buttons for current row

    for product in products:
        product_id = product.get("id") or product.get("_id")

        # Add validation to ensure product name is never None
        product_name = product.get("name")
        if product_name is None:
            product_name = "Unnamed Product"

        # Ensure product_name is a string
        if not isinstance(product_name, str):
            product_name = str(product_name)

        # Create the button with validated text
        button = InlineKeyboardButton(
            text=product_name, callback_data=f"select_product:{product_id}"
        )

        # Check if the product name is long using our utility function
        if should_use_single_row(product_name):
            # Long name - add as its own row immediately
            inline_keyboard.append([button])
        else:
            # Short name - collect for potential pairing
            product_buttons.append(button)

            # When we have 2 buttons, add them as a row
            if len(product_buttons) == 2:
                inline_keyboard.append(product_buttons)
                product_buttons = []  # Reset for next pair

    # Add any remaining button (for odd number of short-named products)
    if product_buttons:
        inline_keyboard.append(product_buttons)

    # Add back button
    inline_keyboard.append(
        [
            InlineKeyboardButton(
                text=format_text("buttons", "products_back"),
                callback_data="return_to_main",
            )
        ]
    )

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def purchase_confirmation_keyboard() -> InlineKeyboardMarkup:
    """Create a keyboard for purchase confirmation."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_confirm"),
                    callback_data="confirm_purchase",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_cancel"),
                    callback_data="cancel_purchase",
                )
            ],
        ]
    )


def insufficient_balance_keyboard() -> InlineKeyboardMarkup:
    """Create a keyboard for insufficient balance message."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "insufficient_add_funds"),
                    callback_data="deposit_funds",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "insufficient_browse"),
                    callback_data="browse_products",
                )
            ],
        ]
    )


def purchase_success_keyboard() -> InlineKeyboardMarkup:
    """Keyboard for successful purchase."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_success_discover"),
                    callback_data="browse_products",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_success_transactions"),
                    callback_data="view_transactions",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "purchase_success_home"),
                    callback_data="return_to_main",
                )
            ],
        ]
    )


def cart_keyboard() -> InlineKeyboardMarkup:
    """Keyboard for cart view."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "cart_checkout"),
                    callback_data="checkout",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "cart_empty"),
                    callback_data="clear_cart",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "cart_continue"),
                    callback_data="browse_products",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "cart_remove"),
                    callback_data="remove_item_menu",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "cart_back_home"),
                    callback_data="return_to_main",
                )
            ],
        ]
    )


def remove_item_keyboard(cart_items) -> InlineKeyboardMarkup:
    """Create a keyboard to select which item to remove from cart."""
    inline_keyboard = []

    # Create a button for each item in the cart
    for i, item in enumerate(cart_items, 1):
        product_name = item.get("name", "Unknown Product")
        # Truncate very long product names
        if len(product_name) > 30:
            product_name = product_name[:27] + "..."

        button = InlineKeyboardButton(
            text=f"{i}. {product_name}",
            callback_data=f"remove_cart_item:{i-1}",  # Index as position in cart
        )
        inline_keyboard.append([button])

    # Add a cancel button
    inline_keyboard.append(
        [
            InlineKeyboardButton(
                text=format_text("buttons", "remove_item_cancel"),
                callback_data="view_cart",
            )
        ]
    )

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def checkout_keyboard() -> InlineKeyboardMarkup:
    """Keyboard for checkout confirmation."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "checkout_complete"),
                    callback_data="confirm_order",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "checkout_cancel"),
                    callback_data="cancel_checkout",
                )
            ],
        ]
    )


def orders_history_keyboard():
    """Keyboard for order history view."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "orders_browse"),
                    callback_data="browse_products",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "orders_back_home"),
                    callback_data="return_to_main",
                )
            ],
        ]
    )


def shop_keyboard():
    """Create the main shop keyboard."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "shop_categories"),
                    callback_data="shop_categories",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "shop_all_products"),
                    callback_data="browse_products",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "shop_main_menu"),
                    callback_data="return_to_main",
                )
            ],
        ]
    )
