from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
import re
from utils.template_helpers import format_text


def _format_thread_id_for_callback(thread_id):
    """
    Format a thread ID (string or ObjectId) for use in callback data.
    This ensures the ID can be safely included in callback_data while
    maintaining enough uniqueness.

    Args:
        thread_id: The thread ID (string or ObjectId)

    Returns:
        A properly formatted string version of the thread ID for callback data
    """
    # First convert to string if it's not already
    thread_id_str = str(thread_id)

    # Clean it to only contain valid hex characters
    clean_id = re.sub(r"[^0-9a-f]", "", thread_id_str.lower())

    # For MongoDB ObjectIds, make sure we use the full 24 character ID if available
    # This prevents issues with partial IDs that might match multiple threads
    if len(clean_id) >= 24:
        return clean_id[:24]

    return clean_id


def admin_support_panel_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for the admin support panel."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_pending"),
                    callback_data="admin_support:pending",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_replied"),
                    callback_data="admin_support:replied",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_all"),
                    callback_data="admin_support:all",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_threads"),
                    callback_data="review_threads:0:all",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_back"),
                    callback_data="back_to_admin",
                )
            ],
        ]
    )


def admin_support_error_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for admin support panel errors."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_retry"),
                    callback_data="admin_support",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_back"),
                    callback_data="back_to_admin",
                )
            ],
        ]
    )


def admin_reply_cancel_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for canceling admin reply."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_cancel"),
                    callback_data="cancel_admin_reply",
                )
            ]
        ]
    )


def admin_reply_success_keyboard(thread_id: str) -> InlineKeyboardMarkup:
    """Create keyboard after successful admin reply."""
    # Format the thread ID properly for callback data
    safe_thread_id = _format_thread_id_for_callback(thread_id)

    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_view_thread"),
                    callback_data=f"view_replies:{safe_thread_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_resolve"),
                    callback_data=f"resolve_support:{safe_thread_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_back_support"),
                    callback_data="admin_support",
                )
            ],
        ]
    )


def admin_thread_resolved_keyboard(thread_id: str) -> InlineKeyboardMarkup:
    """Create keyboard after thread is resolved."""
    # Format the thread ID properly for callback data
    safe_thread_id = _format_thread_id_for_callback(thread_id)

    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_reopen"),
                    callback_data=f"reopen_support:{safe_thread_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_view_thread"),
                    callback_data=f"view_replies:{safe_thread_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_back_support"),
                    callback_data="admin_support",
                )
            ],
        ]
    )


def check_for_replies_keyboard(thread_id: str) -> InlineKeyboardMarkup:
    """Create keyboard for checking for replies on a thread.

    Args:
        thread_id: The ID of the thread to check for replies

    Returns:
        Keyboard with button to check for replies
    """
    # Format the thread ID properly for callback data
    safe_thread_id = _format_thread_id_for_callback(thread_id)

    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_check_replies"),
                    callback_data=f"check_thread_status:{safe_thread_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_support_back_contact"),
                    callback_data="contact_support",
                )
            ],
        ]
    )
