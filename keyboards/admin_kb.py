from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from typing import List, Dict, Any
from utils.template_helpers import format_text
from utils.button_layout import should_use_single_row


def admin_main_keyboard(is_owner=False):
    """Create admin main keyboard based on role."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        ("🔧 Unified Product Management", "unified_product_management"),
        (format_text("buttons", "admin_panel_products"), "admin_products"),
        (format_text("buttons", "admin_panel_users"), "admin_users"),
        (format_text("buttons", "admin_panel_support"), "admin_support"),
        (format_text("buttons", "admin_panel_categories"), "manage_categories"),
        ("🎁 Bonus Management", "bonus_management"),
    ]

    # Add owner-specific buttons if applicable
    if is_owner:
        buttons.extend(
            [
                (format_text("buttons", "owner_panel_admin"), "owner_manage_admins"),
                (format_text("buttons", "owner_panel_stats"), "system_stats"),
                (format_text("buttons", "owner_panel_advanced"), "additional_features"),
            ]
        )

    # Add common buttons
    buttons.extend(
        [
            (
                format_text("buttons", "admin_panel_view_customer"),
                "view_customer_panel",
            ),
            (format_text("buttons", "admin_panel_refresh"), "admin_panel"),
        ]
    )

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def additional_features_keyboard():
    """
    Create keyboard for additional features menu.

    Returns:
        InlineKeyboardMarkup: The keyboard markup
    """
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "owner_advanced_announcement"), "send_announcement"),
        (
            format_text("buttons", "owner_advanced_view_announcements"),
            "view_announcements",
        ),
        (format_text("buttons", "owner_advanced_log_channel"), "set_log_channel"),
        (format_text("buttons", "owner_advanced_templates"), "manage_templates"),
        (format_text("buttons", "owner_advanced_faq"), "manage_faqs"),
        (
            format_text("buttons", "owner_advanced_maintenance"),
            "toggle_maintenance_mode",
        ),
        (
            format_text(
                "buttons",
                "owner_advanced_welcome_message",
                default="🎉 Welcome Message",
            ),
            "manage_welcome_message",
        ),
        (
            format_text("buttons", "owner_advanced_diagnostics"),
            "system_diagnostics",
        ),
        (
            format_text(
                "buttons",
                "owner_advanced_database_export",
                default="💾 Database Export"
            ),
            "database_export_menu",
        ),
        (format_text("buttons", "owner_advanced_back"), "back_to_admin"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def product_management_keyboard():
    """Generate keyboard for product management."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "admin_products_add"), "add_new_product"),
        (format_text("buttons", "admin_products_edit"), "edit_product_menu"),
        (format_text("buttons", "admin_products_delete"), "delete_product_menu"),
        (format_text("buttons", "admin_products_list"), "list_all_products"),
        (format_text("buttons", "admin_products_back"), "back_to_admin"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def user_management_keyboard():
    """Generate keyboard for user management."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "admin_users_search"), "search_user"),
        (format_text("buttons", "admin_users_edit_balance"), "edit_balance_menu"),
        (format_text("buttons", "admin_users_ban"), "ban_user_menu"),
        (format_text("buttons", "admin_users_unban"), "unban_user_menu"),
        (format_text("buttons", "admin_users_list_banned"), "list_banned_users"),
        (format_text("buttons", "admin_users_recent_orders"), "view_recent_orders"),
        (format_text("buttons", "admin_users_back"), "back_to_admin"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def confirm_ban_keyboard(user_id: int):
    """Create a keyboard for confirming a ban."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_confirm_ban"),
                    callback_data=f"confirm_ban:{user_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_cancel_ban"),
                    callback_data="admin_users",
                )
            ],
        ]
    )


def confirm_unban_keyboard(user_id: int):
    """Create a keyboard for confirming an unban."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_confirm_unban"),
                    callback_data=f"confirm_unban:{user_id}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_cancel_unban"),
                    callback_data="admin_users",
                )
            ],
        ]
    )


def banned_user_details_keyboard(user_id: int):
    """Create a keyboard for banned user details."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="Unban User", callback_data=f"confirm_unban:{user_id}"
                )
            ],
            [
                InlineKeyboardButton(
                    text="Back to Banned Users List", callback_data="list_banned_users"
                )
            ],
            [
                InlineKeyboardButton(
                    text="Back to User Management", callback_data="admin_users"
                )
            ],
        ]
    )


def admin_management_keyboard():
    """Admin management keyboard for owner."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "owner_admin_add"), "add_admin"),
        (format_text("buttons", "owner_admin_remove"), "remove_admin"),
        (format_text("buttons", "owner_admin_list"), "list_all_admins"),
        (format_text("buttons", "owner_admin_back"), "back_to_admin"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def products_list_keyboard(products):
    """Generate keyboard with product list for editing."""
    inline_keyboard = []

    if not products:
        # Handle empty product list
        inline_keyboard.append(
            [
                InlineKeyboardButton(
                    text="No products available", callback_data="no_action"
                )
            ]
        )
    else:
        for product in products:
            try:
                product_id = product.get("id", "unknown")
                product_name = product.get("name", "Unnamed Product")
                product_price = product.get("price", 0)

                # Format the button text and make sure we have valid data
                button_text = f"{product_name} - ${float(product_price):.2f}"
                callback_data = f"edit_product:{product_id}"

                inline_keyboard.append(
                    [
                        InlineKeyboardButton(
                            text=button_text, callback_data=callback_data
                        )
                    ]
                )
            except Exception:
                # Skip this product if there's any error processing it
                continue

    # Always add the back button
    inline_keyboard.append(
        [InlineKeyboardButton(text="Back", callback_data="admin_products")]
    )

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def product_edit_field_keyboard(product_id):
    """Generate keyboard for selecting which field to edit."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✏️ Update Name", callback_data=f"edit_field:name"
                )
            ],
            [
                InlineKeyboardButton(
                    text="✏️ Update Description", callback_data=f"edit_field:description"
                )
            ],
            [
                InlineKeyboardButton(
                    text="✏️ Update Price", callback_data=f"edit_field:price"
                )
            ],
            [
                InlineKeyboardButton(
                    text="✏️ Update File Link", callback_data=f"edit_field:file_link"
                )
            ],
            [
                InlineKeyboardButton(
                    text="✏️ Update Image URL", callback_data=f"edit_field:image_url"
                )
            ],
            [InlineKeyboardButton(text="❌ Cancel", callback_data="cancel_edit")],
        ]
    )


def product_add_confirmation_keyboard():
    """Keyboard for confirming product addition."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            # Additional product details first
            [
                InlineKeyboardButton(
                    text="➕ Add Tags", callback_data="add_product_tags"
                ),
                InlineKeyboardButton(
                    text="🔢 Set Quantity", callback_data="set_product_quantity"
                ),
            ],
            [
                InlineKeyboardButton(
                    text="🟢 Set Availability", callback_data="add_availability_option"
                )
            ],
            # Visual separator
            [
                InlineKeyboardButton(
                    text="\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022\u2022",
                    callback_data="dummy_action",
                )
            ],
            # Preview option
            [
                InlineKeyboardButton(
                    text="🔍 Preview Product", callback_data="preview_product"
                )
            ],  # Edit details
            [
                InlineKeyboardButton(
                    text="📝 Edit Details", callback_data="edit_product:"
                )
            ],
            # Final actions
            [
                InlineKeyboardButton(
                    text="✅ Confirm and Add", callback_data="confirm_add_product"
                ),
                InlineKeyboardButton(
                    text="❌ Cancel", callback_data="cancel_add_product"
                ),
            ],
        ]
    )


def product_add_success_keyboard():
    """Generate keyboard after successful product addition."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="➕ Add Another Product", callback_data="add_new_product"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📋 View All Products",
                    callback_data="edit_products_category:all",
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Admin Panel", callback_data="back_to_admin"
                )
            ],
        ]
    )


def edit_balance_success_keyboard():
    """Generate keyboard after successful balance edit."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="Edit Another User", callback_data="edit_balance_menu"
                )
            ],
            [
                InlineKeyboardButton(
                    text="Back to User Management", callback_data="admin_users"
                )
            ],
            [
                InlineKeyboardButton(
                    text="Back to Admin Panel", callback_data="back_to_admin"
                )
            ],
        ]
    )


def welcome_message_keyboard(is_enabled: bool) -> InlineKeyboardMarkup:
    """Create keyboard for welcome message management.

    Args:
        is_enabled: Whether the welcome message is currently enabled

    Returns:
        InlineKeyboardMarkup: The keyboard markup
    """
    status_text = "✅ Enabled" if is_enabled else "❌ Disabled"
    toggle_text = "🔴 Disable" if is_enabled else "🟢 Enable"
    toggle_callback = "toggle_welcome_message"

    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=f"Status: {status_text}", callback_data="no_action"
                )
            ],
            [InlineKeyboardButton(text=toggle_text, callback_data=toggle_callback)],
            [
                InlineKeyboardButton(
                    text="✏️ Edit Message Text", callback_data="edit_welcome_text"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🖼️ Upload New Image", callback_data="upload_welcome_image"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🗑️ Remove Image", callback_data="remove_welcome_image"
                )
            ],
            [
                InlineKeyboardButton(
                    text="👁️ Preview Welcome Message",
                    callback_data="preview_welcome_message",
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Advanced Features",
                    callback_data="additional_features",
                )
            ],
        ]
    )


def maintenance_mode_keyboard(is_enabled: bool) -> InlineKeyboardMarkup:
    """Create keyboard for maintenance mode toggle.

    Args:
        is_enabled: Whether maintenance mode is currently enabled

    Returns:
        InlineKeyboardMarkup: The keyboard markup
    """
    toggle_text = (
        "🔴 Disable Maintenance Mode" if is_enabled else "🟢 Enable Maintenance Mode"
    )
    toggle_callback = "maintenance_off" if is_enabled else "maintenance_on"

    return InlineKeyboardMarkup(
        inline_keyboard=[
            [InlineKeyboardButton(text=toggle_text, callback_data=toggle_callback)],
            [
                InlineKeyboardButton(
                    text="✏️ Edit Maintenance Message",
                    callback_data="edit_maintenance_message",
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Additional Features",
                    callback_data="additional_features",
                )
            ],
        ]
    )


def system_stats_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for system stats view."""
    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="Back to Admin Panel", callback_data="back_to_admin"
                )
            ]
        ]
    )
    return keyboard


def announcement_confirmation_keyboard():
    """Return a keyboard for announcement confirmation."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_announcement_confirm"),
                    callback_data="confirm_announcement",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_announcement_cancel"),
                    callback_data="cancel_announcement",
                )
            ],
        ]
    )


def after_announcement_keyboard():
    """Return a keyboard for after sending announcement."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_announcement_another"),
                    callback_data="send_announcement",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_announcement_view"),
                    callback_data="view_announcements",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_announcement_back"),
                    callback_data="additional_features",
                )
            ],
        ]
    )


def faq_management_keyboard():
    """Return a keyboard for FAQ management."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "faq_view"), "view_faqs_admin"),
        (format_text("buttons", "faq_add"), "add_faq"),
        (format_text("buttons", "faq_edit"), "edit_faq"),
        (format_text("buttons", "faq_delete"), "delete_faq"),
        (format_text("buttons", "faq_back"), "additional_features"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def category_management_keyboard():
    """Keyboard for category management options."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "admin_categories_view"), "list_categories"),
        (format_text("buttons", "admin_categories_add"), "add_category"),
        (format_text("buttons", "admin_categories_manage"), "edit_category"),
        (format_text("buttons", "admin_categories_back"), "back_to_admin"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def template_management_keyboard():
    """Keyboard for template management menu."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "owner_templates_view"), "view_templates"),
        (format_text("buttons", "owner_templates_edit"), "edit_templates"),
        (format_text("buttons", "owner_templates_backup"), "backup_templates"),
        (format_text("buttons", "owner_templates_restore"), "restore_templates"),
        (format_text("buttons", "owner_templates_back"), "additional_features"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def template_file_selection_keyboard(template_files):
    """Keyboard for selecting which template file to view/edit."""
    keyboard = []

    for template_file in template_files:
        # Remove .json extension for display
        display_name = template_file
        if display_name.endswith(".json"):
            display_name = display_name[:-5]

        # Create button for each template file
        keyboard.append(
            [
                InlineKeyboardButton(
                    text=f"📄 {display_name.capitalize()}",
                    callback_data=f"template_file:{template_file}",
                )
            ]
        )

    # Add back button
    keyboard.append(
        [
            InlineKeyboardButton(
                text=format_text("buttons", "template_file_back"),
                callback_data="manage_templates",
            )
        ]
    )

    return InlineKeyboardMarkup(inline_keyboard=keyboard)


def template_edit_confirmation_keyboard(template_file, page=1):
    """
    Keyboard for confirming template changes or returning.

    Args:
        template_file: The template file name
        page: The page number to return to (for paginated templates)
    """
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "template_edit_another"),
                    callback_data=f"template_file:{template_file}:{page}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "template_back_files"),
                    callback_data="edit_templates",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "template_back_menu"),
                    callback_data="manage_templates",
                )
            ],
        ]
    )


def product_tags_management_keyboard():
    """Keyboard for managing product tags."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "product_tags_add"), "add_tag"),
        (format_text("buttons", "product_tags_edit"), "edit_tag"),
        (format_text("buttons", "product_tags_remove"), "remove_tag"),
        (format_text("buttons", "product_tags_view"), "view_all_tags"),
        (format_text("buttons", "product_tags_back"), "product_add_confirmation"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def template_edit_confirm_save_keyboard(template_file, key):
    """
    Keyboard for confirming template edit before saving.

    Args:
        template_file: The template file name
        key: The template key being edited
    """
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text(
                        "buttons", "template_confirm_save", "✅ Confirm and Save"
                    ),
                    callback_data=f"confirm_template_edit:{template_file}:{key}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "template_edit_again", "✏️ Edit Again"),
                    callback_data=f"edit_template_key:{template_file}:{key}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "template_cancel_edit", "❌ Cancel"),
                    callback_data=f"template_file:{template_file}",
                )
            ],
        ]
    )
