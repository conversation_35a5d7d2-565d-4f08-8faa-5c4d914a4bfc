from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from utils.callback_factories import DepositCallback
from utils.template_helpers import format_text
from utils.button_layout import should_use_single_row


def deposit_amount_keyboard():
    """Keyboard with preset deposit amounts and cancel button."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_amount_10"),
                    callback_data="select_amount:10",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_amount_20"),
                    callback_data="select_amount:20",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_amount_50"),
                    callback_data="select_amount:50",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_amount_100"),
                    callback_data="select_amount:100",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_amount_200"),
                    callback_data="select_amount:200",
                ),
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_amount_500"),
                    callback_data="select_amount:500",
                ),
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_amount_custom"),
                    callback_data="custom_amount",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_amount_cancel"),
                    callback_data="cancel_deposit",
                )
            ],
        ]
    )


def deposit_pay_keyboard(amount):
    """Keyboard for payment confirmation."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_pay"),
                    callback_data=f"pay_deposit:{amount}",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_amount_cancel"),
                    callback_data="cancel_deposit",
                )
            ],
        ]
    )


def deposit_cancel_keyboard():
    """Keyboard for canceled deposit."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_return"),
                    callback_data="return_to_main",
                )
            ]
        ]
    )


def custom_amount_cancel_keyboard():
    """Keyboard for canceling custom amount input."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "deposit_amount_cancel"),
                    callback_data="cancel_deposit",
                )
            ]
        ]
    )


def payment_verification_keyboard():
    """Keyboard for verifying payments."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "deposit_try_again"), "deposit_funds"),
        (format_text("buttons", "deposit_return"), "return_to_main"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def payment_success_keyboard():
    """Keyboard for successful payment."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "deposit_view_balance"), "view_balance"),
        (format_text("buttons", "deposit_return"), "return_to_main"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)


def payment_processing_keyboard(track_id):
    """Keyboard for payment still processing."""
    inline_keyboard = []

    # Define all buttons with their text and callback data
    buttons = [
        (format_text("buttons", "deposit_check_again"), f"check_payment:{track_id}"),
        (format_text("buttons", "deposit_amount_cancel"), "cancel_deposit"),
    ]

    # Temporary list to hold buttons for current row
    current_row = []

    for button_text, callback_data in buttons:
        button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

        # Check if the button text is long
        if should_use_single_row(button_text):
            # If we have buttons in the current row, add them first
            if current_row:
                inline_keyboard.append(current_row)
                current_row = []

            # Long text - add as its own row
            inline_keyboard.append([button])
        else:
            # Short text - collect for potential pairing
            current_row.append(button)

            # When we have 2 buttons, add them as a row
            if len(current_row) == 2:
                inline_keyboard.append(current_row)
                current_row = []

    # Add any remaining buttons in the current row
    if current_row:
        inline_keyboard.append(current_row)

    return InlineKeyboardMarkup(inline_keyboard=inline_keyboard)
