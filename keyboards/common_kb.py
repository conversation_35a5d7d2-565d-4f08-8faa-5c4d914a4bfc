from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from utils.template_helpers import format_text


def back_to_menu_keyboard():
    """Simple keyboard with return to menu button."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "common_back_menu"),
                    callback_data="return_to_main",
                )
            ]
        ]
    )


def browse_products_keyboard():
    """Keyboard for browsing products."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "common_browse_products"),
                    callback_data="browse_products",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "common_back_menu"),
                    callback_data="return_to_main",
                )
            ],
        ]
    )


def cancel_checkout_keyboard():
    """Keyboard for canceling checkout."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "common_browse_products"),
                    callback_data="browse_products",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "common_back_menu"),
                    callback_data="return_to_main",
                )
            ],
        ]
    )


def cancel_admin_reply_keyboard():
    """Keyboard for canceling admin reply."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_back_panel"),
                    callback_data="back_to_admin",
                )
            ]
        ]
    )


def cancel_edit_keyboard():
    """Keyboard for canceling edit operations."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_back_products"),
                    callback_data="admin_products",
                )
            ]
        ]
    )


def cancel_add_product_keyboard():
    """Keyboard for canceling product addition."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_product_try_again"),
                    callback_data="add_new_product",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "admin_back_panel"),
                    callback_data="back_to_admin",
                )
            ],
        ]
    )


def help_keyboard():
    """Keyboard for help button response."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "common_back_menu"),
                    callback_data="return_to_main",
                )
            ]
        ]
    )
