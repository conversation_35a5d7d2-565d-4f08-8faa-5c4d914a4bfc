from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from utils.template_helpers import format_text


def empty_orders_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for when user has no orders."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                Inline<PERSON>eyboardButton(
                    text=format_text("buttons", "orders_empty_browse"),
                    callback_data="browse_products",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "orders_empty_return"),
                    callback_data="return_to_main",
                )
            ],
        ]
    )


def orders_view_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for orders view."""
    return InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "orders_view_discover"),
                    callback_data="browse_products",
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "orders_view_return"),
                    callback_data="return_to_main",
                )
            ],
        ]
    )
