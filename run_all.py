#!/usr/bin/env python
"""
Combined runner for Telegram Bot and Flask Callback Server
This script runs both the Telegram bot and the Flask payment callback server
in a single terminal window using threading.
"""

import os
import sys
import time
import threading
import logging
import asyncio
import socket
from typing import Optional, Dict, Any
import argparse
import traceback
from waitress import serve


# Function to get the host machine's IP address
def get_host_ip():
    """Get the primary IP address of the host machine"""
    try:
        # This creates a socket, connects to an external server (but doesn't send data)
        # and gets the local IP address that would be used for that connection
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # Using a public DNS server as the target - doesn't actually connect
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to determine host IP address: {e}")
        return "127.0.0.1"  # Fallback to localhost


# Configure paths to ensure imports work correctly
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Import colorama and rich for terminal output styling
try:
    from colorama import Fore, Back, Style, init as colorama_init
    from rich.logging import RichHandler
    from rich.console import Console
    from rich.panel import Panel

    HAS_RICH = True
except ImportError:
    print("Installing required packages for colored output...")
    import subprocess

    subprocess.check_call([sys.executable, "-m", "pip", "install", "colorama", "rich"])
    from colorama import Fore, Back, Style, init as colorama_init
    from rich.logging import RichHandler
    from rich.console import Console
    from rich.panel import Panel

    HAS_RICH = True

# Initialize colorama for cross-platform color support
colorama_init(autoreset=True)

# Create console for rich output
console = Console()

# Define maintenance mode flag
MAINTENANCE_MODE = os.environ.get("MAINTENANCE_MODE", "0").lower() in (
    "1",
    "true",
    "yes",
)

# Define development mode flag
DEVELOPMENT_MODE = os.environ.get("DEVELOPMENT_MODE", "0").lower() in (
    "1",
    "true",
    "yes",
)


# Custom logger configurations
class CustomBotFormatter(logging.Formatter):
    """Custom formatter for the bot logger with colorama colors"""

    # Store previous level to detect changes
    previous_level = None

    def format(self, record):
        # Store the original levelname
        original_levelname = record.levelname

        # Add a divider if the level changed
        divider = ""
        if (
            CustomBotFormatter.previous_level is not None
            and CustomBotFormatter.previous_level != record.levelno
        ):
            divider = f"\n{'-' * 50}\n"

        # Store the current level for the next message
        CustomBotFormatter.previous_level = record.levelno

        # Add development mode indicator if active
        dev_prefix = (
            f"{Back.MAGENTA}{Fore.WHITE} DEV {Style.RESET_ALL} "
            if DEVELOPMENT_MODE
            else ""
        )

        # Add the BOT prefix to the levelname with background color
        if record.levelno >= logging.ERROR:
            record.levelname = f"{dev_prefix}{Back.RED}{Fore.WHITE} BOT-{record.levelname} {Style.RESET_ALL}"
        elif record.levelno >= logging.WARNING:
            record.levelname = f"{dev_prefix}{Back.YELLOW}{Fore.BLACK} BOT-{record.levelname} {Style.RESET_ALL}"
        else:
            record.levelname = f"{dev_prefix}{Back.BLUE}{Fore.WHITE} BOT-{record.levelname} {Style.RESET_ALL}"

        # Format the record with the colored levelname
        result = divider + super().format(record)

        # Restore the original levelname for future handlers
        record.levelname = original_levelname

        return result


class CustomFlaskFormatter(logging.Formatter):
    """Custom formatter for the Flask logger with colorama colors"""

    # Store previous level to detect changes
    previous_level = None

    def format(self, record):
        # Store the original levelname
        original_levelname = record.levelname

        # Add a divider if the level changed
        divider = ""
        if (
            CustomFlaskFormatter.previous_level is not None
            and CustomFlaskFormatter.previous_level != record.levelno
        ):
            divider = f"\n{'-' * 50}\n"

        # Store the current level for the next message
        CustomFlaskFormatter.previous_level = record.levelno

        # Add development mode indicator if active
        dev_prefix = (
            f"{Back.MAGENTA}{Fore.WHITE} DEV {Style.RESET_ALL} "
            if DEVELOPMENT_MODE
            else ""
        )

        # Add the FLASK prefix to the levelname with background color
        if record.levelno >= logging.ERROR:
            record.levelname = f"{dev_prefix}{Back.RED}{Fore.WHITE} FLASK-{record.levelname} {Style.RESET_ALL}"
        elif record.levelno >= logging.WARNING:
            record.levelname = f"{dev_prefix}{Back.YELLOW}{Fore.BLACK} FLASK-{record.levelname} {Style.RESET_ALL}"
        else:
            record.levelname = f"{dev_prefix}{Back.GREEN}{Fore.BLACK} FLASK-{record.levelname} {Style.RESET_ALL}"

        # Format the record with the colored levelname
        result = divider + super().format(record)

        # Restore the original levelname for future handlers
        record.levelname = original_levelname

        return result


def setup_bot_logger():
    """Configure and return a logger for the bot with custom formatting"""
    bot_logger = logging.getLogger("bot_logger")
    bot_logger.setLevel(logging.INFO)

    # Create handlers
    file_handler = logging.FileHandler("bot.log")
    console_handler = logging.StreamHandler()

    # Create and set formatters
    file_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    file_handler.setFormatter(file_formatter)

    console_handler.setFormatter(CustomBotFormatter("%(message)s"))

    # Add handlers to logger
    bot_logger.addHandler(file_handler)
    bot_logger.addHandler(console_handler)

    # Make sure the logger DOES propagate to the root logger
    bot_logger.propagate = True  # Changed from False to True

    return bot_logger


def setup_flask_logger():
    """Configure and return a logger for Flask with custom formatting"""
    flask_logger = logging.getLogger("flask_logger")
    flask_logger.setLevel(logging.INFO)

    # Create handlers
    file_handler = logging.FileHandler("flask_callback.log")
    console_handler = logging.StreamHandler()

    # Create and set formatters
    file_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    file_handler.setFormatter(file_formatter)

    console_handler.setFormatter(CustomFlaskFormatter("%(message)s"))

    # Add handlers to logger
    flask_logger.addHandler(file_handler)
    flask_logger.addHandler(console_handler)

    # Make sure the logger doesn't propagate to the root logger
    flask_logger.propagate = False

    return flask_logger


# Set up the loggers
bot_logger = setup_bot_logger()
flask_logger = setup_flask_logger()


def run_flask_server(
    host: str, port: int, debug: bool, testing: bool, max_retries: int
):
    """Run the Flask server in a separate thread"""
    try:
        # Use a separate thread to avoid blocking the main thread
        flask_logger.info(f"Starting Flask callback server on {host}:{port}")

        # Log additional information in development mode
        if DEVELOPMENT_MODE and host == "0.0.0.0":
            flask_logger.info(
                "Server is accessible from all network interfaces (0.0.0.0)"
            )
            flask_logger.info(
                "You can access it from other devices using your machine's IP address"
            )

        # Import Flask server components
        from payments.flask_server import app

        # Override the Flask server's logger to use our custom logger
        import logging

        werkzeug_logger = logging.getLogger("werkzeug")
        werkzeug_logger.handlers.clear()
        werkzeug_handler = logging.StreamHandler()
        werkzeug_handler.setFormatter(CustomFlaskFormatter("%(message)s"))
        werkzeug_logger.addHandler(werkzeug_handler)
        werkzeug_logger.propagate = False

        # Configure Flask app based on parameters
        app.config["DEBUG"] = debug
        app.config["TESTING"] = testing
        app.config["MAX_RETRY_ATTEMPTS"] = max_retries

        # Print startup banner
        console.print(
            Panel(
                f"[bold green]🌐 Flask Payment Server[/bold green]\n"
                f"[bright_green]━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[/bright_green]\n"
                f"[cyan]Host:[/cyan] {host}\n"
                f"[cyan]Port:[/cyan] {port}\n"
                f"[cyan]Debug:[/cyan] {debug}\n"
                f"[cyan]Testing:[/cyan] {testing}",
                title="[bold green]FLASK SERVER[/bold green]",
                border_style="green",
                padding=(1, 2),
            )
        )

        # Run the Flask app with waitress server
        serve(app, host=host, port=port)

    except Exception as e:
        flask_logger.error(f"Error in Flask server: {e}")
        flask_logger.error(traceback.format_exc())


async def run_telegram_bot():
    """Run the Telegram bot"""
    try:
        bot_logger.info("Starting Telegram bot")

        # Import the bot's main function
        from main import main as bot_main

        # Import config to verify development mode is set correctly
        import config

        bot_logger.info(f"Development mode in config: {config.DEVELOPMENT_MODE}")

        # Ensure development mode is properly set in the environment
        if DEVELOPMENT_MODE:
            bot_logger.info("Running bot in DEVELOPMENT MODE")
            # Double-check that the environment variable is set
            if os.environ.get("DEVELOPMENT_MODE") != "true":
                os.environ["DEVELOPMENT_MODE"] = "true"
                bot_logger.info("Fixed DEVELOPMENT_MODE environment variable")

        # Determine display mode
        mode_display = []
        if DEVELOPMENT_MODE:
            mode_display.append("[magenta]Development[/magenta]")
        if MAINTENANCE_MODE:
            mode_display.append("[red]Maintenance[/red]")

        if not mode_display:
            mode_display.append("[green]Production[/green]")

        mode_text = " + ".join(mode_display)

        # Print startup banner
        console.print(
            Panel(
                f"[bold blue]🤖 Telegram Bot[/bold blue]\n"
                f"[blue]━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[/blue]\n"
                f"[cyan]API:[/cyan] Telegram Bot API\n"
                f"[cyan]Mode:[/cyan] {mode_text}\n"
                f"[cyan]Status:[/cyan] Active",
                title="[bold blue]TELEGRAM BOT[/bold blue]",
                border_style="blue",
                padding=(1, 2),
            )
        )

        # Run the bot's main function
        await bot_main()

    except Exception as e:
        bot_logger.error(f"Error in Telegram bot: {e}")
        bot_logger.error(traceback.format_exc())
        console.print(f"[bold red]Error in Telegram bot: {e}[/bold red]")
        console.print(traceback.format_exc())


def parse_arguments():
    """Parse command-line arguments"""
    from payments.payment_config import DEFAULT_PORT

    parser = argparse.ArgumentParser(
        description="Run both Telegram bot and Flask server in one process"
    )

    # Application mode arguments - we process these first to set correct defaults
    parser.add_argument(
        "--dev",
        "--development",
        dest="development_mode",
        action="store_true",
        help="Run the application in development mode",
    )
    parser.add_argument(
        "--maintenance",
        action="store_true",
        help="Run the application in maintenance mode",
    )

    # Parse mode arguments first to determine the appropriate host IP
    args, _ = parser.parse_known_args()

    # In production mode, use the actual server IP; in dev mode use 0.0.0.0
    if not args.development_mode:
        detected_host = get_host_ip()
    else:
        detected_host = (
            "0.0.0.0"  # Use 0.0.0.0 in development mode to listen on all interfaces
        )

    # Flask server arguments
    parser.add_argument(
        "--host",
        default=detected_host,
        help=f"Flask server host (detected: {detected_host})",
    )
    parser.add_argument(
        "--port",
        type=int,
        default=DEFAULT_PORT,
        help=f"Flask server port (default: {DEFAULT_PORT})",
    )
    parser.add_argument("--debug", action="store_true", help="Enable Flask debug mode")
    parser.add_argument(
        "--testing", action="store_true", help="Enable Flask testing mode"
    )
    parser.add_argument(
        "--max-retries",
        type=int,
        default=3,
        help="Maximum retry attempts for database operations",
    )

    return parser.parse_args()


async def main():
    """Main function to start both the Flask server and Telegram bot"""
    # Parse command line arguments
    args = parse_arguments()

    # Set environment variables based on command-line arguments
    if args.development_mode:
        os.environ["DEVELOPMENT_MODE"] = "true"
        global DEVELOPMENT_MODE
        DEVELOPMENT_MODE = True
        # Force reload of config module to pick up the new environment variable
        import importlib
        import config

        importlib.reload(config)

    if args.maintenance:
        os.environ["MAINTENANCE_MODE"] = "true"
        global MAINTENANCE_MODE
        MAINTENANCE_MODE = True
        # Force reload of config module to pick up the new environment variable
        import importlib
        import config

        importlib.reload(config)

    # Display welcome banner
    mode_text = ""
    if DEVELOPMENT_MODE:
        mode_text += f"{Back.MAGENTA}{Fore.WHITE} DEVELOPMENT MODE {Style.RESET_ALL} "
    if MAINTENANCE_MODE:
        mode_text += f"{Back.RED}{Fore.WHITE} MAINTENANCE MODE {Style.RESET_ALL} "

    if mode_text:
        mode_display = f"[white]Running in: {mode_text}[/white]\n"
    else:
        mode_display = "[white]Running in: Production Mode[/white]\n"

    console.print(
        Panel(
            "[bold yellow]📡 Combined Service Runner 📡[/bold yellow]\n"
            "[yellow]━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━[/yellow]\n"
            f"{mode_display}"
            "[white]This terminal shows output from two services:[/white]\n"
            f"{Back.BLUE}{Fore.WHITE} 🤖 BOT {Style.RESET_ALL} - Telegram bot messages appear with blue background\n"
            f"{Back.GREEN}{Fore.BLACK} 🌐 FLASK {Style.RESET_ALL} - Flask server messages appear with green background",
            title="[bold yellow]SYSTEM STARTUP[/bold yellow]",
            border_style="yellow",
            padding=(1, 2),
        )
    )

    # Adjust logging levels based on mode
    if DEVELOPMENT_MODE:
        bot_logger.setLevel(logging.DEBUG)
        flask_logger.setLevel(logging.DEBUG)

        # Set pymongo logger to INFO level to avoid excessive debug logs
        pymongo_logger = logging.getLogger("pymongo")
        pymongo_logger.setLevel(logging.INFO)

        # Set motor logger to INFO level (async MongoDB driver)
        motor_logger = logging.getLogger("motor")
        motor_logger.setLevel(logging.INFO)

        console.print(
            "[bold magenta]Development mode: verbose logging enabled[/bold magenta]"
        )
        console.print(
            "[bold cyan]MongoDB debug logs suppressed for cleaner output[/bold cyan]"
        )
    else:
        bot_logger.setLevel(logging.INFO)
        flask_logger.setLevel(logging.INFO)

    # Start the Flask server in a separate thread
    flask_thread = threading.Thread(
        target=run_flask_server,
        args=(args.host, args.port, args.debug, args.testing, args.max_retries),
        daemon=True,  # Set as daemon so it exits when the main thread exits
    )
    flask_thread.start()

    # Give Flask a moment to start before running the bot
    await asyncio.sleep(2)

    # Run the Telegram bot in the main thread (asyncio event loop)
    try:
        await run_telegram_bot()
    except KeyboardInterrupt:
        console.print(
            Panel(
                "[bold red]Received keyboard interrupt[/bold red]\n"
                "[white]Shutting down services...[/white]",
                title="[bold]SHUTDOWN[/bold]",
                border_style="red",
            )
        )
    except Exception as e:
        console.print(
            Panel(
                f"[bold red]Error in main process:[/bold red]\n"
                f"[white]{str(e)}[/white]",
                title="[bold]ERROR[/bold]",
                border_style="red",
            )
        )
        traceback.print_exc()


if __name__ == "__main__":
    try:
        # Print the current environment variables
        print(
            f"DEVELOPMENT_MODE environment variable: {os.getenv('DEVELOPMENT_MODE', 'not set')}"
        )

        # Import config and print the development mode status
        import config

        print(f"DEVELOPMENT_MODE in config: {config.DEVELOPMENT_MODE}")

        # Run the main async function
        asyncio.run(main())
    except KeyboardInterrupt:
        console.print("\n[bold red]Process terminated by user[/bold red]")
    except Exception as e:
        console.print(f"\n[bold red]Fatal error: {e}[/bold red]")
        traceback.print_exc()
