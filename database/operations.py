from __future__ import annotations
import logging
import re

# Corrected datetime import
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any, Union
import functools


# Import pymongo for ReturnDocument
import pymongo

logger = logging.getLogger(__name__)
# --- Database Connection and Collections ---
# Assume these are correctly configured and imported
from .connection import (
    db,
    db_async,
    users_collection,
    users_collection_async,
    transactions_collection,
    transactions_collection_async,
    products_collection,
    products_collection_async,
    carts_collection,
    carts_collection_async,
    admins_collection,
    admins_collection_async,
    settings_collection,
    settings_collection_async,
    banned_users_collection,
    banned_users_collection_async,
    announcements_collection,
    payments_collection,
    support_messages_collection,
    support_messages_collection_async,
    categories_collection,
    categories_collection_async,
    bonus_tiers_collection,
    bonus_tiers_collection_async,
    line_purchase_history_collection,
    user_reservations_collection,
    orders_collection,  # Added missing orders collection
    admin_logs_collection,  # Added missing admin logs collection
    support_messages_collection,  # Added for comprehensive user export
    initialize_indexes,
    initialize_indexes_async,  # Import index functions
)



# --- Configuration ---
from config import OWNER_ID  # Assuming OWNER_ID is an integer

# --- Sandbox Decorator ---
from utils.privileged_operations import sandbox_aware_db_write

# --- Helper Functions ---


def get_db():
    """Returns the database connection."""
    return db



from typing import Any, Optional, Union, TYPE_CHECKING

# --- Try importing BSON ---
try:
    # Import the real classes if available
    from bson import ObjectId
    from bson.errors import InvalidId  # Good practice to catch specific error

    BSON_AVAILABLE = True
except ImportError:
    # Define dummy classes/values if BSON is not available
    # These act as placeholders for runtime checks like isinstance()
    # and prevent NameErrors, though the logic prevents their actual use.
    class ObjectId:
        pass  # Dummy class

    class InvalidId(Exception):
        pass  # Dummy exception

    BSON_AVAILABLE = False

# --- Define ObjectId for Type Hinting ---
# If TYPE_CHECKING is True (i.e., during static analysis),
# ensure ObjectId is defined, preferably by importing it again.
# This assumes bson is available in the type checking environment.
if TYPE_CHECKING:
    try:
        from bson import ObjectId  # Type checker needs the real type
    except ImportError:
        # If bson might *not* be installed even for type checking,
        # you might need a Protocol or Any, but usually, we assume
        # dependencies are available for the checker.
        pass  # Let the checker flag it if bson isn't found


# --- Function Definition ---
# Now the type hint can use ObjectId directly
def _normalize_id(id_value: Any) -> Optional[Union[int, ObjectId, str]]:
    """
    Normalizes an ID value to ObjectId if possible, otherwise keeps original type.
    Handles string representations of ObjectIds and integers.
    Returns the normalized ID or the original value if conversion isn't applicable.
    """
    # Check for existing int or ObjectId (use runtime ObjectId)
    if isinstance(id_value, int):
        return id_value
    # Use BSON_AVAILABLE flag AND check instance against runtime ObjectId
    if BSON_AVAILABLE and isinstance(id_value, ObjectId):
        return id_value  # Return if already a valid ObjectId

    # Handle string conversions
    if isinstance(id_value, str):
        # Try ObjectId conversion only if BSON is available and string is valid
        if BSON_AVAILABLE and ObjectId.is_valid(id_value):
            try:
                # Use the runtime ObjectId constructor
                return ObjectId(id_value)
            except InvalidId:  # Catch specific BSON error
                pass  # Invalid ObjectId format string, proceed
            except Exception:  # Catch other potential errors
                pass  # Proceed

        # Try integer conversion if it's all digits
        # This check happens if BSON wasn't available OR if it wasn't a valid ObjectId string
        if id_value.isdigit():
            try:
                return int(id_value)
            except ValueError:
                pass  # Should not happen if isdigit() is true

    # If no conversion was successful or applicable, return the original value
    return id_value


def _handle_db_error(operation: str, error: Exception, user_id: Optional[int] = None):
    """Logs database errors consistently."""
    user_info = f" for user {user_id}" if user_id else ""
    logger.error(
        f"Database error during '{operation}'{user_info}: {error}", exc_info=True
    )


def _parse_datetime_field(value: Any) -> datetime:
    """
    Parse a datetime field that might be a string, datetime object, or None.

    Args:
        value: The value to parse (datetime, string, or None)

    Returns:
        datetime object, or current time if parsing fails
    """
    if value is None:
        return datetime.now()

    if isinstance(value, datetime):
        return value

    if isinstance(value, str):
        try:
            # Try parsing ISO format first
            if 'T' in value:
                return datetime.fromisoformat(value.replace('Z', '+00:00'))
            else:
                # Try parsing common date formats
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d %H:%M:%S.%f', '%Y-%m-%d']:
                    try:
                        return datetime.strptime(value, fmt)
                    except ValueError:
                        continue
        except (ValueError, TypeError):
            pass

    # If all parsing fails, return current time
    logger.warning(f"Failed to parse datetime field: {value}, using current time")
    return datetime.now()


def _sanitize_data_for_mongodb(data: Any) -> Any:
    """
    Recursively sanitize data for MongoDB storage.
    Converts WindowsPath and other non-serializable objects to strings.
    For paths, only stores the relative path starting from 'uploads/'.
    Preserves ObjectId instances for database references.

    Args:
        data: The data to sanitize

    Returns:
        Sanitized data that can be stored in MongoDB
    """
    import pathlib

    # First check for MongoDB native types that should be preserved
    if BSON_AVAILABLE and isinstance(data, ObjectId):
        return data  # Preserve ObjectId instances

    if data is None:
        return None
    elif isinstance(data, (str, int, float, bool)):
        return data
    elif isinstance(
        data, (pathlib.Path, pathlib.PurePath, pathlib.WindowsPath, pathlib.PosixPath)
    ):
        # Convert Path objects to strings and normalize to be relative to uploads/ folder
        from utils.file_metadata import normalize_file_path
        path_str = str(data).replace("\\", "/")

        # Use the unified normalization to ensure consistent storage
        normalized_path = normalize_file_path(path_str)
        return normalized_path if normalized_path else path_str
    elif isinstance(data, dict):
        # Special handling for image dictionaries with 'path' key
        if "type" in data and data.get("type") == "file" and "path" in data:
            # Create a new dict to avoid modifying the original
            result = dict(data)
            # Process the path value
            path_value = data["path"]
            if isinstance(
                path_value,
                (
                    pathlib.Path,
                    pathlib.PurePath,
                    pathlib.WindowsPath,
                    pathlib.PosixPath,
                ),
            ):
                path_str = str(path_value).replace("\\", "/")
                uploads_index = path_str.rfind("uploads/")
                if uploads_index != -1:
                    result["path"] = path_str[uploads_index:]
                else:
                    result["path"] = path_str
            elif isinstance(path_value, str):
                normalized_path = path_value.replace("\\", "/")
                uploads_index = normalized_path.rfind("uploads/")
                if uploads_index != -1:
                    result["path"] = normalized_path[uploads_index:]
                else:
                    result["path"] = path_value
            else:
                result["path"] = _sanitize_data_for_mongodb(path_value)

            # Process other keys in the dict
            for k, v in data.items():
                if k != "path":
                    result[k] = _sanitize_data_for_mongodb(v)
            return result
        else:
            # Recursively sanitize dictionary values
            # Special handling for category_id to preserve ObjectId
            result = {}
            for k, v in data.items():
                # Special handling for category_id and other ID fields to ensure ObjectId is preserved
                if (
                    (k == "category_id" or k == "_id")
                    and BSON_AVAILABLE
                    and isinstance(v, ObjectId)
                ):
                    result[k] = v
                # Special handling for all file path fields to ensure only the relative path is stored
                elif k in ["file_path", "file_link", "exclusive_file_path", "inventory_file_path", "image_url"] and isinstance(v, str):
                    # Skip URLs and file_id references
                    if v.startswith(("http://", "https://", "file_id:")):
                        result[k] = v
                    else:
                        # Handle both forward and backslash paths
                        # First normalize path to use forward slashes
                        normalized_path = v.replace("\\", "/")
                        # Find the last occurrence of "uploads/" to handle nested paths correctly
                        uploads_index = normalized_path.rfind("uploads/")
                        if uploads_index != -1:
                            result[k] = normalized_path[uploads_index:]
                        else:
                            result[k] = v
                else:
                    result[k] = _sanitize_data_for_mongodb(v)
            return result
    elif isinstance(data, list):
        # Recursively sanitize list items
        return [_sanitize_data_for_mongodb(item) for item in data]
    elif hasattr(data, "__dict__"):
        # Handle custom objects by converting to dict
        return _sanitize_data_for_mongodb(data.__dict__)
    else:
        # Double check for ObjectId again before converting to string
        if BSON_AVAILABLE and isinstance(data, ObjectId):
            return data
        # For any other type, convert to string
        return str(data)


def save_announcement(admin_id, message, recipients_count=0, timestamp=None):
    """Save an announcement to the database."""
    if timestamp is None:
        timestamp = datetime.now()

    announcement = {
        "admin_id": admin_id,
        "message": message,
        "recipients_count": recipients_count,
        "timestamp": timestamp,
    }

    result = announcements_collection.insert_one(announcement)
    announcement["_id"] = result.inserted_id
    return announcement


def get_recent_announcements(limit=10):
    """Get recent announcements from the database."""
    return list(announcements_collection.find().sort("timestamp", -1).limit(limit))


# --- Settings Operations ---


def get_setting(setting_name: str, default: Any = None) -> Optional[Dict[str, Any]]:
    """Gets a specific setting document."""
    try:
        return settings_collection.find_one({"setting_name": setting_name}) or default
    except Exception as e:
        _handle_db_error(f"get_setting ({setting_name})", e)
        return default


async def get_setting_async(
    setting_name: str, default: Any = None
) -> Optional[Dict[str, Any]]:
    """Gets a specific setting document (async)."""
    try:
        result = await settings_collection_async.find_one(
            {"setting_name": setting_name}
        )
        if result is None:
            return default
        return result
    except Exception as e:
        _handle_db_error(f"get_setting_async ({setting_name})", e)
        logger.error(f"Error in get_setting_async for {setting_name}: {str(e)}")
        return default


@sandbox_aware_db_write
def save_setting(setting_name: str, setting_data: Dict[str, Any]) -> bool:
    """Saves or updates a setting document."""
    try:
        setting_data["updated_at"] = datetime.now()
        result = settings_collection.update_one(
            {"setting_name": setting_name}, {"$set": setting_data}, upsert=True
        )
        return result.acknowledged
    except Exception as e:
        _handle_db_error(f"save_setting ({setting_name})", e)
        return False


@sandbox_aware_db_write
async def save_setting_async(setting_name: str, setting_data: Dict[str, Any]) -> bool:
    """Saves or updates a setting document (async)."""
    try:
        setting_data["updated_at"] = datetime.now()
        result = await settings_collection_async.update_one(
            {"setting_name": setting_name}, {"$set": setting_data}, upsert=True
        )
        return result.acknowledged
    except Exception as e:
        _handle_db_error(f"save_setting_async ({setting_name})", e)
        return False


# --- Log Channel ---
def save_log_channel(channel_id: int) -> bool:
    return save_setting("log_channel", {"channel_id": channel_id})


async def save_log_channel_async(channel_id: int) -> bool:
    return await save_setting_async("log_channel", {"channel_id": channel_id})


def get_log_channel() -> Optional[int]:
    setting = get_setting("log_channel")
    return setting.get("channel_id") if setting else None


async def get_log_channel_async() -> Optional[int]:
    setting = await get_setting_async("log_channel")
    return setting.get("channel_id") if setting else None


# --- Maintenance Mode Settings ---
def get_maintenance_mode() -> bool:
    """Gets the maintenance mode setting from the database.

    Returns:
        bool: True if maintenance mode is enabled, False otherwise
    """
    setting = get_setting("maintenance_mode")
    return setting.get("enabled", False) if setting else False


async def get_maintenance_mode_async() -> bool:
    """Gets the maintenance mode setting from the database (async).

    Returns:
        bool: True if maintenance mode is enabled, False otherwise
    """
    setting = await get_setting_async("maintenance_mode")
    return setting.get("enabled", False) if setting else False


def save_maintenance_mode(enabled: bool, message: Optional[str] = None) -> bool:
    """Saves the maintenance mode setting to the database.

    Args:
        enabled: Whether maintenance mode should be enabled
        message: Optional custom maintenance message

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    setting_data = {"enabled": enabled}
    if message:
        setting_data["message"] = message
    return save_setting("maintenance_mode", setting_data)


async def save_maintenance_mode_async(
    enabled: bool, message: Optional[str] = None
) -> bool:
    """Saves the maintenance mode setting to the database (async).

    Args:
        enabled: Whether maintenance mode should be enabled
        message: Optional custom maintenance message

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    setting_data = {"enabled": enabled}
    if message:
        setting_data["message"] = message
    return await save_setting_async("maintenance_mode", setting_data)


# --- Welcome Message Settings ---
def get_welcome_message() -> Dict[str, Any]:
    """Gets the welcome message settings from the database.

    Returns:
        Dict: Welcome message settings or default settings if not found
    """
    default_settings = {
        "enabled": False,
        "message_text": "Welcome to our platform! We're glad to have you here.",
        "image_path": None,
        "created_at": datetime.now(),
    }

    setting = get_setting("welcome_message")
    if not setting:
        # Initialize with default settings
        save_setting("welcome_message", default_settings)
        return default_settings

    return setting


async def get_welcome_message_async() -> Dict[str, Any]:
    """Gets the welcome message settings from the database (async).

    Returns:
        Dict: Welcome message settings or default settings if not found
    """
    default_settings = {
        "enabled": False,
        "message_text": "Welcome to our platform! We're glad to have you here.",
        "image_path": None,
        "created_at": datetime.now(),
    }

    try:
        setting = await get_setting_async("welcome_message")
        if not setting:
            # Initialize with default settings
            success = await save_setting_async("welcome_message", default_settings)
            if not success:
                logger.error("Failed to save default welcome message settings")
            return default_settings

        return setting
    except Exception as e:
        _handle_db_error("get_welcome_message_async", e)
        logger.error(f"Error retrieving welcome message settings: {e}")
        return default_settings


def save_welcome_message(
    enabled: bool, message_text: str, image_path: Optional[str] = None
) -> bool:
    """Saves the welcome message settings to the database.

    Args:
        enabled: Whether the welcome message should be enabled
        message_text: Text content for the welcome message
        image_path: Path to the welcome image/GIF (optional)

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    setting_data = {
        "enabled": enabled,
        "message_text": message_text,
        "updated_at": datetime.now(),
    }

    # Only update image_path if provided
    if image_path is not None:
        setting_data["image_path"] = image_path

    return save_setting("welcome_message", setting_data)


async def save_welcome_message_async(
    enabled: bool, message_text: str, image_path: Optional[str] = None
) -> bool:
    """Saves the welcome message settings to the database (async).

    Args:
        enabled: Whether the welcome message should be enabled
        message_text: Text content for the welcome message
        image_path: Path to the welcome image/GIF (optional)

    Returns:
        bool: True if the operation was successful, False otherwise
    """
    setting_data = {
        "enabled": enabled,
        "message_text": message_text,
        "updated_at": datetime.now(),
    }

    # Only update image_path if provided
    if image_path is not None:
        setting_data["image_path"] = image_path

    return await save_setting_async("welcome_message", setting_data)


# --- Log Settings ---
DEFAULT_LOG_SETTINGS = {
    "log_admin_actions": True,
    "log_user_actions": True,
    "log_product_changes": True,
    "log_category_changes": True,
    "log_payments": True,
    "log_security_events": True,
    "log_errors": True,
    "log_support": True,
    "bypass_owner_filter": True,
}


@functools.lru_cache(maxsize=1)
def get_log_settings() -> Dict[str, Any]:
    """Gets log settings, initializes defaults if needed."""
    settings = get_setting("log_settings")
    if not settings:
        logger.info("Initializing default log settings.")
        # Create full defaults with setting_name included
        full_defaults = {"setting_name": "log_settings", **DEFAULT_LOG_SETTINGS}
        # Save the FULL defaults including setting_name
        success = save_setting("log_settings", full_defaults)
        if not success:
            logger.error("Failed to save default log settings")
            # Try once more after a short delay
            import time

            time.sleep(0.5)
            success = save_setting("log_settings", full_defaults)
            if not success:
                logger.error("Second attempt to save default log settings failed")

        # Verify settings were actually saved by fetching them again
        verification = get_setting("log_settings")
        if verification:
            logger.info("Log settings successfully saved and verified in database")
        else:
            logger.error(
                "CRITICAL: Log settings verification failed after save operation"
            )

        return full_defaults

    updated = False
    for key, value in DEFAULT_LOG_SETTINGS.items():
        if key not in settings:
            settings[key] = value
            updated = True

    if updated:
        logger.info("Updating log settings with new default keys.")
        # Create a clean dict without metadata fields
        update_dict = {
            k: v for k, v in settings.items() if k not in ["updated_at", "_id"]
        }
        # Make sure setting_name is included
        update_dict["setting_name"] = "log_settings"

        success = save_setting("log_settings", update_dict)
        if not success:
            logger.error("Failed to update log settings with new keys")

    return settings


_async_log_settings_cache = None
_async_log_settings_expiry = None


async def get_log_settings_async() -> Dict[str, Any]:
    """Gets log settings (async), initializes defaults, basic caching."""
    global _async_log_settings_cache, _async_log_settings_expiry
    now = datetime.now()
    if (
        _async_log_settings_cache
        and _async_log_settings_expiry
        and now < _async_log_settings_expiry
    ):
        return _async_log_settings_cache

    settings = await get_setting_async("log_settings")
    if not settings:
        logger.info("Initializing default log settings (async).")
        # Create full defaults with setting_name included
        full_defaults = {"setting_name": "log_settings", **DEFAULT_LOG_SETTINGS}
        # Save the FULL defaults including setting_name
        success = await save_setting_async("log_settings", full_defaults)
        if not success:
            logger.error("Failed to save default log settings (async)")

        _async_log_settings_cache = full_defaults
        _async_log_settings_expiry = now + timedelta(seconds=60)  # Use timedelta
        return full_defaults

    updated = False
    for key, value in DEFAULT_LOG_SETTINGS.items():
        if key not in settings:
            settings[key] = value
            updated = True

    if updated:
        logger.info("Updating log settings with new default keys (async).")
        # Create a clean dict without metadata fields
        update_dict = {
            k: v for k, v in settings.items() if k not in ["updated_at", "_id"]
        }
        # Make sure setting_name is included
        update_dict["setting_name"] = "log_settings"

        success = await save_setting_async("log_settings", update_dict)
        if not success:
            logger.error("Failed to update log settings with new keys (async)")

    _async_log_settings_cache = settings
    _async_log_settings_expiry = now + timedelta(seconds=60)  # Use timedelta
    return settings


@sandbox_aware_db_write
def update_log_settings(settings_dict: Dict[str, bool]) -> bool:
    """Updates log settings."""
    get_log_settings.cache_clear()
    global _async_log_settings_cache, _async_log_settings_expiry
    _async_log_settings_cache = None
    _async_log_settings_expiry = None
    valid_settings = {
        k: v for k, v in settings_dict.items() if k in DEFAULT_LOG_SETTINGS
    }
    return save_setting("log_settings", valid_settings)


@sandbox_aware_db_write
async def update_log_settings_async(settings_dict: Dict[str, bool]) -> bool:
    """Updates log settings (async)."""
    get_log_settings.cache_clear()
    global _async_log_settings_cache, _async_log_settings_expiry
    _async_log_settings_cache = None
    _async_log_settings_expiry = None
    valid_settings = {
        k: v for k, v in settings_dict.items() if k in DEFAULT_LOG_SETTINGS
    }
    return await save_setting_async("log_settings", valid_settings)


# --- Logging Checks ---
LOG_EVENT_MAP = {
    "admin_action": "log_admin_actions",
    "user_action": "log_user_actions",
    "product_change": "log_product_changes",
    "category_change": "log_category_changes",
    "payment": "log_payments",
    "security": "log_security_events",
    "error": "log_errors",
    "support": "log_support",
}


def should_log_event(event_type: str) -> bool:
    """Check if an event type should be logged."""
    settings = get_log_settings()
    setting_key = LOG_EVENT_MAP.get(event_type)
    return settings.get(setting_key, True) if setting_key else True


def should_bypass_owner_filter() -> bool:
    """Check if owner actions should bypass logging filters."""
    return get_log_settings().get("bypass_owner_filter", True)


async def should_log_event_async(event_type: str) -> bool:
    """Check if an event type should be logged (async)."""
    settings = await get_log_settings_async()
    setting_key = LOG_EVENT_MAP.get(event_type)
    return settings.get(setting_key, True) if setting_key else True


async def should_bypass_owner_filter_async() -> bool:
    """Check if owner actions should bypass logging filters (async)."""
    return (await get_log_settings_async()).get("bypass_owner_filter", True)


# --- User Operations ---


def get_user(user_id: int) -> Optional[Dict[str, Any]]:
    """Gets user data by user_id. Returns None if error."""
    try:
        return users_collection.find_one({"user_id": user_id})
    except Exception as e:
        _handle_db_error("get_user", e, user_id)
        return None


async def get_user_async(user_id: int) -> Optional[Dict[str, Any]]:
    """Gets user data by user_id (async). Returns None if error."""
    try:
        return await users_collection_async.find_one({"user_id": user_id})
    except Exception as e:
        _handle_db_error("get_user_async", e, user_id)
        return None


def get_or_create_user(
    user_id: int, name: Optional[str] = None, username: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """Gets user data, creates if not exists. Updates name/username/last_seen."""
    try:
        now = datetime.now()
        update_fields = {"last_seen": now}
        if name:
            update_fields["name"] = name
        if username:
            update_fields["username"] = username

        user = users_collection.find_one_and_update(
            {"user_id": user_id},
            {
                "$set": update_fields,
                "$setOnInsert": {"balance": 0.0, "created_at": now},
            },
            upsert=True,
            return_document=pymongo.ReturnDocument.AFTER,  # Use pymongo constant
        )
        return user
    except Exception as e:
        _handle_db_error("get_or_create_user", e, user_id)
        return None


save_user_data = get_or_create_user


async def get_or_create_user_async(
    user_id: int, name: Optional[str] = None, username: Optional[str] = None
) -> Optional[Dict[str, Any]]:
    """Gets user data, creates if not exists (async). Updates name/username/last_seen."""
    try:
        now = datetime.now()
        update_fields = {"last_seen": now}
        if name:
            update_fields["name"] = name
        if username:
            update_fields["username"] = username

        user = await users_collection_async.find_one_and_update(
            {"user_id": user_id},
            {
                "$set": update_fields,
                "$setOnInsert": {"balance": 0.0, "created_at": now},
            },
            upsert=True,
            return_document=pymongo.ReturnDocument.AFTER,  # Use pymongo constant
        )
        return user
    except Exception as e:
        _handle_db_error("get_or_create_user_async", e, user_id)
        return None


save_user_data_async = get_or_create_user_async


@sandbox_aware_db_write
def update_user_balance(user_id: int, new_balance: float) -> bool:
    """Updates a user's balance. Returns True on success. Creates user if not exists."""
    try:
        # First check if the user exists
        user = users_collection.find_one({"user_id": user_id})

        if user:
            # User exists, update balance
            result = users_collection.update_one(
                {"user_id": user_id}, {"$set": {"balance": float(new_balance)}}
            )
            success = result.modified_count > 0 or result.matched_count > 0
        else:
            # User doesn't exist, create with the specified balance
            now = datetime.now()
            user_data = {
                "user_id": user_id,
                "balance": float(new_balance),
                "created_at": now,
                "last_seen": now,
            }
            result = users_collection.insert_one(user_data)
            success = result.acknowledged

            if success:
                logger.info(
                    f"Created new user {user_id} with initial balance {new_balance}"
                )

        return success
    except Exception as e:
        _handle_db_error("update_user_balance", e, user_id)
        return False


@sandbox_aware_db_write
async def update_user_balance_async(user_id: int, new_balance: float) -> bool:
    """Updates a user's balance (async). Returns True on success. Creates user if not exists."""
    try:
        # First check if the user exists
        user = await users_collection_async.find_one({"user_id": user_id})

        if user:
            # User exists, update balance
            result = await users_collection_async.update_one(
                {"user_id": user_id}, {"$set": {"balance": float(new_balance)}}
            )
            success = result.modified_count > 0 or result.matched_count > 0
        else:
            # User doesn't exist, create with the specified balance
            now = datetime.now()
            user_data = {
                "user_id": user_id,
                "balance": float(new_balance),
                "created_at": now,
                "last_seen": now,
            }
            result = await users_collection_async.insert_one(user_data)
            success = result.acknowledged

            if success:
                logger.info(
                    f"Created new user {user_id} with initial balance {new_balance} (async)"
                )

        return success
    except Exception as e:
        _handle_db_error("update_user_balance_async", e, user_id)
        return False


def get_user_balance(user_id: int) -> float:
    """Gets a user's balance, returns 0.0 if user not found or error."""
    user = get_user(user_id)
    return user.get("balance", 0.0) if user else 0.0


async def get_user_balance_async(user_id: int) -> float:
    """Gets a user's balance (async), returns 0.0 if user not found or error."""
    user = await get_user_async(user_id)
    return user.get("balance", 0.0) if user else 0.0


def get_user_by_username(
    username: str, case_insensitive: bool = True
) -> Optional[Dict[str, Any]]:
    """Finds a user by exact username (case-sensitive by default)."""
    if not username:
        return None
    query_username = username.lstrip("@")
    query = {"username": query_username}
    if case_insensitive:
        query = {"username": {"$regex": f"^{query_username}$", "$options": "i"}}
    try:
        return users_collection.find_one(query)
    except Exception as e:
        _handle_db_error(f"get_user_by_username ({username})", e)
        return None


async def get_user_by_username_async(
    username: str, case_insensitive: bool = True
) -> Optional[Dict[str, Any]]:
    """Finds a user by exact username (async, case-sensitive by default)."""
    if not username:
        return None
    query_username = username.lstrip("@")
    query = {"username": query_username}
    if case_insensitive:
        query = {"username": {"$regex": f"^{query_username}$", "$options": "i"}}
    try:
        return await users_collection_async.find_one(query)
    except Exception as e:
        _handle_db_error(f"get_user_by_username_async ({username})", e)
        return None


search_user_by_username = functools.partial(get_user_by_username, case_insensitive=True)
search_user_by_username_async = functools.partial(
    get_user_by_username_async, case_insensitive=True
)


def advanced_user_search(
    query: str,
    search_type: str = "auto",
    limit: int = 10,
    include_banned: bool = False,
    min_balance: Optional[float] = None,
    max_balance: Optional[float] = None,
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None
) -> List[Dict[str, Any]]:
    """
    Advanced user search with multiple criteria and fuzzy matching.

    Args:
        query: Search query (user ID, username, or name)
        search_type: "auto", "id", "username", "name", "partial"
        limit: Maximum number of results
        include_banned: Whether to include banned users
        min_balance: Minimum balance filter
        max_balance: Maximum balance filter
        date_from: Filter users created after this date
        date_to: Filter users created before this date

    Returns:
        List of matching users with relevance scoring
    """
    try:
        from utils.performance_monitor import PerformanceTimer

        with PerformanceTimer("advanced_user_search", "db_operation"):
            results = []

            # Build base query
            base_query = {}

            # Apply filters
            if min_balance is not None or max_balance is not None:
                balance_filter = {}
                if min_balance is not None:
                    balance_filter["$gte"] = min_balance
                if max_balance is not None:
                    balance_filter["$lte"] = max_balance
                base_query["balance"] = balance_filter

            if date_from is not None or date_to is not None:
                date_filter = {}
                if date_from is not None:
                    date_filter["$gte"] = date_from
                if date_to is not None:
                    date_filter["$lte"] = date_to
                base_query["created_at"] = date_filter

            # Determine search strategy
            if search_type == "auto":
                if query.isdigit():
                    search_type = "id"
                elif query.startswith("@"):
                    search_type = "username"
                else:
                    search_type = "partial"

            # Execute search based on type
            if search_type == "id":
                user_id = int(query)
                query_filter = {**base_query, "user_id": user_id}
                user = users_collection.find_one(query_filter)
                if user:
                    user["relevance_score"] = 1.0
                    results = [user]

            elif search_type == "username":
                username = query.lstrip("@")
                # Exact match first
                exact_query = {**base_query, "username": {"$regex": f"^{username}$", "$options": "i"}}
                exact_matches = list(users_collection.find(exact_query).limit(limit))

                # Partial match if no exact matches
                if not exact_matches:
                    partial_query = {**base_query, "username": {"$regex": username, "$options": "i"}}
                    partial_matches = list(users_collection.find(partial_query).limit(limit))
                    for user in partial_matches:
                        user["relevance_score"] = 0.7
                    results = partial_matches
                else:
                    for user in exact_matches:
                        user["relevance_score"] = 1.0
                    results = exact_matches

            elif search_type == "partial":
                # Multi-field partial search
                search_queries = []

                # Search in username
                search_queries.append({
                    **base_query,
                    "username": {"$regex": query, "$options": "i"}
                })

                # Search in name
                search_queries.append({
                    **base_query,
                    "name": {"$regex": query, "$options": "i"}
                })

                # Try as user ID if numeric
                if query.isdigit():
                    search_queries.append({
                        **base_query,
                        "user_id": int(query)
                    })

                # Execute all queries and combine results
                seen_users = set()
                for i, search_query in enumerate(search_queries):
                    matches = list(users_collection.find(search_query).limit(limit))
                    for user in matches:
                        user_id = user["user_id"]
                        if user_id not in seen_users:
                            # Score based on match type (exact ID > username > name)
                            if i == 2:  # ID match
                                user["relevance_score"] = 1.0
                            elif i == 0:  # Username match
                                user["relevance_score"] = 0.8
                            else:  # Name match
                                user["relevance_score"] = 0.6

                            results.append(user)
                            seen_users.add(user_id)

                            if len(results) >= limit:
                                break

                    if len(results) >= limit:
                        break

            # Filter banned users if requested
            if not include_banned:
                results = [user for user in results if not is_banned(user["user_id"])]

            # Sort by relevance score
            results.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

            return results[:limit]

    except Exception as e:
        _handle_db_error("advanced_user_search", e, query)
        return []


async def advanced_user_search_async(
    query: str,
    search_type: str = "auto",
    limit: int = 10,
    include_banned: bool = False,
    min_balance: Optional[float] = None,
    max_balance: Optional[float] = None,
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None
) -> List[Dict[str, Any]]:
    """
    Advanced user search with multiple criteria and fuzzy matching (async).

    Args:
        query: Search query (user ID, username, or name)
        search_type: "auto", "id", "username", "name", "partial"
        limit: Maximum number of results
        include_banned: Whether to include banned users
        min_balance: Minimum balance filter
        max_balance: Maximum balance filter
        date_from: Filter users created after this date
        date_to: Filter users created before this date

    Returns:
        List of matching users with relevance scoring
    """
    try:
        from utils.performance_monitor import PerformanceTimer

        with PerformanceTimer("advanced_user_search_async", "db_operation"):
            results = []

            # Build base query
            base_query = {}

            # Apply filters
            if min_balance is not None or max_balance is not None:
                balance_filter = {}
                if min_balance is not None:
                    balance_filter["$gte"] = min_balance
                if max_balance is not None:
                    balance_filter["$lte"] = max_balance
                base_query["balance"] = balance_filter

            if date_from is not None or date_to is not None:
                date_filter = {}
                if date_from is not None:
                    date_filter["$gte"] = date_from
                if date_to is not None:
                    date_filter["$lte"] = date_to
                base_query["created_at"] = date_filter

            # Determine search strategy
            if search_type == "auto":
                if query.isdigit():
                    search_type = "id"
                elif query.startswith("@"):
                    search_type = "username"
                else:
                    search_type = "partial"

            # Execute search based on type
            if search_type == "id":
                user_id = int(query)
                query_filter = {**base_query, "user_id": user_id}
                user = await users_collection_async.find_one(query_filter)
                if user:
                    user["relevance_score"] = 1.0
                    results = [user]

            elif search_type == "username":
                username = query.lstrip("@")
                # Exact match first
                exact_query = {**base_query, "username": {"$regex": f"^{username}$", "$options": "i"}}
                exact_cursor = users_collection_async.find(exact_query).limit(limit)
                exact_matches = await exact_cursor.to_list(length=limit)

                # Partial match if no exact matches
                if not exact_matches:
                    partial_query = {**base_query, "username": {"$regex": username, "$options": "i"}}
                    partial_cursor = users_collection_async.find(partial_query).limit(limit)
                    partial_matches = await partial_cursor.to_list(length=limit)
                    for user in partial_matches:
                        user["relevance_score"] = 0.7
                    results = partial_matches
                else:
                    for user in exact_matches:
                        user["relevance_score"] = 1.0
                    results = exact_matches

            elif search_type == "partial":
                # Multi-field partial search
                search_queries = []

                # Search in username
                search_queries.append({
                    **base_query,
                    "username": {"$regex": query, "$options": "i"}
                })

                # Search in name
                search_queries.append({
                    **base_query,
                    "name": {"$regex": query, "$options": "i"}
                })

                # Try as user ID if numeric
                if query.isdigit():
                    search_queries.append({
                        **base_query,
                        "user_id": int(query)
                    })

                # Execute all queries and combine results
                seen_users = set()
                for i, search_query in enumerate(search_queries):
                    cursor = users_collection_async.find(search_query).limit(limit)
                    matches = await cursor.to_list(length=limit)
                    for user in matches:
                        user_id = user["user_id"]
                        if user_id not in seen_users:
                            # Score based on match type (exact ID > username > name)
                            if i == 2:  # ID match
                                user["relevance_score"] = 1.0
                            elif i == 0:  # Username match
                                user["relevance_score"] = 0.8
                            else:  # Name match
                                user["relevance_score"] = 0.6

                            results.append(user)
                            seen_users.add(user_id)

                            if len(results) >= limit:
                                break

                    if len(results) >= limit:
                        break

            # Filter banned users if requested
            if not include_banned:
                filtered_results = []
                for user in results:
                    if not await is_banned_async(user["user_id"]):
                        filtered_results.append(user)
                results = filtered_results

            # Sort by relevance score
            results.sort(key=lambda x: x.get("relevance_score", 0), reverse=True)

            return results[:limit]

    except Exception as e:
        _handle_db_error("advanced_user_search_async", e, query)
        return []


def get_comprehensive_user_analytics(user_id: int) -> Dict[str, Any]:
    """
    Get comprehensive analytics for a user including activity metrics,
    spending patterns, and behavioral insights.

    Args:
        user_id: User ID to analyze

    Returns:
        Dict containing comprehensive user analytics
    """
    try:
        from utils.performance_monitor import PerformanceTimer

        with PerformanceTimer("get_comprehensive_user_analytics", "db_operation"):
            # Get basic user data
            user = get_user(user_id)
            if not user:
                return {"error": "User not found"}

            # Get transaction history
            transactions = get_user_transactions(user_id, limit=100)

            # Get order history
            orders = get_user_orders(user_id, limit=50)

            # Get deposit history (including admin credits)
            deposit_data = get_user_deposit_history(user_id, limit=50)
            deposits = deposit_data.get("deposits", [])

            # Calculate analytics
            analytics = {
                "user_info": {
                    "user_id": user_id,
                    "username": user.get("username", ""),
                    "name": user.get("name", "Unknown"),
                    "balance": user.get("balance", 0.0),
                    "created_at": user.get("created_at"),
                    "last_seen": user.get("last_seen"),
                    "is_banned": is_banned(user_id)
                },
                "activity_metrics": _calculate_activity_metrics(user, transactions, orders, deposits),
                "spending_patterns": _calculate_spending_patterns(transactions, orders),
                "deposit_analytics": _calculate_deposit_analytics(deposits),
                "behavioral_insights": _calculate_behavioral_insights(user, transactions, orders, deposits)
            }

            return analytics

    except Exception as e:
        _handle_db_error("get_comprehensive_user_analytics", e, user_id)
        return {"error": f"Failed to calculate analytics: {str(e)}"}


def _calculate_activity_metrics(user: Dict, transactions: List, orders: List, deposits: List) -> Dict[str, Any]:
    """Calculate user activity metrics."""
    now = datetime.now()

    # Safely parse datetime fields that might be strings
    created_at = _parse_datetime_field(user.get("created_at", now))
    last_seen = _parse_datetime_field(user.get("last_seen", now))

    # Account age
    account_age_days = (now - created_at).days if created_at else 0

    # Last activity
    days_since_last_seen = (now - last_seen).days if last_seen else 0

    # Transaction activity
    transaction_count = len(transactions)
    recent_transactions = [t for t in transactions if (now - _parse_datetime_field(t.get("timestamp", now))).days <= 30]

    # Order activity
    order_count = len(orders)
    recent_orders = [o for o in orders if (now - _parse_datetime_field(o.get("timestamp", now))).days <= 30]

    # Deposit activity
    deposit_count = len(deposits)
    recent_deposits = [d for d in deposits if (now - _parse_datetime_field(d.get("created_at", now))).days <= 30]

    return {
        "account_age_days": account_age_days,
        "days_since_last_seen": days_since_last_seen,
        "activity_status": "active" if days_since_last_seen <= 7 else "inactive" if days_since_last_seen <= 30 else "dormant",
        "total_transactions": transaction_count,
        "recent_transactions_30d": len(recent_transactions),
        "total_orders": order_count,
        "recent_orders_30d": len(recent_orders),
        "total_deposits": deposit_count,
        "recent_deposits_30d": len(recent_deposits),
        "avg_transactions_per_month": (transaction_count / max(account_age_days / 30, 1)) if account_age_days > 0 else 0
    }


def _calculate_spending_patterns(transactions: List, orders: List) -> Dict[str, Any]:
    """Calculate user spending patterns."""
    # Calculate total spent
    total_spent = sum(abs(t.get("amount", 0)) for t in transactions if t.get("type") == "purchase")

    # Calculate average order value
    order_amounts = [o.get("amount", 0) for o in orders]
    avg_order_value = sum(order_amounts) / len(order_amounts) if order_amounts else 0

    # Find spending trends
    now = datetime.now()
    monthly_spending = {}

    for transaction in transactions:
        if transaction.get("type") == "purchase":
            timestamp = _parse_datetime_field(transaction.get("timestamp", now))
            month_key = timestamp.strftime("%Y-%m")
            monthly_spending[month_key] = monthly_spending.get(month_key, 0) + abs(transaction.get("amount", 0))

    # Calculate spending frequency
    purchase_transactions = [t for t in transactions if t.get("type") == "purchase"]
    if len(purchase_transactions) > 1:
        timestamps = [_parse_datetime_field(t.get("timestamp", now)) for t in purchase_transactions]
        timestamps.sort()
        intervals = [(timestamps[i] - timestamps[i-1]).days for i in range(1, len(timestamps))]
        avg_days_between_purchases = sum(intervals) / len(intervals) if intervals else 0
    else:
        avg_days_between_purchases = 0

    return {
        "total_spent": total_spent,
        "average_order_value": avg_order_value,
        "total_orders": len(orders),
        "monthly_spending": monthly_spending,
        "avg_days_between_purchases": avg_days_between_purchases,
        "spending_frequency": "high" if avg_days_between_purchases < 7 else "medium" if avg_days_between_purchases < 30 else "low"
    }


def _calculate_deposit_analytics(deposits: List) -> Dict[str, Any]:
    """Calculate deposit analytics."""
    if not deposits:
        return {
            "total_deposits": 0,
            "total_deposited": 0.0,
            "avg_deposit_amount": 0.0,
            "deposit_methods": {},
            "admin_credits_count": 0,
            "gateway_deposits_count": 0
        }

    total_deposited = sum(d.get("actual_paid_amount", 0) for d in deposits)
    avg_deposit = total_deposited / len(deposits) if deposits else 0

    # Categorize by source
    admin_credits = [d for d in deposits if d.get("source_type") == "admin_credit"]
    gateway_deposits = [d for d in deposits if d.get("source_type") == "payment_gateway"]

    # Payment methods
    methods = {}
    for deposit in deposits:
        method = deposit.get("payment_method", "Unknown")
        methods[method] = methods.get(method, 0) + 1

    return {
        "total_deposits": len(deposits),
        "total_deposited": total_deposited,
        "avg_deposit_amount": avg_deposit,
        "deposit_methods": methods,
        "admin_credits_count": len(admin_credits),
        "gateway_deposits_count": len(gateway_deposits),
        "admin_credits_total": sum(d.get("actual_paid_amount", 0) for d in admin_credits),
        "gateway_deposits_total": sum(d.get("actual_paid_amount", 0) for d in gateway_deposits)
    }


def _calculate_behavioral_insights(user: Dict, transactions: List, orders: List, deposits: List) -> Dict[str, Any]:
    """Calculate behavioral insights and user classification."""
    balance = user.get("balance", 0)

    # Calculate ratios
    total_deposited = sum(d.get("actual_paid_amount", 0) for d in deposits)
    total_spent = sum(abs(t.get("amount", 0)) for t in transactions if t.get("type") == "purchase")

    spend_ratio = (total_spent / total_deposited) if total_deposited > 0 else 0

    # User classification
    if total_deposited > 1000 and len(orders) > 20:
        user_tier = "premium"
    elif total_deposited > 100 and len(orders) > 5:
        user_tier = "regular"
    elif total_deposited > 0:
        user_tier = "casual"
    else:
        user_tier = "new"

    # Risk assessment
    risk_factors = []
    if spend_ratio > 0.9:
        risk_factors.append("high_spend_ratio")
    if balance < 0:
        risk_factors.append("negative_balance")
    if len([t for t in transactions if t.get("type") == "admin_deduction"]) > 0:
        risk_factors.append("admin_deductions")

    risk_level = "high" if len(risk_factors) >= 2 else "medium" if len(risk_factors) == 1 else "low"

    return {
        "user_tier": user_tier,
        "spend_ratio": spend_ratio,
        "balance_status": "positive" if balance > 0 else "zero" if balance == 0 else "negative",
        "risk_level": risk_level,
        "risk_factors": risk_factors,
        "total_deposited": total_deposited,
        "total_spent": total_spent,
        "net_balance_change": total_deposited - total_spent
    }


def export_user_data(user_id: int, format: str = "json", include_files: bool = False) -> Dict[str, Any]:
    """
    Export comprehensive user data for admin purposes with enhanced data collection.

    Args:
        user_id: User ID to export data for
        format: Export format ("json", "csv", "summary", "structured_archive")
        include_files: Whether to create actual export files

    Returns:
        Dict containing exported user data or file information
    """
    try:
        from utils.performance_monitor import PerformanceTimer
        import os
        import json
        import zipfile
        import tempfile
        from datetime import datetime

        with PerformanceTimer("export_user_data", "admin_operation"):
            # Get comprehensive analytics with error handling
            try:
                analytics = get_comprehensive_user_analytics(user_id)
                if analytics.get("error"):
                    logger.warning(f"Analytics error for user {user_id}: {analytics['error']}")
                    analytics = {"user_tier": "unknown", "risk_level": "unknown"}
            except Exception as analytics_error:
                logger.warning(f"Failed to get analytics for user {user_id}: {analytics_error}")
                analytics = {"user_tier": "unknown", "risk_level": "unknown"}

            # Get user profile data
            user_profile = get_user(user_id)
            if not user_profile:
                return {"error": "User not found"}

            # Get detailed transaction history (all transactions) with error handling
            try:
                transactions = list(transactions_collection.find({"user_id": user_id}).sort("timestamp", -1))
            except Exception as e:
                logger.warning(f"Failed to get transactions for user {user_id}: {e}")
                transactions = []

            # Get detailed order history (all orders) with error handling
            try:
                orders = list(orders_collection.find({"user_id": user_id}).sort("timestamp", -1))
            except Exception as e:
                logger.warning(f"Failed to get orders for user {user_id}: {e}")
                orders = []

            # Get detailed deposit/payment history (all payments) with error handling
            try:
                payments = list(payments_collection.find({"user_id": user_id}).sort("created_at", -1))
            except Exception as e:
                logger.warning(f"Failed to get payments for user {user_id}: {e}")
                payments = []

            # Get admin actions related to this user with error handling
            try:
                admin_actions = list(admin_logs_collection.find({
                    "$or": [
                        {"target_user_id": user_id},
                        {"details": {"$regex": f"user {user_id}", "$options": "i"}}
                    ]
                }).sort("timestamp", -1))
            except Exception as e:
                logger.warning(f"Failed to get admin actions for user {user_id}: {e}")
                admin_actions = []

            # Get support messages with error handling
            try:
                support_messages = list(support_messages_collection.find({"user_id": user_id}).sort("timestamp", -1))
            except Exception as e:
                logger.warning(f"Failed to get support messages for user {user_id}: {e}")
                support_messages = []

            # Get user activity timeline with error handling
            try:
                activity_timeline = get_user_activity_timeline(user_id, days=365)  # Full year
            except Exception as timeline_error:
                logger.warning(f"Failed to get activity timeline for user {user_id}: {timeline_error}")
                activity_timeline = {"error": "Timeline data unavailable", "activities": []}

            # Prepare comprehensive export data
            export_data = {
                "export_info": {
                    "user_id": user_id,
                    "export_timestamp": datetime.now().isoformat(),
                    "export_format": format,
                    "data_version": "2.0",
                    "exported_by": "admin_system",
                    "collections_included": [
                        "user_profile", "transactions", "orders", "payments",
                        "admin_actions", "support_messages", "activity_timeline", "analytics"
                    ]
                },
                "user_profile": {
                    **user_profile,
                    "_id": str(user_profile.get("_id", "")),  # Convert ObjectId to string
                },
                "analytics": {
                    "activity_metrics": analytics.get("activity_metrics", {}),
                    "spending_patterns": analytics.get("spending_patterns", {}),
                    "deposit_analytics": analytics.get("deposit_analytics", {}),
                    "behavioral_insights": analytics.get("behavioral_insights", {}),
                    "user_tier": analytics.get("user_tier", "unknown"),
                    "risk_level": analytics.get("risk_level", "unknown")
                },
                "transaction_history": [_serialize_document(tx) for tx in transactions],
                "order_history": [_serialize_document(order) for order in orders],
                "payment_history": [_serialize_document(payment) for payment in payments],
                "admin_actions": [_serialize_document(action) for action in admin_actions],
                "support_messages": [_serialize_document(msg) for msg in support_messages],
                "activity_timeline": activity_timeline,
                "summary_statistics": {
                    "total_transactions": len(transactions),
                    "total_orders": len(orders),
                    "total_payments": len(payments),
                    "total_admin_actions": len(admin_actions),
                    "total_support_messages": len(support_messages),
                    "data_completeness": "100%"
                }
            }

            # Calculate export size estimate after export_data is defined
            export_size_kb = len(str(export_data)) / 1024
            export_data["summary_statistics"]["export_size_estimate"] = f"{export_size_kb:.2f} KB"

            if format == "summary":
                # Return only summary data
                return {
                    "export_info": export_data["export_info"],
                    "user_profile": export_data["user_profile"],
                    "analytics": export_data["analytics"],
                    "summary_statistics": export_data["summary_statistics"]
                }

            if format == "structured_archive" and include_files:
                # Create structured folder format with individual collection files
                return _create_structured_export_archive(user_id, export_data)

            return export_data

    except Exception as e:
        _handle_db_error("export_user_data", e, user_id)
        return {"error": f"Failed to export user data: {str(e)}"}


def _serialize_document(doc: Dict[str, Any]) -> Dict[str, Any]:
    """
    Serialize MongoDB document for JSON export by converting ObjectIds and dates.

    Args:
        doc: MongoDB document to serialize

    Returns:
        Serialized document safe for JSON export
    """
    if not doc:
        return {}

    serialized = {}
    for key, value in doc.items():
        if hasattr(value, 'isoformat'):  # datetime objects
            serialized[key] = value.isoformat()
        elif hasattr(value, '__str__') and 'ObjectId' in str(type(value)):  # ObjectId
            serialized[key] = str(value)
        elif isinstance(value, dict):
            serialized[key] = _serialize_document(value)
        elif isinstance(value, list):
            serialized[key] = [_serialize_document(item) if isinstance(item, dict) else item for item in value]
        else:
            serialized[key] = value

    return serialized


def _create_structured_export_archive(user_id: int, export_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create structured folder format with individual collection files in timestamped folders.

    Args:
        user_id: User ID for the export
        export_data: Complete export data

    Returns:
        Dict with file information and paths
    """
    try:
        import os
        import json
        import zipfile
        import tempfile
        from datetime import datetime

        # Custom JSON encoder for datetime and ObjectId serialization
        class CustomJSONEncoder(json.JSONEncoder):
            def default(self, obj):
                if hasattr(obj, 'isoformat'):  # datetime objects
                    return obj.isoformat()
                elif hasattr(obj, '__str__') and 'ObjectId' in str(type(obj)):  # ObjectId
                    return str(obj)
                return super().default(obj)

        # Create timestamp for folder name
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        export_folder_name = f"user_{user_id}_export_{timestamp}"

        # Create temporary directory for export
        temp_dir = tempfile.mkdtemp()
        export_dir = os.path.join(temp_dir, export_folder_name)
        os.makedirs(export_dir, exist_ok=True)

        # Ensure all data is properly serialized before writing to files
        serialized_export_data = _serialize_document(export_data)

        # Create individual collection files
        collection_files = {}

        # Export info file
        with open(os.path.join(export_dir, "export_info.json"), 'w', encoding='utf-8') as f:
            json.dump(serialized_export_data["export_info"], f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
        collection_files["export_info"] = "export_info.json"

        # User profile file
        with open(os.path.join(export_dir, "user_profile.json"), 'w', encoding='utf-8') as f:
            json.dump(serialized_export_data["user_profile"], f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
        collection_files["user_profile"] = "user_profile.json"

        # Analytics file
        with open(os.path.join(export_dir, "analytics.json"), 'w', encoding='utf-8') as f:
            json.dump(serialized_export_data["analytics"], f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
        collection_files["analytics"] = "analytics.json"

        # Transaction history file
        with open(os.path.join(export_dir, "transactions.json"), 'w', encoding='utf-8') as f:
            json.dump(serialized_export_data["transaction_history"], f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
        collection_files["transactions"] = "transactions.json"

        # Order history file
        with open(os.path.join(export_dir, "orders.json"), 'w', encoding='utf-8') as f:
            json.dump(serialized_export_data["order_history"], f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
        collection_files["orders"] = "orders.json"

        # Payment history file
        with open(os.path.join(export_dir, "payments.json"), 'w', encoding='utf-8') as f:
            json.dump(serialized_export_data["payment_history"], f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
        collection_files["payments"] = "payments.json"

        # Admin actions file
        with open(os.path.join(export_dir, "admin_actions.json"), 'w', encoding='utf-8') as f:
            json.dump(serialized_export_data["admin_actions"], f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
        collection_files["admin_actions"] = "admin_actions.json"

        # Support messages file
        with open(os.path.join(export_dir, "support_messages.json"), 'w', encoding='utf-8') as f:
            json.dump(serialized_export_data["support_messages"], f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
        collection_files["support_messages"] = "support_messages.json"

        # Activity timeline file
        with open(os.path.join(export_dir, "activity_timeline.json"), 'w', encoding='utf-8') as f:
            json.dump(serialized_export_data["activity_timeline"], f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
        collection_files["activity_timeline"] = "activity_timeline.json"

        # Summary statistics file
        with open(os.path.join(export_dir, "summary_statistics.json"), 'w', encoding='utf-8') as f:
            json.dump(serialized_export_data["summary_statistics"], f, indent=2, ensure_ascii=False, cls=CustomJSONEncoder)
        collection_files["summary_statistics"] = "summary_statistics.json"

        # Create README file
        readme_content = f"""# User Data Export - User ID: {user_id}

Export Information:
- Export Date: {serialized_export_data['export_info']['export_timestamp']}
- Data Version: {serialized_export_data['export_info']['data_version']}
- Collections Included: {', '.join(serialized_export_data['export_info']['collections_included'])}

File Structure:
"""
        for collection, filename in collection_files.items():
            readme_content += f"- {filename}: {collection.replace('_', ' ').title()} data\n"

        with open(os.path.join(export_dir, "README.md"), 'w', encoding='utf-8') as f:
            f.write(readme_content)

        # Create compressed archive
        archive_path = os.path.join(temp_dir, f"{export_folder_name}.zip")
        with zipfile.ZipFile(archive_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(export_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # Store with relative path from uploads folder as root
                    arcname = os.path.join("uploads", "exports", export_folder_name,
                                         os.path.relpath(file_path, export_dir))
                    zipf.write(file_path, arcname)

        # Get file size
        file_size = os.path.getsize(archive_path)

        return {
            "export_info": serialized_export_data["export_info"],
            "file_info": {
                "archive_path": archive_path,
                "relative_path": f"uploads/exports/{export_folder_name}.zip",
                "folder_name": export_folder_name,
                "file_size": file_size,
                "file_size_mb": file_size / (1024 * 1024),
                "collection_files": collection_files,
                "total_files": len(collection_files) + 1  # +1 for README
            },
            "summary_statistics": serialized_export_data["summary_statistics"]
        }

    except Exception as e:
        logger.error(f"Error creating structured export archive: {e}")
        return {"error": f"Failed to create export archive: {str(e)}"}


def get_user_activity_timeline(user_id: int, days: int = 30) -> List[Dict[str, Any]]:
    """
    Get user activity timeline for the specified number of days.

    Args:
        user_id: User ID to get timeline for
        days: Number of days to look back

    Returns:
        List of activity events sorted by date
    """
    try:
        from utils.performance_monitor import PerformanceTimer

        with PerformanceTimer("get_user_activity_timeline", "db_operation"):
            cutoff_date = datetime.now() - timedelta(days=days)
            timeline = []

            # Get recent transactions
            transactions = list(transactions_collection.find({
                "user_id": user_id,
                "timestamp": {"$gte": cutoff_date}
            }).sort("timestamp", -1))

            for tx in transactions:
                timeline.append({
                    "type": "transaction",
                    "subtype": tx.get("type", "unknown"),
                    "timestamp": tx.get("timestamp"),
                    "amount": tx.get("amount", 0),
                    "description": f"{tx.get('type', 'Unknown').replace('_', ' ').title()}: ${tx.get('amount', 0):.2f}",
                    "details": tx
                })

            # Get recent orders
            orders = list(orders_collection.find({
                "user_id": user_id,
                "timestamp": {"$gte": cutoff_date}
            }).sort("timestamp", -1))

            for order in orders:
                timeline.append({
                    "type": "order",
                    "subtype": order.get("status", "unknown"),
                    "timestamp": order.get("timestamp"),
                    "amount": order.get("amount", 0),
                    "description": f"Order: {order.get('product_name', 'Unknown Product')} - ${order.get('amount', 0):.2f}",
                    "details": order
                })

            # Get recent deposits
            deposits = list(payments_collection.find({
                "user_id": user_id,
                "created_at": {"$gte": cutoff_date}
            }).sort("created_at", -1))

            for deposit in deposits:
                timeline.append({
                    "type": "deposit",
                    "subtype": deposit.get("status", "unknown"),
                    "timestamp": deposit.get("created_at"),
                    "amount": deposit.get("actual_paid_amount", 0),
                    "description": f"Deposit: ${deposit.get('actual_paid_amount', 0):.2f} via {deposit.get('gateway', 'Unknown')}",
                    "details": deposit
                })

            # Get admin actions on this user
            admin_deposits = list(transactions_collection.find({
                "user_id": user_id,
                "type": "admin_deposit",
                "timestamp": {"$gte": cutoff_date}
            }).sort("timestamp", -1))

            for admin_action in admin_deposits:
                timeline.append({
                    "type": "admin_action",
                    "subtype": "credit",
                    "timestamp": admin_action.get("timestamp"),
                    "amount": admin_action.get("amount", 0),
                    "description": f"Admin Credit: ${admin_action.get('amount', 0):.2f} by {admin_action.get('admin_name', 'Unknown Admin')}",
                    "details": admin_action
                })

            # Sort timeline by timestamp (most recent first)
            timeline.sort(key=lambda x: x.get("timestamp", datetime.min), reverse=True)

            return timeline

    except Exception as e:
        _handle_db_error("get_user_activity_timeline", e, user_id)
        return []


def search_users_with_filters(
    query: str = "",
    min_balance: Optional[float] = None,
    max_balance: Optional[float] = None,
    user_tier: Optional[str] = None,
    activity_status: Optional[str] = None,
    risk_level: Optional[str] = None,
    date_from: Optional[datetime] = None,
    date_to: Optional[datetime] = None,
    include_banned: bool = False,
    limit: int = 50
) -> List[Dict[str, Any]]:
    """
    Advanced user search with multiple filters and analytics.

    Args:
        query: Search query for name/username
        min_balance: Minimum balance filter
        max_balance: Maximum balance filter
        user_tier: User tier filter (new, casual, regular, premium)
        activity_status: Activity status filter (active, inactive, dormant)
        risk_level: Risk level filter (low, medium, high)
        date_from: Created after this date
        date_to: Created before this date
        include_banned: Include banned users
        limit: Maximum results

    Returns:
        List of users with analytics data
    """
    try:
        from utils.performance_monitor import PerformanceTimer

        with PerformanceTimer("search_users_with_filters", "db_operation"):
            # Start with basic search if query provided
            if query:
                users = advanced_user_search(
                    query=query,
                    search_type="auto",
                    limit=limit * 2,  # Get more to filter
                    include_banned=include_banned,
                    min_balance=min_balance,
                    max_balance=max_balance,
                    date_from=date_from,
                    date_to=date_to
                )
            else:
                # Build query for all users
                base_query = {}

                if min_balance is not None or max_balance is not None:
                    balance_filter = {}
                    if min_balance is not None:
                        balance_filter["$gte"] = min_balance
                    if max_balance is not None:
                        balance_filter["$lte"] = max_balance
                    base_query["balance"] = balance_filter

                if date_from is not None or date_to is not None:
                    date_filter = {}
                    if date_from is not None:
                        date_filter["$gte"] = date_from
                    if date_to is not None:
                        date_filter["$lte"] = date_to
                    base_query["created_at"] = date_filter

                users = list(users_collection.find(base_query).limit(limit * 2))

                # Add relevance score
                for user in users:
                    user["relevance_score"] = 1.0

            # Apply advanced filters by analyzing each user
            filtered_users = []

            for user in users:
                user_id = user.get("user_id")

                # Skip banned users if not included
                if not include_banned and is_banned(user_id):
                    continue

                # Get analytics for filtering
                analytics = get_comprehensive_user_analytics(user_id)

                if analytics.get("error"):
                    continue

                activity_metrics = analytics.get("activity_metrics", {})
                behavioral_insights = analytics.get("behavioral_insights", {})

                # Apply tier filter
                if user_tier and behavioral_insights.get("user_tier") != user_tier:
                    continue

                # Apply activity status filter
                if activity_status and activity_metrics.get("activity_status") != activity_status:
                    continue

                # Apply risk level filter
                if risk_level and behavioral_insights.get("risk_level") != risk_level:
                    continue

                # Add analytics to user data
                user["analytics"] = analytics
                filtered_users.append(user)

                if len(filtered_users) >= limit:
                    break

            return filtered_users

    except Exception as e:
        _handle_db_error("search_users_with_filters", e, query)
        return []


def get_user_count() -> int:
    """Gets the total number of users."""
    try:
        return users_collection.count_documents({})
    except Exception as e:
        _handle_db_error("get_user_count", e)
        return 0


async def get_user_count_async() -> int:
    """Gets the total number of users (async)."""
    try:
        return await users_collection_async.count_documents({})
    except Exception as e:
        _handle_db_error("get_user_count_async", e)
        return 0


def get_all_user_ids(exclude_banned: bool = True) -> List[int]:
    """Gets all user IDs, optionally excluding banned users."""
    try:
        query = {}
        user_ids = users_collection.distinct("user_id", query)

        if exclude_banned:
            banned_ids = set(banned_users_collection.distinct("user_id"))
            user_ids = [uid for uid in user_ids if uid not in banned_ids]

        return user_ids
    except Exception as e:
        _handle_db_error("get_all_user_ids", e)
        return []


async def get_all_user_ids_async(exclude_banned: bool = True) -> List[int]:
    """Gets all user IDs (async), optionally excluding banned users."""
    try:
        query = {}
        user_ids = await users_collection_async.distinct("user_id", query)

        if exclude_banned:
            banned_ids = set(await banned_users_collection_async.distinct("user_id"))
            user_ids = [uid for uid in user_ids if uid not in banned_ids]

        return user_ids
    except Exception as e:
        _handle_db_error("get_all_user_ids_async", e)
        return []


# --- Ban/Unban Operations ---


def is_banned(user_id: int) -> bool:
    """Checks if a user is banned."""
    try:
        return banned_users_collection.count_documents({"user_id": user_id}) > 0
    except Exception as e:
        _handle_db_error("is_banned", e, user_id)
        return False


async def is_banned_async(user_id: int) -> bool:
    """Checks if a user is banned (async)."""
    try:
        return (
            await banned_users_collection_async.count_documents({"user_id": user_id})
            > 0
        )
    except Exception as e:
        _handle_db_error("is_banned_async", e, user_id)
        return False


@sandbox_aware_db_write
def ban_user(user_id: int, banned_by: int, reason: Optional[str] = None) -> bool:
    """Bans a user. Returns True on success."""
    if is_banned(user_id):
        logger.warning(f"Attempted to ban already banned user {user_id}")
        return False
    try:
        user_data = get_user(user_id)
        banned_doc = {
            "user_id": user_id,
            "banned_by": banned_by,
            "banned_at": datetime.now(),
            "reason": reason or "No reason provided.",
            "name": user_data.get("name") if user_data else None,
            "username": user_data.get("username") if user_data else None,
        }
        result = banned_users_collection.insert_one(banned_doc)
        return result.acknowledged
    except Exception as e:
        _handle_db_error("ban_user", e, user_id)
        return False


@sandbox_aware_db_write
async def ban_user_async(
    user_id: int, banned_by: int, reason: Optional[str] = None
) -> bool:
    """Bans a user (async). Returns True on success."""
    if await is_banned_async(user_id):
        logger.warning(f"Attempted to ban already banned user {user_id} (async)")
        return False
    try:
        user_data = await get_user_async(user_id)
        banned_doc = {
            "user_id": user_id,
            "banned_by": banned_by,
            "banned_at": datetime.now(),
            "reason": reason or "No reason provided.",
            "name": user_data.get("name") if user_data else None,
            "username": user_data.get("username") if user_data else None,
        }
        result = await banned_users_collection_async.insert_one(banned_doc)
        return result.acknowledged
    except Exception as e:
        _handle_db_error("ban_user_async", e, user_id)
        return False


@sandbox_aware_db_write
def unban_user(user_id: int) -> bool:
    """Unbans a user. Returns True on success."""
    if not is_banned(user_id):
        logger.warning(f"Attempted to unban user {user_id} who is not banned.")
        return False
    try:
        result = banned_users_collection.delete_one({"user_id": user_id})
        return result.deleted_count > 0
    except Exception as e:
        _handle_db_error("unban_user", e, user_id)
        return False


@sandbox_aware_db_write
async def unban_user_async(user_id: int) -> bool:
    """Unbans a user (async). Returns True on success."""
    if not await is_banned_async(user_id):
        logger.warning(f"Attempted to unban user {user_id} who is not banned (async).")
        return False
    try:
        result = await banned_users_collection_async.delete_one({"user_id": user_id})
        return result.deleted_count > 0
    except Exception as e:
        _handle_db_error("unban_user_async", e, user_id)
        return False


def get_ban_info(user_id: int) -> Optional[Dict[str, Any]]:
    """Gets ban details for a user."""
    if not is_banned(user_id):
        return None
    try:
        return banned_users_collection.find_one({"user_id": user_id})
    except Exception as e:
        _handle_db_error("get_ban_info", e, user_id)
        return None


async def get_ban_info_async(user_id: int) -> Optional[Dict[str, Any]]:
    """Gets ban details for a user (async)."""
    if not await is_banned_async(user_id):
        return None
    try:
        return await banned_users_collection_async.find_one({"user_id": user_id})
    except Exception as e:
        _handle_db_error("get_ban_info_async", e, user_id)
        return None


def get_all_banned_users() -> List[Dict[str, Any]]:
    """Gets a list of all banned user documents."""
    try:
        return list(banned_users_collection.find())
    except Exception as e:
        _handle_db_error("get_all_banned_users", e)
        return []


async def get_all_banned_users_async() -> List[Dict[str, Any]]:
    """Gets a list of all banned user documents (async)."""
    try:
        cursor = banned_users_collection_async.find()
        return await cursor.to_list(length=None)
    except Exception as e:
        _handle_db_error("get_all_banned_users_async", e)
        return []


# --- Transaction Operations ---


@sandbox_aware_db_write
def add_transaction(
    user_id: int, transaction_type: str, amount: float, **kwargs
) -> Optional[Dict[str, Any]]:
    """Adds a transaction record. Returns the transaction dict on success, None on error."""
    try:
        transaction = {
            "user_id": user_id,
            "type": transaction_type,
            "amount": float(amount),
            "timestamp": datetime.now(),
            **kwargs,
        }
        if "items" in transaction and transaction["items"] is None:
            transaction["items"] = []

        result = transactions_collection.insert_one(transaction)
        if result.acknowledged:
            transaction["_id"] = result.inserted_id
            return transaction
        else:
            logger.error(f"Transaction insert not acknowledged for user {user_id}")
            return None
    except Exception as e:
        _handle_db_error("add_transaction", e, user_id)
        return None


@sandbox_aware_db_write
async def add_transaction_async(
    user_id: int, transaction_type: str, amount: float, **kwargs
) -> Optional[Dict[str, Any]]:
    """Adds a transaction record (async). Returns the transaction dict on success, None on error."""
    try:
        transaction = {
            "user_id": user_id,
            "type": transaction_type,
            "amount": float(amount),
            "timestamp": datetime.now(),
            **kwargs,
        }
        if "items" in transaction and transaction["items"] is None:
            transaction["items"] = []

        result = await transactions_collection_async.insert_one(transaction)
        if result.acknowledged:
            transaction["_id"] = result.inserted_id
            return transaction
        else:
            logger.error(
                f"Transaction insert not acknowledged for user {user_id} (async)"
            )
            return None
    except Exception as e:
        _handle_db_error("add_transaction_async", e, user_id)
        return None


def get_user_transactions(user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
    """Gets user transactions, most recent first."""
    try:
        return list(
            transactions_collection.find({"user_id": user_id})
            .sort("timestamp", -1)
            .limit(limit)
        )
    except Exception as e:
        _handle_db_error("get_user_transactions", e, user_id)
        return []


async def get_user_transactions_async(
    user_id: int, limit: int = 10
) -> List[Dict[str, Any]]:
    """Gets user transactions, most recent first (async)."""
    try:
        cursor = (
            transactions_collection_async.find({"user_id": user_id})
            .sort("timestamp", -1)
            .limit(limit)
        )
        return await cursor.to_list(length=limit)
    except Exception as e:
        _handle_db_error("get_user_transactions_async", e, user_id)
        return []


def get_recent_orders(limit: int = 10) -> List[Dict[str, Any]]:
    """Gets recent purchase transactions."""
    try:
        return list(
            transactions_collection.find({"type": "purchase"})
            .sort("timestamp", -1)
            .limit(limit)
        )
    except Exception as e:
        _handle_db_error("get_recent_orders", e)
        return []


async def get_recent_orders_async(limit: int = 10) -> List[Dict[str, Any]]:
    """Gets recent purchase transactions (async)."""
    try:
        cursor = (
            transactions_collection_async.find({"type": "purchase"})
            .sort("timestamp", -1)
            .limit(limit)
        )
        return await cursor.to_list(length=limit)
    except Exception as e:
        _handle_db_error("get_recent_orders_async", e)
        return []


def get_user_orders(user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
    """Gets a user's purchase transactions."""
    try:
        return list(
            transactions_collection.find({"user_id": user_id, "type": "purchase"})
            .sort("timestamp", -1)
            .limit(limit)
        )
    except Exception as e:
        _handle_db_error("get_user_orders", e, user_id)
        return []


async def get_user_orders_async(user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
    """Gets a user's purchase transactions (async)."""
    try:
        cursor = (
            transactions_collection_async.find({"user_id": user_id, "type": "purchase"})
            .sort("timestamp", -1)
            .limit(limit)
        )
        return await cursor.to_list(length=limit)
    except Exception as e:
        _handle_db_error("get_user_orders_async", e, user_id)
        return []


# --- Cart Operations ---


def get_or_create_cart(user_id: int) -> Optional[Dict[str, Any]]:
    """Gets or creates a user's cart."""
    try:
        now = datetime.now()
        cart = carts_collection.find_one_and_update(
            {"user_id": user_id},
            {"$setOnInsert": {"items": [], "created_at": now}},
            upsert=True,
            return_document=pymongo.ReturnDocument.AFTER,
        )
        return cart
    except Exception as e:
        _handle_db_error("get_or_create_cart", e, user_id)
        return None


async def get_or_create_cart_async(user_id: int) -> Optional[Dict[str, Any]]:
    """Gets or creates a user's cart (async)."""
    try:
        now = datetime.now()
        cart = await carts_collection_async.find_one_and_update(
            {"user_id": user_id},
            {"$setOnInsert": {"items": [], "created_at": now}},
            upsert=True,
            return_document=pymongo.ReturnDocument.AFTER,
        )
        return cart
    except Exception as e:
        _handle_db_error("get_or_create_cart_async", e, user_id)
        return None


@sandbox_aware_db_write
def update_cart(user_id: int, items: List[Dict[str, Any]]) -> bool:
    """Updates a user's cart items. Returns True on success."""
    try:
        result = carts_collection.update_one(
            {"user_id": user_id}, {"$set": {"items": items}}
        )
        return result.matched_count > 0 and result.acknowledged
    except Exception as e:
        _handle_db_error("update_cart", e, user_id)
        return False


@sandbox_aware_db_write
async def update_cart_async(user_id: int, items: List[Dict[str, Any]]) -> bool:
    """Updates a user's cart items (async). Returns True on success."""
    try:
        result = await carts_collection_async.update_one(
            {"user_id": user_id}, {"$set": {"items": items}}
        )
        return result.matched_count > 0 and result.acknowledged
    except Exception as e:
        _handle_db_error("update_cart_async", e, user_id)
        return False


@sandbox_aware_db_write
def clear_cart(user_id: int) -> bool:
    """Clears a user's cart. Returns True on success."""
    return update_cart(user_id, [])


@sandbox_aware_db_write
async def clear_cart_async(user_id: int) -> bool:
    """Clears a user's cart (async). Returns True on success."""
    return await update_cart_async(user_id, [])


# --- Product Operations ---


def get_products(
    filter_query: Optional[Dict] = None,
    projection: Optional[Dict] = None,
    include_purchased_exclusive: bool = False
) -> List[Dict[str, Any]]:
    """Gets products matching a query, with optional projection.
    By default, filters out purchased/removed exclusive products from customer listings."""
    try:
        # Build the final query
        base_query = filter_query or {}

        # Add exclusive product filter unless explicitly requested to include them
        if not include_purchased_exclusive:
            exclusive_filter = {
                "$or": [
                    # Include non-exclusive products
                    {"is_exclusive_single_use": {"$ne": True}},
                    # Include exclusive products that are available (not purchased and not removed)
                    {
                        "is_exclusive_single_use": True,
                        "is_purchased": {"$ne": True},
                        "removed_from_listings": {"$ne": True}
                    }
                ]
            }

            # Combine with existing query
            if base_query:
                final_query = {"$and": [base_query, exclusive_filter]}
            else:
                final_query = exclusive_filter
        else:
            final_query = base_query

        return list(products_collection.find(final_query, projection))
    except Exception as e:
        _handle_db_error(f"get_products ({filter_query=})", e)
        return []


async def get_products_async(
    filter_query: Optional[Dict] = None,
    projection: Optional[Dict] = None,
    include_purchased_exclusive: bool = False
) -> List[Dict[str, Any]]:
    """Gets products matching a query (async).
    By default, filters out purchased/removed exclusive products from customer listings."""
    try:
        # Build the final query
        base_query = filter_query or {}

        # Add exclusive product filter unless explicitly requested to include them
        if not include_purchased_exclusive:
            exclusive_filter = {
                "$or": [
                    # Include non-exclusive products
                    {"is_exclusive_single_use": {"$ne": True}},
                    # Include exclusive products that are available (not purchased and not removed)
                    {
                        "is_exclusive_single_use": True,
                        "is_purchased": {"$ne": True},
                        "removed_from_listings": {"$ne": True}
                    }
                ]
            }

            # Combine with existing query
            if base_query:
                final_query = {"$and": [base_query, exclusive_filter]}
            else:
                final_query = exclusive_filter
        else:
            final_query = base_query

        cursor = products_collection_async.find(final_query, projection)
        return await cursor.to_list(length=None)
    except Exception as e:
        _handle_db_error(f"get_products_async ({filter_query=})", e)
        return []


def get_all_products_for_admin(
    filter_query: Optional[Dict] = None, projection: Optional[Dict] = None
) -> List[Dict[str, Any]]:
    """Gets ALL products including purchased/removed exclusive products - for admin use only."""
    try:
        return list(products_collection.find(filter_query or {}, projection))
    except Exception as e:
        _handle_db_error(f"get_all_products_for_admin ({filter_query=})", e)
        return []


async def get_all_products_for_admin_async(
    filter_query: Optional[Dict] = None, projection: Optional[Dict] = None
) -> List[Dict[str, Any]]:
    """Gets ALL products including purchased/removed exclusive products - for admin use only (async)."""
    try:
        cursor = products_collection_async.find(filter_query or {}, projection)
        return await cursor.to_list(length=None)
    except Exception as e:
        _handle_db_error(f"get_all_products_for_admin_async ({filter_query=})", e)
        return []


def get_products_by_category_for_admin(category_id_any: Any) -> List[Dict[str, Any]]:
    """Gets ALL products by category including purchased/removed exclusive products - for admin use only."""
    norm_id = _normalize_id(category_id_any)
    if norm_id == "no_category":
        query = {"$or": [{"category_id": {"$exists": False}}, {"category_id": None}]}
    elif norm_id is not None:
        # Handle type mismatch between ObjectId and string category_id values
        # Query for both ObjectId and string representations to ensure compatibility
        if BSON_AVAILABLE and isinstance(norm_id, ObjectId):
            # If we have an ObjectId, also check for its string representation
            query = {
                "$or": [
                    {"category_id": norm_id},  # ObjectId format
                    {"category_id": str(norm_id)}  # String format
                ]
            }
        elif isinstance(norm_id, str) and BSON_AVAILABLE and ObjectId.is_valid(norm_id):
            # If we have a valid ObjectId string, also check for ObjectId format
            try:
                obj_id = ObjectId(norm_id)
                query = {
                    "$or": [
                        {"category_id": norm_id},  # String format
                        {"category_id": obj_id}  # ObjectId format
                    ]
                }
            except Exception:
                # Fallback to string-only query if ObjectId conversion fails
                query = {"category_id": norm_id}
        else:
            # For other types (int, etc.), use as-is
            query = {"category_id": norm_id}
    else:
        return []

    try:
        return list(products_collection.find(query))
    except Exception as e:
        _handle_db_error(f"get_products_by_category_for_admin ({category_id_any})", e)
        return []


def get_products_without_category_for_admin() -> List[Dict[str, Any]]:
    """Gets ALL products without category including purchased/removed exclusive products - for admin use only."""
    try:
        query = {"$or": [{"category_id": {"$exists": False}}, {"category_id": None}]}
        return list(products_collection.find(query))
    except Exception as e:
        _handle_db_error("get_products_without_category_for_admin", e)
        return []


# --- Exclusive Product Lifecycle Management ---


@sandbox_aware_db_write
def mark_exclusive_product_delivered_and_remove(
    product_id: Any, user_id: int, order_number: int
) -> Dict[str, Any]:
    """
    Mark exclusive product as delivered and remove from customer listings.
    This is the main function for automatic product removal after successful delivery.

    Args:
        product_id: Product ID
        user_id: User who purchased the product
        order_number: Order number for tracking

    Returns:
        Dict with operation results
    """
    try:
        # Atomic update to mark as delivered and removed
        update_data = {
            "removed_from_listings": True,
            "removal_timestamp": datetime.now(),
            "delivery_confirmed": True,
            "final_buyer_user_id": user_id,
            "completion_order_number": order_number,
            "product_lifecycle_status": "sold_and_delivered"
        }

        # Perform atomic update with strict conditions
        update_result = products_collection.update_one(
            {
                "_id": _normalize_id(product_id),
                "is_exclusive_single_use": True,
                "is_purchased": True,
                "purchased_by_user_id": user_id,
                "removed_from_listings": {"$ne": True}  # Only if not already removed
            },
            {
                "$set": update_data
            }
        )

        if update_result.modified_count == 0:
            # Check why the update failed
            product = get_product(product_id)
            if not product:
                return {"success": False, "error": "Product not found"}
            elif not product.get("is_exclusive_single_use", False):
                return {"success": False, "error": "Product is not exclusive single-use"}
            elif not product.get("is_purchased", False):
                return {"success": False, "error": "Product is not purchased"}
            elif product.get("purchased_by_user_id") != user_id:
                return {"success": False, "error": "Product purchased by different user"}
            elif product.get("removed_from_listings", False):
                return {"success": False, "error": "Product already removed from listings"}
            else:
                return {"success": False, "error": "Unknown update failure"}

        logger.info(f"Successfully marked exclusive product {product_id} as delivered and removed from listings")

        return {
            "success": True,
            "product_id": product_id,
            "user_id": user_id,
            "order_number": order_number,
            "removal_timestamp": update_data["removal_timestamp"],
            "message": "Product successfully removed from customer listings"
        }

    except Exception as e:
        logger.error(f"Error marking exclusive product {product_id} as delivered and removed: {e}")
        return {
            "success": False,
            "error": f"Database operation failed: {str(e)}"
        }


@sandbox_aware_db_write
def rollback_exclusive_product_removal(
    product_id: Any, user_id: int, reason: str = "Delivery failed"
) -> Dict[str, Any]:
    """
    Rollback exclusive product removal and restore to available status.
    Used when delivery fails after purchase.

    Args:
        product_id: Product ID
        user_id: User who attempted to purchase
        reason: Reason for rollback

    Returns:
        Dict with rollback results
    """
    try:
        # Restore product to available status
        rollback_data = {
            "is_purchased": False,
            "purchased_by_user_id": None,
            "purchase_date": None,
            "removed_from_listings": False,
            "removal_timestamp": None,
            "delivery_confirmed": False,
            "rollback_reason": reason,
            "rollback_timestamp": datetime.now(),
            "product_lifecycle_status": "available"
        }

        # Perform atomic rollback
        update_result = products_collection.update_one(
            {
                "_id": _normalize_id(product_id),
                "is_exclusive_single_use": True,
                "purchased_by_user_id": user_id
            },
            {
                "$set": rollback_data,
                "$unset": {
                    "final_buyer_user_id": "",
                    "completion_order_number": ""
                }
            }
        )

        if update_result.modified_count == 0:
            return {"success": False, "error": "Product not found or not purchased by specified user"}

        logger.warning(f"Rolled back exclusive product {product_id} purchase for user {user_id}: {reason}")

        return {
            "success": True,
            "product_id": product_id,
            "user_id": user_id,
            "reason": reason,
            "rollback_timestamp": rollback_data["rollback_timestamp"],
            "message": "Product successfully restored to available status"
        }

    except Exception as e:
        logger.error(f"Error rolling back exclusive product {product_id} removal: {e}")
        return {
            "success": False,
            "error": f"Rollback operation failed: {str(e)}"
        }


def get_exclusive_product_lifecycle_status(product_id: Any) -> Dict[str, Any]:
    """
    Get detailed lifecycle status of an exclusive product.

    Args:
        product_id: Product ID

    Returns:
        Dict with lifecycle information
    """
    try:
        product = get_product(product_id)
        if not product:
            return {"error": "Product not found"}

        if not product.get("is_exclusive_single_use", False):
            return {"error": "Product is not exclusive single-use"}

        status = {
            "product_id": product_id,
            "product_name": product.get("name", "Unknown"),
            "lifecycle_status": product.get("product_lifecycle_status", "unknown"),
            "is_purchased": product.get("is_purchased", False),
            "removed_from_listings": product.get("removed_from_listings", False),
            "delivery_confirmed": product.get("delivery_confirmed", False),
            "created_at": product.get("created_at"),
            "purchase_date": product.get("purchase_date"),
            "removal_timestamp": product.get("removal_timestamp"),
            "purchased_by_user_id": product.get("purchased_by_user_id"),
            "final_buyer_user_id": product.get("final_buyer_user_id"),
            "completion_order_number": product.get("completion_order_number"),
            "rollback_reason": product.get("rollback_reason"),
            "rollback_timestamp": product.get("rollback_timestamp")
        }

        return status

    except Exception as e:
        logger.error(f"Error getting exclusive product lifecycle status for {product_id}: {e}")
        return {"error": f"Status check failed: {str(e)}"}


def get_product(product_id_any: Any) -> Optional[Dict[str, Any]]:
    """Gets a product by _id (ObjectId or str) or integer id."""
    norm_id = _normalize_id(product_id_any)
    query = None

    if BSON_AVAILABLE and isinstance(norm_id, ObjectId):
        # Try both ObjectId and string queries for maximum compatibility
        queries_to_try = [
            {"_id": norm_id},  # Try ObjectId first
            {"_id": str(norm_id)}  # Try string fallback
        ]

        # Try each query until one succeeds
        for query in queries_to_try:
            try:
                result = products_collection.find_one(query)
                if result:
                    return result
            except Exception as e:
                logger.warning(f"Query failed: {query}, error: {e}")

        return None
    elif isinstance(norm_id, int):
        if products_collection.count_documents({"id": norm_id}) > 0:
            query = {"id": norm_id}
        else:
            logger.warning(f"Integer product ID {norm_id} not found using 'id' field.")
            # Try fallback to ObjectId if original was a string
            if (
                BSON_AVAILABLE
                and isinstance(product_id_any, str)
                and ObjectId.is_valid(product_id_any)
            ):
                query = {"_id": ObjectId(product_id_any)}
                logger.info(
                    f"Falling back to _id lookup for stringified ObjectId: {product_id_any}"
                )
    elif isinstance(norm_id, str):
        # Handle string IDs - could be ObjectId strings that failed normalization
        if len(norm_id) == 24 and re.match(r'^[a-fA-F0-9]{24}$', norm_id):
            # Try both ObjectId and string queries for maximum compatibility
            queries_to_try = []

            if BSON_AVAILABLE and ObjectId.is_valid(norm_id):
                try:
                    queries_to_try.append({"_id": ObjectId(norm_id)})
                except Exception as e:
                    logger.warning(f"Failed to convert string {norm_id} to ObjectId: {e}")

            # Always try string-based query as fallback (some databases store _id as strings)
            queries_to_try.append({"_id": norm_id})

            # Try each query until one succeeds
            for query in queries_to_try:
                try:
                    result = products_collection.find_one(query)
                    if result:
                        return result
                except Exception as e:
                    logger.warning(f"Query failed: {query}, error: {e}")

            return None
        else:
            logger.warning(f"get_product received invalid string ID: {norm_id}")
            return None

    if query:
        try:
            result = products_collection.find_one(query)
            return result
        except Exception as e:
            _handle_db_error(f"get_product ({product_id_any})", e)

    return None


async def get_product_async(product_id_any: Any) -> Optional[Dict[str, Any]]:
    """Gets a product by _id or id (async)."""
    norm_id = _normalize_id(product_id_any)
    query = None

    if BSON_AVAILABLE and isinstance(norm_id, ObjectId):
        # Try both ObjectId and string queries for maximum compatibility
        queries_to_try = [
            {"_id": norm_id},  # Try ObjectId first
            {"_id": str(norm_id)}  # Try string fallback
        ]

        # Try each query until one succeeds
        for query in queries_to_try:
            try:
                result = await products_collection_async.find_one(query)
                if result:
                    return result
            except Exception as e:
                logger.warning(f"Query failed (async): {query}, error: {e}")

        return None
    elif isinstance(norm_id, int):
        if await products_collection_async.count_documents({"id": norm_id}) > 0:
            query = {"id": norm_id}
        else:
            logger.warning(
                f"Integer product ID {norm_id} not found using 'id' field (async)."
            )
            # Try fallback to ObjectId if original was a string
            if (
                BSON_AVAILABLE
                and isinstance(product_id_any, str)
                and ObjectId.is_valid(product_id_any)
            ):
                query = {"_id": ObjectId(product_id_any)}
                logger.info(
                    f"Falling back to _id lookup for stringified ObjectId (async): {product_id_any}"
                )
    elif isinstance(norm_id, str):
        # Handle string IDs - could be ObjectId strings that failed normalization
        if len(norm_id) == 24 and re.match(r'^[a-fA-F0-9]{24}$', norm_id):
            # Try both ObjectId and string queries for maximum compatibility
            queries_to_try = []

            if BSON_AVAILABLE and ObjectId.is_valid(norm_id):
                try:
                    queries_to_try.append({"_id": ObjectId(norm_id)})
                except Exception as e:
                    logger.warning(f"Failed to convert string {norm_id} to ObjectId (async): {e}")

            # Always try string-based query as fallback
            queries_to_try.append({"_id": norm_id})

            # Try each query until one succeeds
            for query in queries_to_try:
                try:
                    result = await products_collection_async.find_one(query)
                    if result:
                        return result
                except Exception as e:
                    logger.warning(f"Query failed (async): {query}, error: {e}")

            return None
        else:
            logger.warning(f"get_product_async received invalid string ID: {norm_id}")
            return None

    if query:
        try:
            return await products_collection_async.find_one(query)
        except Exception as e:
            _handle_db_error(f"get_product_async ({product_id_any})", e)
    return None


get_product_by_id = get_product


@sandbox_aware_db_write
def add_product(product_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Adds a new product with comprehensive validation. Returns the full product dict with _id on success."""
    try:
        # Import validator here to avoid circular imports
        from utils.product_validator import product_validator

        # Validate product data comprehensively
        validation_result = product_validator.validate_complete_product(product_data)
        if not validation_result["valid"]:
            logger.error(f"Product validation failed: {validation_result['errors']}")
            return None

        # Use cleaned data from validation
        cleaned_data = validation_result["cleaned_data"]
        product_name = cleaned_data.get("name", "Unnamed Product")

        # Get next ID
        last_product = products_collection.find_one(sort=[("id", -1)])
        new_id = last_product.get("id", 0) + 1 if last_product else 1

        # Price is already validated and converted by the validator
        price = cleaned_data.get("price", 0.0)

        # Build product document using cleaned and validated data
        product_doc = {
            "id": new_id,
            "name": cleaned_data["name"],
            "description": cleaned_data["description"],
            "price": cleaned_data["price"],
            "file_link": cleaned_data.get("file_link", ""),
            "created_at": product_data.get("created_at", datetime.now()),
            "category_id": cleaned_data.get("category_id"),
        }

        # Add product type specific fields from cleaned data (use cleaned_data instead of product_data)
        type_specific_fields = [
            "is_line_based", "is_exclusive_single_use", "image_url",
            # Regular product file metadata fields
            "file_name", "file_size", "file_type", "file_path", "file_mime_type",
            # Line-based product fields
            "inventory_file_path", "total_lines", "available_lines", "reserved_lines",
            "line_price", "max_quantity_per_order", "preview_format", "allow_shared_inventory",
            # Exclusive product fields
            "is_purchased", "purchased_by_user_id", "purchase_date",
            "exclusive_file_path", "exclusive_file_type", "exclusive_file_size",
            "exclusive_file_mime_type", "expiration_date"
        ]

        for field in type_specific_fields:
            if field in cleaned_data and cleaned_data[field] is not None:
                product_doc[field] = cleaned_data[field]

        # Remove None category_id
        if product_doc.get("category_id") is None:
            del product_doc["category_id"]

        # Sanitize product_doc to handle WindowsPath objects
        sanitized_product_doc = _sanitize_data_for_mongodb(product_doc)

        result = products_collection.insert_one(sanitized_product_doc)
        if result.acknowledged:
            sanitized_product_doc["_id"] = result.inserted_id
            return sanitized_product_doc
        else:
            logger.error(f"Product insert not acknowledged: {product_name}")
            return None
    except Exception as e:
        _handle_db_error(f"add_product ({product_data.get('name')})", e)
        return None


@sandbox_aware_db_write
async def add_product_async(product_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """Adds a new product (async). Returns the full product dict with _id on success."""
    try:
        product_name = product_data.get("name") or "Unnamed Product"

        # Get next ID
        last_product = await products_collection_async.find_one(sort=[("id", -1)])
        new_id = last_product.get("id", 0) + 1 if last_product else 1

        # Handle price conversion safely
        try:
            price = float(product_data.get("price", 0.0))
        except (ValueError, TypeError):
            logger.error(f"Invalid price value for product {product_name} (async)")
            price = 0.0

        product_doc = {
            "id": new_id,
            "name": product_name,
            "description": product_data.get("description", ""),
            "price": price,
            "file_link": product_data.get("file_link"),
            "created_at": product_data.get("created_at", datetime.now()),

            "category_id": _normalize_id(product_data.get("category_id")),
            **{
                k: v
                for k, v in product_data.items()
                if k
                not in [
                    "id",
                    "name",
                    "description",
                    "price",
                    "file_link",
                    "created_at",
                    "category_id",

                ]
                and v is not None
            },
        }
        if product_doc.get("category_id") is None:
            del product_doc["category_id"]

        # Sanitize product_doc to handle WindowsPath objects
        sanitized_product_doc = _sanitize_data_for_mongodb(product_doc)

        result = await products_collection_async.insert_one(sanitized_product_doc)
        if result.acknowledged:
            sanitized_product_doc["_id"] = result.inserted_id
            return sanitized_product_doc
        else:
            logger.error(f"Product insert not acknowledged (async): {product_name}")
            return None
    except Exception as e:
        _handle_db_error(f"add_product_async ({product_data.get('name')})", e)
        return None


@sandbox_aware_db_write
def update_product(
    product_id_any: Any, update_data: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    """Updates a product by _id or id. Returns updated product or None."""
    product = get_product(product_id_any)
    if not product:
        return None
    target_id = product.get("_id")
    if not target_id:
        logger.error(
            f"Could not determine _id for product {product_id_any} during update."
        )
        return None

    try:
        update_set_data = {}
        update_unset_data = {}

        for key, value in update_data.items():
            if key == "category_id":
                normalized_cat_id = _normalize_id(value)
                if normalized_cat_id is None:
                    # Unset the category field if value is None
                    update_unset_data["category_id"] = ""
                else:
                    update_set_data["category_id"] = normalized_cat_id
            elif key == "price":
                update_set_data["price"] = float(value)
            elif value is None:
                # General handling for unsetting fields if value is None
                # Exclude critical fields like name, price from being unset this way
                if key not in ["name", "price"]:
                    update_unset_data[key] = ""
            else:
                update_set_data[key] = value

        update_query = {}
        if update_set_data:
            # Sanitize update_set_data to handle WindowsPath objects
            sanitized_update_set_data = _sanitize_data_for_mongodb(update_set_data)
            update_query["$set"] = sanitized_update_set_data
        if update_unset_data:
            update_query["$unset"] = update_unset_data

        if not update_query:  # Nothing to update
            logger.warning(f"No valid update data provided for product {target_id}")
            return product  # Return original product

        result = products_collection.update_one({"_id": target_id}, update_query)

        if result.matched_count > 0:
            return products_collection.find_one({"_id": target_id})
        else:
            logger.warning(f"Update product matched 0 documents for _id {target_id}")
            return None
    except Exception as e:
        _handle_db_error(f"update_product ({product_id_any})", e)
        return None


@sandbox_aware_db_write
async def update_product_async(
    product_id_any: Any, update_data: Dict[str, Any]
) -> Optional[Dict[str, Any]]:
    """Updates a product by _id or id (async). Returns updated product or None."""
    product = await get_product_async(product_id_any)
    if not product:
        return None
    target_id = product.get("_id")
    if not target_id:
        logger.error(
            f"Could not determine _id for product {product_id_any} during update (async)."
        )
        return None

    try:
        update_set_data = {}
        update_unset_data = {}
        for key, value in update_data.items():
            if key == "category_id":
                normalized_cat_id = _normalize_id(value)
                if normalized_cat_id is None:
                    update_unset_data["category_id"] = ""
                else:
                    update_set_data["category_id"] = normalized_cat_id
            elif key == "price":
                update_set_data["price"] = float(value)
            elif value is None and key not in ["name", "price"]:
                update_unset_data[key] = ""
            else:
                update_set_data[key] = value

        update_query = {}
        if update_set_data:
            # Sanitize update_set_data to handle WindowsPath objects
            sanitized_update_set_data = _sanitize_data_for_mongodb(update_set_data)
            update_query["$set"] = sanitized_update_set_data
        if update_unset_data:
            update_query["$unset"] = update_unset_data

        if not update_query:
            logger.warning(
                f"No valid update data provided for product {target_id} (async)"
            )
            return product

        result = await products_collection_async.update_one(
            {"_id": target_id}, update_query
        )
        if result.matched_count > 0:
            return await products_collection_async.find_one({"_id": target_id})
        else:
            logger.warning(
                f"Update product (async) matched 0 documents for _id {target_id}"
            )
            return None
    except Exception as e:
        _handle_db_error(f"update_product_async ({product_id_any})", e)
        return None


@sandbox_aware_db_write
def delete_product(product_id_any: Any) -> bool:
    """Deletes a product by _id or id. Returns True on success."""
    product = get_product(product_id_any)
    if not product:
        return False
    target_id = product.get("_id")
    if not target_id:
        return False

    try:
        result = products_collection.delete_one({"_id": target_id})
        return result.deleted_count > 0
    except Exception as e:
        _handle_db_error(f"delete_product ({product_id_any})", e)
        return False


@sandbox_aware_db_write
async def delete_product_async(product_id_any: Any) -> bool:
    """Deletes a product by _id or id (async). Returns True on success."""
    product = await get_product_async(product_id_any)
    if not product:
        return False
    target_id = product.get("_id")
    if not target_id:
        return False

    try:
        result = await products_collection_async.delete_one({"_id": target_id})
        return result.deleted_count > 0
    except Exception as e:
        _handle_db_error(f"delete_product_async ({product_id_any})", e)
        return False


# --- Line-based Inventory Operations ---


@sandbox_aware_db_write
def update_product_inventory(product_id_any: Any, available_lines: int, reserved_lines: int = None) -> Dict[str, Any]:
    """Updates line-based product inventory counts with consistent return format."""
    try:
        update_data = {"available_lines": available_lines}
        if reserved_lines is not None:
            update_data["reserved_lines"] = reserved_lines

        result = update_product(product_id_any, update_data)
        if result is not None:
            return {
                "success": True,
                "product_id": product_id_any,
                "updated_fields": update_data,
                "timestamp": datetime.now().isoformat()
            }
        else:
            return {
                "success": False,
                "error": "Failed to update product inventory",
                "product_id": product_id_any
            }
    except Exception as e:
        _handle_db_error(f"update_product_inventory ({product_id_any})", e)
        return {
            "success": False,
            "error": str(e),
            "product_id": product_id_any
        }


@sandbox_aware_db_write
async def update_product_inventory_async(product_id_any: Any, available_lines: int, reserved_lines: int = None) -> bool:
    """Updates line-based product inventory counts (async)."""
    try:
        update_data = {"available_lines": available_lines}
        if reserved_lines is not None:
            update_data["reserved_lines"] = reserved_lines

        result = await update_product_async(product_id_any, update_data)
        return result is not None
    except Exception as e:
        _handle_db_error(f"update_product_inventory_async ({product_id_any})", e)
        return False


def reserve_inventory_lines(product_id_any: Any, quantity: int) -> bool:
    """Reserve lines for a pending purchase using atomic operations. Returns True if successful."""
    try:
        # Get the product to validate it's line-based
        product = get_product(product_id_any)
        if not product or not product.get("is_line_based"):
            logger.warning(f"Product {product_id_any} is not line-based or not found")
            return False

        # Get the MongoDB _id for atomic operations
        product_mongo_id = product.get("_id")
        if not product_mongo_id:
            logger.error(f"Could not determine MongoDB _id for product {product_id_any}")
            return False

        # Perform atomic inventory reservation using MongoDB's conditional update
        # This prevents race conditions by ensuring available_lines >= quantity
        result = products_collection.update_one(
            {
                "_id": product_mongo_id,
                "is_line_based": True,
                "available_lines": {"$gte": quantity}  # Ensure sufficient inventory
            },
            {
                "$inc": {
                    "available_lines": -quantity,  # Decrease available
                    "reserved_lines": quantity     # Increase reserved
                }
            }
        )

        success = result.modified_count > 0
        if success:
            logger.info(f"Successfully reserved {quantity} lines for product {product_id_any}")
        else:
            logger.warning(f"Failed to reserve {quantity} lines for product {product_id_any} - insufficient inventory or concurrent modification")

        return success

    except Exception as e:
        _handle_db_error(f"reserve_inventory_lines ({product_id_any})", e)
        return False


def reserve_inventory_lines_for_user(product_id_any: Any, quantity: int, user_id: int) -> bool:
    """
    Reserve lines for a pending purchase with support for shared inventory mode.

    Args:
        product_id_any: Product ID
        quantity: Number of lines to reserve
        user_id: User ID making the purchase

    Returns:
        True if successful, False otherwise
    """
    try:
        # Validate input parameters
        if not isinstance(user_id, int) or user_id <= 0:
            logger.warning(f"Invalid user_id: {user_id} (type: {type(user_id)})")
            return False

        if not isinstance(quantity, int) or quantity <= 0:
            logger.warning(f"Invalid quantity: {quantity} (type: {type(quantity)})")
            return False

        # Get the product to validate it's line-based
        product = get_product(product_id_any)
        if not product or not product.get("is_line_based"):
            logger.warning(f"Product {product_id_any} is not line-based or not found")
            return False

        # Check if this product uses shared inventory
        allow_shared_inventory = product.get("allow_shared_inventory", False)

        if allow_shared_inventory:
            # For shared inventory, check user-specific availability to prevent duplicate purchases
            total_lines = product.get("total_lines", 0)
            available_for_user = get_available_lines_for_user(user_id, product_id_any, total_lines)
            currently_reserved = get_user_reserved_lines_count(user_id, product_id_any)

            # User can't reserve more than they have available (accounting for existing reservations)
            max_can_reserve = len(available_for_user) - currently_reserved

            if max_can_reserve < quantity:
                logger.warning(f"User {user_id} has insufficient new lines available for product {product_id_any}. "
                             f"Available: {len(available_for_user)}, Currently reserved: {currently_reserved}, "
                             f"Can reserve: {max_can_reserve}, Requested: {quantity}")
                return False

        # Use unified reservation logic for both shared and exclusive inventory
        # This decreases available_lines and increases reserved_lines for both modes
        product_mongo_id = product.get("_id")
        if not product_mongo_id:
            logger.error(f"Could not determine MongoDB _id for product {product_id_any}")
            return False

        if allow_shared_inventory:
            # For shared inventory, create user reservation first, then update product
            # This ensures consistent state for error handling
            user_reservation_created = add_user_reservation(user_id, product_id_any, quantity)
            if not user_reservation_created:
                logger.warning(f"Failed to create user reservation for user {user_id} on product {product_id_any}")
                return False

            # Update product reserved_lines for confirmation logic
            # but don't decrease available_lines (since multiple users can purchase the same lines)
            result = products_collection.update_one(
                {
                    "_id": product_mongo_id,
                    "is_line_based": True
                },
                {
                    "$inc": {
                        "reserved_lines": quantity  # Increase reserved for confirmation logic
                        # available_lines stays the same for shared inventory
                    }
                }
            )

            success = result.modified_count > 0
            if success:
                logger.info(f"Successfully reserved {quantity} lines for user {user_id} on shared inventory product {product_id_any}")
            else:
                # Product update failed - clean up the user reservation we created
                logger.warning(f"Failed to update product reservation count for shared inventory product {product_id_any}")
                remove_user_reservation(user_id, product_id_any, quantity)
                logger.info(f"Cleaned up user reservation for user {user_id} due to product update failure")
        else:
            # For exclusive inventory, perform atomic inventory reservation using MongoDB's conditional update
            # This prevents race conditions by ensuring available_lines >= quantity
            result = products_collection.update_one(
                {
                    "_id": product_mongo_id,
                    "is_line_based": True,
                    "available_lines": {"$gte": quantity}  # Ensure sufficient inventory
                },
                {
                    "$inc": {
                        "available_lines": -quantity,  # Decrease available
                        "reserved_lines": quantity     # Increase reserved
                    }
                }
            )

            success = result.modified_count > 0
            if success:
                logger.info(f"Successfully reserved {quantity} lines for user {user_id} on exclusive inventory product {product_id_any}")
            else:
                logger.warning(f"Failed to reserve {quantity} lines for user {user_id} on product {product_id_any} - insufficient inventory or concurrent modification")

        return success

    except Exception as e:
        _handle_db_error(f"reserve_inventory_lines_for_user ({product_id_any})", e)
        return False


async def reserve_inventory_lines_async(product_id_any: Any, quantity: int) -> bool:
    """Reserve lines for a pending purchase using atomic operations (async). Returns True if successful."""
    try:
        # Get the product to validate it's line-based
        product = await get_product_async(product_id_any)
        if not product or not product.get("is_line_based"):
            logger.warning(f"Product {product_id_any} is not line-based or not found (async)")
            return False

        # Get the MongoDB _id for atomic operations
        product_mongo_id = product.get("_id")
        if not product_mongo_id:
            logger.error(f"Could not determine MongoDB _id for product {product_id_any} (async)")
            return False

        # Perform atomic inventory reservation using MongoDB's conditional update
        # This prevents race conditions by ensuring available_lines >= quantity
        result = await products_collection_async.update_one(
            {
                "_id": product_mongo_id,
                "is_line_based": True,
                "available_lines": {"$gte": quantity}  # Ensure sufficient inventory
            },
            {
                "$inc": {
                    "available_lines": -quantity,  # Decrease available
                    "reserved_lines": quantity     # Increase reserved
                }
            }
        )

        success = result.modified_count > 0
        if success:
            logger.info(f"Successfully reserved {quantity} lines for product {product_id_any} (async)")
        else:
            logger.warning(f"Failed to reserve {quantity} lines for product {product_id_any} - insufficient inventory or concurrent modification (async)")

        return success

    except Exception as e:
        _handle_db_error(f"reserve_inventory_lines_async ({product_id_any})", e)
        return False


def release_reserved_lines(product_id_any: Any, quantity: int, user_id: int = None) -> bool:
    """Release reserved lines back to available inventory using atomic operations.

    Args:
        product_id_any: Product ID
        quantity: Number of lines to release
        user_id: Optional user ID for tracking user-specific reservations
    """
    try:
        # Get the product to validate it's line-based
        product = get_product(product_id_any)
        if not product or not product.get("is_line_based"):
            logger.warning(f"Product {product_id_any} is not line-based or not found")
            return False

        # Get the MongoDB _id for atomic operations
        product_mongo_id = product.get("_id")
        if not product_mongo_id:
            logger.error(f"Could not determine MongoDB _id for product {product_id_any}")
            return False

        # Check if this product uses shared inventory for user reservation tracking
        allow_shared_inventory = product.get("allow_shared_inventory", False)

        if allow_shared_inventory:
            # For shared inventory, don't modify product inventory counts
            # Just remove user-specific reservation
            success = True

            # Remove user-specific reservation if user_id provided
            if user_id:
                remove_user_reservation(user_id, product_id_any, quantity)
                logger.info(f"Successfully released {quantity} lines for shared inventory product {product_id_any} for user {user_id}")
            else:
                logger.info(f"Successfully released {quantity} lines for shared inventory product {product_id_any} (no user specified)")
        else:
            # For exclusive inventory, check current reserved lines to avoid releasing more than available
            reserved = product.get("reserved_lines", 0)
            if reserved < quantity:
                logger.warning(f"Attempting to release more lines ({quantity}) than reserved ({reserved}) for product {product_id_any}")
                quantity = reserved

            if quantity <= 0:
                logger.info(f"No lines to release for product {product_id_any}")
                return True
            # For exclusive inventory, release lines back to available inventory
            result = products_collection.update_one(
                {
                    "_id": product_mongo_id,
                    "is_line_based": True,
                    "reserved_lines": {"$gte": quantity}  # Ensure sufficient reserved lines
                },
                {
                    "$inc": {
                        "available_lines": quantity,   # Increase available
                        "reserved_lines": -quantity   # Decrease reserved
                    }
                }
            )

            success = result.modified_count > 0
            if success:
                logger.info(f"Successfully released {quantity} lines for exclusive inventory product {product_id_any}")
            else:
                logger.warning(f"Failed to release {quantity} lines for product {product_id_any} - insufficient reserved lines or concurrent modification")

        return success

    except Exception as e:
        _handle_db_error(f"release_reserved_lines ({product_id_any})", e)
        return False


def confirm_line_purchase(product_id_any: Any, quantity: int) -> bool:
    """Confirm purchase by removing reserved lines from inventory using atomic operations."""
    try:
        # Get the product to validate it's line-based
        product = get_product(product_id_any)
        if not product or not product.get("is_line_based"):
            logger.warning(f"Product {product_id_any} is not line-based or not found")
            return False

        # Get the MongoDB _id for atomic operations
        product_mongo_id = product.get("_id")
        if not product_mongo_id:
            logger.error(f"Could not determine MongoDB _id for product {product_id_any}")
            return False

        # Check current reserved lines to avoid confirming more than available
        reserved = product.get("reserved_lines", 0)
        if reserved < quantity:
            logger.warning(f"Attempting to confirm more lines ({quantity}) than reserved ({reserved}) for product {product_id_any}")
            quantity = reserved

        if quantity <= 0:
            logger.info(f"No lines to confirm for product {product_id_any}")
            return True

        # Perform atomic purchase confirmation using MongoDB's conditional update
        result = products_collection.update_one(
            {
                "_id": product_mongo_id,
                "is_line_based": True,
                "reserved_lines": {"$gte": quantity}  # Ensure sufficient reserved lines
            },
            {
                "$inc": {
                    "reserved_lines": -quantity,  # Decrease reserved (items are consumed)
                    "total_lines": -quantity      # Decrease total (items are consumed)
                }
            }
        )

        success = result.modified_count > 0
        if success:
            logger.info(f"Successfully confirmed purchase of {quantity} lines for product {product_id_any}")
        else:
            logger.warning(f"Failed to confirm purchase of {quantity} lines for product {product_id_any} - insufficient reserved lines or concurrent modification")

        return success

    except Exception as e:
        _handle_db_error(f"confirm_line_purchase ({product_id_any})", e)
        return False


def confirm_line_purchase_with_history(product_id_any: Any, quantity: int, user_id: int, line_indices: List[int] = None) -> bool:
    """
    Confirm line purchase with support for both exclusive and shared inventory modes.

    Args:
        product_id_any: Product ID
        quantity: Number of lines to confirm
        user_id: User ID making the purchase
        line_indices: Specific line indices purchased (for shared inventory tracking)

    Returns:
        True if successful, False otherwise
    """
    try:
        # Validate input parameters
        if not isinstance(user_id, int) or user_id <= 0:
            logger.warning(f"Invalid user_id: {user_id} (type: {type(user_id)})")
            return False

        if not isinstance(quantity, int) or quantity <= 0:
            logger.warning(f"Invalid quantity: {quantity} (type: {type(quantity)})")
            return False

        # Get the product to validate it's line-based
        product = get_product(product_id_any)
        if not product or not product.get("is_line_based"):
            logger.warning(f"Product {product_id_any} is not line-based or not found")
            return False

        # Check if this product uses shared inventory for user tracking
        allow_shared_inventory = product.get("allow_shared_inventory", False)

        # Get the MongoDB _id for atomic operations
        product_mongo_id = product.get("_id")
        if not product_mongo_id:
            logger.error(f"Could not determine MongoDB _id for product {product_id_any}")
            return False

        # For shared inventory, validate user reservations instead of product reservations
        if allow_shared_inventory:
            # Check user-specific reservations for shared inventory
            user_reserved = get_user_reserved_lines_count(user_id, product_id_any)
            if user_reserved < quantity:
                logger.warning(f"User {user_id} attempting to confirm more lines ({quantity}) than reserved ({user_reserved}) for shared product {product_id_any}")
                quantity = user_reserved
        else:
            # For exclusive inventory, check product-level reservations
            reserved = product.get("reserved_lines", 0)
            if reserved < quantity:
                logger.warning(f"Attempting to confirm more lines ({quantity}) than reserved ({reserved}) for product {product_id_any}")
                quantity = reserved

        if quantity <= 0:
            logger.info(f"No lines to confirm for product {product_id_any}")
            return True

        # Use unified confirmation logic for both shared and exclusive inventory
        # For shared inventory: only decrease reserved_lines (keep total_lines intact for reuse)
        # For exclusive inventory: decrease both reserved_lines and total_lines (consume inventory)
        if allow_shared_inventory:
            # For shared inventory, update inventory counts to reflect used lines
            # and track purchase history
            success = True

            # Remove user-specific reservation for shared inventory
            reservation_removed = remove_user_reservation(user_id, product_id_any, quantity)
            if not reservation_removed:
                logger.warning(f"Failed to remove user reservation for user {user_id}, product {product_id_any}, quantity {quantity}")
                # Don't fail the entire operation for reservation removal issues

            # For shared inventory, only decrease reserved_lines - available_lines and total_lines stay the same
            # This allows other users to purchase the same lines
            result = products_collection.update_one(
                {
                    "_id": product_mongo_id,
                    "is_line_based": True,
                    "reserved_lines": {"$gte": quantity}
                },
                {
                    "$inc": {
                        "reserved_lines": -quantity  # Only decrease reserved (purchase complete)
                    }
                    # available_lines and total_lines remain unchanged for shared inventory
                    # This allows multiple users to purchase the same lines
                }
            )

            if result.modified_count == 0:
                logger.warning(f"Failed to update inventory counts for shared inventory product {product_id_any}")
                success = False

            # Track user purchase history for shared inventory
            if line_indices:
                history_added = add_line_purchase_history(user_id, product_id_any, line_indices, quantity)
                if not history_added:
                    logger.error(f"Failed to record purchase history for user {user_id}, product {product_id_any}, lines {line_indices}")
                    success = False
            else:
                logger.warning(f"No line indices provided for shared inventory purchase confirmation - user {user_id}, product {product_id_any}")

            if success:
                logger.info(f"Successfully confirmed shared inventory purchase of {quantity} lines for product {product_id_any} by user {user_id}")
            else:
                logger.error(f"Failed to confirm shared inventory purchase for user {user_id}, product {product_id_any}")
        else:
            # For exclusive inventory, consume the inventory (decrease reserved and total)
            # Note: available_lines was already decreased during reservation and should stay decreased
            result = products_collection.update_one(
                {
                    "_id": product_mongo_id,
                    "is_line_based": True,
                    "reserved_lines": {"$gte": quantity}
                },
                {
                    "$inc": {
                        "reserved_lines": -quantity,  # Decrease reserved (items are consumed)
                        "total_lines": -quantity      # Decrease total (items are consumed)
                    }
                    # available_lines remains decreased from reservation - this is correct
                }
            )

            success = result.modified_count > 0
            if success:
                logger.info(f"Successfully confirmed exclusive inventory purchase of {quantity} lines for product {product_id_any} by user {user_id}")
            else:
                logger.warning(f"Failed to confirm purchase of {quantity} lines for product {product_id_any}")

        return success

    except Exception as e:
        _handle_db_error(f"confirm_line_purchase_with_history ({product_id_any})", e)
        return False


def get_product_by_name(
    name: str, case_insensitive: bool = True
) -> Optional[Dict[str, Any]]:
    """Gets a product by name (case-insensitive by default)."""
    if not name:
        return None
    query = {"name": name}
    if case_insensitive:
        query = {"name": {"$regex": f"^{name}$", "$options": "i"}}
    try:
        return products_collection.find_one(query)
    except Exception as e:
        _handle_db_error(f"get_product_by_name ({name})", e)
        return None


async def get_product_by_name_async(
    name: str, case_insensitive: bool = True
) -> Optional[Dict[str, Any]]:
    """Gets a product by name (async, case-insensitive by default)."""
    if not name:
        return None
    query = {"name": name}
    if case_insensitive:
        query = {"name": {"$regex": f"^{name}$", "$options": "i"}}
    try:
        return await products_collection_async.find_one(query)
    except Exception as e:
        _handle_db_error(f"get_product_by_name_async ({name})", e)
        return None


def get_product_count() -> int:
    """Gets the total number of products."""
    try:
        return products_collection.count_documents({})
    except Exception as e:
        _handle_db_error("get_product_count", e)
        return 0











async def get_product_count_async() -> int:
    """Gets the total number of products (async)."""
    try:
        return await products_collection_async.count_documents({})
    except Exception as e:
        _handle_db_error("get_product_count_async", e)
        return 0


# --- Category Operations ---


def generate_slug(name: str) -> str:
    """Generates a URL-friendly slug."""
    import re

    slug = name.lower().strip()
    slug = re.sub(r"[\s]+", "-", slug)
    slug = re.sub(r"[^\w\-]+", "", slug)
    slug = re.sub(r"[-]+", "-", slug)
    return slug


def category_exists(name: str) -> bool:
    """Checks if a category name exists (case-insensitive)."""
    try:
        return (
            categories_collection.count_documents(
                {"name": {"$regex": f"^{name}$", "$options": "i"}}
            )
            > 0
        )
    except Exception as e:
        _handle_db_error(f"category_exists ({name})", e)
        return False


@sandbox_aware_db_write
def add_category(
    name: str, description: str, image_data: Optional[Dict] = None
) -> Optional[Dict[str, Any]]:
    """Adds a category. Returns category dict with _id on success."""
    if category_exists(name):
        logger.warning(f"Attempted to add existing category: {name}")
        return None
    try:
        last_category = categories_collection.find_one(sort=[("id", -1)])
        new_id = last_category.get("id", 0) + 1 if last_category else 1

        category_doc = {
            "id": new_id,
            "name": name,
            "slug": generate_slug(name),
            "description": description,
            "created_at": datetime.now(),
        }
        if image_data:
            category_doc["image"] = image_data

        # Sanitize category_doc to handle WindowsPath objects
        sanitized_category_doc = _sanitize_data_for_mongodb(category_doc)

        result = categories_collection.insert_one(sanitized_category_doc)
        if result.acknowledged:
            sanitized_category_doc["_id"] = result.inserted_id
            return sanitized_category_doc
        else:
            logger.error(f"Category insert not acknowledged: {name}")
            return None
    except Exception as e:
        _handle_db_error(f"add_category ({name})", e)
        return None


create_category = add_category





def get_all_categories() -> List[Dict[str, Any]]:
    """Gets all categories, sorted by name."""
    try:
        return list(categories_collection.find().sort("name", 1))
    except Exception as e:
        _handle_db_error("get_all_categories", e)
        return []





def get_category_by_id(category_id_any: Any) -> Optional[Dict[str, Any]]:
    """Gets a category by _id or id."""
    if category_id_any is None:
        return None

    # First attempt direct lookup with the provided ID
    try:
        category = categories_collection.find_one({"_id": category_id_any})
        if category:
            return category
    except Exception:
        pass

    # Try with integer ID if appropriate
    if isinstance(category_id_any, str) and category_id_any.isdigit():
        try:
            category = categories_collection.find_one({"id": int(category_id_any)})
            if category:
                return category
        except Exception:
            pass

    # Try with string representation comparison for ObjectId
    if isinstance(category_id_any, (str, ObjectId)):
        try:
            for category in categories_collection.find():
                if str(category.get("_id", "")) == str(category_id_any):
                    return category
        except Exception as e:
            _handle_db_error(
                f"get_category_by_id ObjectId check ({category_id_any})", e
            )

    # Final fallback - try with string ID
    try:
        category = categories_collection.find_one({"id": category_id_any})
        if category:
            return category
    except Exception as e:
        _handle_db_error(f"get_category_by_id ({category_id_any})", e)

    return None





def get_category_by_name(name: str) -> Optional[Dict[str, Any]]:
    """Gets category by name (case-insensitive)."""
    if not name:
        return None
    try:
        return categories_collection.find_one(
            {"name": {"$regex": f"^{name}$", "$options": "i"}}
        )
    except Exception as e:
        _handle_db_error(f"get_category_by_name ({name})", e)
        return None





def get_products_by_category(category_id_any: Any) -> List[Dict[str, Any]]:
    """Gets products by category _id or id.
    Automatically filters out purchased/removed exclusive products from customer listings."""
    norm_id = _normalize_id(category_id_any)
    if norm_id == "no_category":
        base_query = {"$or": [{"category_id": {"$exists": False}}, {"category_id": None}]}
    elif norm_id is not None:
        # Handle type mismatch between ObjectId and string category_id values
        # Query for both ObjectId and string representations to ensure compatibility
        if BSON_AVAILABLE and isinstance(norm_id, ObjectId):
            # If we have an ObjectId, also check for its string representation
            base_query = {
                "$or": [
                    {"category_id": norm_id},  # ObjectId format
                    {"category_id": str(norm_id)}  # String format
                ]
            }
        elif isinstance(norm_id, str) and BSON_AVAILABLE and ObjectId.is_valid(norm_id):
            # If we have a valid ObjectId string, also check for ObjectId format
            try:
                obj_id = ObjectId(norm_id)
                base_query = {
                    "$or": [
                        {"category_id": norm_id},  # String format
                        {"category_id": obj_id}  # ObjectId format
                    ]
                }
            except Exception:
                # Fallback to string-only query if ObjectId conversion fails
                base_query = {"category_id": norm_id}
        else:
            # For other types (int, etc.), use as-is
            base_query = {"category_id": norm_id}
    else:
        return []

    # Add filter to exclude purchased/removed exclusive products from customer listings
    query = {
        "$and": [
            base_query,
            {
                "$or": [
                    # Include non-exclusive products
                    {"is_exclusive_single_use": {"$ne": True}},
                    # Include exclusive products that are available (not purchased and not removed)
                    {
                        "is_exclusive_single_use": True,
                        "is_purchased": {"$ne": True},
                        "removed_from_listings": {"$ne": True}
                    }
                ]
            }
        ]
    }

    try:
        return list(products_collection.find(query))
    except Exception as e:
        _handle_db_error(f"get_products_by_category ({category_id_any})", e)
        return []





def get_products_without_category() -> List[Dict[str, Any]]:
    """Gets products without a category assigned.
    Automatically filters out purchased/removed exclusive products from customer listings."""
    try:
        base_query = {"$or": [{"category_id": {"$exists": False}}, {"category_id": None}]}

        # Add filter to exclude purchased/removed exclusive products
        query = {
            "$and": [
                base_query,
                {
                    "$or": [
                        # Include non-exclusive products
                        {"is_exclusive_single_use": {"$ne": True}},
                        # Include exclusive products that are available (not purchased and not removed)
                        {
                            "is_exclusive_single_use": True,
                            "is_purchased": {"$ne": True},
                            "removed_from_listings": {"$ne": True}
                        }
                    ]
                }
            ]
        }

        return list(products_collection.find(query))
    except Exception as e:
        _handle_db_error("get_products_without_category", e)
        return []





@sandbox_aware_db_write
def delete_category(category_id_any: Any) -> bool:
    """Deletes a category by _id or id and reassigns its products."""
    category = get_category_by_id(category_id_any)
    if not category:
        return False
    cat_link_id = category.get("_id") or category.get("id")
    if not cat_link_id:
        logger.error(f"Could not determine linking ID for category: {category}")
        return False

    try:
        # Get count of products using this category
        product_count = products_collection.count_documents(
            {"category_id": cat_link_id}
        )

        # If products are using this category, update them to have no category
        if product_count > 0:
            logger.info(
                f"Updating {product_count} products to remove category {category.get('name')} reference"
            )
            products_collection.update_many(
                {"category_id": cat_link_id}, {"$set": {"category_id": None}}
            )

        # Now delete the category
        target_id_for_delete = category.get("_id")
        if not target_id_for_delete:
            logger.error(f"Could not determine _id for category deletion: {category}")
            return False

        result = categories_collection.delete_one({"_id": target_id_for_delete})
        return result.deleted_count > 0
    except Exception as e:
        _handle_db_error(f"delete_category ({category_id_any})", e)
        return False





@sandbox_aware_db_write
def update_category(category_id_any: Any, update_data: Dict[str, Any]) -> bool:
    """Updates a category by _id or id."""
    category = get_category_by_id(category_id_any)
    if not category:
        return False
    target_id = category.get("_id")
    if not target_id:
        return False

    if "name" in update_data:
        update_data["slug"] = generate_slug(update_data["name"])

    # Sanitize update_data to handle WindowsPath objects
    sanitized_update_data = _sanitize_data_for_mongodb(update_data)

    try:
        result = categories_collection.update_one(
            {"_id": target_id}, {"$set": sanitized_update_data}
        )
        return result.matched_count > 0
    except Exception as e:
        _handle_db_error(f"update_category ({category_id_any})", e)
        return False





# --- Payment Operations ---


def ensure_payment_indexes():
    """Ensure all necessary indexes exist for the payments collection."""
    try:
        logger.info("Creating/verifying payment collection indexes...")
        # Create indexes for payments collection
        payments_collection.create_index("track_id", unique=True)
        payments_collection.create_index("user_id")
        payments_collection.create_index("status")
        payments_collection.create_index([("user_id", 1), ("created_at", -1)])
        payments_collection.create_index("created_at")
        logger.info("Payment collection indexes created/verified successfully")
        return True
    except Exception as e:
        logger.error(f"Failed to create payment indexes: {e}", exc_info=True)
        return False





@sandbox_aware_db_write
def save_payment_details(**kwargs) -> Optional[Dict[str, Any]]:
    """
    Saves payment details. Returns payment dict on success.

    This function now tracks both the requested amount and the actual paid amount.
    The actual_paid_amount will be updated later during payment verification.
    """
    try:
        # Extract the amount from kwargs
        amount = float(kwargs.get("amount", 0))

        # Create the payment record with additional security fields
        payment = {
            "created_at": datetime.now(),
            "updated_at": datetime.now(),
            "status": "pending",
            "requested_amount": amount,  # Store the original requested amount
            "actual_paid_amount": 0,  # Will be updated during verification
            "payment_verified": False,  # Will be set to True after verification
            **kwargs,
        }

        # Log the payment creation
        logger.info(
            f"Creating payment record for track_id {kwargs.get('track_id')}: "
            f"Requested amount: ${amount:.2f}"
        )

        # Insert the payment record
        result = payments_collection.insert_one(payment)
        if result.acknowledged:
            payment["_id"] = result.inserted_id
            return payment
        else:
            logger.error(
                f"Payment insert not acknowledged for track_id {kwargs.get('track_id')}"
            )
            return None
    except Exception as e:
        _handle_db_error(f"save_payment_details ({kwargs.get('track_id')})", e)
        return None





def get_payment_by_track_id(track_id: str) -> Optional[Dict[str, Any]]:
    """Gets payment details by track_id."""
    if not track_id:
        return None
    try:
        return payments_collection.find_one({"track_id": track_id})
    except Exception as e:
        _handle_db_error(f"get_payment_by_track_id ({track_id})", e)
        return None





@sandbox_aware_db_write
def update_payment_status_atomic(
    track_id: str,
    new_status: str,
    expected_status: Optional[str] = None,
    **kwargs
) -> Optional[Dict[str, Any]]:
    """
    Atomically updates payment status only if it matches the expected status.
    This prevents race conditions during concurrent payment processing.

    Args:
        track_id: The payment track ID
        new_status: The new payment status to set
        expected_status: The expected current status (None to skip check)
        **kwargs: Additional fields to update, such as actual_paid_amount

    Returns:
        The updated payment document or None if no update occurred
    """
    if not track_id:
        return None

    try:
        # Build the query filter
        query_filter = {"track_id": track_id}
        if expected_status is not None:
            query_filter["status"] = expected_status

        # Prepare update data
        update_data = {"status": new_status, "updated_at": datetime.now(), **kwargs}

        # Use find_one_and_update for atomic operation
        updated_payment = payments_collection.find_one_and_update(
            query_filter,
            {"$set": update_data},
            return_document=pymongo.ReturnDocument.AFTER
        )

        if updated_payment:
            logger.info(f"Atomically updated payment {track_id} status to {new_status}")

            # Log amount comparison if actual_paid_amount is provided
            if "actual_paid_amount" in kwargs:
                actual_paid_amount = float(kwargs.get("actual_paid_amount", 0))
                requested_amount = float(updated_payment.get("amount", 0))
                logger.info(
                    f"Payment {track_id} amounts: Requested=${requested_amount:.2f}, "
                    f"Actual=${actual_paid_amount:.2f}"
                )
        else:
            if expected_status:
                logger.warning(
                    f"Payment {track_id} not updated - status was not '{expected_status}' or payment not found"
                )
            else:
                logger.warning(f"Payment {track_id} not found for status update")

        return updated_payment

    except Exception as e:
        _handle_db_error(f"update_payment_status_atomic ({track_id})", e)
        return None


@sandbox_aware_db_write
def update_payment_status(
    track_id: str, status: str, **kwargs
) -> Optional[Dict[str, Any]]:
    """
    Updates payment status and optionally other fields.

    Args:
        track_id: The payment track ID
        status: The new payment status
        **kwargs: Additional fields to update, such as actual_paid_amount

    Returns:
        The updated payment document or None if error
    """
    if not track_id:
        return None
    try:
        # Get the current payment record to compare amounts if needed
        current_payment = get_payment_by_track_id(track_id)

        # Prepare update data
        update_data = {"status": status, "updated_at": datetime.now(), **kwargs}

        # If actual_paid_amount is provided, validate it
        if "actual_paid_amount" in kwargs:
            actual_paid_amount = float(kwargs.get("actual_paid_amount", 0))
            requested_amount = (
                float(current_payment.get("amount", 0)) if current_payment else 0
            )

            # Log the amount comparison
            logger.info(
                f"Payment {track_id} update: Requested=${requested_amount:.2f}, "
                f"Actual=${actual_paid_amount:.2f}"
            )

            # Check for suspicious amount differences
            if actual_paid_amount > 0 and requested_amount > 0:
                if actual_paid_amount < requested_amount * 0.5:  # Less than 50% paid
                    logger.warning(
                        f"SUSPICIOUS: Payment {track_id} received only "
                        f"{(actual_paid_amount/requested_amount)*100:.1f}% of requested amount"
                    )
                elif actual_paid_amount > requested_amount * 1.5:  # More than 150% paid
                    logger.warning(
                        f"UNUSUAL: Payment {track_id} received "
                        f"{(actual_paid_amount/requested_amount)*100:.1f}% of requested amount"
                    )

        # Update the payment record
        result = payments_collection.update_one(
            {"track_id": track_id}, {"$set": update_data}
        )
        if result.matched_count > 0:
            return get_payment_by_track_id(track_id)
        else:
            logger.warning(
                f"Update payment status found no match for track_id {track_id}"
            )
            return None
    except Exception as e:
        _handle_db_error(f"update_payment_status ({track_id})", e)
        return None





def get_user_pending_payments(user_id: int) -> List[Dict[str, Any]]:
    """Get all pending payments for a user."""
    return list(payments_collection.find({"user_id": user_id, "status": "pending"}))





def get_latest_payment(user_id: int) -> Optional[Dict[str, Any]]:
    """Get the latest payment for a user (synchronous)."""
    try:
        # Ensure user_id is an integer
        user_id_int = int(user_id)

        # Log the request for debugging
        logger.info(f"Fetching latest payment for user_id: {user_id_int}")

        # First check if we have any payments for this user
        count = payments_collection.count_documents({"user_id": user_id_int})
        logger.info(f"Found {count} total payments for user_id: {user_id_int}")

        if count == 0:
            logger.info(f"No payments found for user_id: {user_id_int}")
            return None

        # Sort by created_at first, then by _id to break ties consistently
        payment = payments_collection.find_one(
            {"user_id": user_id_int}, sort=[("created_at", -1), ("_id", -1)]
        )

        if payment:
            logger.info(
                f"Latest payment found for user_id {user_id_int}: "
                f"track_id={payment.get('track_id')}, "
                f"created_at={payment.get('created_at')}, "
                f"status={payment.get('status')}"
            )
        else:
            logger.warning(
                f"No payment found for user_id {user_id_int} despite count={count}"
            )

        return payment
    except Exception as e:
        _handle_db_error("get_latest_payment", e, user_id)
        return None





def get_latest_payment_by_user_id(user_id: int) -> Optional[Dict[str, Any]]:
    """Gets the latest payment for a user (any status)."""
    try:
        return payments_collection.find_one(
            {"user_id": user_id}, sort=[("created_at", -1)]
        )
    except Exception as e:
        _handle_db_error("get_latest_payment_by_user_id", e, user_id)
        return None





# --- Admin Role Operations ---


@functools.lru_cache(maxsize=128)
def is_admin(user_id: int) -> bool:
    """Checks if user is an admin (checks DB)."""
    if not isinstance(user_id, int):
        return False
    try:
        return admins_collection.count_documents({"user_id": user_id}) > 0
    except Exception as e:
        _handle_db_error("is_admin", e, user_id)
        return False


async def is_admin_async(user_id: int) -> bool:
    """Checks if user is an admin (checks DB, async)."""
    if not isinstance(user_id, int):
        return False
    try:
        return await admins_collection_async.count_documents({"user_id": user_id}) > 0
    except Exception as e:
        _handle_db_error("is_admin_async", e, user_id)
        return False


def is_owner(user_id: int) -> bool:
    """Checks if user ID matches the configured OWNER_ID."""
    return isinstance(user_id, int) and user_id == OWNER_ID


def is_owner_or_admin(user_id: int) -> bool:
    """Checks if user is owner OR an admin in the DB."""
    return is_owner(user_id) or is_admin(user_id)


async def is_owner_or_admin_async(user_id: int) -> bool:
    """Checks if user is owner OR an admin in the DB (async)."""
    return is_owner(user_id) or await is_admin_async(user_id)


def get_user_role(user_id: int) -> str:
    """Determines user role: 'owner', 'admin', or 'customer'."""
    if is_owner(user_id):
        return "owner"
    if is_admin(user_id):
        return "admin"
    return "customer"


async def get_user_role_async(user_id: int) -> str:
    """Determines user role (async)."""
    if is_owner(user_id):
        return "owner"
    if await is_admin_async(user_id):
        return "admin"
    return "customer"


@sandbox_aware_db_write
def add_admin(
    user_id: int,
    added_by: int,
    name: Optional[str] = None,
    username: Optional[str] = None,
) -> bool:
    """Adds a user as an admin. Returns True on success."""
    if is_owner_or_admin(user_id):
        logger.warning(f"Attempt to add existing owner/admin: {user_id}")
        return False
    try:
        if not name or not username:
            user_data = get_user(user_id)
            name = name or (user_data.get("name") if user_data else None)
            username = username or (user_data.get("username") if user_data else None)

        admin_doc = {
            "user_id": user_id,
            "added_by": added_by,
            "is_owner": False,
            "added_at": datetime.now(),
            "name": name,
            "username": username,
        }
        result = admins_collection.insert_one(admin_doc)
        is_admin.cache_clear()
        return result.acknowledged
    except Exception as e:
        _handle_db_error("add_admin", e, user_id)
        return False


@sandbox_aware_db_write
async def add_admin_async(
    user_id: int,
    added_by: int,
    name: Optional[str] = None,
    username: Optional[str] = None,
) -> bool:
    """Adds a user as an admin (async). Returns True on success."""
    if await is_owner_or_admin_async(user_id):
        logger.warning(f"Attempt to add existing owner/admin (async): {user_id}")
        return False
    try:
        if not name or not username:
            user_data = await get_user_async(user_id)
            name = name or (user_data.get("name") if user_data else None)
            username = username or (user_data.get("username") if user_data else None)

        admin_doc = {
            "user_id": user_id,
            "added_by": added_by,
            "is_owner": False,
            "added_at": datetime.now(),
            "name": name,
            "username": username,
        }
        result = await admins_collection_async.insert_one(admin_doc)
        is_admin.cache_clear()
        return result.acknowledged
    except Exception as e:
        _handle_db_error("add_admin_async", e, user_id)
        return False


@sandbox_aware_db_write
def remove_admin(user_id: int) -> bool:
    """Removes admin privileges from a user. Returns True on success."""
    if is_owner(user_id):
        logger.error(f"Attempted to remove owner {user_id} via remove_admin.")
        return False
    if not is_admin(user_id):
        logger.warning(f"Attempted to remove non-admin user {user_id}.")
        return False
    try:
        result = admins_collection.delete_one({"user_id": user_id})
        is_admin.cache_clear()
        return result.deleted_count > 0
    except Exception as e:
        _handle_db_error("remove_admin", e, user_id)
        return False


@sandbox_aware_db_write
async def remove_admin_async(user_id: int) -> bool:
    """Removes admin privileges (async). Returns True on success."""
    if is_owner(user_id):
        logger.error(f"Attempted to remove owner {user_id} via remove_admin_async.")
        return False
    if not await is_admin_async(user_id):
        logger.warning(f"Attempted to remove non-admin user {user_id} (async).")
        return False
    try:
        result = await admins_collection_async.delete_one({"user_id": user_id})
        is_admin.cache_clear()
        return result.deleted_count > 0
    except Exception as e:
        _handle_db_error("remove_admin_async", e, user_id)
        return False


def get_all_admins() -> List[Dict[str, Any]]:
    """Gets all documents from the admins collection."""
    try:
        return list(admins_collection.find({"is_owner": {"$ne": True}}))
    except Exception as e:
        _handle_db_error("get_all_admins", e)
        return []


async def get_all_admins_async() -> List[Dict[str, Any]]:
    """Gets all documents from the admins collection (async)."""
    try:
        cursor = admins_collection_async.find({"is_owner": {"$ne": True}})
        return await cursor.to_list(length=None)
    except Exception as e:
        _handle_db_error("get_all_admins_async", e)
        return []


# --- Support Messages ---
# (Add robust error handling and parameter validation to these functions)


def save_support_message(**kwargs) -> Optional[Dict[str, Any]]:
    """Logs a new support message."""
    try:
        message_data = {
            "timestamp": datetime.now(),
            "status": "pending",
            "replies": [],
            "last_reply_time": None,
            **kwargs,
        }
        result = support_messages_collection.insert_one(message_data)
        if result.acknowledged:
            message_data["_id"] = result.inserted_id
            return message_data
        else:
            logger.error(
                f"Support message insert not acknowledged for user {kwargs.get('user_id')}"
            )
            return None
    except Exception as e:
        _handle_db_error(f"save_support_message ({kwargs.get('user_id')})", e)
        return None


async def save_support_message_async(**kwargs) -> Optional[Dict[str, Any]]:
    """Logs a new support message (async)."""
    try:
        message_data = {
            "timestamp": datetime.now(),
            "status": "pending",
            "replies": [],
            "last_reply_time": None,
            **kwargs,
        }
        result = await support_messages_collection_async.insert_one(message_data)
        if result.acknowledged:
            message_data["_id"] = result.inserted_id
            return message_data
        else:
            logger.error(
                f"Support message insert not acknowledged for user {kwargs.get('user_id')} (async)"
            )
            return None
    except Exception as e:
        _handle_db_error(f"save_support_message_async ({kwargs.get('user_id')})", e)
        return None


log_support_message = save_support_message
log_support_message_async = save_support_message_async


def get_support_thread(thread_id_any: Any) -> Optional[Dict[str, Any]]:
    """Gets a support thread by _id, with fallback to find by ID suffix."""
    try:
        # First try to normalize the ID to an ObjectId
        norm_id = _normalize_id(thread_id_any)

        # If we have a valid ObjectId, try direct lookup
        if BSON_AVAILABLE and isinstance(norm_id, ObjectId):
            thread = support_messages_collection.find_one({"_id": norm_id})
            if thread:
                return thread

        # If the ID is a string and we didn't find it directly, try to match by suffix
        if isinstance(thread_id_any, str) and len(thread_id_any) > 0:
            # Get all threads and search for one with matching ID suffix
            threads = list(support_messages_collection.find().limit(100))
            for thread in threads:
                thread_id_str = str(thread.get("_id", ""))
                if thread_id_str.endswith(thread_id_any):
                    return thread

        # Fetch the thread document from the database using the original method as fallback
        db = get_db()
        if (
            BSON_AVAILABLE
            and isinstance(thread_id_any, str)
            and ObjectId.is_valid(thread_id_any)
        ):
            try:
                thread = db.support_messages.find_one({"_id": ObjectId(thread_id_any)})
                if thread:
                    return thread
            except Exception:
                pass

        # Fix for initial message not appearing in messages list
        if thread and "message_text" in thread and "messages" in thread:
            # Check if the initial message is already in the messages list
            has_initial_message = any(
                msg.get("sender_type") == "user"
                and msg.get("content") == thread.get("message_text")
                and not msg.get("is_reply", False)
                for msg in thread.get("messages", [])
            )

            # If not found, add the initial message to messages list
            if not has_initial_message and thread.get("message_text"):
                initial_message = {
                    "sender_type": "user",
                    "content": thread.get("message_text", ""),
                    "timestamp": thread.get("timestamp"),
                    "has_media": thread.get("has_media", False),
                }

                # Insert at the beginning of messages list or create a new list
                if "messages" not in thread or not thread["messages"]:
                    thread["messages"] = [initial_message]
                else:
                    thread["messages"].insert(0, initial_message)

                logger.info(
                    f"Added initial message to thread {str(thread.get('_id', 'unknown'))} conversation"
                )

        # If there's still no messages array or it's empty but there's a message_text
        elif (
            thread
            and "message_text" in thread
            and thread.get("message_text")
            and ("messages" not in thread or not thread.get("messages"))
        ):
            thread["messages"] = [
                {
                    "sender_type": "user",
                    "content": thread.get("message_text", ""),
                    "timestamp": thread.get("timestamp"),
                    "has_media": thread.get("has_media", False),
                }
            ]
            logger.info(
                f"Created messages array for thread {str(thread.get('_id', 'unknown'))}"
            )

        # Add debug logging for thread data
        if thread:
            message_count = len(thread.get("messages", []))
            status = thread.get("status", "unknown")
            thread_id_str = str(thread.get("_id", "unknown"))
            logger.info(
                f"Retrieved thread {thread_id_str}: {message_count} messages, status: {status}"
            )
        else:
            logger.warning(f"Thread {str(thread_id_any)} not found in database")

        return thread
    except Exception as e:
        _handle_db_error(f"get_support_thread ({str(thread_id_any)})", e)
        return None


async def get_support_thread_async(thread_id_any: Any) -> Optional[Dict[str, Any]]:
    """Gets a support thread by _id (async), with fallback to find by ID suffix."""
    # First try the normal path using normalized ID
    norm_id = _normalize_id(thread_id_any)
    if BSON_AVAILABLE and isinstance(norm_id, ObjectId):
        try:
            thread = await support_messages_collection_async.find_one({"_id": norm_id})
            if thread:
                return thread
        except Exception as e:
            _handle_db_error(
                f"get_support_thread_async direct lookup ({thread_id_any})", e
            )

    # If the ID is a string and we didn't find it directly, try to match by suffix
    if isinstance(thread_id_any, str) and len(thread_id_any) > 0:
        try:
            # Get all threads and search for one with matching ID suffix
            cursor = support_messages_collection_async.find().limit(100)
            all_threads = await cursor.to_list(length=100)
            for thread in all_threads:
                if str(thread.get("_id", "")).endswith(thread_id_any):
                    return thread
        except Exception as e:
            _handle_db_error(
                f"get_support_thread_async suffix lookup ({thread_id_any})", e
            )

    # Log error only if both attempts failed
    logger.error(f"Support thread not found with ID (async): {thread_id_any}")
    return None


def get_support_messages(
    status: Optional[str] = None, limit: int = 50
) -> List[Dict[str, Any]]:
    """Gets support messages, optionally filtered by status."""
    valid_statuses = ["pending", "replied", "resolved", None]
    if status not in valid_statuses:
        logger.error(f"Invalid status filter: {status}")
        return []
    if not isinstance(limit, int) or limit <= 0:
        logger.error(f"Invalid limit: {limit}")
        limit = 50

    try:
        query = {"status": status} if status else {}
        return list(
            support_messages_collection.find(query)
            .sort([("last_reply_time", -1), ("timestamp", -1)])
            .limit(limit)
        )
    except Exception as e:
        _handle_db_error(f"get_support_messages (status={status})", e)
        return []


async def get_support_messages_async(
    status: Optional[str] = None, limit: int = 50
) -> List[Dict[str, Any]]:
    """Gets support messages (async), optionally filtered by status."""
    valid_statuses = ["pending", "replied", "resolved", None]
    if status not in valid_statuses:
        logger.error(f"Invalid status filter (async): {status}")
        return []
    if not isinstance(limit, int) or limit <= 0:
        logger.error(f"Invalid limit (async): {limit}")
        limit = 50

    try:
        query = {"status": status} if status else {}
        cursor = (
            support_messages_collection_async.find(query)
            .sort([("last_reply_time", -1), ("timestamp", -1)])
            .limit(limit)
        )
        return await cursor.to_list(length=limit)
    except Exception as e:
        _handle_db_error(f"get_support_messages_async (status={status})", e)
        return []


@sandbox_aware_db_write
def add_reply_to_support_message(thread_id_any: Any, reply_data: Dict) -> bool:
    """Adds a reply (user or admin) to a support thread."""
    # First try to normalize the ID to an ObjectId
    norm_id = _normalize_id(thread_id_any)

    # If we have a valid ObjectId, try direct lookup
    if BSON_AVAILABLE and isinstance(norm_id, ObjectId):
        try:
            reply_data["reply_time"] = datetime.now()
            new_status = "replied" if reply_data.get("is_admin") else "pending"
            result = support_messages_collection.update_one(
                {"_id": norm_id},
                {
                    "$push": {"replies": reply_data},
                    "$set": {
                        "status": new_status,
                        "last_reply_time": reply_data["reply_time"],
                    },
                },
            )
            if result.matched_count > 0:
                return True
        except Exception as e:
            _handle_db_error(
                f"add_reply_to_support_message direct ObjectId ({thread_id_any})", e
            )

    # If the ID is a string, try to convert it to ObjectId
    if isinstance(thread_id_any, str) and BSON_AVAILABLE:
        try:
            if ObjectId.is_valid(thread_id_any):
                object_id = ObjectId(thread_id_any)
                reply_data["reply_time"] = datetime.now()
                new_status = "replied" if reply_data.get("is_admin") else "pending"
                result = support_messages_collection.update_one(
                    {"_id": object_id},
                    {
                        "$push": {"replies": reply_data},
                        "$set": {
                            "status": new_status,
                            "last_reply_time": reply_data["reply_time"],
                        },
                    },
                )
                if result.matched_count > 0:
                    return True

            # If ID isn't a valid ObjectId, try to find by suffix match
            elif len(thread_id_any) > 0:
                # Get all threads and search for one with matching ID suffix
                threads = list(support_messages_collection.find().limit(100))
                for thread in threads:
                    thread_id_str = str(thread.get("_id", ""))
                    if thread_id_str.endswith(thread_id_any):
                        reply_data["reply_time"] = datetime.now()
                        new_status = (
                            "replied" if reply_data.get("is_admin") else "pending"
                        )
                        result = support_messages_collection.update_one(
                            {"_id": thread["_id"]},
                            {
                                "$push": {"replies": reply_data},
                                "$set": {
                                    "status": new_status,
                                    "last_reply_time": reply_data["reply_time"],
                                },
                            },
                        )
                        return result.acknowledged
        except Exception as e:
            _handle_db_error(
                f"add_reply_to_support_message string ID ({thread_id_any})", e
            )

    logger.error(
        f"Failed to add reply - no matching thread found for ID: {thread_id_any}"
    )
    return False


@sandbox_aware_db_write
async def add_reply_to_support_message_async(
    thread_id_any: Any, reply_data: Dict
) -> bool:
    """Adds a reply (user or admin) to a support thread (async)."""
    norm_id = _normalize_id(thread_id_any)
    if not norm_id or not (BSON_AVAILABLE and isinstance(norm_id, ObjectId)):
        logger.error(
            f"Invalid thread ID format for resolve_support_thread: {thread_id_any}"
        )
        return False
    try:
        reply_data["reply_time"] = datetime.now()
        new_status = "replied" if reply_data.get("is_admin") else "pending"
        result = await support_messages_collection_async.update_one(
            {"_id": norm_id},
            {
                "$push": {"replies": reply_data},
                "$set": {
                    "status": new_status,
                    "last_reply_time": reply_data["reply_time"],
                },
            },
        )
        if result.matched_count == 0:
            logger.warning(f"add_reply_async found no thread with ID {norm_id}")
            return False
        return result.acknowledged
    except Exception as e:
        _handle_db_error(f"add_reply_to_support_message_async ({thread_id_any})", e)
        return False


# Consolidate user reply functions
def add_user_reply_to_support_message(
    thread_id_any: Any,
    user_id: int,
    full_name: str,
    reply_message: str,
    media_id: Optional[str] = None,
    media_type: Optional[str] = None,
) -> bool:
    """Adds a user's reply to a support message thread."""
    reply_data = {
        "user_id": user_id,  # Keep user_id consistent
        "name": full_name,
        "is_admin": False,
        "message": reply_message,
        # Add media info if provided
        **({"media_id": media_id} if media_id else {}),
        **({"media_type": media_type} if media_type else {}),
    }
    return add_reply_to_support_message(thread_id_any, reply_data)


async def add_user_reply_to_support_message_async(
    thread_id_any: Any,
    user_id: int,
    full_name: str,
    reply_message: str,
    media_id: Optional[str] = None,
    media_type: Optional[str] = None,
) -> bool:
    """Adds a user's reply to a support message thread (async)."""
    reply_data = {
        "user_id": user_id,
        "name": full_name,
        "is_admin": False,
        "message": reply_message,
        **({"media_id": media_id} if media_id else {}),
        **({"media_type": media_type} if media_type else {}),
    }
    return await add_reply_to_support_message_async(thread_id_any, reply_data)


@sandbox_aware_db_write
def resolve_support_thread(thread_id_any: Any, admin_id: int) -> bool:
    """Marks a support thread as resolved."""
    norm_id = _normalize_id(thread_id_any)
    if not norm_id or not (BSON_AVAILABLE and isinstance(norm_id, ObjectId)):
        logger.error(
            f"Invalid thread ID format for resolve_support_thread: {thread_id_any}"
        )
        return False
    try:
        result = support_messages_collection.update_one(
            {"_id": norm_id},
            {
                "$set": {
                    "status": "resolved",
                    "resolved_by": admin_id,
                    "resolved_at": datetime.now(),
                }
            },
        )
        return result.matched_count > 0
    except Exception as e:
        _handle_db_error(f"resolve_support_thread ({thread_id_any})", e)
        return False


@sandbox_aware_db_write
async def resolve_support_thread_async(thread_id_any: Any, admin_id: int) -> bool:
    """Marks a support thread as resolved (async)."""
    norm_id = _normalize_id(thread_id_any)
    if not norm_id or not (BSON_AVAILABLE and isinstance(norm_id, ObjectId)):
        logger.error(
            f"Invalid thread ID format for resolve_support_thread_async: {thread_id_any}"
        )
        return False
    try:
        result = await support_messages_collection_async.update_one(
            {"_id": norm_id},
            {
                "$set": {
                    "status": "resolved",
                    "resolved_by": admin_id,
                    "resolved_at": datetime.now(),
                }
            },
        )
        return result.matched_count > 0
    except Exception as e:
        _handle_db_error(f"resolve_support_thread_async ({thread_id_any})", e)
        return False


# Add these functions to track message counts in threads


def get_consecutive_user_messages(thread_id: str) -> int:
    """
    Get the count of consecutive user messages since the last admin response.

    Args:
        thread_id: The support thread ID

    Returns:
        Number of consecutive user messages
    """
    try:
        thread = get_support_thread(thread_id)
        if not thread:
            return 0

        # Get all replies in the thread
        replies = thread.get("replies", [])

        # Count consecutive user messages at the end of the conversation
        count = 0
        for reply in reversed(replies):
            if reply.get("is_admin", False):
                # Found an admin message, stop counting
                break
            # Not an admin message, so it's a user message
            count += 1

        return count
    except Exception as e:
        logger.error(f"Error getting consecutive user messages: {e}")
        # Default to 0 to avoid blocking legitimate messages on error
        return 0


def can_send_more_messages(thread_id: str, max_consecutive: int = 2) -> bool:
    """
    Check if user can send more messages to this thread.

    Args:
        thread_id: The support thread ID
        max_consecutive: Maximum allowed consecutive messages

    Returns:
        True if user can send more messages, False otherwise
    """
    consecutive_count = get_consecutive_user_messages(thread_id)
    return consecutive_count < max_consecutive


def get_owner_id():
    """
    Retrieve the owner's ID from the database.

    Returns:
        int: The user ID of the bot owner, or None if not found
    """
    try:
        db = get_db()
        users_collection = db.users

        # Find the user with role 'owner'
        owner = users_collection.find_one({"role": "owner"})

        if owner:
            return owner.get("user_id")
        return None
    except Exception as e:
        logger.error(f"Error retrieving owner ID: {e}")
        return None


# --- Statistics ---


def get_shop_statistics() -> Dict[str, Any]:
    """Gets shop statistics."""
    stats = {
        "total_products": 0,
        "total_users": 0,
        "total_orders": 0,
        "total_revenue": 0.0,
        "recent_sales": [],
    }
    try:
        stats["total_products"] = get_product_count()
        stats["total_users"] = get_user_count()
        stats["total_orders"] = transactions_collection.count_documents(
            {"type": "purchase"}
        )
        stats["recent_sales"] = get_recent_orders(5)
        pipeline = [
            {"$match": {"type": "purchase"}},
            {"$group": {"_id": None, "total": {"$sum": "$amount"}}},
        ]
        revenue_result = list(transactions_collection.aggregate(pipeline))
        stats["total_revenue"] = revenue_result[0]["total"] if revenue_result else 0.0
    except Exception as e:
        _handle_db_error("get_shop_statistics", e)
    return stats


async def get_shop_statistics_async() -> Dict[str, Any]:
    """Gets shop statistics (async)."""
    stats = {
        "total_products": 0,
        "total_users": 0,
        "total_orders": 0,
        "total_revenue": 0.0,
        "recent_sales": [],
    }
    try:
        stats["total_products"] = await get_product_count_async()
        stats["total_users"] = await get_user_count_async()
        stats["total_orders"] = await transactions_collection_async.count_documents(
            {"type": "purchase"}
        )
        stats["recent_sales"] = await get_recent_orders_async(5)
        pipeline = [
            {"$match": {"type": "purchase"}},
            {"$group": {"_id": None, "total": {"$sum": "$amount"}}},
        ]
        revenue_cursor = transactions_collection_async.aggregate(pipeline)
        revenue_result = await revenue_cursor.to_list(length=1)
        stats["total_revenue"] = revenue_result[0]["total"] if revenue_result else 0.0
    except Exception as e:
        _handle_db_error("get_shop_statistics_async", e)
    return stats


def get_user_active_support_threads(user_id: int) -> List[Dict]:
    """
    Get active support threads for a specific user.

    Args:
        user_id: User ID to check for active threads

    Returns:
        List of active support threads (pending or replied status)
    """
    try:
        db = get_db()
        # Find threads where status is pending or replied (not resolved)
        threads = list(
            db.support_messages.find(
                {"user_id": user_id, "status": {"$in": ["pending", "replied"]}}
            ).sort("timestamp", -1)
        )  # Most recent first

        return threads
    except Exception as e:
        _handle_db_error(f"get_user_active_support_threads ({user_id})", e)
        return []


# --- Database Initialization ---


# Added missing function definitions
def initialize_categories_collection():
    """Check if the categories collection exists and create indexes."""
    try:
        collection_names = db.list_collection_names()
        if "categories" not in collection_names:
            logger.info("Creating 'categories' collection.")
            db.create_collection("categories")

        # Ensure indexes exist (create_index is idempotent)
        categories_collection.create_index("name", unique=True)
        categories_collection.create_index("slug", unique=True, sparse=True)
        categories_collection.create_index(
            "id", unique=True, sparse=True
        )  # Index for manual int ID
        logger.info("Categories collection indexes verified/created.")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize categories collection: {e}")
        return False


async def initialize_categories_collection_async():
    """Check if the categories collection exists and create indexes (async)."""
    try:
        collection_names = await db_async.list_collection_names()
        if "categories" not in collection_names:
            logger.info("Creating 'categories' collection (async).")
            await db_async.create_collection("categories")

        await categories_collection_async.create_index("name", unique=True)
        await categories_collection_async.create_index("slug", unique=True, sparse=True)
        await categories_collection_async.create_index("id", unique=True, sparse=True)
        logger.info("Categories collection indexes verified/created (async).")
        return True
    except Exception as e:
        logger.error(f"Failed to initialize categories collection (async): {e}")
        return False


def initialize_database():
    """Initializes indexes and potentially adds default data."""
    logger.info("Initializing database (sync)...")
    try:
        initialize_indexes()
        # Call the newly defined functions
        initialize_categories_collection()

        # Ensure payment indexes are created
        ensure_payment_indexes()

        # Explicitly initialize log settings
        logger.info("Ensuring log settings exist in database...")
        settings = get_log_settings()
        logger.info(
            f"Log settings initialized: {len(settings.keys()) if settings else 0} settings"
        )

        if admins_collection.count_documents({}) == 0 and is_owner(OWNER_ID):
            logger.info(f"Adding OWNER_ID {OWNER_ID} as initial admin.")
            owner_user = get_user(OWNER_ID)
            add_admin(
                user_id=OWNER_ID,
                added_by=OWNER_ID,
                name=owner_user.get("name") if owner_user else "Owner",
                username=owner_user.get("username") if owner_user else None,
            )
            admins_collection.update_one(
                {"user_id": OWNER_ID}, {"$set": {"is_owner": True}}
            )

        logger.info("Database initialization (sync) complete.")
    except Exception as e:
        logger.critical(f"FATAL: Database initialization failed: {e}", exc_info=True)
        raise


async def initialize_database_async():
    """Initializes indexes and potentially adds default data (async)."""
    logger.info("Initializing database (async)...")
    try:
        await initialize_indexes_async()
        # Call the newly defined async functions
        await initialize_categories_collection_async()

        # Ensure payment indexes are created
        ensure_payment_indexes()  # Use sync version

        # Explicitly initialize log settings
        logger.info("Ensuring log settings exist in database (async)...")
        settings = await get_log_settings_async()
        logger.info(
            f"Log settings initialized (async): {len(settings.keys()) if settings else 0} settings"
        )

        if await admins_collection_async.count_documents({}) == 0 and is_owner(
            OWNER_ID
        ):
            logger.info(f"Adding OWNER_ID {OWNER_ID} as initial admin (async).")
            owner_user = await get_user_async(OWNER_ID)
            await add_admin_async(
                user_id=OWNER_ID,
                added_by=OWNER_ID,
                name=owner_user.get("name") if owner_user else "Owner",
                username=owner_user.get("username") if owner_user else None,
            )
            await admins_collection_async.update_one(
                {"user_id": OWNER_ID}, {"$set": {"is_owner": True}}
            )

        logger.info("Database initialization (async) complete.")
    except Exception as e:
        logger.critical(
            f"FATAL: Async database initialization failed: {e}", exc_info=True
        )
        raise


# --- Bonus Tier Operations ---


@sandbox_aware_db_write
def create_bonus_tier(
    threshold: float,
    bonus_percentage: float = None,
    created_by: int = 0,
    description: Optional[str] = None,
    is_active: bool = True,
    bonus_fixed_amount: float = None,
    bonus_type: str = "percentage"
) -> Optional[Dict[str, Any]]:
    """Creates a new bonus tier with enhanced validation."""
    try:
        from .models import BonusTier
        from utils.unified_validation import UnifiedValidation

        # Enhanced validation using unified validation system

        # Validate threshold
        threshold_validation = UnifiedValidation.validate_bonus_tier_threshold(threshold)
        if not threshold_validation["valid"]:
            logger.error(f"Invalid threshold: {threshold_validation['error']}")
            return None
        threshold = threshold_validation["sanitized_threshold"]

        # Validate bonus type
        type_validation = UnifiedValidation.validate_bonus_tier_type(bonus_type)
        if not type_validation["valid"]:
            logger.error(f"Invalid bonus type: {type_validation['error']}")
            return None
        bonus_type = type_validation["sanitized_type"]

        # Validate bonus configuration based on type
        if bonus_type == "percentage":
            if bonus_percentage is None:
                logger.error("Bonus percentage is required for percentage type")
                return None

            # Convert from user input (e.g., 15) to decimal (0.15) if needed
            if bonus_percentage > 1:  # Assume user input format
                percentage_validation = UnifiedValidation.validate_bonus_percentage(bonus_percentage)
                if not percentage_validation["valid"]:
                    logger.error(f"Invalid percentage: {percentage_validation['error']}")
                    return None
                bonus_percentage = percentage_validation["decimal_value"]
            else:  # Already in decimal format, validate directly
                if bonus_percentage <= 0 or bonus_percentage > 1:
                    logger.error("Bonus percentage must be between 0 and 1 (inclusive) for percentage type")
                    return None

        elif bonus_type == "fixed":
            if bonus_fixed_amount is None:
                logger.error("Bonus fixed amount is required for fixed type")
                return None

            amount_validation = UnifiedValidation.validate_bonus_fixed_amount(bonus_fixed_amount)
            if not amount_validation["valid"]:
                logger.error(f"Invalid fixed amount: {amount_validation['error']}")
                return None
            bonus_fixed_amount = amount_validation["sanitized_amount"]

        # Validate description
        desc_validation = UnifiedValidation.validate_bonus_tier_description(description)
        if not desc_validation["valid"]:
            logger.error(f"Invalid description: {desc_validation['error']}")
            return None
        description = desc_validation["sanitized_description"]

        # Check if tier with same threshold already exists (with tolerance for floating point)
        existing = bonus_tiers_collection.find_one({
            "threshold": {"$gte": threshold - 0.01, "$lte": threshold + 0.01}
        })
        if existing:
            logger.error(f"Bonus tier with threshold ${threshold} already exists")
            return None

        bonus_tier = BonusTier(
            threshold=threshold,
            bonus_percentage=bonus_percentage,
            bonus_fixed_amount=bonus_fixed_amount,
            bonus_type=bonus_type,
            is_active=is_active,
            created_by=created_by,
            description=description
        )

        tier_dict = bonus_tier.to_dict()
        result = bonus_tiers_collection.insert_one(tier_dict)

        if result.acknowledged:
            tier_dict["_id"] = result.inserted_id
            if bonus_type == "fixed":
                logger.info(f"Created bonus tier: ${threshold} -> ${bonus_fixed_amount} fixed bonus")
            else:
                logger.info(f"Created bonus tier: ${threshold} -> {bonus_percentage*100}% bonus")
            return tier_dict
        return None
    except Exception as e:
        _handle_db_error("create_bonus_tier", e)
        return None


def create_fixed_bonus_tier(
    threshold: float,
    fixed_amount: float,
    created_by: int = 0,
    description: Optional[str] = None,
    is_active: bool = True
) -> Optional[Dict[str, Any]]:
    """
    Create a fixed-amount bonus tier.

    Args:
        threshold: Minimum deposit amount to trigger this bonus
        fixed_amount: Fixed bonus amount (e.g., 10.0 for $10)
        created_by: Admin user ID who created this tier
        description: Optional description for this tier
        is_active: Whether this tier is currently active

    Returns:
        Dict containing the created tier data, or None if creation failed
    """
    return create_bonus_tier(
        threshold=threshold,
        bonus_fixed_amount=fixed_amount,
        bonus_type="fixed",
        created_by=created_by,
        description=description,
        is_active=is_active
    )


@sandbox_aware_db_write
async def create_bonus_tier_async(
    threshold: float,
    bonus_percentage: float = None,
    created_by: int = 0,
    description: Optional[str] = None,
    is_active: bool = True,
    bonus_fixed_amount: float = None,
    bonus_type: str = "percentage"
) -> Optional[Dict[str, Any]]:
    """Creates a new bonus tier (async) with enhanced validation."""
    try:
        from .models import BonusTier
        from utils.unified_validation import UnifiedValidation

        # Enhanced validation using unified validation system

        # Validate threshold
        threshold_validation = UnifiedValidation.validate_bonus_tier_threshold(threshold)
        if not threshold_validation["valid"]:
            logger.error(f"Invalid threshold: {threshold_validation['error']}")
            return None
        threshold = threshold_validation["sanitized_threshold"]

        # Validate bonus type
        type_validation = UnifiedValidation.validate_bonus_tier_type(bonus_type)
        if not type_validation["valid"]:
            logger.error(f"Invalid bonus type: {type_validation['error']}")
            return None
        bonus_type = type_validation["sanitized_type"]

        # Validate bonus configuration based on type
        if bonus_type == "percentage":
            if bonus_percentage is None:
                logger.error("Bonus percentage is required for percentage type")
                return None

            # Convert from user input (e.g., 15) to decimal (0.15) if needed
            if bonus_percentage > 1:  # Assume user input format
                percentage_validation = UnifiedValidation.validate_bonus_percentage(bonus_percentage)
                if not percentage_validation["valid"]:
                    logger.error(f"Invalid percentage: {percentage_validation['error']}")
                    return None
                bonus_percentage = percentage_validation["decimal_value"]
            else:  # Already in decimal format, validate directly
                if bonus_percentage <= 0 or bonus_percentage > 1:
                    logger.error("Bonus percentage must be between 0 and 1 (inclusive) for percentage type")
                    return None

        elif bonus_type == "fixed":
            if bonus_fixed_amount is None:
                logger.error("Bonus fixed amount is required for fixed type")
                return None

            amount_validation = UnifiedValidation.validate_bonus_fixed_amount(bonus_fixed_amount)
            if not amount_validation["valid"]:
                logger.error(f"Invalid fixed amount: {amount_validation['error']}")
                return None
            bonus_fixed_amount = amount_validation["sanitized_amount"]

        # Validate description
        desc_validation = UnifiedValidation.validate_bonus_tier_description(description)
        if not desc_validation["valid"]:
            logger.error(f"Invalid description: {desc_validation['error']}")
            return None
        description = desc_validation["sanitized_description"]

        # Check if tier with same threshold already exists (with tolerance for floating point)
        existing = await bonus_tiers_collection_async.find_one({
            "threshold": {"$gte": threshold - 0.01, "$lte": threshold + 0.01}
        })
        if existing:
            logger.error(f"Bonus tier with threshold ${threshold} already exists")
            return None

        bonus_tier = BonusTier(
            threshold=threshold,
            bonus_percentage=bonus_percentage,
            bonus_fixed_amount=bonus_fixed_amount,
            bonus_type=bonus_type,
            is_active=is_active,
            created_by=created_by,
            description=description
        )

        tier_dict = bonus_tier.to_dict()
        result = await bonus_tiers_collection_async.insert_one(tier_dict)

        if result.acknowledged:
            tier_dict["_id"] = result.inserted_id
            if bonus_type == "fixed":
                logger.info(f"Created bonus tier (async): ${threshold} -> ${bonus_fixed_amount} fixed bonus")
            else:
                logger.info(f"Created bonus tier (async): ${threshold} -> {bonus_percentage*100}% bonus")
            return tier_dict
        return None
    except Exception as e:
        _handle_db_error("create_bonus_tier_async", e)
        return None


def get_all_bonus_tiers(active_only: bool = False) -> List[Dict[str, Any]]:
    """Gets all bonus tiers, optionally filtered by active status."""
    try:
        query = {"is_active": True} if active_only else {}
        return list(bonus_tiers_collection.find(query).sort("threshold", 1))
    except Exception as e:
        _handle_db_error("get_all_bonus_tiers", e)
        return []


async def get_all_bonus_tiers_async(active_only: bool = False) -> List[Dict[str, Any]]:
    """Gets all bonus tiers (async), optionally filtered by active status."""
    try:
        query = {"is_active": True} if active_only else {}
        cursor = bonus_tiers_collection_async.find(query).sort("threshold", 1)
        return await cursor.to_list(length=None)
    except Exception as e:
        _handle_db_error("get_all_bonus_tiers_async", e)
        return []


def get_bonus_tier_by_id(tier_id: str) -> Optional[Dict[str, Any]]:
    """Gets a bonus tier by its ID."""
    try:
        from bson import ObjectId
        if not ObjectId.is_valid(tier_id):
            return None
        return bonus_tiers_collection.find_one({"_id": ObjectId(tier_id)})
    except Exception as e:
        _handle_db_error("get_bonus_tier_by_id", e)
        return None


async def get_bonus_tier_by_id_async(tier_id: str) -> Optional[Dict[str, Any]]:
    """Gets a bonus tier by its ID (async)."""
    try:
        from bson import ObjectId
        if not ObjectId.is_valid(tier_id):
            return None
        return await bonus_tiers_collection_async.find_one({"_id": ObjectId(tier_id)})
    except Exception as e:
        _handle_db_error("get_bonus_tier_by_id_async", e)
        return None


@sandbox_aware_db_write
def update_bonus_tier(
    tier_id: str,
    threshold: Optional[float] = None,
    bonus_percentage: Optional[float] = None,
    bonus_fixed_amount: Optional[float] = None,
    bonus_type: Optional[str] = None,
    is_active: Optional[bool] = None,
    description: Optional[str] = None
) -> bool:
    """Updates a bonus tier with enhanced validation."""
    try:
        from bson import ObjectId
        from utils.unified_validation import UnifiedValidation

        if not ObjectId.is_valid(tier_id):
            logger.error(f"Invalid tier ID: {tier_id}")
            return False

        update_data = {"updated_at": datetime.now()}

        # Validate threshold if provided
        if threshold is not None:
            threshold_validation = UnifiedValidation.validate_bonus_tier_threshold(threshold)
            if not threshold_validation["valid"]:
                logger.error(f"Invalid threshold: {threshold_validation['error']}")
                return False
            threshold = threshold_validation["sanitized_threshold"]

            # Check if another tier with same threshold exists (with tolerance)
            existing = bonus_tiers_collection.find_one({
                "threshold": {"$gte": threshold - 0.01, "$lte": threshold + 0.01},
                "_id": {"$ne": ObjectId(tier_id)}
            })
            if existing:
                logger.error(f"Another bonus tier with threshold ${threshold} already exists")
                return False
            update_data["threshold"] = threshold

        # Validate bonus type if provided
        if bonus_type is not None:
            type_validation = UnifiedValidation.validate_bonus_tier_type(bonus_type)
            if not type_validation["valid"]:
                logger.error(f"Invalid bonus type: {type_validation['error']}")
                return False
            bonus_type = type_validation["sanitized_type"]
            update_data["bonus_type"] = bonus_type

            # Clear opposite bonus field when changing type
            if bonus_type == "percentage":
                update_data["bonus_fixed_amount"] = None
            elif bonus_type == "fixed":
                update_data["bonus_percentage"] = None

        # Validate bonus percentage if provided
        if bonus_percentage is not None:
            # Handle both user input format (15) and decimal format (0.15)
            if bonus_percentage > 1:  # User input format
                percentage_validation = UnifiedValidation.validate_bonus_percentage(bonus_percentage)
                if not percentage_validation["valid"]:
                    logger.error(f"Invalid percentage: {percentage_validation['error']}")
                    return False
                bonus_percentage = percentage_validation["decimal_value"]
            else:  # Decimal format
                if bonus_percentage <= 0 or bonus_percentage > 1:
                    logger.error("Bonus percentage must be between 0 and 1 (inclusive)")
                    return False

            update_data["bonus_percentage"] = bonus_percentage
            # Ensure type is set to percentage if updating percentage
            if bonus_type is None:
                update_data["bonus_type"] = "percentage"
                update_data["bonus_fixed_amount"] = None

        # Validate bonus fixed amount if provided
        if bonus_fixed_amount is not None:
            amount_validation = UnifiedValidation.validate_bonus_fixed_amount(bonus_fixed_amount)
            if not amount_validation["valid"]:
                logger.error(f"Invalid fixed amount: {amount_validation['error']}")
                return False
            bonus_fixed_amount = amount_validation["sanitized_amount"]
            update_data["bonus_fixed_amount"] = bonus_fixed_amount
            # Ensure type is set to fixed if updating fixed amount
            if bonus_type is None:
                update_data["bonus_type"] = "fixed"
                update_data["bonus_percentage"] = None

        # Validate is_active if provided
        if is_active is not None:
            if not isinstance(is_active, bool):
                logger.error("is_active must be a boolean")
                return False
            update_data["is_active"] = is_active

        # Validate description if provided
        if description is not None:
            desc_validation = UnifiedValidation.validate_bonus_tier_description(description)
            if not desc_validation["valid"]:
                logger.error(f"Invalid description: {desc_validation['error']}")
                return False
            update_data["description"] = desc_validation["sanitized_description"]

        result = bonus_tiers_collection.update_one(
            {"_id": ObjectId(tier_id)},
            {"$set": update_data}
        )

        success = result.modified_count > 0
        if success:
            logger.info(f"Updated bonus tier {tier_id}")
        return success
    except Exception as e:
        _handle_db_error("update_bonus_tier", e)
        return False


@sandbox_aware_db_write
async def update_bonus_tier_async(
    tier_id: str,
    threshold: Optional[float] = None,
    bonus_percentage: Optional[float] = None,
    bonus_fixed_amount: Optional[float] = None,
    bonus_type: Optional[str] = None,
    is_active: Optional[bool] = None,
    description: Optional[str] = None
) -> bool:
    """Updates a bonus tier (async) with enhanced validation."""
    try:
        from bson import ObjectId
        from utils.unified_validation import UnifiedValidation

        if not ObjectId.is_valid(tier_id):
            logger.error(f"Invalid tier ID: {tier_id}")
            return False

        update_data = {"updated_at": datetime.now()}

        # Validate threshold if provided
        if threshold is not None:
            threshold_validation = UnifiedValidation.validate_bonus_tier_threshold(threshold)
            if not threshold_validation["valid"]:
                logger.error(f"Invalid threshold: {threshold_validation['error']}")
                return False
            threshold = threshold_validation["sanitized_threshold"]

            # Check if another tier with same threshold exists (with tolerance)
            existing = await bonus_tiers_collection_async.find_one({
                "threshold": {"$gte": threshold - 0.01, "$lte": threshold + 0.01},
                "_id": {"$ne": ObjectId(tier_id)}
            })
            if existing:
                logger.error(f"Another bonus tier with threshold ${threshold} already exists")
                return False
            update_data["threshold"] = threshold

        # Validate bonus type if provided
        if bonus_type is not None:
            type_validation = UnifiedValidation.validate_bonus_tier_type(bonus_type)
            if not type_validation["valid"]:
                logger.error(f"Invalid bonus type: {type_validation['error']}")
                return False
            bonus_type = type_validation["sanitized_type"]
            update_data["bonus_type"] = bonus_type

            # Clear opposite bonus field when changing type
            if bonus_type == "percentage":
                update_data["bonus_fixed_amount"] = None
            elif bonus_type == "fixed":
                update_data["bonus_percentage"] = None

        # Validate bonus percentage if provided
        if bonus_percentage is not None:
            # Handle both user input format (15) and decimal format (0.15)
            if bonus_percentage > 1:  # User input format
                percentage_validation = UnifiedValidation.validate_bonus_percentage(bonus_percentage)
                if not percentage_validation["valid"]:
                    logger.error(f"Invalid percentage: {percentage_validation['error']}")
                    return False
                bonus_percentage = percentage_validation["decimal_value"]
            else:  # Decimal format
                if bonus_percentage <= 0 or bonus_percentage > 1:
                    logger.error("Bonus percentage must be between 0 and 1 (inclusive)")
                    return False

            update_data["bonus_percentage"] = bonus_percentage
            # Ensure type is set to percentage if updating percentage
            if bonus_type is None:
                update_data["bonus_type"] = "percentage"
                update_data["bonus_fixed_amount"] = None

        # Validate bonus fixed amount if provided
        if bonus_fixed_amount is not None:
            amount_validation = UnifiedValidation.validate_bonus_fixed_amount(bonus_fixed_amount)
            if not amount_validation["valid"]:
                logger.error(f"Invalid fixed amount: {amount_validation['error']}")
                return False
            bonus_fixed_amount = amount_validation["sanitized_amount"]
            update_data["bonus_fixed_amount"] = bonus_fixed_amount
            # Ensure type is set to fixed if updating fixed amount
            if bonus_type is None:
                update_data["bonus_type"] = "fixed"
                update_data["bonus_percentage"] = None

        # Validate is_active if provided
        if is_active is not None:
            if not isinstance(is_active, bool):
                logger.error("is_active must be a boolean")
                return False
            update_data["is_active"] = is_active

        # Validate description if provided
        if description is not None:
            desc_validation = UnifiedValidation.validate_bonus_tier_description(description)
            if not desc_validation["valid"]:
                logger.error(f"Invalid description: {desc_validation['error']}")
                return False
            update_data["description"] = desc_validation["sanitized_description"]

        result = await bonus_tiers_collection_async.update_one(
            {"_id": ObjectId(tier_id)},
            {"$set": update_data}
        )

        success = result.modified_count > 0
        if success:
            logger.info(f"Updated bonus tier {tier_id} (async)")
        return success
    except Exception as e:
        _handle_db_error("update_bonus_tier_async", e)
        return False


@sandbox_aware_db_write
def delete_bonus_tier(tier_id: str) -> bool:
    """Deletes a bonus tier."""
    try:
        from bson import ObjectId
        if not ObjectId.is_valid(tier_id):
            return False

        result = bonus_tiers_collection.delete_one({"_id": ObjectId(tier_id)})
        success = result.deleted_count > 0
        if success:
            logger.info(f"Deleted bonus tier {tier_id}")
        return success
    except Exception as e:
        _handle_db_error("delete_bonus_tier", e)
        return False


@sandbox_aware_db_write
async def delete_bonus_tier_async(tier_id: str) -> bool:
    """Deletes a bonus tier (async)."""
    try:
        from bson import ObjectId
        if not ObjectId.is_valid(tier_id):
            return False

        result = await bonus_tiers_collection_async.delete_one({"_id": ObjectId(tier_id)})
        success = result.deleted_count > 0
        if success:
            logger.info(f"Deleted bonus tier {tier_id} (async)")
        return success
    except Exception as e:
        _handle_db_error("delete_bonus_tier_async", e)
        return False


def _validate_tier_data_consistency(tier_data: Dict[str, Any]) -> bool:
    """
    Validate that tier data is internally consistent.

    Args:
        tier_data: Dictionary containing tier data to validate

    Returns:
        bool: True if data is consistent, False otherwise
    """
    try:
        bonus_type = tier_data.get("bonus_type", "percentage")
        bonus_percentage = tier_data.get("bonus_percentage")
        bonus_fixed_amount = tier_data.get("bonus_fixed_amount")

        if bonus_type == "percentage":
            if bonus_percentage is None:
                logger.error("Percentage type tier missing bonus_percentage")
                return False
            if bonus_fixed_amount is not None:
                logger.warning("Percentage type tier has bonus_fixed_amount, will be cleared")
        elif bonus_type == "fixed":
            if bonus_fixed_amount is None:
                logger.error("Fixed type tier missing bonus_fixed_amount")
                return False
            if bonus_percentage is not None:
                logger.warning("Fixed type tier has bonus_percentage, will be cleared")
        else:
            logger.error(f"Invalid bonus_type: {bonus_type}")
            return False

        return True
    except Exception as e:
        logger.error(f"Error validating tier data consistency: {e}")
        return False


def get_applicable_bonus_tier(deposit_amount: float) -> Optional[Dict[str, Any]]:
    """Gets the highest applicable bonus tier for a deposit amount."""
    try:
        from utils.unified_validation import UnifiedValidation

        # Validate deposit amount
        threshold_validation = UnifiedValidation.validate_bonus_tier_threshold(deposit_amount)
        if not threshold_validation["valid"]:
            logger.error(f"Invalid deposit amount for tier lookup: {threshold_validation['error']}")
            return None

        deposit_amount = threshold_validation["sanitized_threshold"]

        # Find all active tiers that the deposit amount qualifies for
        applicable_tiers = list(bonus_tiers_collection.find({
            "is_active": True,
            "threshold": {"$lte": deposit_amount}
        }).sort("threshold", -1))  # Sort by threshold descending to get highest first

        if applicable_tiers:
            tier = applicable_tiers[0]  # Return the highest threshold tier

            # Validate tier data consistency before returning
            if not _validate_tier_data_consistency(tier):
                logger.error(f"Inconsistent tier data found for tier {tier.get('_id')}")
                return None

            return tier
        return None
    except Exception as e:
        _handle_db_error("get_applicable_bonus_tier", e)
        return None


def create_bonus_tier_atomic(tier_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
    """
    Create a bonus tier with atomic operations and full validation.

    Args:
        tier_data: Dictionary containing all tier data

    Returns:
        Dict containing created tier data or None if failed
    """
    try:
        from .models import BonusTier
        from utils.unified_validation import UnifiedValidation

        # Extract and validate all required fields
        threshold = tier_data.get("threshold")
        bonus_type = tier_data.get("bonus_type", "percentage")
        bonus_percentage = tier_data.get("bonus_percentage")
        bonus_fixed_amount = tier_data.get("bonus_fixed_amount")
        description = tier_data.get("description")
        is_active = tier_data.get("is_active", True)
        created_by = tier_data.get("created_by", 0)

        # Comprehensive validation
        threshold_validation = UnifiedValidation.validate_bonus_tier_threshold(threshold)
        if not threshold_validation["valid"]:
            logger.error(f"Invalid threshold: {threshold_validation['error']}")
            return None
        threshold = threshold_validation["sanitized_threshold"]

        type_validation = UnifiedValidation.validate_bonus_tier_type(bonus_type)
        if not type_validation["valid"]:
            logger.error(f"Invalid bonus type: {type_validation['error']}")
            return None
        bonus_type = type_validation["sanitized_type"]

        # Validate bonus values based on type
        if bonus_type == "percentage":
            if bonus_percentage is None:
                logger.error("Percentage type requires bonus_percentage")
                return None

            if bonus_percentage > 1:  # User input format
                percentage_validation = UnifiedValidation.validate_bonus_percentage(bonus_percentage)
                if not percentage_validation["valid"]:
                    logger.error(f"Invalid percentage: {percentage_validation['error']}")
                    return None
                bonus_percentage = percentage_validation["decimal_value"]

            # Clear fixed amount for consistency
            bonus_fixed_amount = None

        elif bonus_type == "fixed":
            if bonus_fixed_amount is None:
                logger.error("Fixed type requires bonus_fixed_amount")
                return None

            amount_validation = UnifiedValidation.validate_bonus_fixed_amount(bonus_fixed_amount)
            if not amount_validation["valid"]:
                logger.error(f"Invalid fixed amount: {amount_validation['error']}")
                return None
            bonus_fixed_amount = amount_validation["sanitized_amount"]

            # Clear percentage for consistency
            bonus_percentage = None

        # Validate description
        if description is not None:
            desc_validation = UnifiedValidation.validate_bonus_tier_description(description)
            if not desc_validation["valid"]:
                logger.error(f"Invalid description: {desc_validation['error']}")
                return None
            description = desc_validation["sanitized_description"]

        # Check for existing tier with same threshold (atomic check)
        existing = bonus_tiers_collection.find_one({
            "threshold": {"$gte": threshold - 0.01, "$lte": threshold + 0.01}
        })
        if existing:
            logger.error(f"Bonus tier with threshold ${threshold} already exists")
            return None

        # Create tier object
        bonus_tier = BonusTier(
            threshold=threshold,
            bonus_percentage=bonus_percentage,
            bonus_fixed_amount=bonus_fixed_amount,
            bonus_type=bonus_type,
            is_active=is_active,
            created_by=created_by,
            description=description
        )

        # Validate final tier data consistency
        tier_dict = bonus_tier.to_dict()
        if not _validate_tier_data_consistency(tier_dict):
            logger.error("Final tier data validation failed")
            return None

        # Atomic insert
        result = bonus_tiers_collection.insert_one(tier_dict)

        if result.acknowledged:
            tier_dict["_id"] = result.inserted_id

            # Log creation with details
            if bonus_type == "fixed":
                logger.info(f"Created bonus tier (atomic): ${threshold} -> ${bonus_fixed_amount} fixed bonus")
            else:
                logger.info(f"Created bonus tier (atomic): ${threshold} -> {bonus_percentage*100:.1f}% bonus")

            return tier_dict

        logger.error("Database insert was not acknowledged")
        return None

    except Exception as e:
        _handle_db_error("create_bonus_tier_atomic", e)
        return None


def validate_tier_operation_safety(operation: str, tier_id: str = None, **kwargs) -> Dict[str, Any]:
    """
    Validate that a tier operation is safe to perform.

    Args:
        operation: Type of operation ('create', 'update', 'delete')
        tier_id: ID of tier for update/delete operations
        **kwargs: Additional parameters for validation

    Returns:
        Dict with validation result and safety information
    """
    try:
        from bson import ObjectId

        result = {
            "safe": False,
            "warnings": [],
            "errors": [],
            "affected_tiers": 0
        }

        if operation == "create":
            threshold = kwargs.get("threshold")
            if threshold is not None:
                # Check for potential conflicts
                nearby_tiers = list(bonus_tiers_collection.find({
                    "threshold": {
                        "$gte": threshold - 5.0,  # Within $5
                        "$lte": threshold + 5.0
                    }
                }))

                if nearby_tiers:
                    result["warnings"].append(f"Found {len(nearby_tiers)} tiers within $5 of new threshold")
                    result["affected_tiers"] = len(nearby_tiers)

                # Check for exact duplicates
                exact_match = bonus_tiers_collection.find_one({
                    "threshold": {"$gte": threshold - 0.01, "$lte": threshold + 0.01}
                })
                if exact_match:
                    result["errors"].append(f"Tier with threshold ${threshold} already exists")
                    return result

        elif operation == "update":
            if not tier_id or not ObjectId.is_valid(tier_id):
                result["errors"].append("Invalid tier ID for update operation")
                return result

            # Check if tier exists
            existing_tier = bonus_tiers_collection.find_one({"_id": ObjectId(tier_id)})
            if not existing_tier:
                result["errors"].append("Tier not found for update")
                return result

            # Check threshold conflicts if updating threshold
            new_threshold = kwargs.get("threshold")
            if new_threshold is not None:
                conflicting_tier = bonus_tiers_collection.find_one({
                    "threshold": {"$gte": new_threshold - 0.01, "$lte": new_threshold + 0.01},
                    "_id": {"$ne": ObjectId(tier_id)}
                })
                if conflicting_tier:
                    result["errors"].append(f"Another tier with threshold ${new_threshold} already exists")
                    return result

        elif operation == "delete":
            if not tier_id or not ObjectId.is_valid(tier_id):
                result["errors"].append("Invalid tier ID for delete operation")
                return result

            # Check if tier exists
            existing_tier = bonus_tiers_collection.find_one({"_id": ObjectId(tier_id)})
            if not existing_tier:
                result["errors"].append("Tier not found for deletion")
                return result

            # Check if tier is currently active and warn
            if existing_tier.get("is_active", True):
                result["warnings"].append("Deleting an active tier may affect ongoing bonuses")

        # If no errors, operation is safe
        if not result["errors"]:
            result["safe"] = True

        return result

    except Exception as e:
        logger.error(f"Error validating tier operation safety: {e}")
        return {
            "safe": False,
            "errors": [f"Validation error: {str(e)}"],
            "warnings": [],
            "affected_tiers": 0
        }


def log_tier_operation(operation: str, user_id: int, tier_data: Dict[str, Any] = None,
                      tier_id: str = None, success: bool = True, error: str = None) -> None:
    """
    Log bonus tier operations for audit trail.

    Args:
        operation: Type of operation performed
        user_id: ID of user performing operation
        tier_data: Tier data for create operations
        tier_id: Tier ID for update/delete operations
        success: Whether operation succeeded
        error: Error message if operation failed
    """
    try:
        from datetime import datetime

        log_entry = {
            "timestamp": datetime.now(),
            "operation": operation,
            "user_id": user_id,
            "success": success,
            "ip_address": None,  # Could be enhanced to capture IP
            "user_agent": None   # Could be enhanced to capture user agent
        }

        if tier_id:
            log_entry["tier_id"] = tier_id

        if tier_data:
            # Log sanitized tier data (remove sensitive info if any)
            sanitized_data = {
                "threshold": tier_data.get("threshold"),
                "bonus_type": tier_data.get("bonus_type"),
                "is_active": tier_data.get("is_active"),
                "description_length": len(tier_data.get("description", "")) if tier_data.get("description") else 0
            }

            if tier_data.get("bonus_type") == "percentage":
                sanitized_data["bonus_percentage"] = tier_data.get("bonus_percentage")
            elif tier_data.get("bonus_type") == "fixed":
                sanitized_data["bonus_fixed_amount"] = tier_data.get("bonus_fixed_amount")

            log_entry["tier_data"] = sanitized_data

        if error:
            log_entry["error"] = error

        # Log to admin logs collection for audit trail
        try:
            admin_logs_collection.insert_one(log_entry)
        except Exception as log_error:
            logger.error(f"Failed to write audit log: {log_error}")

        # Also log to application logger
        if success:
            logger.info(f"Tier {operation} by user {user_id}: {tier_id or 'new tier'}")
        else:
            logger.warning(f"Failed tier {operation} by user {user_id}: {error}")

    except Exception as e:
        logger.error(f"Error in tier operation logging: {e}")


def check_tier_operation_rate_limit(user_id: int, operation: str, window_minutes: int = 5,
                                   max_operations: int = 10) -> Dict[str, Any]:
    """
    Check if user has exceeded rate limits for tier operations.

    Args:
        user_id: ID of user to check
        operation: Type of operation
        window_minutes: Time window in minutes
        max_operations: Maximum operations allowed in window

    Returns:
        Dict with rate limit status
    """
    try:
        from datetime import datetime, timedelta

        # Calculate time window
        window_start = datetime.now() - timedelta(minutes=window_minutes)

        # Count recent operations by this user
        recent_ops = admin_logs_collection.count_documents({
            "user_id": user_id,
            "operation": {"$regex": f"^{operation}"},
            "timestamp": {"$gte": window_start},
            "success": True
        })

        rate_limited = recent_ops >= max_operations

        return {
            "rate_limited": rate_limited,
            "current_count": recent_ops,
            "max_allowed": max_operations,
            "window_minutes": window_minutes,
            "reset_time": window_start + timedelta(minutes=window_minutes) if rate_limited else None
        }

    except Exception as e:
        logger.error(f"Error checking rate limit: {e}")
        # Fail open - don't block operations if rate limiting fails
        return {
            "rate_limited": False,
            "current_count": 0,
            "max_allowed": max_operations,
            "window_minutes": window_minutes,
            "error": str(e)
        }


async def get_applicable_bonus_tier_async(deposit_amount: float) -> Optional[Dict[str, Any]]:
    """Gets the highest applicable bonus tier for a deposit amount (async)."""
    try:
        # Find all active tiers that the deposit amount qualifies for
        cursor = bonus_tiers_collection_async.find({
            "is_active": True,
            "threshold": {"$lte": deposit_amount}
        }).sort("threshold", -1)  # Sort by threshold descending to get highest first

        applicable_tiers = await cursor.to_list(length=None)

        if applicable_tiers:
            return applicable_tiers[0]  # Return the highest threshold tier
        return None
    except Exception as e:
        _handle_db_error("get_applicable_bonus_tier_async", e)
        return None


def calculate_bonus_amount(deposit_amount: float) -> Dict[str, Any]:
    """
    Calculates bonus amount for a deposit.

    Returns:
        Dict with keys: bonus_amount, total_amount, tier_used, original_amount
    """
    try:
        logger.info(f"Calculating bonus for deposit amount: ${deposit_amount:.2f}")

        # Validate input
        if deposit_amount <= 0:
            logger.warning(f"Invalid deposit amount for bonus calculation: ${deposit_amount:.2f}")
            return {
                "bonus_amount": 0.0,
                "total_amount": deposit_amount,
                "tier_used": None,
                "original_amount": deposit_amount
            }

        tier = get_applicable_bonus_tier(deposit_amount)

        if not tier:
            logger.info(f"No applicable bonus tier found for ${deposit_amount:.2f}")
            return {
                "bonus_amount": 0.0,
                "total_amount": deposit_amount,
                "tier_used": None,
                "original_amount": deposit_amount
            }

        # Log tier found
        tier_threshold = tier.get("threshold", 0)
        bonus_type = tier.get("bonus_type", "percentage")
        logger.info(f"Found applicable tier: ${tier_threshold:.2f} threshold, type: {bonus_type}")

        # Calculate bonus based on tier type with proper decimal precision
        from decimal import Decimal, ROUND_HALF_UP

        deposit_decimal = Decimal(str(deposit_amount))
        bonus_amount_decimal = Decimal('0')

        if tier.get("bonus_type") == "fixed" and tier.get("bonus_fixed_amount") is not None:
            bonus_amount_decimal = Decimal(str(tier["bonus_fixed_amount"]))
            logger.info(f"Fixed bonus calculation: ${bonus_amount_decimal:.2f}")
        elif tier.get("bonus_type") == "percentage" and tier.get("bonus_percentage") is not None:
            bonus_percentage = tier["bonus_percentage"]
            bonus_percentage_decimal = Decimal(str(bonus_percentage))
            bonus_amount_decimal = deposit_decimal * bonus_percentage_decimal
            logger.info(f"Percentage bonus calculation: {bonus_percentage*100:.1f}% of ${deposit_amount:.2f} = ${bonus_amount_decimal:.6f}")
        else:
            # Fallback to percentage for backward compatibility
            bonus_percentage = tier.get("bonus_percentage", 0)
            bonus_percentage_decimal = Decimal(str(bonus_percentage))
            bonus_amount_decimal = deposit_decimal * bonus_percentage_decimal
            logger.info(f"Fallback percentage calculation: {bonus_percentage*100:.1f}% of ${deposit_amount:.2f} = ${bonus_amount_decimal:.6f}")

        # Round bonus amount to 2 decimal places for monetary display
        bonus_amount_rounded = bonus_amount_decimal.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

        # Validate bonus amount
        if bonus_amount_rounded < 0:
            logger.error(f"Negative bonus amount calculated: ${bonus_amount_rounded:.2f}")
            bonus_amount_rounded = Decimal('0')

        # Calculate total with proper precision
        total_amount_decimal = deposit_decimal + bonus_amount_rounded

        # Convert back to float for return (maintaining precision)
        bonus_amount = float(bonus_amount_rounded)
        total_amount = float(total_amount_decimal)
        logger.info(f"Bonus calculation complete: ${deposit_amount:.2f} + ${bonus_amount:.2f} = ${total_amount:.2f}")

        return {
            "bonus_amount": bonus_amount,
            "total_amount": total_amount,
            "tier_used": tier,
            "original_amount": deposit_amount
        }
    except Exception as e:
        logger.error(f"Error calculating bonus amount for ${deposit_amount:.2f}: {e}")
        _handle_db_error("calculate_bonus_amount", e)
        return {
            "bonus_amount": 0.0,
            "total_amount": deposit_amount,
            "tier_used": None,
            "original_amount": deposit_amount,
            "error": str(e)
        }


async def calculate_bonus_amount_async(deposit_amount: float) -> Dict[str, Any]:
    """
    Calculates bonus amount for a deposit (async).

    Returns:
        Dict with keys: bonus_amount, total_amount, tier_used, original_amount
    """
    try:
        tier = await get_applicable_bonus_tier_async(deposit_amount)

        if not tier:
            return {
                "bonus_amount": 0.0,
                "total_amount": deposit_amount,
                "tier_used": None,
                "original_amount": deposit_amount
            }

        # Calculate bonus based on tier type with proper decimal precision
        from decimal import Decimal, ROUND_HALF_UP

        deposit_decimal = Decimal(str(deposit_amount))
        bonus_amount_decimal = Decimal('0')

        if tier.get("bonus_type") == "fixed" and tier.get("bonus_fixed_amount") is not None:
            bonus_amount_decimal = Decimal(str(tier["bonus_fixed_amount"]))
        elif tier.get("bonus_type") == "percentage" and tier.get("bonus_percentage") is not None:
            bonus_percentage_decimal = Decimal(str(tier["bonus_percentage"]))
            bonus_amount_decimal = deposit_decimal * bonus_percentage_decimal
        else:
            # Fallback to percentage for backward compatibility
            bonus_percentage_decimal = Decimal(str(tier.get("bonus_percentage", 0)))
            bonus_amount_decimal = deposit_decimal * bonus_percentage_decimal

        # Round bonus amount to 2 decimal places for monetary display
        bonus_amount_rounded = bonus_amount_decimal.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

        # Calculate total with proper precision
        total_amount_decimal = deposit_decimal + bonus_amount_rounded

        # Convert back to float for return (maintaining precision)
        bonus_amount = float(bonus_amount_rounded)
        total_amount = float(total_amount_decimal)

        return {
            "bonus_amount": bonus_amount,
            "total_amount": total_amount,
            "tier_used": tier,
            "original_amount": deposit_amount
        }
    except Exception as e:
        _handle_db_error("calculate_bonus_amount_async", e)
        return {
            "bonus_amount": 0.0,
            "total_amount": deposit_amount,
            "tier_used": None,
            "original_amount": deposit_amount
        }


# --- User Deposit History Operations ---


def _safe_get_deposit_date(deposit):
    """
    Safely extract date from deposit record, handling both string and datetime formats.

    Args:
        deposit: Deposit record dictionary

    Returns:
        datetime: Parsed datetime object or datetime.min as fallback
    """
    try:
        # Try created_at first
        date_value = deposit.get("created_at")
        if date_value:
            if isinstance(date_value, str):
                # Try to parse string date
                try:
                    return datetime.fromisoformat(date_value.replace('Z', '+00:00'))
                except (ValueError, AttributeError):
                    try:
                        return datetime.strptime(date_value, "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        try:
                            return datetime.strptime(date_value, "%Y-%m-%dT%H:%M:%S")
                        except ValueError:
                            pass
            elif isinstance(date_value, datetime):
                return date_value

        # Try timestamp as fallback
        timestamp_value = deposit.get("timestamp")
        if timestamp_value:
            if isinstance(timestamp_value, str):
                try:
                    return datetime.fromisoformat(timestamp_value.replace('Z', '+00:00'))
                except (ValueError, AttributeError):
                    try:
                        return datetime.strptime(timestamp_value, "%Y-%m-%d %H:%M:%S")
                    except ValueError:
                        try:
                            return datetime.strptime(timestamp_value, "%Y-%m-%dT%H:%M:%S")
                        except ValueError:
                            pass
            elif isinstance(timestamp_value, datetime):
                return timestamp_value

        # Return minimum datetime as fallback
        return datetime.min

    except Exception as e:
        logger.warning(f"Error parsing date from deposit record: {e}")
        return datetime.min


def get_user_deposit_history(
    user_id: int,
    page: int = 1,
    limit: int = 10,
    include_failed: bool = False
) -> Dict[str, Any]:
    """
    Get comprehensive deposit history for a user with pagination.
    Includes both payment gateway deposits and admin credit additions.

    Args:
        user_id: User ID to get deposits for
        page: Page number (1-based)
        limit: Number of deposits per page
        include_failed: Whether to include failed/expired deposits

    Returns:
        Dict containing deposits, pagination info, and summary statistics
    """
    try:
        # Get payment gateway deposits
        payment_query = {"user_id": user_id}
        if not include_failed:
            payment_query["status"] = {"$in": ["completed", "pending", "new", "waiting", "paying"]}

        payment_deposits = list(payments_collection.find(payment_query))

        # Get admin credit transactions (admin_deposit type)
        admin_deposit_query = {
            "user_id": user_id,
            "type": "admin_deposit"
        }
        admin_deposits = list(transactions_collection.find(admin_deposit_query))

        # Combine and sort all deposits by date
        all_deposits = []

        # Add payment deposits with source type
        for deposit in payment_deposits:
            deposit["source_type"] = "payment_gateway"
            all_deposits.append(deposit)

        # Add admin deposits with source type
        for deposit in admin_deposits:
            deposit["source_type"] = "admin_credit"
            all_deposits.append(deposit)

        # Sort by date (most recent first) with safe date handling
        all_deposits.sort(key=_safe_get_deposit_date, reverse=True)

        # Apply pagination
        total_count = len(all_deposits)
        skip = (page - 1) * limit
        paginated_deposits = all_deposits[skip:skip + limit]

        # Enrich deposits with bonus and fee information
        enriched_deposits = []
        for deposit in paginated_deposits:
            enriched_deposit = _enrich_deposit_data(deposit)
            enriched_deposits.append(enriched_deposit)

        # Calculate summary statistics
        summary_stats = _calculate_deposit_summary(user_id)

        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit if total_count > 0 else 1
        has_next = page < total_pages
        has_prev = page > 1

        return {
            "deposits": enriched_deposits,
            "pagination": {
                "current_page": page,
                "total_pages": total_pages,
                "total_count": total_count,
                "has_next": has_next,
                "has_prev": has_prev,
                "limit": limit
            },
            "summary": summary_stats
        }

    except Exception as e:
        _handle_db_error("get_user_deposit_history", e, user_id)
        return {
            "deposits": [],
            "pagination": {
                "current_page": 1,
                "total_pages": 0,
                "total_count": 0,
                "has_next": False,
                "has_prev": False,
                "limit": limit
            },
            "summary": {
                "total_deposits": 0,
                "total_amount": 0.0,
                "total_credited": 0.0,
                "total_bonuses": 0.0,
                "total_fees": 0.0,
                "average_deposit": 0.0,
                "successful_deposits": 0,
                "failed_deposits": 0
            }
        }


async def get_user_deposit_history_async(
    user_id: int,
    page: int = 1,
    limit: int = 10,
    include_failed: bool = False
) -> Dict[str, Any]:
    """
    Get comprehensive deposit history for a user with pagination (async).
    Includes both payment gateway deposits and admin credit additions.

    Args:
        user_id: User ID to get deposits for
        page: Page number (1-based)
        limit: Number of deposits per page
        include_failed: Whether to include failed/expired deposits

    Returns:
        Dict containing deposits, pagination info, and summary statistics
    """
    try:
        # Get payment gateway deposits
        payment_query = {"user_id": user_id}
        if not include_failed:
            payment_query["status"] = {"$in": ["completed", "pending", "new", "waiting", "paying"]}

        payment_cursor = payments_collection_async.find(payment_query)
        payment_deposits = await payment_cursor.to_list(length=None)

        # Get admin credit transactions (admin_deposit type)
        admin_deposit_query = {
            "user_id": user_id,
            "type": "admin_deposit"
        }
        admin_cursor = transactions_collection_async.find(admin_deposit_query)
        admin_deposits = await admin_cursor.to_list(length=None)

        # Combine and sort all deposits by date
        all_deposits = []

        # Add payment deposits with source type
        for deposit in payment_deposits:
            deposit["source_type"] = "payment_gateway"
            all_deposits.append(deposit)

        # Add admin deposits with source type
        for deposit in admin_deposits:
            deposit["source_type"] = "admin_credit"
            all_deposits.append(deposit)

        # Sort by date (most recent first) with safe date handling
        all_deposits.sort(key=_safe_get_deposit_date, reverse=True)

        # Apply pagination
        total_count = len(all_deposits)
        skip = (page - 1) * limit
        paginated_deposits = all_deposits[skip:skip + limit]

        # Enrich deposits with bonus and fee information
        enriched_deposits = []
        for deposit in paginated_deposits:
            enriched_deposit = await _enrich_deposit_data_async(deposit)
            enriched_deposits.append(enriched_deposit)

        # Calculate summary statistics
        summary_stats = await _calculate_deposit_summary_async(user_id)

        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit if total_count > 0 else 1
        has_next = page < total_pages
        has_prev = page > 1

        return {
            "deposits": enriched_deposits,
            "pagination": {
                "current_page": page,
                "total_pages": total_pages,
                "total_count": total_count,
                "has_next": has_next,
                "has_prev": has_prev,
                "limit": limit
            },
            "summary": summary_stats
        }

    except Exception as e:
        _handle_db_error("get_user_deposit_history_async", e, user_id)
        return {
            "deposits": [],
            "pagination": {
                "current_page": 1,
                "total_pages": 0,
                "total_count": 0,
                "has_next": False,
                "has_prev": False,
                "limit": limit
            },
            "summary": {
                "total_deposits": 0,
                "total_amount": 0.0,
                "total_credited": 0.0,
                "total_bonuses": 0.0,
                "total_fees": 0.0,
                "average_deposit": 0.0,
                "successful_deposits": 0,
                "failed_deposits": 0
            }
        }


def _enrich_deposit_data(deposit: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enrich deposit data with bonus and fee calculations.
    Handles both payment gateway deposits and admin credit transactions.

    Args:
        deposit: Raw deposit data from payments or transactions collection

    Returns:
        Enriched deposit data with calculated fields
    """
    try:
        enriched = deposit.copy()
        source_type = deposit.get("source_type", "payment_gateway")

        if source_type == "admin_credit":
            # Handle admin credit transactions
            amount = deposit.get("amount", 0.0)

            # Admin credits don't have bonuses (they're manual additions)
            enriched["bonus_amount"] = 0.0
            enriched["bonus_tier"] = None
            enriched["total_credited"] = amount
            enriched["processing_fee"] = 0.0

            # Admin-specific information
            enriched["payment_gateway"] = "Admin Panel"
            enriched["external_tx_id"] = ""
            enriched["payment_method"] = "Manual Credit"
            enriched["admin_id"] = deposit.get("admin_id", "Unknown")
            enriched["admin_name"] = deposit.get("admin_name", "Unknown Admin")
            enriched["note"] = deposit.get("note", "")
            enriched["status"] = "completed"  # Admin credits are always completed

            # Use transaction timestamp
            timestamp = deposit.get("timestamp")
            if timestamp:
                parsed_timestamp = _parse_datetime_field(timestamp)
                enriched["formatted_date"] = parsed_timestamp.strftime("%Y-%m-%d %H:%M:%S")
                enriched["created_at"] = parsed_timestamp  # Normalize field name
            else:
                enriched["formatted_date"] = "Unknown"

            # Set amounts for consistency
            enriched["requested_amount"] = amount
            enriched["actual_paid_amount"] = amount

        else:
            # Handle payment gateway deposits
            requested_amount = deposit.get("requested_amount", 0.0)
            actual_paid_amount = deposit.get("actual_paid_amount", 0.0)

            # Calculate bonus information if deposit is completed
            if deposit.get("status") == "completed" and actual_paid_amount > 0:
                bonus_calc = calculate_bonus_amount(actual_paid_amount)
                enriched["bonus_amount"] = bonus_calc["bonus_amount"]
                enriched["bonus_tier"] = bonus_calc["tier_used"]
                enriched["total_credited"] = bonus_calc["total_amount"]
            else:
                enriched["bonus_amount"] = 0.0
                enriched["bonus_tier"] = None
                enriched["total_credited"] = actual_paid_amount

            # Calculate fees (difference between requested and actual if applicable)
            processing_fee = max(0, requested_amount - actual_paid_amount) if requested_amount > actual_paid_amount else 0.0
            enriched["processing_fee"] = processing_fee

            # Add payment gateway information
            enriched["payment_gateway"] = deposit.get("gateway", "OxaPay")
            enriched["external_tx_id"] = deposit.get("txID", "")
            enriched["payment_method"] = deposit.get("currency", "Unknown")
            enriched["admin_id"] = None
            enriched["admin_name"] = None
            enriched["note"] = ""

            # Format timestamps
            created_at = deposit.get("created_at")
            if created_at:
                parsed_created_at = _parse_datetime_field(created_at)
                enriched["formatted_date"] = parsed_created_at.strftime("%Y-%m-%d %H:%M:%S")
            else:
                enriched["formatted_date"] = "Unknown"

        return enriched

    except Exception as e:
        logger.error(f"Error enriching deposit data: {e}")
        return deposit


async def _enrich_deposit_data_async(deposit: Dict[str, Any]) -> Dict[str, Any]:
    """
    Enrich deposit data with bonus and fee calculations (async).
    Handles both payment gateway deposits and admin credit transactions.

    Args:
        deposit: Raw deposit data from payments or transactions collection

    Returns:
        Enriched deposit data with calculated fields
    """
    try:
        enriched = deposit.copy()
        source_type = deposit.get("source_type", "payment_gateway")

        if source_type == "admin_credit":
            # Handle admin credit transactions
            amount = deposit.get("amount", 0.0)

            # Admin credits don't have bonuses (they're manual additions)
            enriched["bonus_amount"] = 0.0
            enriched["bonus_tier"] = None
            enriched["total_credited"] = amount
            enriched["processing_fee"] = 0.0

            # Admin-specific information
            enriched["payment_gateway"] = "Admin Panel"
            enriched["external_tx_id"] = ""
            enriched["payment_method"] = "Manual Credit"
            enriched["admin_id"] = deposit.get("admin_id", "Unknown")
            enriched["admin_name"] = deposit.get("admin_name", "Unknown Admin")
            enriched["note"] = deposit.get("note", "")
            enriched["status"] = "completed"  # Admin credits are always completed

            # Use transaction timestamp
            timestamp = deposit.get("timestamp")
            if timestamp:
                parsed_timestamp = _parse_datetime_field(timestamp)
                enriched["formatted_date"] = parsed_timestamp.strftime("%Y-%m-%d %H:%M:%S")
                enriched["created_at"] = parsed_timestamp  # Normalize field name
            else:
                enriched["formatted_date"] = "Unknown"

            # Set amounts for consistency
            enriched["requested_amount"] = amount
            enriched["actual_paid_amount"] = amount

        else:
            # Handle payment gateway deposits
            requested_amount = deposit.get("requested_amount", 0.0)
            actual_paid_amount = deposit.get("actual_paid_amount", 0.0)

            # Calculate bonus information if deposit is completed
            if deposit.get("status") == "completed" and actual_paid_amount > 0:
                bonus_calc = await calculate_bonus_amount_async(actual_paid_amount)
                enriched["bonus_amount"] = bonus_calc["bonus_amount"]
                enriched["bonus_tier"] = bonus_calc["tier_used"]
                enriched["total_credited"] = bonus_calc["total_amount"]
            else:
                enriched["bonus_amount"] = 0.0
                enriched["bonus_tier"] = None
                enriched["total_credited"] = actual_paid_amount

            # Calculate fees (difference between requested and actual if applicable)
            processing_fee = max(0, requested_amount - actual_paid_amount) if requested_amount > actual_paid_amount else 0.0
            enriched["processing_fee"] = processing_fee

            # Add payment gateway information
            enriched["payment_gateway"] = deposit.get("gateway", "OxaPay")
            enriched["external_tx_id"] = deposit.get("txID", "")
            enriched["payment_method"] = deposit.get("currency", "Unknown")
            enriched["admin_id"] = None
            enriched["admin_name"] = None
            enriched["note"] = ""

            # Format timestamps
            created_at = deposit.get("created_at")
            if created_at:
                parsed_created_at = _parse_datetime_field(created_at)
                enriched["formatted_date"] = parsed_created_at.strftime("%Y-%m-%d %H:%M:%S")
            else:
                enriched["formatted_date"] = "Unknown"

        return enriched

    except Exception as e:
        logger.error(f"Error enriching deposit data (async): {e}")
        return deposit


def _calculate_deposit_summary(user_id: int) -> Dict[str, Any]:
    """
    Calculate summary statistics for user deposits.
    Includes both payment gateway deposits and admin credit additions.

    Args:
        user_id: User ID to calculate summary for

    Returns:
        Dict containing summary statistics
    """
    try:
        # Get payment gateway deposits
        payment_deposits = list(payments_collection.find({"user_id": user_id}))

        # Get admin credit transactions
        admin_deposits = list(transactions_collection.find({
            "user_id": user_id,
            "type": "admin_deposit"
        }))

        if not payment_deposits and not admin_deposits:
            return {
                "total_deposits": 0,
                "total_amount": 0.0,
                "total_credited": 0.0,
                "total_bonuses": 0.0,
                "total_fees": 0.0,
                "average_deposit": 0.0,
                "successful_deposits": 0,
                "failed_deposits": 0
            }

        total_deposits = len(payment_deposits) + len(admin_deposits)
        total_amount = 0.0
        total_credited = 0.0
        total_bonuses = 0.0
        total_fees = 0.0
        successful_deposits = 0
        failed_deposits = 0

        # Process payment gateway deposits
        for deposit in payment_deposits:
            status = deposit.get("status", "")
            requested_amount = deposit.get("requested_amount", 0.0)
            actual_paid_amount = deposit.get("actual_paid_amount", 0.0)

            if status == "completed":
                successful_deposits += 1
                total_amount += actual_paid_amount

                # Calculate bonus for completed deposits
                if actual_paid_amount > 0:
                    bonus_calc = calculate_bonus_amount(actual_paid_amount)
                    bonus_amount = bonus_calc["bonus_amount"]
                    total_bonuses += bonus_amount
                    total_credited += bonus_calc["total_amount"]
                else:
                    total_credited += actual_paid_amount

                # Calculate processing fees
                if requested_amount > actual_paid_amount:
                    total_fees += (requested_amount - actual_paid_amount)

            elif status in ["failed", "expired", "cancelled"]:
                failed_deposits += 1

        # Process admin credit transactions (always successful)
        for deposit in admin_deposits:
            amount = deposit.get("amount", 0.0)
            successful_deposits += 1
            total_amount += amount
            total_credited += amount
            # Admin credits don't have bonuses or fees

        # Calculate average deposit amount
        average_deposit = total_amount / successful_deposits if successful_deposits > 0 else 0.0

        return {
            "total_deposits": total_deposits,
            "total_amount": total_amount,
            "total_credited": total_credited,
            "total_bonuses": total_bonuses,
            "total_fees": total_fees,
            "average_deposit": average_deposit,
            "successful_deposits": successful_deposits,
            "failed_deposits": failed_deposits
        }

    except Exception as e:
        logger.error(f"Error calculating deposit summary for user {user_id}: {e}")
        return {
            "total_deposits": 0,
            "total_amount": 0.0,
            "total_credited": 0.0,
            "total_bonuses": 0.0,
            "total_fees": 0.0,
            "average_deposit": 0.0,
            "successful_deposits": 0,
            "failed_deposits": 0
        }


async def _calculate_deposit_summary_async(user_id: int) -> Dict[str, Any]:
    """
    Calculate summary statistics for user deposits (async).
    Includes both payment gateway deposits and admin credit additions.

    Args:
        user_id: User ID to calculate summary for

    Returns:
        Dict containing summary statistics
    """
    try:
        # Get payment gateway deposits
        payment_cursor = payments_collection_async.find({"user_id": user_id})
        payment_deposits = await payment_cursor.to_list(length=None)

        # Get admin credit transactions
        admin_cursor = transactions_collection_async.find({
            "user_id": user_id,
            "type": "admin_deposit"
        })
        admin_deposits = await admin_cursor.to_list(length=None)

        if not payment_deposits and not admin_deposits:
            return {
                "total_deposits": 0,
                "total_amount": 0.0,
                "total_credited": 0.0,
                "total_bonuses": 0.0,
                "total_fees": 0.0,
                "average_deposit": 0.0,
                "successful_deposits": 0,
                "failed_deposits": 0
            }

        total_deposits = len(payment_deposits) + len(admin_deposits)
        total_amount = 0.0
        total_credited = 0.0
        total_bonuses = 0.0
        total_fees = 0.0
        successful_deposits = 0
        failed_deposits = 0

        # Process payment gateway deposits
        for deposit in payment_deposits:
            status = deposit.get("status", "")
            requested_amount = deposit.get("requested_amount", 0.0)
            actual_paid_amount = deposit.get("actual_paid_amount", 0.0)

            if status == "completed":
                successful_deposits += 1
                total_amount += actual_paid_amount

                # Calculate bonus for completed deposits
                if actual_paid_amount > 0:
                    bonus_calc = await calculate_bonus_amount_async(actual_paid_amount)
                    bonus_amount = bonus_calc["bonus_amount"]
                    total_bonuses += bonus_amount
                    total_credited += bonus_calc["total_amount"]
                else:
                    total_credited += actual_paid_amount

                # Calculate processing fees
                if requested_amount > actual_paid_amount:
                    total_fees += (requested_amount - actual_paid_amount)

            elif status in ["failed", "expired", "cancelled"]:
                failed_deposits += 1

        # Process admin credit transactions (always successful)
        for deposit in admin_deposits:
            amount = deposit.get("amount", 0.0)
            successful_deposits += 1
            total_amount += amount
            total_credited += amount
            # Admin credits don't have bonuses or fees

        # Calculate average deposit amount
        average_deposit = total_amount / successful_deposits if successful_deposits > 0 else 0.0

        return {
            "total_deposits": total_deposits,
            "total_amount": total_amount,
            "total_credited": total_credited,
            "total_bonuses": total_bonuses,
            "total_fees": total_fees,
            "average_deposit": average_deposit,
            "successful_deposits": successful_deposits,
            "failed_deposits": failed_deposits
        }

    except Exception as e:
        logger.error(f"Error calculating deposit summary for user {user_id} (async): {e}")
        return {
            "total_deposits": 0,
            "total_amount": 0.0,
            "total_credited": 0.0,
            "total_bonuses": 0.0,
            "total_fees": 0.0,
            "average_deposit": 0.0,
            "successful_deposits": 0,
            "failed_deposits": 0
        }


# --- Line Purchase History Operations ---


@sandbox_aware_db_write
def add_line_purchase_history(user_id: int, product_id: Any, line_indices: List[int], quantity: int, purchase_data: Dict[str, Any] = None) -> bool:
    """
    Add a record of line purchase history for shared inventory tracking.

    Args:
        user_id: User ID who made the purchase
        product_id: Product ID
        line_indices: List of line indices that were purchased
        quantity: Number of lines purchased
        purchase_data: Additional purchase metadata

    Returns:
        True if successful, False otherwise
    """
    try:
        # Normalize product_id
        if isinstance(product_id, str) and product_id.isdigit():
            product_id = int(product_id)

        purchase_record = {
            "user_id": user_id,
            "product_id": product_id,
            "line_indices": line_indices,
            "quantity": quantity,
            "purchase_date": datetime.now(),
            "purchase_data": purchase_data or {}
        }

        result = line_purchase_history_collection.insert_one(purchase_record)
        return result.acknowledged

    except Exception as e:
        _handle_db_error(f"add_line_purchase_history (user={user_id}, product={product_id})", e)
        return False


def get_user_purchased_lines(user_id: int, product_id: Any) -> List[int]:
    """
    Get all line indices that a user has previously purchased for a product.

    Args:
        user_id: User ID
        product_id: Product ID

    Returns:
        List of line indices that the user has already purchased
    """
    try:
        # Validate input parameters
        if not isinstance(user_id, int) or user_id <= 0:
            logger.warning(f"Invalid user_id: {user_id} (type: {type(user_id)})")
            return []

        # Normalize product_id and create query that handles multiple formats
        normalized_product_id = product_id
        if isinstance(product_id, str) and product_id.isdigit():
            normalized_product_id = int(product_id)

        # Create a query that searches for multiple product_id formats to handle data inconsistencies
        # This ensures we find records regardless of how the product_id was stored
        query_conditions = [{"product_id": normalized_product_id}]

        # Add string representation if we have an ObjectId
        if BSON_AVAILABLE and isinstance(normalized_product_id, ObjectId):
            query_conditions.append({"product_id": str(normalized_product_id)})

        # Add ObjectId representation if we have a valid ObjectId string
        if isinstance(normalized_product_id, str) and BSON_AVAILABLE and ObjectId.is_valid(normalized_product_id):
            query_conditions.append({"product_id": ObjectId(normalized_product_id)})

        # Find all purchase records for this user and product using OR query
        records = line_purchase_history_collection.find({
            "user_id": user_id,
            "$or": query_conditions
        })

        # Collect all purchased line indices
        purchased_indices = []
        record_count = 0
        for record in records:
            record_count += 1
            line_indices = record.get("line_indices", [])
            purchased_indices.extend(line_indices)
            logger.debug(f"get_user_purchased_lines: Record {record_count} for user {user_id}, product {product_id}: {line_indices}")

        # Return unique indices
        unique_indices = list(set(purchased_indices))
        logger.debug(f"get_user_purchased_lines: user={user_id}, product={product_id}, "
                    f"records_found={record_count}, unique_purchased_indices={unique_indices}")

        return unique_indices

    except Exception as e:
        _handle_db_error(f"get_user_purchased_lines (user={user_id}, product={product_id})", e)
        return []


def has_user_purchased_lines(user_id: int, product_id: Any, line_indices: List[int]) -> bool:
    """
    Check if a user has already purchased any of the specified line indices.

    Args:
        user_id: User ID
        product_id: Product ID
        line_indices: List of line indices to check

    Returns:
        True if user has purchased any of these lines before, False otherwise
    """
    try:
        purchased_indices = get_user_purchased_lines(user_id, product_id)
        return any(idx in purchased_indices for idx in line_indices)

    except Exception as e:
        _handle_db_error(f"has_user_purchased_lines (user={user_id}, product={product_id})", e)
        return False


def get_available_lines_for_user(user_id: int, product_id: Any, total_lines: int) -> List[int]:
    """
    Get line indices that are available for a user to purchase (not previously bought).

    Args:
        user_id: User ID
        product_id: Product ID
        total_lines: Total number of lines in the product

    Returns:
        List of line indices available for purchase by this user
    """
    try:
        # Validate input parameters
        if not isinstance(user_id, int) or user_id <= 0:
            logger.warning(f"Invalid user_id: {user_id} (type: {type(user_id)})")
            return []

        if not isinstance(total_lines, int) or total_lines <= 0:
            logger.warning(f"Invalid total_lines: {total_lines} (type: {type(total_lines)})")
            return []

        purchased_indices = get_user_purchased_lines(user_id, product_id)
        all_indices = list(range(total_lines))
        available_indices = [idx for idx in all_indices if idx not in purchased_indices]

        # Debug logging for shared inventory availability calculation
        logger.debug(f"get_available_lines_for_user: user={user_id}, product={product_id}, "
                    f"total_lines={total_lines}, purchased={len(purchased_indices)}, "
                    f"available={len(available_indices)}")

        return available_indices

    except Exception as e:
        _handle_db_error(f"get_available_lines_for_user (user={user_id}, product={product_id})", e)
        return []  # Return empty list on error for safety





@sandbox_aware_db_write
def add_user_reservation(user_id: int, product_id: Any, quantity: int) -> bool:
    """
    Add a user-specific reservation record.

    Args:
        user_id: User ID
        product_id: Product ID
        quantity: Number of lines reserved

    Returns:
        True if successful, False otherwise
    """
    try:
        # Normalize product_id
        if isinstance(product_id, str) and product_id.isdigit():
            product_id = int(product_id)

        # Set reservation to expire in 30 minutes (reasonable time for checkout)
        from datetime import timedelta
        expiration_time = datetime.now() + timedelta(minutes=30)

        reservation_record = {
            "user_id": user_id,
            "product_id": product_id,
            "quantity": quantity,
            "status": "active",
            "created_at": datetime.now(),
            "expires_at": expiration_time
        }

        result = user_reservations_collection.insert_one(reservation_record)
        return result.acknowledged

    except Exception as e:
        _handle_db_error(f"add_user_reservation (user={user_id}, product={product_id})", e)
        return False


@sandbox_aware_db_write
def remove_user_reservation(user_id: int, product_id: Any, quantity: int) -> bool:
    """
    Remove or reduce a user-specific reservation record.

    Args:
        user_id: User ID
        product_id: Product ID
        quantity: Number of lines to remove from reservation

    Returns:
        True if successful, False otherwise
    """
    try:
        # Normalize product_id
        if isinstance(product_id, str) and product_id.isdigit():
            product_id = int(product_id)

        query_conditions = [
            {"product_id": product_id},
            {"product_id": str(product_id)}
        ]

        # Find active reservations for this user and product
        reservations = list(user_reservations_collection.find({
            "user_id": user_id,
            "$or": query_conditions,
            "status": "active"
        }).sort("created_at", 1))  # Oldest first

        remaining_to_remove = quantity
        for reservation in reservations:
            if remaining_to_remove <= 0:
                break

            reservation_quantity = reservation.get("quantity", 0)
            if reservation_quantity <= remaining_to_remove:
                # Remove entire reservation
                user_reservations_collection.delete_one({"_id": reservation["_id"]})
                remaining_to_remove -= reservation_quantity
            else:
                # Reduce reservation quantity
                new_quantity = reservation_quantity - remaining_to_remove
                user_reservations_collection.update_one(
                    {"_id": reservation["_id"]},
                    {"$set": {"quantity": new_quantity}}
                )
                remaining_to_remove = 0

        return True

    except Exception as e:
        _handle_db_error(f"remove_user_reservation (user={user_id}, product={product_id})", e)
        return False


@sandbox_aware_db_write
def cleanup_expired_reservations() -> int:
    """
    Clean up expired user reservations.

    Returns:
        Number of expired reservations removed
    """
    try:
        current_time = datetime.now()

        # Find and remove expired reservations
        result = user_reservations_collection.delete_many({
            "status": "active",
            "expires_at": {"$lt": current_time}
        })

        removed_count = result.deleted_count
        if removed_count > 0:
            logger.info(f"Cleaned up {removed_count} expired user reservations")

        return removed_count

    except Exception as e:
        _handle_db_error("cleanup_expired_reservations", e)
        return 0


def get_user_reserved_lines_count(user_id: int, product_id: Any) -> int:
    """
    Get the count of lines currently reserved by a specific user for a product.
    This function also cleans up expired reservations for the user.

    Args:
        user_id: User ID
        product_id: Product ID

    Returns:
        Number of lines currently reserved by this user
    """
    try:
        # Clean up expired reservations for this user first
        current_time = datetime.now()
        user_reservations_collection.delete_many({
            "user_id": user_id,
            "status": "active",
            "expires_at": {"$lt": current_time}
        })

        # Normalize product_id
        if isinstance(product_id, str) and product_id.isdigit():
            product_id = int(product_id)

        # Query user reservations collection
        query_conditions = [
            {"product_id": product_id},
            {"product_id": str(product_id)}
        ]

        # Find active reservations for this user and product
        reservations = user_reservations_collection.find({
            "user_id": user_id,
            "$or": query_conditions,
            "status": "active",
            "expires_at": {"$gte": current_time}  # Only count non-expired reservations
        })

        total_reserved = sum(reservation.get("quantity", 0) for reservation in reservations)
        return total_reserved

    except Exception as e:
        _handle_db_error(f"get_user_reserved_lines_count (user={user_id}, product={product_id})", e)
        return 0
