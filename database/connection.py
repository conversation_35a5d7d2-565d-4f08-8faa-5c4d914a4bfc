import certifi
import asyncio
import motor.motor_asyncio
from pymongo import MongoClient
from pymongo.collection import Collection
from motor.motor_asyncio import AsyncIOMotorClient, AsyncIOMotorCollection
import logging
from pymongo.server_api import ServerApi  # New import for MongoDB 5.0+ features
import functools

# Import from project root
from config import (
    MONGO_URI,
    DB_NAME,
    MONGO_CONNECT_TIMEOUT_MS,
    MONGO_SOCKET_TIMEOUT_MS,
    MONGO_SERVER_SELECTION_TIMEOUT_MS,
)

# Setup logger
logger = logging.getLogger(__name__)

# Connection pool settings
MAX_POOL_SIZE = 100
MIN_POOL_SIZE = 10
MAX_IDLE_TIME_MS = 30000  # 30 seconds

# Updated connection settings for modern MongoDB deployments
connection_settings = {
    "tlsCAFile": certifi.where(),
    "maxPoolSize": MAX_POOL_SIZE,
    "minPoolSize": MIN_POOL_SIZE,
    "maxIdleTimeMS": MAX_IDLE_TIME_MS,
    "server_api": Server<PERSON><PERSON>("1"),  # Using stable API for MongoDB 5.0+
    "retryWrites": True,
    "connectTimeoutMS": MONGO_CONNECT_TIMEOUT_MS,  # Using value from config
    "socketTimeoutMS": MONGO_SOCKET_TIMEOUT_MS,  # Using value from config
    "serverSelectionTimeoutMS": MONGO_SERVER_SELECTION_TIMEOUT_MS,  # Using value from config
    "ssl": True,
    "tls": True,
    "tlsAllowInvalidCertificates": False,  # Set to True only for testing if still having issues
}

# Synchronous MongoDB connection for compatibility with existing code
try:
    client = MongoClient(MONGO_URI, **connection_settings)
    # Test the connection
    client.server_info()
    logger.info("Successfully connected to MongoDB (synchronous)")
except Exception as e:
    logger.error(f"MongoDB connection error: {e}")
    # Try without tlsCAFile and server_api
    try:
        fallback_settings = {
            "maxPoolSize": MAX_POOL_SIZE,
            "minPoolSize": MIN_POOL_SIZE,
            "maxIdleTimeMS": MAX_IDLE_TIME_MS,
            "retryWrites": True,
            "connectTimeoutMS": 20000,  # Even longer timeout for fallback
            "socketTimeoutMS": 90000,  # Even longer timeout for fallback
            "ssl": True,
            "tls": True,
        }
        client = MongoClient(MONGO_URI, **fallback_settings)
        client.server_info()
        logger.info(
            "Successfully connected to MongoDB with fallback settings (synchronous)"
        )
    except Exception as e:
        logger.error(f"Fatal MongoDB connection error: {e}")
        raise

db = client[DB_NAME]

# MongoDB collections (synchronous)
users_collection: Collection = db["users"]
transactions_collection: Collection = db["transactions"]
products_collection: Collection = db["products"]
carts_collection: Collection = db["carts"]
admins_collection: Collection = db["admins"]
orders_collection: Collection = db["orders"]
payments_collection: Collection = db["payments"]
support_messages_collection: Collection = db["support_messages"]

banned_users_collection: Collection = db["banned_users"]
categories_collection: Collection = db["categories"]
settings_collection: Collection = db["settings"]
announcements_collection: Collection = db["announcements"]
bonus_tiers_collection: Collection = db["bonus_tiers"]
line_purchase_history_collection: Collection = db["line_purchase_history"]
user_reservations_collection: Collection = db["user_reservations"]
admin_logs_collection: Collection = db["admin_logs"]

# Async MongoDB client for new code
try:
    async_client = motor.motor_asyncio.AsyncIOMotorClient(
        MONGO_URI, **connection_settings
    )
    # Add the async database reference
    db_async = async_client[DB_NAME]
    logger.info("Successfully connected to MongoDB (asynchronous)")
except Exception as e:
    logger.error(f"Async MongoDB connection error: {e}")
    # Try without tlsCAFile and server_api
    try:
        fallback_settings = {
            "maxPoolSize": MAX_POOL_SIZE,
            "minPoolSize": MIN_POOL_SIZE,
            "maxIdleTimeMS": MAX_IDLE_TIME_MS,
            "retryWrites": True,
            "connectTimeoutMS": 20000,  # Even longer timeout for fallback
            "socketTimeoutMS": 90000,  # Even longer timeout for fallback
            "ssl": True,
            "tls": True,
        }
        async_client = motor.motor_asyncio.AsyncIOMotorClient(
            MONGO_URI, **fallback_settings
        )
        db_async = async_client[DB_NAME]
        logger.info(
            "Successfully connected to MongoDB with fallback settings (asynchronous)"
        )
    except Exception as e:
        logger.error(f"Fatal async MongoDB connection error: {e}")
        raise

# Async MongoDB collections
users_collection_async: AsyncIOMotorCollection = db_async["users"]
transactions_collection_async: AsyncIOMotorCollection = db_async["transactions"]
products_collection_async: AsyncIOMotorCollection = db_async["products"]
carts_collection_async: AsyncIOMotorCollection = db_async["carts"]
orders_collection_async: AsyncIOMotorCollection = db_async["orders"]
payments_collection_async: AsyncIOMotorCollection = db_async["payments"]
support_messages_collection_async: AsyncIOMotorCollection = db_async["support_messages"]
notifications_collection_async: AsyncIOMotorCollection = None  # noqa
banned_users_collection_async: AsyncIOMotorCollection = db_async["banned_users"]
categories_collection_async: AsyncIOMotorCollection = db_async["categories"]
admins_collection_async: AsyncIOMotorCollection = db_async["admins"]
settings_collection_async: AsyncIOMotorCollection = db_async["settings"]
announcements_collection_async: AsyncIOMotorCollection = db_async["announcements"]
bonus_tiers_collection_async: AsyncIOMotorCollection = db_async["bonus_tiers"]
line_purchase_history_collection_async: AsyncIOMotorCollection = db_async["line_purchase_history"]
admin_logs_collection_async: AsyncIOMotorCollection = db_async["admin_logs"]


# Function to create MongoDB indexes
def initialize_indexes():
    """Create indexes for frequently queried fields in MongoDB collections."""
    # User collection indexes
    users_collection.create_index("user_id", unique=True)
    users_collection.create_index("username")

    # Transactions collection indexes
    transactions_collection.create_index("user_id")
    transactions_collection.create_index("transaction_type")
    transactions_collection.create_index("timestamp")

    # Products collection indexes
    products_collection.create_index("product_id", unique=True)
    products_collection.create_index("name")
    products_collection.create_index("category_id")
    products_collection.create_index("is_available")

    # Carts collection indexes
    carts_collection.create_index("user_id", unique=True)

    # Orders collection indexes
    orders_collection.create_index("user_id")
    orders_collection.create_index("order_id", unique=True)
    orders_collection.create_index("status")
    orders_collection.create_index("timestamp")

    # Support messages indexes
    support_messages_collection.create_index("user_id")
    support_messages_collection.create_index("status")
    support_messages_collection.create_index("timestamp")

    # Banned users collection indexes
    banned_users_collection.create_index("user_id", unique=True)

    # Categories collection indexes
    categories_collection.create_index("name", unique=True)
    categories_collection.create_index("slug", unique=True)

    # Payments collection indexes
    payments_collection.create_index("track_id", unique=True)
    payments_collection.create_index("user_id")
    payments_collection.create_index("status")
    payments_collection.create_index([("user_id", 1), ("created_at", -1)])
    payments_collection.create_index("created_at")

    # Bonus tiers collection indexes
    bonus_tiers_collection.create_index("threshold")
    bonus_tiers_collection.create_index("is_active")
    bonus_tiers_collection.create_index([("threshold", 1), ("is_active", 1)])

    logger.info("MongoDB indexes created successfully")


# Async version of initialize_indexes
async def initialize_indexes_async():
    """Create indexes asynchronously for MongoDB collections."""
    # User collection indexes
    await users_collection_async.create_index("user_id", unique=True)
    await users_collection_async.create_index("username")

    # Transactions collection indexes
    await transactions_collection_async.create_index("user_id")
    await transactions_collection_async.create_index("transaction_type")
    await transactions_collection_async.create_index("timestamp")

    # Products collection indexes
    await products_collection_async.create_index("product_id", unique=True)
    await products_collection_async.create_index("name")
    await products_collection_async.create_index("category_id")
    await products_collection_async.create_index("is_available")

    # Carts collection indexes
    await carts_collection_async.create_index("user_id", unique=True)

    # Orders collection indexes
    await orders_collection_async.create_index("user_id")
    await orders_collection_async.create_index("order_id", unique=True)
    await orders_collection_async.create_index("status")
    await orders_collection_async.create_index("timestamp")

    # Support messages indexes
    await support_messages_collection_async.create_index("user_id")
    await support_messages_collection_async.create_index("status")
    await support_messages_collection_async.create_index("timestamp")

    # Banned users collection indexes
    await banned_users_collection_async.create_index("user_id", unique=True)

    # Categories collection indexes
    await categories_collection_async.create_index("name", unique=True)
    await categories_collection_async.create_index("slug", unique=True)

    # Payments collection indexes
    await payments_collection_async.create_index("track_id", unique=True)
    await payments_collection_async.create_index("user_id")
    await payments_collection_async.create_index("status")
    await payments_collection_async.create_index([("user_id", 1), ("created_at", -1)])
    await payments_collection_async.create_index("created_at")

    # Bonus tiers collection indexes
    await bonus_tiers_collection_async.create_index("threshold")
    await bonus_tiers_collection_async.create_index("is_active")
    await bonus_tiers_collection_async.create_index([("threshold", 1), ("is_active", 1)])

    logger.info("MongoDB async indexes created successfully")


@functools.lru_cache(maxsize=1)
def setup_database(*args, **kwargs):  # noqa: Used indirectly
    """Initialize the database with required data and indexes (async)."""
    # Use original initialize_indexes for now to maintain compatibility
    initialize_indexes()
    # Fix circular import by importing only when needed
    from .operations import initialize_database_async

    asyncio.run(initialize_database_async())

    logger.info("Database setup completed")
    return db


# Add a connection health check function
async def check_database_connection():
    """Check if the database connection is healthy."""
    try:
        # Ping the database
        await db_async.command("ping")
        logger.info("Database connection is healthy")
        return True
    except Exception as e:
        logger.error(f"Database connection check failed: {e}")
        return False


# Add a function to reconnect to database
async def reconnect_database():
    """Attempt to reconnect to the database after connection failure."""
    global client, async_client, db, db_async

    logger.info("Attempting to reconnect to MongoDB...")
    retry_count = 0
    max_retries = 5  # Increased from 3 to 5 attempts

    while retry_count < max_retries:
        try:
            # Try to reconnect with synchronous client
            connection_params = connection_settings.copy()

            # If this isn't the first attempt, adjust settings progressively
            if retry_count > 0:
                # Increase timeouts with each retry
                connection_params["connectTimeoutMS"] = 15000 + (retry_count * 5000)
                connection_params["socketTimeoutMS"] = 60000 + (retry_count * 15000)

            # For later retries, try more permissive settings
            if retry_count >= 2:
                connection_params["tlsInsecure"] = True

            # Last attempt with minimal settings
            if retry_count == max_retries - 1:
                connection_params = {
                    "connectTimeoutMS": 45000,
                    "socketTimeoutMS": 120000,
                    "ssl": True,
                    "serverSelectionTimeoutMS": 60000,
                }

            logger.info(
                f"Reconnection attempt {retry_count+1} with settings: {connection_params}"
            )

            client = MongoClient(MONGO_URI, **connection_params)
            client.server_info()  # This will throw an exception if connection fails
            db = client[DB_NAME]

            # Try to reconnect with async client
            async_client = motor.motor_asyncio.AsyncIOMotorClient(
                MONGO_URI, **connection_params
            )
            await async_client.admin.command("ping")  # Verify async connection
            db_async = async_client[DB_NAME]

            # Reassign collections
            global users_collection, transactions_collection, products_collection
            global carts_collection, admins_collection, orders_collection
            global payments_collection, support_messages_collection
            global notifications_collection, banned_users_collection, categories_collection
            global settings_collection, announcements_collection

            # Sync collections
            users_collection = db["users"]
            transactions_collection = db["transactions"]
            products_collection = db["products"]
            carts_collection = db["carts"]
            admins_collection = db["admins"]
            orders_collection = db["orders"]
            payments_collection = db["payments"]
            support_messages_collection = db["support_messages"]
            notifications_collection = db["notifications"]
            banned_users_collection = db["banned_users"]
            categories_collection = db["categories"]
            settings_collection = db["settings"]
            announcements_collection = db["announcements"]
            bonus_tiers_collection = db["bonus_tiers"]

            # Async collections
            global users_collection_async, transactions_collection_async, products_collection_async
            global carts_collection_async, orders_collection_async, payments_collection_async
            global support_messages_collection_async, notifications_collection_async
            global banned_users_collection_async, categories_collection_async, admins_collection_async
            global settings_collection_async, announcements_collection_async

            users_collection_async = db_async["users"]
            transactions_collection_async = db_async["transactions"]
            products_collection_async = db_async["products"]
            carts_collection_async = db_async["carts"]
            orders_collection_async = db_async["orders"]
            payments_collection_async = db_async["payments"]
            support_messages_collection_async = db_async["support_messages"]
            notifications_collection_async = db_async["notifications"]
            banned_users_collection_async = db_async["banned_users"]
            categories_collection_async = db_async["categories"]
            admins_collection_async = db_async["admins"]
            settings_collection_async = db_async["settings"]
            announcements_collection_async = db_async["announcements"]
            bonus_tiers_collection_async = db_async["bonus_tiers"]

            logger.info("Successfully reconnected to MongoDB")
            return True
        except Exception as e:
            retry_count += 1
            logger.error(f"Reconnection attempt {retry_count} failed: {e}")
            await asyncio.sleep(2**retry_count)  # Exponential backoff

    logger.critical("Failed to reconnect to database after multiple attempts")
    return False
