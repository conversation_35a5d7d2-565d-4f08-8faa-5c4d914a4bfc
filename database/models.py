from typing import Dict, List, Optional, Union, Any
from datetime import datetime
from pydantic import BaseModel, Field

# Conditional BSON import
try:
    from bson import ObjectId
    BSON_AVAILABLE = True
except ImportError:
    # Define dummy ObjectId class if BSON is not available
    class ObjectId:
        @staticmethod
        def is_valid(oid):
            return False

        def __init__(self, oid=None):
            self.oid = oid

        def __str__(self):
            return str(self.oid) if self.oid else ""
    BSON_AVAILABLE = False

# Define __all__ to explicitly export these classes
__all__ = ["User", "Transaction", "Product", "CartItem", "Cart", "Admin", "Category", "BonusTier"]


# Custom ObjectId field for Pydantic
class PyObjectId(ObjectId):  # noqa: Used for type definition
    @classmethod
    def __get_validators__(cls):
        yield cls.validate

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __modify_schema__(cls, field_schema):
        field_schema.update(type="string")


class Config:  # noqa: Used as inner class in Pydantic models
    populate_by_name = True  # noqa
    arbitrary_types_allowed = True  # noqa


class User(BaseModel):
    user_id: int
    balance: float = 0.0
    created_at: datetime = Field(default_factory=datetime.now)
    name: Optional[str] = None
    username: Optional[str] = None
    last_seen: datetime = Field(default_factory=datetime.now)

    class Config:
        populate_by_name = True

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "User":
        return cls(
            user_id=data.get("user_id"),
            balance=data.get("balance", 0.0),
            created_at=data.get("created_at", datetime.now()),
            name=data.get("name"),
            username=data.get("username"),
            last_seen=data.get("last_seen", datetime.now()),
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "user_id": self.user_id,
            "balance": self.balance,
            "created_at": self.created_at,
            "name": self.name,
            "username": self.username,
            "last_seen": self.last_seen,
        }


class Transaction(BaseModel):
    user_id: int
    type: str
    amount: float
    timestamp: datetime = Field(default_factory=datetime.now)
    additional_data: Dict[str, Any] = Field(default_factory=dict)

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Transaction":
        transaction_type = data.pop("type", "unknown")
        user_id = data.pop("user_id", None)
        amount = data.pop("amount", 0.0)
        timestamp = data.pop("timestamp", datetime.now())

        return cls(
            user_id=user_id,
            type=transaction_type,
            amount=amount,
            timestamp=timestamp,
            additional_data=data,
        )

    def to_dict(self) -> Dict[str, Any]:
        result = {
            "user_id": self.user_id,
            "type": self.type,
            "amount": self.amount,
            "timestamp": self.timestamp,
        }
        result.update(self.additional_data)
        return result


class Product(BaseModel):
    id: Optional[int] = None
    name: str
    description: str = ""
    price: float
    file_link: str = ""
    image_url: Optional[str] = None
    category_id: Optional[int] = None
    created_at: datetime = Field(default_factory=datetime.now)
    # Regular product file metadata fields
    file_name: Optional[str] = None  # Original filename of uploaded file
    file_size: Optional[int] = None  # File size in bytes
    file_type: Optional[str] = None  # File type (document, image, etc.)
    file_path: Optional[str] = None  # Local file path for uploaded files
    file_mime_type: Optional[str] = None  # MIME type of the file
    # Digital line-based inventory fields
    is_line_based: bool = False  # Whether this product uses line-based inventory
    inventory_file_path: Optional[str] = None  # Path to the inventory .txt file
    total_lines: int = 0  # Total lines in the inventory file
    available_lines: int = 0  # Currently available lines for sale
    reserved_lines: int = 0  # Lines reserved for pending orders
    line_price: Optional[float] = None  # Price per line (if different from base price)
    allow_shared_inventory: bool = False  # Whether multiple users can purchase the same lines
    max_quantity_per_order: int = 1  # Maximum lines a customer can buy in one order
    preview_format: Optional[str] = None  # Sample format to show customers
    # Exclusive single-use product fields
    is_exclusive_single_use: bool = False  # Whether this product is exclusive single-use
    is_purchased: bool = False  # Whether this exclusive product has been purchased
    purchased_by_user_id: Optional[int] = None  # User ID who purchased this exclusive product
    purchase_date: Optional[datetime] = None  # When this exclusive product was purchased
    exclusive_file_path: Optional[str] = None  # Path to the exclusive file
    exclusive_file_type: Optional[str] = None  # File type (image, document, etc.)
    exclusive_file_size: Optional[int] = None  # File size in bytes
    exclusive_file_mime_type: Optional[str] = None  # MIME type of the file
    expiration_date: Optional[datetime] = None  # Optional expiration date for availability

    class Config:
        populate_by_name = True

    @staticmethod
    def price_must_be_positive(v):  # noqa: Used by Pydantic validation
        if v <= 0:
            raise ValueError("Price must be positive")
        return v

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Product":
        return cls(
            id=data.get("id"),
            name=data.get("name", ""),
            description=data.get("description", ""),
            price=data.get("price", 0.0),
            file_link=data.get("file_link", ""),
            image_url=data.get("image_url"),
            category_id=data.get("category_id"),
            created_at=data.get("created_at", datetime.now()),
            file_name=data.get("file_name"),
            file_size=data.get("file_size"),
            file_type=data.get("file_type"),
            file_path=data.get("file_path"),
            file_mime_type=data.get("file_mime_type"),
            is_line_based=data.get("is_line_based", False),
            inventory_file_path=data.get("inventory_file_path"),
            total_lines=data.get("total_lines", 0),
            available_lines=data.get("available_lines", 0),
            reserved_lines=data.get("reserved_lines", 0),
            line_price=data.get("line_price"),
            max_quantity_per_order=data.get("max_quantity_per_order", 1),
            preview_format=data.get("preview_format"),
            allow_shared_inventory=data.get("allow_shared_inventory", False),
            is_exclusive_single_use=data.get("is_exclusive_single_use", False),
            is_purchased=data.get("is_purchased", False),
            purchased_by_user_id=data.get("purchased_by_user_id"),
            purchase_date=data.get("purchase_date"),
            exclusive_file_path=data.get("exclusive_file_path"),
            exclusive_file_type=data.get("exclusive_file_type"),
            exclusive_file_size=data.get("exclusive_file_size"),
            exclusive_file_mime_type=data.get("exclusive_file_mime_type"),
            expiration_date=data.get("expiration_date"),
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "price": self.price,
            "file_link": self.file_link,
            "image_url": self.image_url,
            "category_id": self.category_id,
            "created_at": self.created_at,
            "file_name": self.file_name,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "file_path": self.file_path,
            "file_mime_type": self.file_mime_type,
            "is_line_based": self.is_line_based,
            "inventory_file_path": self.inventory_file_path,
            "total_lines": self.total_lines,
            "available_lines": self.available_lines,
            "reserved_lines": self.reserved_lines,
            "line_price": self.line_price,
            "max_quantity_per_order": self.max_quantity_per_order,
            "preview_format": self.preview_format,
            "allow_shared_inventory": self.allow_shared_inventory,
            "is_exclusive_single_use": self.is_exclusive_single_use,
            "is_purchased": self.is_purchased,
            "purchased_by_user_id": self.purchased_by_user_id,
            "purchase_date": self.purchase_date,
            "exclusive_file_path": self.exclusive_file_path,
            "exclusive_file_type": self.exclusive_file_type,
            "exclusive_file_size": self.exclusive_file_size,
            "exclusive_file_mime_type": self.exclusive_file_mime_type,
            "expiration_date": self.expiration_date,
        }


class CartItem(BaseModel):
    product_id: int
    name: str
    description: str
    price: float
    file_link: str
    image_url: Optional[str] = None
    quantity: int = 1  # Quantity for line-based products
    is_line_based: bool = False  # Whether this is a line-based product
    is_exclusive_single_use: bool = False  # Whether this is an exclusive single-use product

    class Config:
        populate_by_name = True

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "CartItem":
        return cls(
            product_id=data.get("product_id"),
            name=data.get("name", ""),
            description=data.get("description", ""),
            price=data.get("price", 0.0),
            file_link=data.get("file_link", ""),
            image_url=data.get("image_url"),
            quantity=data.get("quantity", 1),
            is_line_based=data.get("is_line_based", False),
            is_exclusive_single_use=data.get("is_exclusive_single_use", False),
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "product_id": self.product_id,
            "name": self.name,
            "description": self.description,
            "price": self.price,
            "file_link": self.file_link,
            "image_url": self.image_url,
            "quantity": self.quantity,
            "is_line_based": self.is_line_based,
        }


class Cart(BaseModel):
    user_id: int
    items: List[CartItem] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.now)

    class Config:
        populate_by_name = True

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Cart":
        items = [CartItem.from_dict(item) for item in data.get("items", [])]
        return cls(
            user_id=data.get("user_id"),
            items=items,
            created_at=data.get("created_at", datetime.now()),
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "user_id": self.user_id,
            "items": [item.to_dict() for item in self.items],
            "created_at": self.created_at,
        }


class Admin(BaseModel):
    user_id: int
    added_by: Optional[int] = None
    is_owner: bool = False
    added_at: datetime = Field(default_factory=datetime.now)
    name: Optional[str] = None
    username: Optional[str] = None

    class Config:
        populate_by_name = True

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Admin":
        return cls(
            user_id=data.get("user_id"),
            added_by=data.get("added_by"),
            is_owner=data.get("is_owner", False),
            added_at=data.get("added_at", datetime.now()),
            name=data.get("name"),
            username=data.get("username"),
        )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "user_id": self.user_id,
            "added_by": self.added_by,
            "is_owner": self.is_owner,
            "added_at": self.added_at,
            "name": self.name,
            "username": self.username,
        }


class Category(BaseModel):
    id: int
    name: str
    slug: Optional[str] = None
    image: Optional[Dict[str, Any]] = None
    description: str = ""
    created_at: datetime = Field(default_factory=datetime.now)

    class Config:
        populate_by_name = True

    @staticmethod
    def name_must_not_be_empty(v):  # noqa: Used by Pydantic validation
        if not v or not v.strip():
            raise ValueError("Category name cannot be empty")
        return v

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Category":
        return cls(
            id=data.get("id"),
            name=data.get("name", ""),
            slug=data.get("slug"),
            image=data.get("image"),
            description=data.get("description", ""),
            created_at=data.get("created_at", datetime.now()),
        )

    def to_dict(self) -> Dict[str, Any]:
        result = {
            "id": self.id,
            "name": self.name,
            "slug": self.slug,
            "description": self.description,
            "created_at": self.created_at,
        }
        if self.image:
            result["image"] = self.image
        return result


def create_user(*args, **kwargs):  # noqa: Used by application logic
    user = {
        "user_id": kwargs.get("user_id"),
        "username": kwargs.get("username"),
        "first_name": kwargs.get("first_name"),
        "last_name": kwargs.get("last_name"),
        "registered_at": datetime.now(),
        "last_activity": datetime.now(),
        "is_admin": False,
        "balance": 0.0,
        "orders": [],
    }
    return user


def create_order(*args, **kwargs):  # noqa: Used by application logic
    order = {
        "user_id": kwargs.get("user_id"),
        "product_id": kwargs.get("product_id"),
        "amount": kwargs.get("amount"),
        "payment_method": kwargs.get("payment_method"),
        "status": "pending",
        "created_at": datetime.now(),
        "updated_at": datetime.now(),
    }
    return order


class BonusTier(BaseModel):
    """Model for bonus tier configuration."""
    tier_id: Optional[str] = Field(default=None, alias="_id")
    threshold: float = Field(..., description="Minimum deposit amount to trigger this bonus")
    bonus_percentage: Optional[float] = Field(default=None, description="Bonus percentage to apply (e.g., 0.10 for 10%)")
    bonus_fixed_amount: Optional[float] = Field(default=None, description="Fixed bonus amount (e.g., 10.0 for $10)")
    bonus_type: str = Field(default="percentage", description="Type of bonus: 'percentage' or 'fixed'")
    is_active: bool = Field(default=True, description="Whether this tier is currently active")
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    created_by: Optional[int] = Field(default=None, description="Admin user ID who created this tier")
    description: Optional[str] = Field(default=None, description="Optional description for this tier")

    class Config:
        populate_by_name = True
        arbitrary_types_allowed = True

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "BonusTier":
        """Create BonusTier instance from dictionary."""
        return cls(
            tier_id=str(data.get("_id")) if data.get("_id") else None,
            threshold=float(data.get("threshold", 0)),
            bonus_percentage=float(data.get("bonus_percentage")) if data.get("bonus_percentage") is not None else None,
            bonus_fixed_amount=float(data.get("bonus_fixed_amount")) if data.get("bonus_fixed_amount") is not None else None,
            bonus_type=data.get("bonus_type", "percentage"),
            is_active=bool(data.get("is_active", True)),
            created_at=data.get("created_at", datetime.now()),
            updated_at=data.get("updated_at", datetime.now()),
            created_by=data.get("created_by"),
            description=data.get("description"),
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert BonusTier instance to dictionary."""
        result = {
            "threshold": self.threshold,
            "bonus_type": self.bonus_type,
            "is_active": self.is_active,
            "created_at": self.created_at,
            "updated_at": self.updated_at,
        }
        if self.bonus_percentage is not None:
            result["bonus_percentage"] = self.bonus_percentage
        if self.bonus_fixed_amount is not None:
            result["bonus_fixed_amount"] = self.bonus_fixed_amount
        if self.tier_id:
            result["_id"] = self.tier_id
        if self.created_by:
            result["created_by"] = self.created_by
        if self.description:
            result["description"] = self.description
        return result

    def calculate_bonus_amount(self, deposit_amount: float) -> float:
        """Calculate the bonus amount for a given deposit amount."""
        if not self.is_active or deposit_amount < self.threshold:
            return 0.0

        if self.bonus_type == "fixed" and self.bonus_fixed_amount is not None:
            return self.bonus_fixed_amount
        elif self.bonus_type == "percentage" and self.bonus_percentage is not None:
            return deposit_amount * self.bonus_percentage
        else:
            # Fallback to percentage if bonus_type is not set properly
            if self.bonus_percentage is not None:
                return deposit_amount * self.bonus_percentage
            return 0.0
