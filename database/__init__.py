# Empty __init__.py file to make 'database' a proper package
from .connection import initialize_indexes


# Avoid circular imports
def setup_database():
    """Initialize the database with required data and indexes."""
    from .connection import initialize_indexes
    from .operations import initialize_database

    initialize_indexes()
    initialize_database()


# Export async setup function
async def setup_database_async():
    """Initialize the database with required data and indexes (async)."""
    from .connection import initialize_indexes_async
    from .operations import initialize_database_async

    # Run synchronous initialization first for compatibility
    from .connection import initialize_indexes

    initialize_indexes()

    # Then run async initialization
    await initialize_indexes_async()
    await initialize_database_async()


# Export these functions to make them accessible
__all__ = [
    "setup_database",
    "setup_database_async",
    # Add other functions that are used elsewhere but flagged as unused
]


# Mark these functions as used by the module system
def setup_database(*args, **kwargs):  # noqa
    from .connection import setup_database as _setup_database

    return _setup_database(*args, **kwargs)


def setup_database_async(*args, **kwargs):  # noqa
    from .connection import setup_database_async as _setup_database_async

    return _setup_database_async(*args, **kwargs)
