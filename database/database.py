import certifi
from pymongo import MongoClient
from pymongo.server_api import <PERSON><PERSON><PERSON>
from config import <PERSON><PERSON>GO_URI, MONGO_DB
from datetime import datetime
import logging

# Setup logger
logger = logging.getLogger(__name__)

# Connection settings with modern features
connection_settings = {
    "tlsCAFile": certifi.where(),
    "server_api": Server<PERSON>pi("1"),  # Using stable API for MongoDB 5.0+
    "retryWrites": True,
    "connectTimeoutMS": 15000,  # Increased timeout (15 seconds)
    "socketTimeoutMS": 60000,  # Increased timeout (60 seconds)
    "ssl": True,
    "tls": True,
}

# Connect to MongoDB with improved error handling
try:
    client = MongoClient(MONGO_URI, **connection_settings)
    db = client[MONGO_DB]
    # Test the connection
    client.server_info()
    logger.info("Database connection established in database.py")
except Exception as e:
    logger.error(f"Error connecting to MongoDB in database.py: {e}")
    # Fallback to basic connection without advanced settings
    try:
        # First fallback with adjusted SSL settings
        fallback_settings = {
            "retryWrites": True,
            "connectTimeoutMS": 20000,  # Longer timeout (20 seconds)
            "socketTimeoutMS": 90000,  # Longer timeout (90 seconds)
            "ssl": True,
            "tls": True,
            "tlsInsecure": True,  # Less secure but may work around some SSL issues
        }
        client = MongoClient(MONGO_URI, **fallback_settings)
        logger.info(
            "Fallback database connection established in database.py with adjusted SSL settings"
        )

        # Test the connection
        client.server_info()
    except Exception as e2:
        logger.error(f"Second connection attempt failed: {e2}")
        # Final fallback with minimal settings
        try:
            client = MongoClient(
                MONGO_URI, connectTimeoutMS=30000, socketTimeoutMS=120000
            )
            client.server_info()
            logger.info("Final fallback database connection established in database.py")
        except Exception as e3:
            logger.error(f"Fatal error connecting to MongoDB: {e3}")
            raise

# Initialize collections
users_collection = db.users
products_collection = db.products
transactions_collection = db.transactions
admins_collection = db.admins
carts_collection = db.carts


# Initialize the admin collection with the owner if it's empty
def init_db():
    """Initialize database with required collections and owner admin account."""
    from config import OWNER_ID
    from .operations import get_log_settings

    # If admins collection is empty, add the owner - updated to use estimated_document_count
    if admins_collection.estimated_document_count() == 0 and OWNER_ID:
        admins_collection.insert_one(
            {
                "user_id": OWNER_ID,
                "role": "owner",
                "is_owner": True,
                "added_at": datetime.now(),
            }
        )
        logger.info(f"Added owner (ID: {OWNER_ID}) to admins collection")
    else:
        logger.info("Admin collection already initialized or no owner ID specified")

    # Explicitly initialize log settings
    try:
        logger.info("Initializing log settings during database setup...")
        settings = get_log_settings()
        logger.info(
            f"Log settings initialized with {len(settings.keys()) if settings else 0} settings"
        )
    except Exception as e:
        logger.error(f"Error initializing log settings: {e}")
