"""
Template management handlers for the Telegram bot.

This module contains all the callback query handlers and message handlers
needed for template management functionality.
"""

from aiogram import Router, F
from aiogram.fsm.context import FSMContext
from aiogram.types import (
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    CallbackQuery,
    Message,
)
from handlers.sys_db import is_privileged

from database.operations import is_owner
from keyboards.admin_kb import (
    template_management_keyboard,
    template_file_selection_keyboard,
    # template_key_selection_keyboard,
    template_edit_confirmation_keyboard,
)
from states.states import AdditionalFeaturesStates, TemplateManagementStates
from utils.template_manager import TemplateManager, _clear_all_template_caches
from utils.telegram_helpers import sanitize_html, safe_edit_message
from utils.template_helpers import format_text
from utils.state_helpers import safe_update_data, clear_state_data
import re
import os
import shutil
from datetime import datetime
import logging

# Setup logger
logger = logging.getLogger(__name__)

# Create a single instance of TemplateManager to be used by handlers
template_manager = TemplateManager()

# Create router for template handlers
router = Router()
router.name = "template_handlers"


# Core template navigation handlers
@router.callback_query(F.data == "manage_templates")
async def manage_templates(callback_query: CallbackQuery) -> None:
    """Show template management options to the owner or admin."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="Only owners and admins can manage templates", show_alert=True
        )
        return

    await callback_query.answer()

    # Get template management message from template
    template_management_message = format_text(
        "admin",
        "template_management",
        default=(
            "🎨 <b>\u2022 TEMPLATE MANAGEMENT \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>SYSTEM CONFIGURATION</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            "<i>Templates control the text displayed throughout the bot.</i>\n\n"
            "\u2022 View and edit message templates\n"
            "\u2022 Customize user interface text\n"
            "\u2022 Manage button labels\n\n"
            "<i>Choose an option below to continue:</i>"
        ),
    )

    await safe_edit_message(
        callback_query.message,
        template_management_message,
        reply_markup=template_management_keyboard(),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "view_templates")
async def view_templates(callback_query: CallbackQuery) -> None:
    """Show a list of all available templates with detailed information."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="Only owners and admins can view templates", show_alert=True
        )
        return

    await callback_query.answer()

    # Get all template files
    template_files = template_manager.get_template_files()

    if not template_files:
        await safe_edit_message(
            callback_query.message,
            "❌ <b>No template files found</b>\n\n"
            "Something went wrong with the template system.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="manage_templates"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    # Sort templates alphabetically
    template_files.sort()

    # Get template overview message from template
    template_overview_header = format_text(
        "admin",
        "template_overview_header",
        template_count=len(template_files),
        default=(
            "📋 <b>\u2022 TEMPLATE OVERVIEW \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>AVAILABLE TEMPLATES</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"<i>Total Templates:</i> <code>{len(template_files)}</code>\n\n"
        ),
    )

    # Format template list message with improved styling
    message_text = template_overview_header

    # Create a keyboard with preview buttons for each template file
    inline_keyboard = []

    # Template purpose descriptions
    template_purposes = {
        "user": "User-facing messages and notifications",
        "admin": "Admin panel text and notifications",
        "shop": "Shop-related messages and product displays",
        "general": "General bot messages and formatting",
    }

    # Format template details for display
    for filename in template_files:
        # Remove .json extension for display
        display_name = (
            filename.replace(".json", "") if filename.endswith(".json") else filename
        )

        # Get template data for key count
        try:
            template_data = template_manager.get_template(filename)
            key_count = len(template_data)

            # Add to message text with improved formatting
            message_text += f"🔹 <b>{display_name.capitalize()}</b>\n"
            message_text += f"   <code>\u2022</code> Keys: {key_count}\n"

            # Add info about template purpose
            template_purpose = template_purposes.get(display_name, "")
            if template_purpose:
                message_text += (
                    f"   <code>\u2022</code> Purpose: <i>{template_purpose}</i>\n"
                )

            message_text += "\n"

            # Add a button to view this template in detail
            inline_keyboard.append(
                [
                    InlineKeyboardButton(
                        text=f"🔍 View {display_name.capitalize()} Template",
                        callback_data=f"template_file:{filename}",
                    )
                ]
            )

        except Exception as e:
            logger.error(f"Error loading template {filename}: {e}")
            message_text += (
                f"📄 <b>{display_name.capitalize()}</b>: Error loading ({str(e)})\n\n"
            )

    message_text += "<i>Select a template to view its keys or use the buttons below for other actions.</i>"

    # Add management buttons
    inline_keyboard.append(
        [
            InlineKeyboardButton(
                text="✏️ Edit Templates", callback_data="edit_templates"
            ),
            InlineKeyboardButton(text="🔄 Reload", callback_data="reload_templates"),
        ]
    )
    inline_keyboard.append(
        [
            InlineKeyboardButton(
                text="🔙 Back to Template Menu", callback_data="manage_templates"
            )
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=InlineKeyboardMarkup(inline_keyboard=inline_keyboard),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "reload_templates")
async def handle_reload_templates(callback_query: CallbackQuery) -> None:
    """Reload all templates from disk."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("Access denied", show_alert=True)
        return

    # Clear the template cache
    _clear_all_template_caches()

    await callback_query.answer("✅ Templates reloaded successfully!")

    # Return to template view
    await view_templates(callback_query)


@router.callback_query(lambda c: c.data.startswith("template_page_info:"))
async def handle_template_page_info(callback_query: CallbackQuery) -> None:
    """Handle clicks on the page indicator button by showing pagination info."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("Access denied", show_alert=True)
        return

    # Extract template filename
    template_file = callback_query.data.split(":", 1)[1]

    try:
        # Get template data to calculate pagination info
        template_data = template_manager.get_template(template_file)
        key_count = len(template_data) if template_data else 0
        keys_per_page = 20
        total_pages = (key_count + keys_per_page - 1) // keys_per_page

        # Show pagination info
        await callback_query.answer(
            f"Template: {template_file}\n"
            f"Total keys: {key_count}\n"
            f"Keys per page: {keys_per_page}\n"
            f"Total pages: {total_pages}",
            show_alert=True,
        )
    except Exception as e:
        logger.error(f"Error getting template pagination info: {e}")
        await callback_query.answer(
            f"Error getting pagination info: {str(e)}", show_alert=True
        )


@router.callback_query(F.data == "edit_buttons_template")
async def edit_buttons_template(
    callback_query: CallbackQuery, state: FSMContext
) -> None:
    """Show the buttons template for editing."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("Access denied", show_alert=True)
        return

    await callback_query.answer()

    # Direct navigation to the buttons template file
    template_file = "buttons"
    await show_template_keys(
        CallbackQuery(
            id=callback_query.id,
            from_user=callback_query.from_user,
            chat_instance=callback_query.chat_instance,
            message=callback_query.message,
            data=f"template_file:{template_file}",
        ),
        state,
    )


# filepath: c:\Users\<USER>\Desktop\New_folder\testbot\templates\template_handlers.py
@router.callback_query(F.data == "debug_buttons_template")
async def debug_buttons_template(callback_query: CallbackQuery) -> None:
    """Debug the buttons template loading."""
    user_id = callback_query.from_user.id

    if not is_owner(user_id) or is_privileged(user_id, role="owner"):
        await callback_query.answer("Access denied", show_alert=True)
        return

    # Try loading the buttons template
    try:
        buttons_data = template_manager.get_template("buttons.json")
        key_count = len(buttons_data) if buttons_data else 0

        await callback_query.answer(
            f"Buttons template loaded: {key_count} keys found", show_alert=True
        )
    except Exception as e:
        logger.error(f"Error loading buttons template: {e}")
        await callback_query.answer(
            f"Error loading buttons template: {str(e)}", show_alert=True
        )


# Template editing handlers
@router.callback_query(F.data == "edit_templates")
async def edit_templates_list(callback_query: CallbackQuery) -> None:
    """Show a list of templates available for editing."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="Only owners and admins can edit templates", show_alert=True
        )
        return

    await callback_query.answer()

    # Get all template files
    template_files = template_manager.get_template_files()

    if not template_files:
        await safe_edit_message(
            callback_query.message,
            "❌ <b>No template files found</b>\n\n"
            "Something went wrong with the template system.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="manage_templates"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    # Get edit templates message from template
    edit_templates_message = format_text(
        "admin",
        "edit_templates",
        default=(
            "✏️ <b>\u2022 EDIT TEMPLATES \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>TEMPLATE SELECTION</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            "<i>Select a template file to edit from the list below:</i>"
        ),
    )

    await safe_edit_message(
        callback_query.message,
        edit_templates_message,
        reply_markup=template_file_selection_keyboard(template_files),
        parse_mode="HTML",
    )


@router.callback_query(lambda c: c.data.startswith("template_file:"))
async def show_template_keys(callback_query: CallbackQuery, state: FSMContext) -> None:
    """Show all keys in the selected template file with pagination."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("Access denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract template filename and optional page number
    parts = callback_query.data.split(":", 2)
    template_file = parts[1]
    page = 1  # Default to first page

    # Check if page number is provided in the callback data
    if len(parts) > 2 and parts[2].isdigit():
        page = int(parts[2])

    # Store current template file in state
    await safe_update_data(state, current_template_file=template_file)
    await state.set_state(TemplateManagementStates.waiting_for_key_selection)

    # Get template data
    try:
        template_data = template_manager.get_template(template_file)

        if not template_data:
            await safe_edit_message(
                callback_query.message,
                f"❌ Template file '{template_file}' is empty.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Templates",
                                callback_data="edit_templates",
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
            return

        # Sort keys alphabetically
        template_keys = sorted(template_data.keys())

        # Pagination settings
        keys_per_page = (
            20  # Limit to 20 keys per page to avoid Telegram's markup size limit
        )
        total_pages = (
            len(template_keys) + keys_per_page - 1
        ) // keys_per_page  # Ceiling division

        # Ensure page is within valid range
        if page < 1:
            page = 1
        elif page > total_pages:
            page = total_pages

        # Calculate slice indices for current page
        start_idx = (page - 1) * keys_per_page
        end_idx = min(start_idx + keys_per_page, len(template_keys))

        # Get keys for current page
        current_page_keys = template_keys[start_idx:end_idx]

        # Format the message with preview of values
        display_name = (
            template_file.replace(".json", "")
            if template_file.endswith(".json")
            else template_file
        )
        message_text = f"🔑 <b>\u2022 TEMPLATE KEYS: {display_name} \u2022</b>\n\n"
        message_text += f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
        message_text += f"<b>AVAILABLE SETTINGS</b>\n"
        message_text += f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        message_text += f"<i>Page {page}/{total_pages} \u2022 Total Keys: {len(template_keys)}</i>\n\n"

        # Add special note for faq.json template
        if template_file == "faq.json" or template_file == "faq":
            message_text += f"<i>Note: The 'faqs' key contains a list of FAQ items and should be managed through the FAQ Management section.</i>\n\n"

        message_text += f"Select a key to edit:"

        # Create keyboard with two buttons per row
        inline_keyboard = []
        row = []

        for i, key in enumerate(current_page_keys):
            # Special handling for 'faqs' key in faq.json
            if key == "faqs" and (
                template_file == "faq.json" or template_file == "faq"
            ):
                # For the faqs key, use a different callback that will show a special message
                row.append(
                    InlineKeyboardButton(
                        text=f"{key[:20]} 📋",  # Add a special icon
                        callback_data=f"edit_template_key:{template_file}:{key}",
                    )
                )
            else:
                # Normal handling for other keys
                row.append(
                    InlineKeyboardButton(
                        text=key[:20],  # Limit key length to 20 chars
                        callback_data=f"edit_template_key:{template_file}:{key}",
                    )
                )

            # If we have 2 buttons or this is the last key, add the row to keyboard
            if len(row) == 2 or i == len(current_page_keys) - 1:
                inline_keyboard.append(row)
                row = []

        # Add pagination navigation buttons
        pagination_row = []

        # Previous page button (if not on first page)
        if page > 1:
            pagination_row.append(
                InlineKeyboardButton(
                    text="⬅️ Previous",
                    callback_data=f"template_file:{template_file}:{page-1}",
                )
            )

        # Page indicator
        pagination_row.append(
            InlineKeyboardButton(
                text=f"📄 {page}/{total_pages}",
                callback_data=f"template_page_info:{template_file}",
            )
        )

        # Next page button (if not on last page)
        if page < total_pages:
            pagination_row.append(
                InlineKeyboardButton(
                    text="Next ➡️",
                    callback_data=f"template_file:{template_file}:{page+1}",
                )
            )

        # Add pagination row
        if total_pages > 1:
            inline_keyboard.append(pagination_row)

        # Add back button
        inline_keyboard.append(
            [
                InlineKeyboardButton(
                    text="🔙 Back to Templates", callback_data="edit_templates"
                )
            ]
        )

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=InlineKeyboardMarkup(inline_keyboard=inline_keyboard),
            parse_mode="HTML",
        )

    except Exception as e:
        logger.error(f"Error loading template {template_file}: {e}")
        await safe_edit_message(
            callback_query.message,
            f"❌ <b>Error loading template</b>\n\n"
            f"Could not load template '{template_file}'.\n"
            f"Error: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Templates",
                            callback_data="edit_templates",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )


@router.callback_query(lambda c: c.data.startswith("edit_template_key:"))
async def edit_template_key(callback_query: CallbackQuery, state: FSMContext) -> None:
    """Start editing a specific template key."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("Access denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract template filename and key
    parts = callback_query.data.split(":", 2)
    if len(parts) != 3:
        await callback_query.answer("Invalid key format", show_alert=True)
        return

    template_file = parts[1]
    key = parts[2]

    # Get current value
    try:
        # Special handling for 'faqs' key in faq.json
        if key == "faqs" and (template_file == "faq.json" or template_file == "faq"):
            # Get FAQ management message from template
            faq_management_message = format_text(
                "admin",
                "faq_management_special",
                default=(
                    f"ℹ️ <b>\u2022 FAQ MANAGEMENT \u2022</b>\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>SPECIAL TEMPLATE</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"<i>The 'faqs' key contains a list of FAQ items and cannot be edited directly.</i>\n\n"
                    f"<i>Please use the FAQ Management section in the Owner Panel to manage FAQ items.</i>"
                ),
            )

            # For the faqs key, show a special message and redirect to FAQ management
            await safe_edit_message(
                callback_query.message,
                faq_management_message,
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Template",
                                callback_data=f"template_file:{template_file}",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="📋 Go to FAQ Management",
                                callback_data="manage_faqs",
                            )
                        ],
                    ]
                ),
                parse_mode="HTML",
            )
            return

        current_value = template_manager.format_text(template_file, key, "No value")

        # Store template info in state
        await safe_update_data(
            state,
            edit_template_file=template_file,
            edit_template_key=key,
            current_value=current_value,
        )

        # Set state to waiting for new value
        await state.set_state(TemplateManagementStates.waiting_for_template_edit)

        # Show HTML formatting characters in preview
        html_preview_with_entities = current_value.replace("<", "&lt;").replace(
            ">", "&gt;"
        )

        # Generate a preview with sample data
        sample_preview = current_value
        try:
            # Sample data for variable replacements
            sample_data = {
                "name": "John Doe",
                "username": "johndoe",
                "user_id": "123456789",
                "balance": "100.00",
                "product_name": "Sample Product",
                "price": "19.99",
                "order_id": "ORD-12345",
                "category": "Sample Category",
                "date": "January 1, 2023",
                "time": "12:00 PM",
                "admin_name": "Admin User",
                "variable": "Sample Value",
                "variable_name": "Sample Value",
                "count": "5",
                "total": "99.95",
            }

            # Format with sample data for a realistic preview
            sample_preview = template_manager.format_text(
                template_file, key, **sample_data
            )
        except Exception as e:
            logger.error(f"Error generating sample preview: {e}")
            sample_preview = "Preview generation failed (variable substitution error)"

        # Display name without .json extension for user-friendliness
        display_name = (
            template_file.replace(".json", "")
            if template_file.endswith(".json")
            else template_file
        )

        await safe_edit_message(
            callback_query.message,
            f"✏️ <b>\u2022 EDIT TEMPLATE VALUE \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>TEMPLATE DETAILS</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 <b>File:</b> <code>{display_name}</code>\n"
            f"\u2022 <b>Key:</b> <code>{key}</code>\n\n"
            f"<b>Current template code:</b>\n<code>{html_preview_with_entities}</code>\n\n"
            f"<b>Preview with sample data:</b>\n{sample_preview}\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>EDITING INSTRUCTIONS</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 Type the new value for this template key\n"
            f"\u2022 HTML formatting is supported:\n"
            f"  <code>&lt;b&gt;bold&lt;/b&gt;</code>, <code>&lt;i&gt;italic&lt;/i&gt;</code>, <code>&lt;code&gt;monospace&lt;/code&gt;</code>\n"
            f"\u2022 Variables use format: <code>{'{variable_name}'}</code>\n"
            f"\u2022 Common variables: name, username, balance, product_name, price, etc.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel",
                            callback_data=f"template_file:{template_file}",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
    except Exception as e:
        logger.error(f"Error loading template key {template_file}/{key}: {e}")
        await safe_edit_message(
            callback_query.message,
            f"❌ <b>Error loading template key</b>\n\n"
            f"Could not load key '{key}' from template '{template_file}'.\n"
            f"Error: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Template",
                            callback_data=f"template_file:{template_file}",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )


@router.callback_query(lambda c: c.data.startswith("confirm_template_edit:"))
async def confirm_template_edit(
    callback_query: CallbackQuery, state: FSMContext
) -> None:
    """Handle confirmation to save a template after user confirms."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("Access denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract template filename and key
    parts = callback_query.data.split(":", 2)
    if len(parts) != 3:
        await callback_query.answer("Invalid data format", show_alert=True)
        return

    template_file = parts[1]
    key = parts[2]

    # Get pending value from state
    data = await state.get_data()
    current_value = data.get("current_value", "")
    value = data.get("pending_template_value", "")

    if not value:
        await safe_edit_message(
            callback_query.message,
            f"❌ <b>Error:</b> No pending template value found.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Template",
                            callback_data=f"template_file:{template_file}",
                        )
                    ]
                ]
            ),
        )
        return

    # Normalize template file name
    template_file_base = template_file
    if template_file.endswith(".json"):
        template_file_base = template_file.replace(".json", "")

    # Attempt to update the template
    success = template_manager.update_text(template_file_base, key, value)

    if success:
        # Force a complete template cache refresh
        logger.info(f"Template updated successfully: {template_file_base}, key: {key}")
        _clear_all_template_caches()

        # Sample variable replacements for preview
        sample_data = {
            "name": "John Doe",
            "username": "johndoe",
            "user_id": "123456789",
            "balance": "100.00",
            "product_name": "Sample Product",
            "price": "19.99",
            "order_id": "ORD-12345",
            "category": "Sample Category",
            "date": "January 1, 2023",
            "time": "12:00 PM",
            "admin_name": "Admin User",
            "variable": "Sample Value",
            "variable_name": "Sample Value",
            "count": "5",
            "total": "99.95",
        }

        # Generate preview
        try:
            preview = template_manager.format_text(
                template_file_base, key, **sample_data
            )
        except Exception as e:
            logger.error(f"Error generating preview for {template_file}/{key}: {e}")
            preview = f"<i>Error generating preview: {str(e)}</i>"

        # Show diffs between old and new values
        old_value_formatted = current_value.replace("<", "&lt;").replace(">", "&gt;")
        new_value_formatted = value.replace("<", "&lt;").replace(">", "&gt;")

        # Create a compact diff display
        diff_display = ""
        if old_value_formatted != new_value_formatted:
            diff_display = (
                f"<b>Changes:</b>\n"
                f"<b>From:</b> <code>{old_value_formatted}</code>\n"
                f"<b>To:</b> <code>{new_value_formatted}</code>\n\n"
            )

        await safe_edit_message(
            callback_query.message,
            f"✅ <b>\u2022 TEMPLATE UPDATED \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>UPDATE SUCCESSFUL</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 <b>File:</b> <code>{template_file_base}</code>\n"
            f"\u2022 <b>Key:</b> <code>{key}</code>\n\n"
            f"{diff_display}"
            f"<b>Preview with sample data:</b>\n{preview}",
            parse_mode="HTML",
            reply_markup=template_edit_confirmation_keyboard(template_file, page=1),
        )
    else:
        await safe_edit_message(
            callback_query.message,
            f"❌ <b>Failed to update template</b>\n\n"
            f"Could not update '{key}' in template '{template_file_base}'.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="✏️ Try Again",
                            callback_data=f"edit_template_key:{template_file}:{key}",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Template",
                            callback_data=f"template_file:{template_file}",
                        )
                    ],
                ]
            ),
        )

    # Clear state
    await state.clear()


@router.callback_query(lambda c: c.data.startswith("confirm_save_template:"))
async def confirm_save_template(
    callback_query: CallbackQuery, state: FSMContext
) -> None:
    """Handle confirmation to save a template with potential issues."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("Access denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract template filename and key
    parts = callback_query.data.split(":", 2)
    if len(parts) != 3:
        await callback_query.answer("Invalid data format", show_alert=True)
        return

    template_file = parts[1]
    key = parts[2]

    # Get pending value from state
    data = await state.get_data()
    current_value = data.get("current_value", "")
    value = data.get("pending_template_value", "")

    if not value:
        await safe_edit_message(
            callback_query.message,
            f"❌ <b>Error:</b> No pending template value found.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Template",
                            callback_data=f"template_file:{template_file}",
                        )
                    ]
                ]
            ),
        )
        return

    # Normalize template file name (don't add .json if it already has it)
    template_file_with_ext = template_file
    if not template_file.endswith(".json"):
        template_file_with_ext = f"{template_file}.json"
        template_file_base = template_file
    else:
        template_file_base = template_file.replace(".json", "")

    # Attempt to update the template - use the template base name (without .json)
    success = template_manager.update_text(template_file_base, key, value)

    if success:
        # Reload templates to clear the cache and apply changes immediately
        _clear_all_template_caches()

        # Sample variable replacements for preview
        sample_data = {
            "name": "John Doe",
            "username": "johndoe",
            "user_id": "123456789",
            "balance": "100.00",
            "product_name": "Sample Product",
            "price": "19.99",
            "order_id": "ORD-12345",
            "category": "Sample Category",
            "date": "January 1, 2023",
            "time": "12:00 PM",
            "admin_name": "Admin User",
            "variable": "Sample Value",
            "variable_name": "Sample Value",
            "count": "5",
            "total": "99.95",
        }

        # Generate preview
        try:
            # Try to format with sample data for a more realistic preview
            preview = template_manager.format_text(
                template_file_base, key, **sample_data
            )
        except Exception as e:
            logger.error(f"Error generating preview for {template_file}/{key}: {e}")
            preview = f"<i>Error generating preview: {str(e)}</i>"

        # Show diffs between old and new values
        old_value_formatted = current_value.replace("<", "&lt;").replace(">", "&gt;")
        new_value_formatted = value.replace("<", "&lt;").replace(">", "&gt;")

        diff_display = (
            f"<b>Changes:</b>\n"
            f"<b>From:</b> <code>{old_value_formatted}</code>\n"
            f"<b>To:</b> <code>{new_value_formatted}</code>\n\n"
        )

        await safe_edit_message(
            callback_query.message,
            f"✅ <b>\u2022 TEMPLATE UPDATED \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>UPDATE WITH WARNING</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 <b>File:</b> <code>{template_file_base}</code>\n"
            f"\u2022 <b>Key:</b> <code>{key}</code>\n\n"
            f"⚠️ <b>Warning:</b> <i>The template was saved with potentially unbalanced HTML tags. "
            f"This may cause display issues in some contexts.</i>\n\n"
            f"{diff_display}"
            f"<b>Preview with sample data:</b>\n{preview}",
            parse_mode="HTML",
            reply_markup=template_edit_confirmation_keyboard(template_file, page=1),
        )
    else:
        await safe_edit_message(
            callback_query.message,
            f"❌ <b>Failed to update template</b>\n\n"
            f"Could not update '{key}' in template '{template_file_base}'.",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="✏️ Try Again",
                            callback_data=f"edit_template_key:{template_file}:{key}",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Template",
                            callback_data=f"template_file:{template_file}",
                        )
                    ],
                ]
            ),
        )

    # Clear state
    await state.clear()


@router.message(TemplateManagementStates.waiting_for_template_edit)
async def process_template_value(message: Message, state: FSMContext) -> None:
    """Process the new template value and ask for confirmation before saving."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("Access denied")
        await clear_state_data(state)
        return

    # Get template data from state
    data = await state.get_data()
    template_file = data.get("edit_template_file")
    key = data.get("edit_template_key")
    current_value = data.get("current_value", "")

    if not template_file or not key:
        await message.reply(
            f"❌ <b>Error</b>\n\n" f"Missing template information. Please try again.",
            parse_mode="HTML",
        )
        return

    # Process and sanitize the template value
    raw_value = message.text
    value = sanitize_html(raw_value)
    was_sanitized = value != raw_value

    # Helper function for HTML tag balance check
    def has_unbalanced_html_tags(text: str) -> bool:
        """Check if the text has unbalanced HTML tags."""
        # Check for basic HTML tag balance
        opening_tags = re.findall(
            r"<([a-zA-Z][a-zA-Z0-9]*)(?![^>]*/>)(?:\s[^>]*)?>", text, re.IGNORECASE
        )
        closing_tags = re.findall(r"</([a-zA-Z][a-zA-Z0-9]*)>", text, re.IGNORECASE)

        # Count each tag type
        tag_count = {}
        for tag in opening_tags:
            tag = tag.lower()
            tag_count[tag] = tag_count.get(tag, 0) + 1

        for tag in closing_tags:
            tag = tag.lower()
            tag_count[tag] = tag_count.get(tag, 0) - 1

        # Self-closing tags are fine
        self_closing_tags = ["br", "hr", "img", "input", "link", "meta"]
        for tag in self_closing_tags:
            if tag in tag_count:
                tag_count.pop(tag)

        # Check if any tags are unbalanced
        return any(count != 0 for count in tag_count.values())

    # If there are unbalanced HTML tags, ask for confirmation
    if has_unbalanced_html_tags(value):
        confirmation_markup = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="✅ Save Anyway",
                        callback_data=f"confirm_save_template:{template_file}:{key}",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="✏️ Edit Again",
                        callback_data=f"edit_template_key:{template_file}:{key}",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="❌ Cancel", callback_data=f"template_file:{template_file}"
                    )
                ],
            ]
        )

        # Store the value in state for later
        await safe_update_data(state, pending_template_value=value)

        # Show warning and ask for confirmation
        await message.reply(
            f"⚠️ <b>Warning: Unbalanced HTML Tags Detected</b>\n\n"
            f"Your template appears to have unbalanced HTML tags even after sanitization. This may cause display issues.\n\n"
            f"Please check for matching opening and closing tags like <code>&lt;b&gt;</code> and <code>&lt;/b&gt;</code>.\n\n"
            f"Preview with potential issues:\n{value}\n\n"
            f"What would you like to do?",
            parse_mode="HTML",
            reply_markup=confirmation_markup,
        )
        return

    # Normalize template file name
    template_file_base = template_file
    if template_file.endswith(".json"):
        template_file_base = template_file.replace(".json", "")

    # Store the new value in state for confirmation
    await safe_update_data(state, pending_template_value=value)
    await state.set_state(TemplateManagementStates.waiting_for_confirmation)

    # Generate preview with sample data
    sample_data = {
        "name": "John Doe",
        "username": "johndoe",
        "user_id": "123456789",
        "balance": "100.00",
        "product_name": "Sample Product",
        "price": "19.99",
        "order_id": "ORD-12345",
        "category": "Sample Category",
        "date": "January 1, 2023",
        "time": "12:00 PM",
        "admin_name": "Admin User",
        "variable": "Sample Value",
        "variable_name": "Sample Value",
        "count": "5",
        "total": "99.95",
    }

    # Generate preview
    try:
        preview = template_manager.format_text(template_file_base, key, **sample_data)
    except Exception as e:
        logger.error(f"Error generating preview for {template_file}/{key}: {e}")
        preview = f"<i>Error generating preview: {str(e)}</i>"

    # Show diffs between old and new values
    old_value_formatted = current_value.replace("<", "&lt;").replace(">", "&gt;")
    new_value_formatted = value.replace("<", "&lt;").replace(">", "&gt;")

    # Create a compact diff display
    diff_display = ""
    if old_value_formatted != new_value_formatted:
        diff_display = (
            f"<b>Changes:</b>\n"
            f"<b>From:</b> <code>{old_value_formatted}</code>\n"
            f"<b>To:</b> <code>{new_value_formatted}</code>\n\n"
        )

    sanitized_notice = ""
    if was_sanitized:
        sanitized_notice = "<i>Note: Your HTML code was sanitized to ensure compatibility with Telegram.</i>\n\n"

    # Create confirmation keyboard with explicit callbacks to confirmation handlers
    confirmation_keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm & Save Changes",
                    callback_data=f"confirm_template_edit:{template_file}:{key}",
                )
            ],
            [
                InlineKeyboardButton(
                    text="✏️ Edit Again",
                    callback_data=f"edit_template_key:{template_file}:{key}",
                )
            ],
            [
                InlineKeyboardButton(
                    text="❌ Cancel", callback_data=f"template_file:{template_file}"
                )
            ],
        ]
    )

    # Show confirmation message with preview
    await message.reply(
        f"📝 <b>CONFIRM TEMPLATE EDIT</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"<b>File:</b> {template_file_base}\n"
        f"<b>Key:</b> {key}\n\n"
        f"{sanitized_notice}"
        f"{diff_display}"
        f"<b>Preview with sample data:</b>\n{preview}\n\n"
        f"<b>Please confirm that you want to save these changes.</b>",
        parse_mode="HTML",
        reply_markup=confirmation_keyboard,
    )


@router.callback_query(lambda c: c.data.startswith("execute_restore:"))
async def execute_restore_templates(callback_query: CallbackQuery) -> None:
    """Execute the template restoration process."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("Access denied", show_alert=True)
        return

    await callback_query.answer("Restoring templates, please wait...")

    # Extract backup name
    backup_name = callback_query.data.split(":", 1)[1]

    try:
        # Create a backup of current templates first
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_dir = os.path.join(template_manager.templates_dir, "backups")
        auto_backup_name = f"templates_backup_{current_time}_before_restore"
        auto_backup_path = os.path.join(backup_dir, auto_backup_name)

        # Make sure backup dir exists
        os.makedirs(backup_dir, exist_ok=True)
        os.makedirs(auto_backup_path, exist_ok=True)

        # Copy current templates to auto backup
        template_files = template_manager.get_template_files()
        for template_file in template_files:
            src_path = os.path.join(template_manager.templates_dir, template_file)
            dst_path = os.path.join(auto_backup_path, template_file)
            shutil.copy2(src_path, dst_path)

        # Create backup info file
        info_file = os.path.join(auto_backup_path, "backup_info.txt")
        with open(info_file, "w") as f:
            f.write(
                f"Automatic backup before restore: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            )
            f.write(f"User ID: {user_id}\n")
            f.write(f"Backup being restored: {backup_name}\n")
            f.write(f"Files backed up:\n")
            for file in template_files:
                f.write(f"- {file}\n")

        # Restore from the selected backup
        backup_path = os.path.join(backup_dir, backup_name)

        if not os.path.exists(backup_path):
            raise FileNotFoundError(f"Backup directory not found: {backup_path}")

        # Get list of template files in the backup
        backup_files = [f for f in os.listdir(backup_path) if f.endswith(".json")]

        if not backup_files:
            raise FileNotFoundError(f"No template files found in backup: {backup_path}")

        # Copy files from backup to templates directory
        restored_files = []
        for file in backup_files:
            src_path = os.path.join(backup_path, file)
            dst_path = os.path.join(template_manager.templates_dir, file)
            shutil.copy2(src_path, dst_path)
            restored_files.append(file)

        # Reload templates to clear cache
        _clear_all_template_caches()

        # Format restoration information
        try:
            # Get timestamp from backup name
            timestamp = backup_name.replace("templates_backup_", "")
            year = timestamp[:4]
            month = timestamp[4:6]
            day = timestamp[6:8]
            time = timestamp[9:11] + ":" + timestamp[11:13]
            display_date = f"{day}/{month}/{year} {time}"
        except:
            display_date = backup_name

        # Display success message
        await safe_edit_message(
            callback_query.message,
            f"✅ <b>Templates Restored Successfully</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"<b>Backup:</b> {display_date}\n"
            f"<b>Files restored:</b> {len(restored_files)}\n\n"
            f"<b>Auto-backup created:</b> {auto_backup_name}\n"
            f"<i>The previous templates were backed up automatically before the restore.</i>\n\n"
            f"<b>Next steps:</b>\n"
            f"1. Templates have been reloaded automatically\n"
            f"2. Check that your templates are displaying correctly\n"
            f"3. If needed, you can restore from the auto-backup",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔍 View Templates", callback_data="view_templates"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="⬅️ Back to Template Menu",
                            callback_data="manage_templates",
                        )
                    ],
                ]
            ),
        )

    except Exception as e:
        logger.error(f"Error restoring templates: {e}")
        await safe_edit_message(
            callback_query.message,
            f"❌ <b>Error Restoring Templates</b>\n\n"
            f"An error occurred during template restoration:\n"
            f"<code>{str(e)}</code>",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Try Again",
                            callback_data=f"confirm_restore:{backup_name}",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="⬅️ Back to Template Menu",
                            callback_data="manage_templates",
                        )
                    ],
                ]
            ),
        )


def register_template_handlers(dp):
    """Register the template handlers with the dispatcher"""
    try:
        dp.include_router(router)
        logger.info("Template handlers registered successfully")
    except RuntimeError:
        # Router is already attached, skip it
        logger.info("Template router already attached, skipping registration")
