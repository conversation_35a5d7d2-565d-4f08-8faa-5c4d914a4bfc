{"owner_welcome": "👑 <b>• OWNER CONTROL CENTER •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>MASTER ACCESS</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Welcome back, {owner_name}! You have full system access.</i>\n\n• Configure system permissions and roles\n• Manage administrative staff and privileges\n• Access advanced system configuration\n• Monitor complete financial operations\n\n ▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n <i>Select an option from the menu below to begin.</i>", "owner_manage_admins": "👑 <b>• ADMIN MANAGEMENT •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>STAFF CONTROLS</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Manage your administrative team:</i>\n\nCurrent admin count: <b>{admin_count}</b>\n\n• Add new administrators to your team\n• Remove administrative access when needed\n• View complete list of system administrators\n\n<i>Select an option from the menu below to proceed.</i>", "add_admin": "👤 <b>• ADD NEW ADMIN •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>GRANT PRIVILEGES</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Please enter the user ID of the person you want to make an admin.</i>\n\n• The user must have used the bot at least once\n• User ID should be a numeric value\n• New admins will have immediate access\n\n<i>Type the User ID below or use the Cancel button to return.</i>", "remove_admin": "❌ <b>• REMOVE ADMIN •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>REVOKE PRIVILEGES</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Select an administrator to remove from the system:</i>", "admin_list": "📋 <b>• ADMINISTRATOR LIST •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>PRIVILEGED ACCOUNTS</b>\n<b>━━━━━━━━━━━━━━━━━━</b>", "admin_added": "✓ <b>• ADMIN ADDED •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>PRIVILEGES GRANTED</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>New administrator successfully added to the system.</i>\n\n<b>User ID:</b> <code>{user_id}</code>\n<b>Username:</b> <code>@{username}</code>\n<b>Name:</b> {name}\n<b>Added by:</b> {added_by}\n<b>Date:</b> {date}\n\n<i>This user now has administrative access to the system.</i>", "admin_removed": "✓ <b>• ADMIN REMOVED •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>PRIVILEGES REVOKED</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Administrator successfully removed from the system.</i>\n\n<b>User ID:</b> <code>{user_id}</code>\n<b>Username:</b> <code>@{username}</code>\n<b>Name:</b> {name}\n<b>Removed by:</b> {removed_by}\n<b>Date:</b> {date}\n\n<i>This user no longer has administrative access.</i>", "system_stats": "📊 <b>• SYSTEM DASHBOARD •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>USERS & COMMUNITY</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n• Total Users: <code>{total_users}</code>\n• Administrators: <code>{total_admins}</code>\n• New Users (24h): <code>{new_users_24h}</code>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>SHOP STATISTICS</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n• Categories: <code>{total_categories}</code>\n• Products: <code>{total_products}</code>\n• Transactions: <code>{total_transactions}</code>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>FINANCIAL SUMMARY</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n• Total Revenue: <code>{total_sales}</code>\n• Recent Sales (24h): <code>{sales_amount_24h}</code>\n\n<i>Last updated: {current_time}</i>", "bot_settings": "🔧 <b>• BOT SETTINGS •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>SYSTEM CONFIGURATION</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Configure critical bot settings:</i>\n\n• Establish logging and notification channels\n• Set system-wide parameters and limits\n• Configure payment methods and gateways\n• Manage maintenance mode and uptime\n\n<i>Select an option from the menu below to proceed.</i>", "categories_management": "📂 <b>• CATEGORY MANAGEMENT •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>PRODUCT ORGANIZATION</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Manage your shop's product categories:</i>\n\n• Create new categories for organization\n• Edit existing category details and images\n• Remove unused or redundant categories\n• View products within each category\n\n<i>Select an option from the menu below to proceed.</i>", "log_channel_setup": "📝 <b>• LOG CHANNEL SETUP •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>ACTIVITY MONITORING</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Configure a Telegram channel for system logs:</i>\n\n• Create a private channel in Telegram\n• Add this bot as an administrator with posting rights\n• Enter the channel ID below (format: -100xxxxxxxxxx)\n\n<i>All system activity will be logged to this channel.</i>", "confirm_remove_admin": "⚠️ <b>• CONFIRM ADMIN REMOVAL •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>VERIFICATION REQUIRED</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>You are about to remove an administrator:</i>\n\n<b>User:</b> {admin_name}\n<b>ID:</b> <code>{admin_id}</code>\n<b>Username:</b> {admin_username}\n\n⚠️ This action will revoke all administrative privileges for this user.\n\n<i>Please confirm this action to proceed.</i>", "payment_gateway_setup": "💳 <b>• PAYMENT GATEWAY SETUP •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>FINANCIAL CONFIGURATION</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Configure payment processing integration:</i>\n\n• Set up API keys and merchant credentials\n• Configure currency and payment methods\n• Set minimum and maximum transaction limits\n• Customize payment confirmation messages\n\n<i>Enter the required information below to configure your payment gateway.</i>", "maintenance_mode": "🛠️ <b>• MAINTENANCE MODE •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>SYSTEM MAINTENANCE</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Maintenance mode status:</i> <b>{status}</b>\n\nWhen enabled, only owners and admins can access the bot while regular users will see a maintenance message.\n\n<b>Custom Message:</b>\n{maintenance_message}\n\n<i>Use the controls below to manage maintenance mode.</i>", "database_management": "💾 <b>• DATABASE MANAGEMENT •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>DATA OPERATIONS</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Manage your system's database:</i>\n\n• Create database backups and exports\n• Import data from previous backups\n• Perform database optimization\n• View database statistics and health\n\n<i>Select an option from the menu below to proceed.</i>", "database_export_welcome": "💾 <b>• DATABAS<PERSON> EXPORT CENTER •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>📊 SECURE DATA EXPORT SYSTEM</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Professional database export management:</i>\n\n✅ <b>Multiple Export Formats</b>\n   • BSON Archive (MongoDB native)\n   • Compressed BSON Archive\n   • JSON Export (human readable)\n   • Compressed JSON Export\n\n🔒 <b>Security Features</b>\n   • Token-based secure downloads\n   • Comprehensive audit logging\n   • Rate limiting and access control\n   • Automatic file cleanup\n\n⚙️ <b>Advanced Options</b>\n   • Collection filtering\n   • Date range selection\n   • Custom export configurations\n   • Progress tracking\n\n<i>Select an option below to begin your database export.</i>", "database_export_formats": "📦 <b>• EXPORT FORMAT SELECTION •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>🎯 CHOOSE YOUR EXPORT FORMAT</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Select the most appropriate format for your needs:</i>\n\n🗃️ <b>BSON Archive</b>\n   • MongoDB native binary format\n   • Fastest export and import\n   • Preserves all data types\n   • Best for database migrations\n\n🗜️ <b>Compressed BSON Archive</b>\n   • Same as BSON but compressed\n   • Smaller file size\n   • Ideal for storage and transfer\n   • Recommended for large databases\n\n📄 <b>JSON Export</b>\n   • Human-readable text format\n   • Easy to inspect and edit\n   • Compatible with many tools\n   • Good for data analysis\n\n📦 <b>Compressed JSON Export</b>\n   • JSON format with compression\n   • Balance of readability and size\n   • Suitable for archival purposes\n   • Easy to process programmatically\n\n<i>Choose your preferred format below:</i>", "database_export_progress": "⏳ <b>• EXPORT IN PROGRESS •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>🔄 PROCESSING YOUR DATABASE EXPORT</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Your database export is being processed...</i>\n\n📋 <b>Export Details:</b>\n   • Format: {export_format}\n   • Collections: {collection_count}\n   • Started: {start_time}\n   • Requested by: {username}\n\n🔄 <b>Current Status:</b>\n   {current_status}\n\n📊 <b>Progress:</b> {progress_percentage}%\n{progress_bar}\n\n⏱️ <b>Estimated Time:</b> {estimated_time}\n\n<i>Please wait while we securely prepare your export...</i>\n<i>You will be notified when the export is complete.</i>", "database_export_success": "✅ <b>• EXPORT COMPLETED SUCCESSFULLY •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>📥 YOUR DATABASE EXPORT IS READY</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Export completed successfully!</i>\n\n📄 <b>File Details:</b>\n   • Filename: {filename}\n   • Format: {export_format}\n   • File Size: {file_size_mb} MB\n   • Collections: {collection_count}\n\n🕐 <b>Timing:</b>\n   • Started: {start_time}\n   • Completed: {completion_time}\n   • Duration: {duration}\n\n🔒 <b>Security:</b>\n   • Secure download token generated\n   • File expires in 24 hours\n   • Download attempts: 3 maximum\n   • Audit trail maintained\n\n<i>Click the download button below to securely retrieve your export file.</i>", "database_export_failed": "❌ <b>• EXPORT FAILED •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>⚠️ EXPORT PROCESS ENCOUNTERED AN ERROR</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Unfortunately, your database export failed to complete.</i>\n\n📋 <b>Export Details:</b>\n   • Format: {export_format}\n   • Started: {start_time}\n   • Failed: {failure_time}\n   • Requested by: {username}\n\n❌ <b>Error Information:</b>\n   {error_message}\n\n🔧 <b>Possible Solutions:</b>\n   • Try a different export format\n   • Reduce the scope (select fewer collections)\n   • Check system resources and try again\n   • Contact support if the issue persists\n\n<i>All export attempts are logged for troubleshooting purposes.</i>", "database_export_history": "📋 <b>• EXPORT HISTORY •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>📊 YOUR RECENT DATABASE EXPORTS</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Review your export activity and download available files:</i>\n\n{export_list}\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>📈 SUMMARY STATISTICS</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n• Total Exports: {total_exports}\n• Successful: {successful_exports}\n• Failed: {failed_exports}\n• Total Data Exported: {total_size_mb} MB\n\n<i>Export files are automatically deleted after 24 hours for security.</i>", "database_export_statistics": "📊 <b>• EXPORT STATISTICS •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>🌐 SYSTEM-WIDE EXPORT METRICS</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n📈 <b>Overall Statistics:</b>\n   • Total Exports: {total_exports}\n   • Successful: {successful_exports}\n   • Failed: {failed_exports}\n   • Success Rate: {success_rate}%\n   • Active Exports: {active_exports}\n\n💽 <b>Data Volume:</b>\n   • Total Exported: {total_exported_mb} MB\n   • Average Export Size: {avg_export_size_mb} MB\n   • Largest Export: {largest_export_mb} MB\n\n👤 <b>Your Statistics:</b>\n   • Your Exports: {user_exports}\n   • Your Success Rate: {user_success_rate}%\n   • Your Data Exported: {user_exported_mb} MB\n\n🗃️ <b>Database Information:</b>\n   • Collections Available: {total_collections}\n   • Database Size: {database_size_mb} MB\n   • Export Formats: 4 available\n\n⚙️ <b>System Configuration:</b>\n   • File Retention: 24 hours\n   • Size Limit: 2 GB per export\n   • Rate Limit: 3 exports per hour\n   • Security: Token-based downloads\n\n<i>Statistics updated in real-time.</i>", "database_export_security": "🔒 <b>• EXPORT SECURITY OVERVIEW •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>🛡️ COMPREHENSIVE SECURITY MEASURES</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Your database exports are protected by multiple security layers:</i>\n\n🔐 <b>Access Control:</b>\n   • Owner-level privileges required\n   • Multi-factor authentication support\n   • Session-based security validation\n   • Real-time privilege verification\n\n🚦 <b>Rate Limiting:</b>\n   • Maximum 3 exports per hour\n   • Automatic cooldown periods\n   • Suspicious activity detection\n   • Progressive restriction enforcement\n\n📝 <b>Audit Logging:</b>\n   • Complete activity trail\n   • Security event monitoring\n   • Failed attempt tracking\n   • Compliance reporting\n\n🔗 <b>Secure Downloads:</b>\n   • Token-based authentication\n   • Time-limited access (2 hours)\n   • Maximum 3 download attempts\n   • Automatic token expiration\n\n🧹 <b>Data Protection:</b>\n   • Automatic file cleanup (24 hours)\n   • Encrypted temporary storage\n   • Secure file transmission\n   • No permanent data retention\n\n<i>All security measures are continuously monitored and logged.</i>"}