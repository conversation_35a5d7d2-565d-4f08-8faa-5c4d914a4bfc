# Button Template System

## Overview

The button template system provides a centralized way to manage all button texts used throughout the bot. Button texts are stored in the `buttons.json` template file, organized by functionality.

## Benefits

1. **Centralized Management**
   - All button texts are stored in a single file
   - Easy to update and maintain
   - Consistent formatting across the application

2. **Localization Ready**
   - Button texts can be easily translated
   - Multiple language support can be added without changing code

3. **Improved Code Organization**
   - Separation of content from presentation
   - Cleaner keyboard code that focuses on structure rather than content

## Usage

### Loading Button Texts

```python
from utils.template_helpers import format_text

# Load a button text from the buttons.json template file
button_text = format_text("buttons", "main_menu_wallet")
```

### Creating Buttons with Template Texts

```python
from aiogram.types import InlineKeyboardButton
from utils.template_helpers import format_text

# Create a button with text from the template
button = InlineKeyboardButton(
    text=format_text("buttons", "main_menu_wallet"),
    callback_data="view_balance"
)
```

## Button Categories

The button texts are organized into the following categories:

1. **Main Menu Buttons**
   - `main_menu_wallet`, `main_menu_shop`, etc.

2. **Balance Buttons**
   - `balance_add_funds`, `balance_verify_payment`, etc.

3. **Deposit Buttons**
   - `deposit_10`, `deposit_20`, `deposit_50`, etc.

4. **Transaction Buttons**
   - `transactions_back_wallet`, `transactions_back_home`, etc.

5. **Support Buttons**
   - `support_message`, `support_contact`, etc.

6. **Purchase Buttons**
   - `purchase_confirm`, `purchase_cancel`, etc.

7. **Cart Buttons**
   - `cart_checkout`, `cart_empty`, etc.

8. **Checkout Buttons**
   - `checkout_complete`, `checkout_cancel`, etc.

9. **Orders Buttons**
   - `orders_browse`, `orders_back_home`, etc.

10. **Shop Buttons**
    - `shop_categories`, `shop_all_products`, etc.

11. **Common Buttons**
    - `common_back`, `common_cancel`, `common_confirm`, etc.

## Adding New Buttons

To add a new button:

1. Add the button text to the `buttons.json` file with an appropriate key
2. Use `format_text("buttons", "your_button_key")` to load the text in your keyboard function

## Updating Button Texts

To update a button text:

1. Locate the button key in the `buttons.json` file
2. Update the text value
3. The change will be automatically reflected throughout the application

## Best Practices

1. Use consistent naming conventions for button keys
2. Group related buttons together in the template file
3. Include emojis in the template text, not in the code
4. Use the common buttons for frequently used actions
