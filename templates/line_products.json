{"line_product_welcome": "📋 <b>• LINE-BASED PRODUCTS •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>DIGITAL INVENTORY SYSTEM</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n🎯 <i>Each item is delivered individually from our digital inventory</i>\n\n▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n<i>Browse our collection of premium digital products</i>", "quantity_selection_header": "📋 <b>• QUANTITY SELECTION •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>SELECT QUANTITY</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>", "cart_line_item": "📋 <b>{product_name}</b>\n🔢 <b>Quantity:</b> <code>{quantity} items</code>\n💰 <b>Unit Price:</b> <code>${unit_price:.2f}</code>\n💵 <b>Total:</b> <code>${total_price:.2f}</code>", "cart_regular_item": "📄 <b>{product_name}</b>\n💰 <code>${price:.2f}</code>", "delivery_confirmation": "📋 <b>• DELIVERY CONFIRMATION •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>ORDER COMPLETED</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n✅ <b>Your order has been delivered successfully!</b>\n\n📋 <b>Product:</b> {product_name}\n🔢 <b>Quantity:</b> <code>{quantity} items</code>\n💰 <b>Unit Price:</b> <code>${unit_price:.2f}</code>\n💵 <b>Total Paid:</b> <code>${total_price:.2f}</code>\n🆔 <b>Order #:</b> <code>{order_number}</code>\n\n📤 <b>Delivery Method:</b> File attachment\n📝 <b>File Format:</b> Text file (.txt)\n\n▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n\nℹ️ <i>Your digital items are ready for use!</i>\n📞 <i>Contact support if you have any issues</i>", "delivery_error": "📋 <b>• DELIVERY ERROR •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>ORDER PROCESSING FAILED</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n❌ <b>We encountered an issue with your order</b>\n\n⚠️ <b>Error Type:</b> {error_type}\nℹ️ <b>Details:</b> {error_details}\n🆔 <b>Order #:</b> <code>{order_number}</code>\n\n▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n\nℹ️ <i>Our team has been notified and will resolve this issue</i>\n📞 <i>Please contact support with your order number for assistance</i>\n💰 <i>Any payments will be refunded if the issue cannot be resolved</i>", "stock_status": {"in_stock": "🟢 Available", "low_stock": "🟡 Low Stock", "out_of_stock": "🔴 Out of Stock", "unlimited": "♾️ Unlimited"}, "admin_inventory_upload": "📋 <b>• INVENTORY FILE UPLOAD •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>STEP 6: DIGITAL INVENTORY</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n📊 <b>Upload your inventory file containing the digital items:</b>\n\n📝 <b>File Requirements:</b>\n• Must be a .txt file\n• One item per line\n• UTF-8 encoding\n• No empty lines\n\n📋 <b>Example format:</b>\n<code>username1:password1:<EMAIL>\nusername2:password2:<EMAIL>\naccount3:pass3:<EMAIL></code>\n\n▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n\n✨ <i>Each line will become a sellable item in your inventory</i>", "admin_inventory_success": "📋 <b>• INVENTORY PROCESSED •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>FILE UPLOAD SUCCESSFUL</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n✅ <b>Your inventory file has been processed successfully!</b>\n\n📊 <b>Inventory Details:</b>\n📝 <b>Total Lines:</b> <code>{total_lines}</code>\n🟢 <b>Available Items:</b> <code>{available_lines}</code>\n📁 <b>File Path:</b> <code>{file_path}</code>\n\n▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n\n🎯 <i>Your line-based product is ready for sale!</i>", "admin_line_product_summary": "📋 <b>• LINE PRODUCT SUMMARY •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>PRODUCT OVERVIEW</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n📋 <b>Name:</b> {product_name}\nℹ️ <b>Description:</b> {description}\n💰 <b>Price per item:</b> <code>${line_price:.2f}</code>\n📦 <b>Available stock:</b> {available_lines} items\n🔢 <b>Max per order:</b> {max_quantity} items\n📝 <b>Item format:</b> <code>{preview_format}</code>\n\n▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n\n🎯 <i>Ready for customers to purchase individual items</i>", "shop_line_product_display": "📋 <b>• LINE-BASED PRODUCT •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>DIGITAL INVENTORY</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n📋 <b>{product_name}</b>\n\nℹ️ <b>Description:</b>\n<i>{description}</i>\n\n💰 <b>Price per item:</b> <code>${line_price:.2f}</code>\n📦 <b>Stock:</b> {stock_status} ({available_lines} items)\n🔢 <b>Max per order:</b> <code>{max_quantity} items</code>\n📝 <b>Item format:</b> <code>{preview_format}</code>\n\n▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n\n<i>Each item is delivered individually from our digital inventory</i>", "error_messages": {"invalid_file_type": "📋 <b>• INVALID FILE TYPE •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>FILE FORMAT ERROR</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\nPlease upload a .txt file containing your inventory.\nEach line should represent one sellable item.\n\nPlease try again.", "insufficient_stock": "📋 <b>• INSUFFICIENT STOCK •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>INVENTORY ERROR</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\nInsufficient stock. Available: {available} items\nRequested: {requested} items\n\nPlease try again.", "max_quantity_exceeded": "📋 <b>• QUANTITY LIMIT EXCEEDED •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>ORDER LIMIT ERROR</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\nMaximum {max_quantity} items per order\nRequested: {requested} items\n\nPlease try again.", "product_not_found": "📋 <b>• PRODUCT NOT FOUND •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>PRODUCT ERROR</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\nThe requested product could not be found.\n\nPlease try again.", "inventory_file_missing": "📋 <b>• INVENTORY FILE MISSING •</b>\n\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n<b>INVENTORY ERROR</b>\n<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\nInventory file not found or not accessible.\n\nPlease contact support."}, "buttons": {"select_quantity": "🔢 Select Quantity", "add_to_cart": "🛒 Add to Cart", "buy_now": "💰 Buy Now", "view_inventory": "📊 View Inventory", "upload_inventory": "📁 Upload Inventory", "back_to_product": "🔙 Back to Product", "continue_shopping": "🛍️ Continue Shopping"}}