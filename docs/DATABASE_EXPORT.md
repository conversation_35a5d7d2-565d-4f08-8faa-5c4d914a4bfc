# Database Export Functionality

## Overview

The Database Export functionality provides a comprehensive, secure system for administrators to export MongoDB database backups through the Telegram bot interface. This feature is designed with enterprise-level security, audit logging, and user experience in mind.

## Features

### 🔒 Security Features
- **Owner-only Access**: Only users with owner privileges can access database export functionality
- **Rate Limiting**: Maximum 3 exports per hour per admin to prevent abuse
- **Audit Logging**: Complete activity trail for all export operations
- **Token-based Downloads**: Secure download links with expiration and attempt limits
- **Suspicious Activity Detection**: Automatic detection of unusual export patterns
- **Input Validation**: Comprehensive validation of all export parameters

### 📦 Export Formats
- **BSON Archive**: MongoDB native binary format (fastest, preserves all data types)
- **Compressed BSON Archive**: BSON format with gzip compression (smaller file size)
- **JSON Export**: Human-readable text format (easy to inspect and edit)
- **Compressed JSON Export**: JSON format with compression (balance of readability and size)

### ⚙️ Advanced Options
- **Collection Filtering**: Export specific collections instead of entire database
- **Date Range Filtering**: Export data from specific time periods
- **Preset Configurations**: Quick selection for common export scenarios
- **Progress Tracking**: Real-time progress updates during export process

### 🛡️ Data Protection
- **Automatic Cleanup**: Export files deleted after 24 hours
- **Size Limits**: Maximum 2GB per export to prevent resource exhaustion
- **Secure Storage**: Temporary files stored in protected directory
- **Download Limits**: Maximum 3 download attempts per token

## Architecture

### Core Components

1. **DatabaseExporter** (`utils/database_export.py`)
   - Main export engine
   - Handles MongoDB dump operations
   - File compression and format conversion
   - Export lifecycle management

2. **DatabaseExportSecurity** (`utils/database_export_security.py`)
   - Security validation and access control
   - Rate limiting and suspicious activity detection
   - Comprehensive audit logging
   - Security statistics and reporting

3. **SecureDownloadManager** (`utils/secure_download.py`)
   - Token-based download system
   - Secure file delivery
   - Download attempt tracking
   - Automatic token expiration

4. **ExportProgressTracker** (`utils/export_progress_tracker.py`)
   - Real-time progress monitoring
   - User-friendly status updates
   - Performance metrics collection
   - Progress message formatting

5. **DatabaseExportScheduler** (`utils/database_export_scheduler.py`)
   - Automated cleanup scheduling
   - Maintenance task management
   - Statistics reporting
   - System health monitoring

### Admin Interface Integration

The functionality is integrated into the existing admin panel through:
- **Owner Advanced Menu**: New "Database Export" button
- **Callback Handlers**: Complete set of handlers in `handlers/sys_db.py`
- **Template Integration**: UI messages in `templates/owner.json`
- **Button Templates**: Button text in `templates/buttons.json`

## Usage

### Accessing Database Export

1. Use `/admin_panel` command as an owner
2. Navigate to "Advanced Features"
3. Select "Database Export"

### Creating an Export

1. **Quick Export**: Choose a format and export entire database
2. **Advanced Export**: Configure collections, date ranges, and other options
3. **Monitor Progress**: Real-time updates during export process
4. **Secure Download**: Use token-based download system

### Export Options

#### Format Selection
- Choose based on your needs (speed vs. readability)
- Consider file size requirements
- Plan for import compatibility

#### Collection Filtering
- **User Data**: users, admins, banned_users
- **Commerce Data**: products, orders, transactions, payments, carts, categories
- **System Data**: settings, announcements, support_messages
- **Custom Selection**: Choose specific collections

#### Advanced Configuration
- Date range filtering for time-based exports
- Custom export parameters
- Progress tracking preferences

## Security Model

### Access Control
- Owner-level privileges required
- Real-time privilege verification
- Session-based security validation

### Rate Limiting
- 3 exports per hour per admin
- Progressive restriction enforcement
- Automatic cooldown periods

### Audit Trail
- Complete activity logging
- Security event monitoring
- Failed attempt tracking
- Compliance reporting

### Download Security
- Token-based authentication
- 2-hour token expiration
- Maximum 3 download attempts
- Secure file transmission

## Configuration

### Environment Variables
- `MONGO_URI`: MongoDB connection string
- `MONGO_DB`: Database name to export

### System Limits
- **Max Export Size**: 2GB per export
- **File Retention**: 24 hours
- **Rate Limit**: 3 exports per hour
- **Download Attempts**: 3 per token
- **Token Expiration**: 2 hours

### Directories
- **Export Storage**: `temp/database_exports/`
- **Security Logs**: `__pycache__/db_export_security.log`
- **Audit Data**: `__pycache__/db_export_audit.json`
- **Download Tokens**: `__pycache__/download_tokens.json`

## Monitoring and Maintenance

### Automated Tasks
- **Hourly Cleanup**: Remove expired export files
- **Daily Audit**: Clean old audit logs and history
- **Weekly Reports**: Generate statistics summaries

### Manual Operations
- **Force Cleanup**: Immediate cleanup of expired files
- **Statistics Review**: View export and security metrics
- **Security Audit**: Review suspicious activities

### Health Checks
- Export success rates
- System resource usage
- Security event frequency
- User activity patterns

## Troubleshooting

### Common Issues

1. **Export Fails to Start**
   - Check owner privileges
   - Verify rate limits not exceeded
   - Review security validation

2. **Export Process Fails**
   - Check MongoDB connectivity
   - Verify mongodump availability
   - Review system resources

3. **Download Issues**
   - Verify token validity
   - Check file expiration
   - Confirm download attempts

4. **Performance Issues**
   - Monitor database size
   - Check system resources
   - Review export parameters

### Error Messages
- **Security Validation Failed**: Check user privileges and rate limits
- **Export Process Failed**: Review MongoDB connectivity and tools
- **File Too Large**: Reduce scope or use compression
- **Token Expired**: Generate new download token

## Testing

### Unit Tests
Run comprehensive tests with:
```bash
cd testbots
python -m pytest tests/test_database_export.py -v
```

### Integration Tests
Validate complete functionality:
```bash
cd testbots
python scripts/test_database_export_integration.py
```

### Manual Testing
1. Test export creation with different formats
2. Verify security restrictions work
3. Test download token system
4. Validate cleanup processes

## Dependencies

### Required Packages
- `pymongo`: MongoDB driver
- `motor`: Async MongoDB driver
- `aiogram`: Telegram bot framework
- `schedule`: Task scheduling
- `pathlib`: File path handling

### External Tools
- **mongodump**: MongoDB export utility (for BSON exports)
- **gzip**: Compression utility (for compressed formats)

### System Requirements
- Python 3.8+
- MongoDB 4.0+
- Sufficient disk space for temporary files
- Network access to MongoDB instance

## Best Practices

### Security
- Regularly review audit logs
- Monitor for suspicious activities
- Keep export files minimal and focused
- Use compressed formats for large exports

### Performance
- Export during low-traffic periods
- Use collection filtering for large databases
- Monitor system resources during exports
- Clean up old files regularly

### Maintenance
- Review export statistics weekly
- Update security configurations as needed
- Test backup and restore procedures
- Document export procedures for team

## Future Enhancements

### Planned Features
- Incremental export support
- Export scheduling
- Email notifications
- Export templates
- Multi-format exports
- Cloud storage integration

### Potential Improvements
- Streaming exports for very large databases
- Export encryption
- Custom compression algorithms
- Export verification
- Automated testing integration
