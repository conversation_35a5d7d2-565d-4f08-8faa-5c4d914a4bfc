# Enhanced Bonus System Documentation

## Overview

The enhanced bonus system now supports both **percentage-based** and **fixed-amount** bonuses with a comprehensive admin interface for easy management. This system maintains full backward compatibility while adding powerful new features.

## Key Features

### ✨ **Dual Bonus Types**
- **Percentage Bonuses**: Traditional percentage of deposit amount (e.g., 15% of $100 = $15)
- **Fixed Amount Bonuses**: Fixed bonus regardless of deposit size (e.g., $20 for any qualifying deposit)

### 🎛️ **Complete Admin Interface**
- Create, edit, and delete bonus tiers
- Switch between percentage and fixed bonus types
- Toggle tier active/inactive status
- Real-time bonus calculation preview
- Comprehensive tier management

### 🔄 **Backward Compatibility**
- Existing percentage-based tiers continue to work
- Automatic migration of legacy tier data
- No disruption to current bonus operations

## Admin Interface Usage

### Accessing Bonus Management

1. **Navigate to Admin Panel** → **🎁 Bonus Management**
2. **View All Tiers** to see existing bonus configurations
3. **Add New Tier** to create percentage or fixed bonus tiers

### Creating Bonus Tiers

#### **Percentage-Based Tier**
```
Step 1: Set threshold (e.g., $50.00)
Step 2: Choose "Percentage Bonus"
Step 3: Enter percentage (e.g., 15 for 15%)
Step 4: Add description (optional)
Step 5: Confirm creation
```

**Example**: $50 threshold with 15% bonus
- User deposits $50 → receives $57.50 ($7.50 bonus)
- User deposits $100 → receives $115.00 ($15.00 bonus)

#### **Fixed-Amount Tier**
```
Step 1: Set threshold (e.g., $25.00)
Step 2: Choose "Fixed Amount Bonus"
Step 3: Enter fixed amount (e.g., 10.00 for $10)
Step 4: Add description (optional)
Step 5: Confirm creation
```

**Example**: $25 threshold with $10 fixed bonus
- User deposits $25 → receives $35.00 ($10.00 bonus)
- User deposits $100 → receives $110.00 ($10.00 bonus)

### Editing Existing Tiers

1. **Select tier** from the tier list
2. **Choose Edit** from tier details
3. **Select what to edit**:
   - 💰 **Edit Threshold**: Change minimum deposit amount
   - 🎯 **Edit Bonus Type**: Switch between percentage/fixed
   - 📈 **Edit Percentage**: Change percentage value (percentage tiers)
   - 💵 **Edit Fixed Amount**: Change fixed bonus amount (fixed tiers)
   - 📝 **Edit Description**: Update tier description

### Managing Tier Status

- **🔄 Toggle**: Activate/deactivate tiers without deleting
- **🗑️ Delete**: Permanently remove unused tiers
- **📊 View Details**: See complete tier configuration

## Bonus Calculation Logic

### Tier Priority Rules

1. **Highest Threshold Wins**: The tier with the highest threshold that the deposit qualifies for is used
2. **Active Tiers Only**: Inactive tiers are ignored in calculations
3. **Single Tier Application**: Only one tier is applied per deposit

### Calculation Examples

**Scenario**: Three active tiers exist
- Tier A: $20 → 10% bonus
- Tier B: $50 → $15 fixed bonus  
- Tier C: $100 → 20% bonus

**Deposit Calculations**:
- $15 deposit → No bonus (below all thresholds)
- $25 deposit → $2.50 bonus (10% from Tier A)
- $60 deposit → $15.00 bonus (fixed from Tier B)
- $120 deposit → $24.00 bonus (20% from Tier C)

## Database Schema

### BonusTier Model
```python
{
    "_id": ObjectId,
    "threshold": float,              # Minimum deposit amount
    "bonus_type": str,               # "percentage" or "fixed"
    "bonus_percentage": float,       # For percentage type (0.15 = 15%)
    "bonus_fixed_amount": float,     # For fixed type ($10.00)
    "is_active": bool,               # Whether tier is active
    "description": str,              # Optional description
    "created_at": datetime,          # Creation timestamp
    "updated_at": datetime,          # Last update timestamp
    "created_by": int                # Admin user ID
}
```

## API Functions

### Creating Tiers

#### **Percentage-Based Tier**
```python
from database.operations import create_bonus_tier

tier = create_bonus_tier(
    threshold=50.0,
    bonus_percentage=0.15,  # 15%
    bonus_type="percentage",
    description="15% bonus for $50+ deposits",
    is_active=True
)
```

#### **Fixed-Amount Tier**
```python
from database.operations import create_fixed_bonus_tier

tier = create_fixed_bonus_tier(
    threshold=25.0,
    fixed_amount=10.0,  # $10
    description="$10 bonus for $25+ deposits",
    is_active=True
)
```

### Updating Tiers

#### **Change Bonus Type**
```python
from database.operations import update_bonus_tier

# Change from percentage to fixed
success = update_bonus_tier(
    tier_id="507f1f77bcf86cd799439011",
    bonus_type="fixed",
    bonus_fixed_amount=20.0
)

# Change from fixed to percentage
success = update_bonus_tier(
    tier_id="507f1f77bcf86cd799439011",
    bonus_type="percentage",
    bonus_percentage=0.25
)
```

#### **Update Values**
```python
# Update threshold
update_bonus_tier(tier_id, threshold=75.0)

# Update percentage
update_bonus_tier(tier_id, bonus_percentage=0.20)

# Update fixed amount
update_bonus_tier(tier_id, bonus_fixed_amount=25.0)

# Toggle status
update_bonus_tier(tier_id, is_active=False)
```

### Calculating Bonuses

```python
from database.operations import calculate_bonus_amount

result = calculate_bonus_amount(100.0)
# Returns:
# {
#     "bonus_amount": 15.0,
#     "total_amount": 115.0,
#     "tier_used": {...},
#     "original_amount": 100.0
# }
```

### Applying Bonuses to Wallets

```python
from utils.bonus_calculator import BonusCalculator

result = BonusCalculator.apply_bonus_to_wallet(
    user_id=12345,
    deposit_amount=100.0,
    track_id="DEPOSIT_001"
)
# Returns:
# {
#     "success": True,
#     "bonus_amount": 15.0,
#     "new_balance": 215.0,
#     "tier_used": {...},
#     "transaction_id": "...",
#     "message": "Bonus applied: +$15.00"
# }
```

## Migration and Compatibility

### Automatic Migration

Existing percentage-based tiers are automatically compatible:
- `bonus_type` defaults to "percentage" if not specified
- `bonus_percentage` field continues to work as before
- No manual migration required

### Legacy Support

The system maintains full backward compatibility:
- Old percentage calculations continue to work
- Existing admin interfaces remain functional
- No breaking changes to existing code

## Testing

### Running Tests

```bash
# Test admin interface functionality
python3 test_admin_interface.py

# Run comprehensive test suite
python3 test_comprehensive_bonus_system.py

# Test specific $20 tier setup
python3 create_20_dollar_tier.py
```

### Test Coverage

- ✅ Admin interface CRUD operations
- ✅ Percentage and fixed bonus calculations
- ✅ Edge cases and error handling
- ✅ Tier priority and selection rules
- ✅ Backward compatibility
- ✅ Bonus application to user wallets

## Troubleshooting

### Common Issues

**Issue**: Tier not applying bonus
- **Check**: Tier is active (`is_active: true`)
- **Check**: Deposit amount meets threshold
- **Check**: No higher-priority tier exists

**Issue**: Wrong bonus amount calculated
- **Check**: Bonus type is correct (percentage vs fixed)
- **Check**: Bonus value is set correctly
- **Check**: Multiple tiers aren't conflicting

**Issue**: Admin interface not showing new options
- **Check**: Handlers are properly registered
- **Check**: States are updated in states.py
- **Check**: User has owner/admin privileges

### Debug Commands

```python
# List all tiers with details
from database.operations import get_all_bonus_tiers
tiers = get_all_bonus_tiers()
for tier in tiers:
    print(f"${tier['threshold']:.2f} -> {tier.get('bonus_type', 'percentage')}")

# Test specific calculation
from database.operations import calculate_bonus_amount
result = calculate_bonus_amount(50.0)
print(f"Bonus: ${result['bonus_amount']:.2f}")
```

## Performance Considerations

- **Database Indexing**: Threshold field is indexed for fast tier lookup
- **Caching**: Tier data is cached for frequent calculations
- **Async Support**: All operations support async for better performance
- **Minimal Overhead**: New fields don't impact existing percentage calculations

## Security Notes

- **Admin Only**: Bonus management requires owner/admin privileges
- **Input Validation**: All inputs are validated before database operations
- **Transaction Safety**: Bonus applications use database transactions
- **Audit Trail**: All tier changes are logged with timestamps and user IDs
