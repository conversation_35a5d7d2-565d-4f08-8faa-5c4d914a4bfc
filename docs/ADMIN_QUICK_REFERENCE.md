# Bonus System Admin Quick Reference

## 🚀 Quick Start

### Access Bonus Management
1. Go to **Admin Panel** → **🎁 Bonus Management**
2. View existing tiers or create new ones

### Create New Bonus Tier

#### **Percentage Bonus** (e.g., 15% of deposit)
```
Threshold: 50.00
Type: Percentage Bonus
Value: 15 (for 15%)
Description: 15% bonus for deposits $50+
```
**Result**: $50 deposit → $57.50 total ($7.50 bonus)

#### **Fixed Bonus** (e.g., $10 flat bonus)
```
Threshold: 25.00
Type: Fixed Amount Bonus
Value: 10.00 (for $10)
Description: $10 bonus for deposits $25+
```
**Result**: $25 deposit → $35.00 total ($10.00 bonus)

## 📊 Common Tier Configurations

### **Starter Tier** - Encourage small deposits
- **Threshold**: $10.00
- **Type**: Fixed Amount
- **Bonus**: $5.00
- **Use Case**: Get new users started

### **Standard Tier** - Regular percentage bonus
- **Threshold**: $50.00
- **Type**: Percentage
- **Bonus**: 10%
- **Use Case**: Standard reward for medium deposits

### **Premium Tier** - High-value fixed bonus
- **Threshold**: $200.00
- **Type**: Fixed Amount
- **Bonus**: $50.00
- **Use Case**: Reward large deposits with substantial bonus

### **VIP Tier** - High percentage for big spenders
- **Threshold**: $500.00
- **Type**: Percentage
- **Bonus**: 20%
- **Use Case**: Premium rewards for VIP customers

## 🎯 Bonus Type Comparison

| Aspect | Percentage Bonus | Fixed Amount Bonus |
|--------|------------------|-------------------|
| **Calculation** | % of deposit amount | Same amount always |
| **Scales with deposit** | ✅ Yes | ❌ No |
| **Predictable cost** | ❌ No | ✅ Yes |
| **User incentive** | Higher deposits = higher bonus | Threshold-based incentive |
| **Best for** | Rewarding large deposits | Encouraging minimum deposits |

## 🔧 Management Actions

### **View Tier Details**
- Click any tier from the list
- See complete configuration
- Check active/inactive status

### **Edit Existing Tier**
1. Select tier → **✏️ Edit**
2. Choose what to modify:
   - **💰 Threshold**: Change minimum deposit
   - **🎯 Bonus Type**: Switch percentage ↔ fixed
   - **📈 Percentage**: Update percentage value
   - **💵 Fixed Amount**: Update fixed bonus
   - **📝 Description**: Change description

### **Toggle Tier Status**
- **🔄 Toggle**: Activate/deactivate without deleting
- Inactive tiers don't apply to new deposits
- Useful for temporary promotions

### **Delete Tier**
- **🗑️ Delete**: Permanently remove tier
- Confirm deletion to prevent accidents
- Cannot be undone

## ⚡ Quick Commands

### **Disable All Bonuses** (Emergency)
1. Go to **📊 View All Tiers**
2. For each active tier: **🔄 Toggle** to deactivate
3. No bonuses will be applied until reactivated

### **Create Promotion Tier**
1. **➕ Add New Tier**
2. Set low threshold (e.g., $20)
3. Set attractive bonus (e.g., $15 fixed)
4. Add description: "Limited time promotion"
5. Remember to deactivate/delete when promotion ends

### **Test Bonus Calculation**
Use the test scripts:
```bash
python3 test_admin_interface.py
```

## 🎨 Display Examples

### **Tier List Display**
```
🟢 $10.00 → $5.00 fixed
🟢 $50.00 → 10.0%
🔴 $100.00 → $25.00 fixed (inactive)
🟢 $200.00 → 15.0%
```

### **Tier Details Display**
```
💰 Threshold: $50.00
🎁 Bonus: 10.0% bonus
📊 Type: Percentage
🔄 Status: 🟢 Active
📝 Description: Standard 10% bonus tier
```

## 🚨 Important Rules

### **Tier Priority**
- **Highest qualifying threshold wins**
- Only ONE tier applies per deposit
- Example: $100 deposit with tiers at $25, $50, $75
  - Uses $75 tier (highest qualifying)

### **Threshold Logic**
- Deposit must be **≥ threshold** to qualify
- $49.99 deposit does NOT qualify for $50 tier
- $50.00 deposit DOES qualify for $50 tier

### **Type Switching**
- Can change percentage tier to fixed tier
- Can change fixed tier to percentage tier
- Previous bonus value is cleared when switching

## 🔍 Troubleshooting

### **Bonus Not Applied**
1. Check tier is **🟢 Active**
2. Verify deposit meets **threshold**
3. Confirm no **higher tier** exists
4. Check **bonus calculation** logic

### **Wrong Bonus Amount**
1. Verify **bonus type** (percentage vs fixed)
2. Check **bonus value** is correct
3. Ensure **tier priority** is as expected

### **Admin Interface Issues**
1. Confirm **owner/admin privileges**
2. Check **handlers are registered**
3. Verify **states are updated**

## 📈 Best Practices

### **Tier Structure**
- Start with 2-3 tiers maximum
- Leave gaps between thresholds ($25, $75, $200)
- Mix percentage and fixed bonuses strategically

### **Bonus Values**
- Keep percentage bonuses under 25%
- Make fixed bonuses meaningful but sustainable
- Consider your profit margins

### **Descriptions**
- Use clear, descriptive names
- Include promotion end dates if applicable
- Mention any special conditions

### **Testing**
- Always test new tiers before going live
- Use test scripts to verify calculations
- Monitor bonus costs after implementation

## 🎯 Example Tier Setups

### **Conservative Setup** (Low bonus costs)
```
Tier 1: $25 → $3 fixed
Tier 2: $100 → 5%
Tier 3: $500 → $40 fixed
```

### **Aggressive Setup** (High user incentive)
```
Tier 1: $10 → $8 fixed
Tier 2: $50 → 15%
Tier 3: $200 → 20%
```

### **Hybrid Setup** (Balanced approach)
```
Tier 1: $20 → $10 fixed
Tier 2: $75 → 12%
Tier 3: $300 → $75 fixed
```

## 📞 Support

If you encounter issues:
1. Check this quick reference
2. Run test scripts to verify functionality
3. Review the detailed documentation
4. Check system logs for errors

Remember: Changes take effect immediately for new deposits!
