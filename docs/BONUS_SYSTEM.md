# Bonus Reward System Documentation

## Overview

The Bonus Reward System is a feature that automatically rewards users with additional credits based on their purchase amounts. When a user makes a purchase that meets or exceeds a configured threshold, they receive a bonus percentage of their purchase amount added to their wallet balance.

## Features

### Core Functionality
- **Threshold-based Rewards**: Configure minimum purchase amounts to trigger bonuses
- **Percentage-based Bonuses**: Set bonus percentages (e.g., 10%, 20%, 30%)
- **Multiple Tiers**: Support for multiple bonus tiers with different thresholds
- **Active/Inactive Status**: Enable or disable bonus tiers as needed
- **Automatic Application**: Bonuses are automatically calculated and applied during purchase

### Admin Interface
- **Tier Management**: Create, edit, delete, and toggle bonus tiers
- **Real-time Configuration**: Changes take effect immediately
- **Validation**: Prevents duplicate thresholds and invalid configurations
- **Audit Trail**: Track who created and modified bonus tiers

## How It Works

### Example Behavior
If an admin configures:
- **Tier 1**: $50 threshold → 10% bonus
- **Tier 2**: $100 threshold → 20% bonus

Then:
- User pays $30 → receives $30 (no bonus, below threshold)
- User pays $50 → receives $55 ($50 + $5 bonus)
- User pays $100 → receives $120 ($100 + $20 bonus)
- User pays $150 → receives $180 ($150 + $30 bonus using 20% tier)

### Tier Selection Logic
- The system finds all active tiers where the purchase amount meets or exceeds the threshold
- The tier with the **highest threshold** that the purchase qualifies for is selected
- Only one bonus tier is applied per purchase

## Database Schema

### BonusTier Collection
```javascript
{
  "_id": ObjectId,
  "threshold": Number,           // Minimum purchase amount (e.g., 50.0)
  "bonus_percentage": Number,    // Bonus percentage as decimal (e.g., 0.10 for 10%)
  "is_active": Boolean,          // Whether this tier is currently active
  "description": String,         // Optional description
  "created_at": Date,           // When the tier was created
  "updated_at": Date,           // When the tier was last modified
  "created_by": Number          // User ID of the admin who created it
}
```

### Transaction Records
Bonus applications create transaction records with:
- `transaction_type`: "bonus"
- `amount`: The bonus amount credited
- `bonus_tier_id`: Reference to the tier that was applied
- `original_purchase_amount`: The original purchase amount

## API Reference

### Database Operations

#### Creating Bonus Tiers
```python
from database.operations import create_bonus_tier

tier = create_bonus_tier(
    threshold=100.0,
    bonus_percentage=0.15,  # 15%
    created_by=admin_user_id,
    description="Premium tier bonus",
    is_active=True
)
```

#### Retrieving Bonus Tiers
```python
from database.operations import get_all_bonus_tiers, get_applicable_bonus_tier

# Get all tiers
all_tiers = get_all_bonus_tiers()

# Get only active tiers
active_tiers = get_all_bonus_tiers(active_only=True)

# Get applicable tier for a purchase amount
tier = get_applicable_bonus_tier(150.0)
```

#### Updating Bonus Tiers
```python
from database.operations import update_bonus_tier

success = update_bonus_tier(
    tier_id="507f1f77bcf86cd799439011",
    threshold=120.0,
    bonus_percentage=0.25,
    is_active=True,
    description="Updated premium tier"
)
```

### Bonus Calculator

#### Calculate Bonus Preview
```python
from utils.bonus_calculator import BonusCalculator

preview = BonusCalculator.get_bonus_preview(100.0)
# Returns: {
#   "has_bonus": True,
#   "bonus_amount": 15.0,
#   "total_amount": 115.0,
#   "bonus_percentage": 15.0,
#   "tier_threshold": 100.0,
#   "preview_text": "🎉 Bonus: +$15.00 (15.0%)"
# }
```

#### Apply Bonus to Wallet
```python
from utils.bonus_calculator import BonusCalculator

result = BonusCalculator.apply_bonus_to_wallet(
    user_id=123,
    purchase_amount=100.0,
    order_id="ORDER123"
)
# Returns: {
#   "success": True,
#   "bonus_amount": 15.0,
#   "new_balance": 165.0,
#   "tier_used": {...},
#   "transaction_id": "..."
# }
```

## Admin Interface Usage

### Accessing Bonus Management
1. Log in as an admin or owner
2. Go to Admin Panel
3. Select "🎁 Bonus Management"

### Creating a New Bonus Tier
1. Click "➕ Add New Tier"
2. Enter the minimum purchase threshold (e.g., 50.00)
3. Enter the bonus percentage (e.g., 10 for 10%)
4. Optionally add a description
5. Confirm the tier creation

### Managing Existing Tiers
1. Click "📊 View All Tiers"
2. Select a tier to view details
3. Use the action buttons to:
   - ✏️ Edit tier properties
   - 🔄 Toggle active/inactive status
   - 🗑️ Delete the tier

### Best Practices
- Start with conservative bonus percentages (5-15%)
- Use clear, descriptive names for tiers
- Test with small amounts before going live
- Monitor bonus usage and adjust as needed
- Disable rather than delete tiers to preserve history

## Integration Points

### Purchase Flow Integration
The bonus system is integrated into the purchase confirmation process in `handlers/products.py`:

1. **After successful purchase**: Bonus calculation is triggered
2. **Bonus application**: Credits are added to user wallet
3. **Transaction logging**: Bonus transaction is recorded
4. **User notification**: Success message includes bonus information

### Admin Interface Integration
- Added to admin keyboard in `keyboards/admin_kb.py`
- Registered in `handlers/register_handlers.py`
- Templates added to `templates/admin.json`

## Migration and Setup

### Running the Migration
```bash
cd testbots
python migrations/bonus_system_migration.py migrate
```

### Checking Migration Status
```bash
python migrations/bonus_system_migration.py status
```

### Rolling Back (if needed)
```bash
python migrations/bonus_system_migration.py rollback --force
```

## Testing

### Running Unit Tests
```bash
cd testbots
python -m pytest tests/test_bonus_system.py -v
```

### Manual Testing Checklist
1. ✅ Create bonus tiers via admin interface
2. ✅ Make purchases that qualify for bonuses
3. ✅ Verify bonus amounts are calculated correctly
4. ✅ Check that bonuses are added to wallet balance
5. ✅ Confirm transaction records are created
6. ✅ Test tier activation/deactivation
7. ✅ Verify highest tier selection logic
8. ✅ Test edge cases (exact threshold amounts)

## Troubleshooting

### Common Issues

#### Bonus Not Applied
- Check if bonus tiers are active
- Verify purchase amount meets threshold
- Check database connection
- Review error logs

#### Incorrect Bonus Amount
- Verify tier configuration (percentage as decimal)
- Check tier selection logic
- Ensure no duplicate thresholds

#### Admin Interface Issues
- Verify admin permissions
- Check handler registration
- Review template configuration

### Logging
The system logs bonus-related activities:
- Bonus tier creation/modification
- Bonus calculations and applications
- Error conditions and failures

Check logs in the application's logging system for detailed information.

## Security Considerations

- Only admins and owners can manage bonus tiers
- Input validation prevents invalid configurations
- Transaction integrity is maintained
- Audit trail tracks all modifications
- Sandbox mode bypasses bonus application for testing

## Performance Notes

- Bonus calculations are performed synchronously during purchase
- Database queries are optimized with proper indexing
- Minimal impact on purchase flow performance
- Caching could be added for high-volume scenarios
