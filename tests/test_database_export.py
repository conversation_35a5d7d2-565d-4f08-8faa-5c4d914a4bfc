"""
Comprehensive Tests for Database Export Functionality
Tests security validation, file handling, error scenarios, and admin integration.
"""

import pytest
import asyncio
import tempfile
import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import uuid

# Import the modules to test
from utils.database_export import DatabaseExporter, create_database_export, get_export_formats
from utils.database_export_security import DatabaseExportSecurity, validate_export_request
from utils.secure_download import SecureDownloadManager, create_download_token
from utils.export_progress_tracker import ExportProgressTracker, ExportStatus

class TestDatabaseExporter:
    """Test the core database export functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.exporter = DatabaseExporter()
        self.test_user_id = 12345
        self.test_username = "test_admin"
        
    def test_export_formats_available(self):
        """Test that all expected export formats are available."""
        formats = get_export_formats()
        
        expected_formats = ["bson_archive", "bson_compressed", "json", "json_compressed"]
        assert all(fmt in formats for fmt in expected_formats)
        
        for format_key, format_info in formats.items():
            assert "extension" in format_info
            assert "description" in format_info
            assert "mongodump_args" in format_info
            assert "compress" in format_info
    
    def test_generate_export_filename(self):
        """Test export filename generation."""
        # Test with no collections
        filename = self.exporter._generate_export_filename("bson_archive")
        assert filename.endswith(".archive")
        assert "full_db" in filename
        
        # Test with few collections
        collections = ["users", "products"]
        filename = self.exporter._generate_export_filename("json", collections)
        assert filename.endswith(".json")
        assert "users_products" in filename
        
        # Test with many collections
        many_collections = [f"collection_{i}" for i in range(10)]
        filename = self.exporter._generate_export_filename("bson_compressed", many_collections)
        assert filename.endswith(".archive.gz")
        assert "and_more" in filename
    
    def test_parse_mongo_uri(self):
        """Test MongoDB URI parsing."""
        # Mock the MONGO_URI
        with patch('utils.database_export.MONGO_URI', 'mongodb://localhost:27017/test_db'):
            params = self.exporter._parse_mongo_uri()
            assert params["host"] == "localhost"
            assert params["port"] == 27017
            assert params["database"] == "telegram_shop_bot1"  # From DB_NAME
    
    def test_check_mongodump_available(self):
        """Test mongodump availability check."""
        # Mock successful mongodump check
        with patch('subprocess.run') as mock_run:
            mock_run.return_value.returncode = 0
            assert self.exporter._check_mongodump_available() == True
            
        # Mock failed mongodump check
        with patch('subprocess.run') as mock_run:
            mock_run.side_effect = FileNotFoundError()
            assert self.exporter._check_mongodump_available() == False
    
    @pytest.mark.asyncio
    async def test_create_export_validation(self):
        """Test export creation with validation."""
        # Mock security validation to pass
        with patch('utils.database_export.validate_export_request') as mock_validate:
            mock_validate.return_value = {"allowed": True}
            
            # Mock mongodump availability
            with patch.object(self.exporter, '_check_mongodump_available', return_value=True):
                # Mock the actual export process
                with patch.object(self.exporter, '_run_mongodump', return_value=True):
                    # Mock file creation
                    with patch('pathlib.Path.exists', return_value=True):
                        with patch('pathlib.Path.stat') as mock_stat:
                            mock_stat.return_value.st_size = 1024 * 1024  # 1MB
                            
                            result = await self.exporter.create_export(
                                self.test_user_id, 
                                self.test_username, 
                                "json"
                            )
                            
                            assert result["status"] == "completed"
                            assert result["user_id"] == self.test_user_id
                            assert result["format"] == "json"
    
    @pytest.mark.asyncio
    async def test_create_export_security_failure(self):
        """Test export creation with security validation failure."""
        with patch('utils.database_export.validate_export_request') as mock_validate:
            mock_validate.return_value = {
                "allowed": False, 
                "reason": "Rate limit exceeded"
            }
            
            with pytest.raises(Exception) as exc_info:
                await self.exporter.create_export(
                    self.test_user_id, 
                    self.test_username, 
                    "json"
                )
            
            assert "Security validation failed" in str(exc_info.value)
    
    def test_export_statistics(self):
        """Test export statistics generation."""
        # Add some mock export history
        self.exporter._export_history = [
            {"status": "completed", "file_size": 1024},
            {"status": "completed", "file_size": 2048},
            {"status": "failed", "file_size": 0}
        ]
        
        stats = self.exporter.get_export_statistics()
        
        assert stats["total_exports"] == 3
        assert stats["successful_exports"] == 2
        assert stats["failed_exports"] == 1
        assert stats["success_rate"] == 66.66666666666667
        assert stats["total_exported_size"] == 3072


class TestDatabaseExportSecurity:
    """Test the security module for database exports."""
    
    def setup_method(self):
        """Set up test environment."""
        self.security = DatabaseExportSecurity()
        self.test_user_id = 12345
        self.test_username = "test_admin"
    
    def test_validate_export_request_unauthorized(self):
        """Test validation with unauthorized user."""
        with patch('utils.database_export_security.is_privileged', return_value=False):
            result = self.security.validate_export_request(
                self.test_user_id, 
                self.test_username, 
                {"format": "json"}
            )
            
            assert result["allowed"] == False
            assert "Insufficient privileges" in result["reason"]
            assert result["security_level"] == "critical"
    
    def test_validate_export_request_rate_limit(self):
        """Test validation with rate limit exceeded."""
        with patch('utils.database_export_security.is_privileged', return_value=True):
            # Simulate rate limit exceeded
            with patch.object(self.security, '_check_rate_limits') as mock_rate_check:
                mock_rate_check.return_value = {
                    "allowed": False,
                    "reason": "Too many attempts",
                    "limit_type": "hourly_exports"
                }
                
                result = self.security.validate_export_request(
                    self.test_user_id, 
                    self.test_username, 
                    {"format": "json"}
                )
                
                assert result["allowed"] == False
                assert "Too many attempts" in result["reason"]
                assert result["security_level"] == "high"
    
    def test_validate_export_parameters(self):
        """Test export parameter validation."""
        # Valid parameters
        valid_params = {
            "format": "json",
            "collections": ["users", "products"],
            "date_filter": {"start_date": datetime.now()}
        }
        
        result = self.security._validate_export_parameters(valid_params)
        assert result["valid"] == True
        
        # Invalid format
        invalid_params = {"format": "invalid_format"}
        result = self.security._validate_export_parameters(invalid_params)
        assert result["valid"] == False
        assert "Invalid export format" in result["reason"]
        
        # Invalid collections
        invalid_collections = {"collections": "not_a_list"}
        result = self.security._validate_export_parameters(invalid_collections)
        assert result["valid"] == False
        assert "Collections must be a list" in result["reason"]
    
    def test_suspicious_activity_detection(self):
        """Test suspicious activity detection."""
        request_data = {"collections": ["admins", "settings", "payments"]}
        
        # Should detect suspicious activity for sensitive collections
        result = self.security._detect_suspicious_activity(self.test_user_id, request_data)
        # Note: This might be False initially, but after multiple calls it should become True
        
        # Simulate multiple rapid requests
        for _ in range(5):
            self.security._record_export_attempt(self.test_user_id)
        
        result = self.security._detect_suspicious_activity(self.test_user_id, request_data)
        # Should detect suspicious activity after multiple attempts
    
    def test_security_statistics(self):
        """Test security statistics generation."""
        # Add some mock audit entries
        self.security.audit_log = [
            {
                "timestamp": datetime.now().isoformat(),
                "event_type": "export_success",
                "severity": "normal"
            },
            {
                "timestamp": datetime.now().isoformat(),
                "event_type": "unauthorized_export_attempt",
                "severity": "critical"
            }
        ]
        
        stats = self.security.get_security_statistics()
        
        assert "total_security_events" in stats
        assert "events_by_type" in stats
        assert "events_by_severity" in stats


class TestSecureDownloadManager:
    """Test the secure download system."""
    
    def setup_method(self):
        """Set up test environment."""
        self.download_manager = SecureDownloadManager()
        self.test_user_id = 12345
        self.test_username = "test_admin"
        self.test_export_id = str(uuid.uuid4())
        
        # Create a temporary test file
        self.temp_file = tempfile.NamedTemporaryFile(delete=False)
        self.temp_file.write(b"test export data")
        self.temp_file.close()
    
    def teardown_method(self):
        """Clean up test environment."""
        # Remove temporary file
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_create_download_token(self):
        """Test download token creation."""
        result = self.download_manager.create_download_token(
            user_id=self.test_user_id,
            username=self.test_username,
            export_id=self.test_export_id,
            file_path=self.temp_file.name,
            filename="test_export.json",
            file_size=1024
        )
        
        assert result["success"] == True
        assert "token_id" in result
        assert "expires_at" in result
        assert result["max_attempts"] == 3
    
    def test_validate_download_token(self):
        """Test download token validation."""
        # Create a token first
        token_result = self.download_manager.create_download_token(
            user_id=self.test_user_id,
            username=self.test_username,
            export_id=self.test_export_id,
            file_path=self.temp_file.name,
            filename="test_export.json",
            file_size=1024
        )
        
        token_id = token_result["token_id"]
        
        # Validate with correct user
        validation = self.download_manager.validate_download_token(token_id, self.test_user_id)
        assert validation["valid"] == True
        assert "token_data" in validation
        
        # Validate with wrong user
        validation = self.download_manager.validate_download_token(token_id, 99999)
        assert validation["valid"] == False
        assert "Unauthorized access" in validation["reason"]
        
        # Validate non-existent token
        validation = self.download_manager.validate_download_token("invalid_token", self.test_user_id)
        assert validation["valid"] == False
        assert "Invalid or expired" in validation["reason"]
    
    def test_download_rate_limiting(self):
        """Test download rate limiting."""
        # Simulate multiple download attempts
        for _ in range(6):  # Exceed the limit of 5
            self.download_manager.download_attempts[self.test_user_id] = [datetime.now()] * 6
        
        result = self.download_manager.create_download_token(
            user_id=self.test_user_id,
            username=self.test_username,
            export_id=self.test_export_id,
            file_path=self.temp_file.name,
            filename="test_export.json",
            file_size=1024
        )
        
        assert result["success"] == False
        assert "rate limit exceeded" in result["error"].lower()
    
    def test_download_statistics(self):
        """Test download statistics generation."""
        # Add some mock download history
        self.download_manager.download_history = [
            {
                "user_id": self.test_user_id,
                "file_size": 1024,
                "downloaded_at": datetime.now().isoformat()
            },
            {
                "user_id": 99999,
                "file_size": 2048,
                "downloaded_at": datetime.now().isoformat()
            }
        ]
        
        stats = self.download_manager.get_download_statistics()
        
        assert "total_downloads" in stats
        assert "total_data_downloaded" in stats
        assert "unique_users" in stats
        assert stats["total_downloads"] == 2
        assert stats["total_data_downloaded"] == 3072


class TestExportProgressTracker:
    """Test the export progress tracking system."""
    
    def setup_method(self):
        """Set up test environment."""
        self.tracker = ExportProgressTracker()
        self.test_export_id = str(uuid.uuid4())
        self.test_user_id = 12345
        self.test_username = "test_admin"
    
    def test_start_tracking(self):
        """Test starting progress tracking."""
        result = self.tracker.start_tracking(
            export_id=self.test_export_id,
            user_id=self.test_user_id,
            username=self.test_username,
            total_collections=5
        )
        
        assert result["export_id"] == self.test_export_id
        assert result["user_id"] == self.test_user_id
        assert result["status"] == ExportStatus.INITIALIZING
        assert result["progress_percentage"] == 0
        assert result["total_collections"] == 5
    
    def test_update_progress(self):
        """Test progress updates."""
        # Start tracking
        self.tracker.start_tracking(
            export_id=self.test_export_id,
            user_id=self.test_user_id,
            username=self.test_username,
            total_collections=5
        )
        
        # Update progress
        success = self.tracker.update_progress(
            export_id=self.test_export_id,
            status=ExportStatus.EXPORTING,
            progress_percentage=50,
            collections_processed=2,
            current_collection="users"
        )
        
        assert success == True
        
        progress = self.tracker.get_progress(self.test_export_id)
        assert progress["status"] == ExportStatus.EXPORTING
        assert progress["progress_percentage"] == 50
        assert progress["collections_processed"] == 2
        assert progress["current_collection"] == "users"
    
    def test_complete_export(self):
        """Test export completion."""
        # Start tracking
        self.tracker.start_tracking(
            export_id=self.test_export_id,
            user_id=self.test_user_id,
            username=self.test_username
        )
        
        # Complete successfully
        success = self.tracker.complete_export(
            export_id=self.test_export_id,
            success=True,
            final_message="Export completed successfully"
        )
        
        assert success == True
        
        progress = self.tracker.get_progress(self.test_export_id)
        assert progress["status"] == ExportStatus.COMPLETED
        assert progress["progress_percentage"] == 100
        assert "completed_at" in progress
    
    def test_format_progress_message(self):
        """Test progress message formatting."""
        # Start tracking
        self.tracker.start_tracking(
            export_id=self.test_export_id,
            user_id=self.test_user_id,
            username=self.test_username,
            total_collections=3
        )
        
        # Update progress
        self.tracker.update_progress(
            export_id=self.test_export_id,
            status=ExportStatus.EXPORTING,
            progress_percentage=75,
            collections_processed=2,
            current_collection="products"
        )
        
        message = self.tracker.format_progress_message(self.test_export_id)
        
        assert "75%" in message
        assert "products" in message
        assert "2/3" in message
        assert "📦" in message  # Export emoji
    
    def test_progress_statistics(self):
        """Test progress statistics generation."""
        # Add some mock exports
        self.tracker.active_exports = {
            "export1": {"status": ExportStatus.COMPLETED, "started_at": datetime.now(), "completed_at": datetime.now()},
            "export2": {"status": ExportStatus.FAILED, "started_at": datetime.now()},
            "export3": {"status": ExportStatus.EXPORTING, "started_at": datetime.now()}
        }
        
        stats = self.tracker.get_progress_statistics()
        
        assert stats["total_tracked_exports"] == 3
        assert stats["active_exports"] == 1
        assert stats["completed_exports"] == 1
        assert stats["failed_exports"] == 1


class TestIntegration:
    """Integration tests for the complete database export system."""
    
    @pytest.mark.asyncio
    async def test_full_export_workflow(self):
        """Test the complete export workflow from start to finish."""
        test_user_id = 12345
        test_username = "test_admin"
        
        # Mock all external dependencies
        with patch('utils.database_export_security.is_privileged', return_value=True):
            with patch('utils.database_export.DatabaseExporter._check_mongodump_available', return_value=True):
                with patch('utils.database_export.DatabaseExporter._run_mongodump', return_value=True):
                    with patch('pathlib.Path.exists', return_value=True):
                        with patch('pathlib.Path.stat') as mock_stat:
                            mock_stat.return_value.st_size = 1024 * 1024  # 1MB
                            
                            # Create export
                            result = await create_database_export(
                                user_id=test_user_id,
                                username=test_username,
                                export_format="json"
                            )
                            
                            assert result["status"] == "completed"
                            assert result["user_id"] == test_user_id
                            
                            # Create download token
                            token_result = create_download_token(
                                user_id=test_user_id,
                                username=test_username,
                                export_id=result["export_id"],
                                file_path=result["file_path"],
                                filename=result["filename"],
                                file_size=result["file_size"]
                            )
                            
                            assert token_result["success"] == True
                            assert "token_id" in token_result
    
    def test_error_handling(self):
        """Test error handling throughout the system."""
        # Test with invalid export format
        security = DatabaseExportSecurity()
        
        with patch('utils.database_export_security.is_privileged', return_value=True):
            result = security.validate_export_request(
                12345, 
                "test_user", 
                {"format": "invalid_format"}
            )
            
            assert result["allowed"] == False
            assert "Invalid parameters" in result["reason"]


if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])
