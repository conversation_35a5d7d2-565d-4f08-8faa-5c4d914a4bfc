"""
Unit Tests for Critical Fixes
Tests for race condition fixes, security improvements, and data validation.
Ensures all critical fixes work correctly under various conditions.
"""

import unittest
import tempfile
import os
import shutil
import threading
import time
from decimal import Decimal
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Import modules to test
from utils.price_calculator import PriceCalculator, PriceCalculationError
from utils.product_validator import ProductValidator, ProductValidationError
from utils.file_resource_manager import FileResourceManager, FileResourceError
from utils.transaction_manager import TransactionManager, TransactionError
from utils.cart_manager import CartManager, CartError, CartErrorType


class TestPriceCalculator(unittest.TestCase):
    """Test price calculation with decimal precision."""
    
    def setUp(self):
        self.calculator = PriceCalculator()
    
    def test_decimal_conversion(self):
        """Test conversion of various types to Decimal."""
        # Test integer
        result = self.calculator.to_decimal(10)
        self.assertEqual(result, Decimal('10'))
        
        # Test float
        result = self.calculator.to_decimal(10.99)
        self.assertEqual(result, Decimal('10.99'))
        
        # Test string
        result = self.calculator.to_decimal('15.50')
        self.assertEqual(result, Decimal('15.50'))
        
        # Test Decimal
        decimal_val = Decimal('20.25')
        result = self.calculator.to_decimal(decimal_val)
        self.assertEqual(result, decimal_val)
    
    def test_invalid_decimal_conversion(self):
        """Test error handling for invalid decimal conversion."""
        with self.assertRaises(PriceCalculationError):
            self.calculator.to_decimal("invalid")
        
        with self.assertRaises(PriceCalculationError):
            self.calculator.to_decimal(None)
    
    def test_currency_rounding(self):
        """Test proper currency rounding."""
        # Test standard rounding
        result = self.calculator.round_currency(10.995)
        self.assertEqual(result, Decimal('11.00'))
        
        # Test half-up rounding
        result = self.calculator.round_currency(10.125)
        self.assertEqual(result, Decimal('10.13'))
    
    def test_total_price_calculation(self):
        """Test total price calculation with quantity."""
        result = self.calculator.calculate_total_price(Decimal('9.99'), 3)
        self.assertEqual(result, Decimal('29.97'))
        
        # Test with float input
        result = self.calculator.calculate_total_price(10.50, 2)
        self.assertEqual(result, Decimal('21.00'))
    
    def test_price_validation(self):
        """Test price validation rules."""
        # Valid price
        result = self.calculator.validate_price(10.99)
        self.assertTrue(result['valid'])
        self.assertEqual(result['decimal_price'], Decimal('10.99'))
        
        # Negative price
        result = self.calculator.validate_price(-5.00)
        self.assertFalse(result['valid'])
        self.assertIn('negative', result['error'])
        
        # Zero price
        result = self.calculator.validate_price(0)
        self.assertFalse(result['valid'])
        self.assertIn('zero', result['error'])


class TestProductValidator(unittest.TestCase):
    """Test product validation functionality."""
    
    def setUp(self):
        self.validator = ProductValidator()
    
    def test_product_name_validation(self):
        """Test product name validation."""
        # Valid name
        result = self.validator.validate_product_name("Test Product")
        self.assertTrue(result['valid'])
        self.assertEqual(result['cleaned_name'], "Test Product")
        
        # Empty name
        result = self.validator.validate_product_name("")
        self.assertFalse(result['valid'])
        
        # Name too long
        long_name = "x" * 201
        result = self.validator.validate_product_name(long_name)
        self.assertFalse(result['valid'])
        
        # Name with suspicious characters
        result = self.validator.validate_product_name("Test<script>alert('xss')</script>")
        self.assertFalse(result['valid'])
    
    def test_product_description_validation(self):
        """Test product description validation."""
        # Valid description
        result = self.validator.validate_product_description("A great product")
        self.assertTrue(result['valid'])
        
        # Description with script injection
        result = self.validator.validate_product_description("Test javascript:alert('xss')")
        self.assertFalse(result['valid'])
        
        # Too long description
        long_desc = "x" * 2001
        result = self.validator.validate_product_description(long_desc)
        self.assertFalse(result['valid'])
    
    def test_complete_product_validation(self):
        """Test complete product validation."""
        valid_product = {
            "name": "Test Product",
            "description": "A test product",
            "price": 19.99,
            "category_id": 1
        }
        
        result = self.validator.validate_complete_product(valid_product)
        self.assertTrue(result['valid'])
        self.assertIn('cleaned_data', result)
        
        # Invalid product (missing name)
        invalid_product = {
            "description": "A test product",
            "price": 19.99
        }
        
        result = self.validator.validate_complete_product(invalid_product)
        self.assertFalse(result['valid'])
        self.assertIn('errors', result)


class TestFileResourceManager(unittest.TestCase):
    """Test file resource management."""
    
    def setUp(self):
        self.manager = FileResourceManager()
        self.test_dir = tempfile.mkdtemp()
        self.test_file = os.path.join(self.test_dir, "test.txt")
        
        # Create test file
        with open(self.test_file, 'w') as f:
            f.write("Test content\nLine 2\nLine 3")
    
    def tearDown(self):
        shutil.rmtree(self.test_dir, ignore_errors=True)
        self.manager.cleanup_all_resources()
    
    def test_managed_file_context(self):
        """Test managed file context manager."""
        with self.manager.managed_file(self.test_file, 'r') as f:
            content = f.read()
            self.assertIn("Test content", content)
        
        # Verify file is closed
        status = self.manager.get_resource_status()
        self.assertEqual(status['open_file_handles'], 0)
    
    def test_safe_file_copy(self):
        """Test safe file copy operation."""
        dest_file = os.path.join(self.test_dir, "copy.txt")
        
        result = self.manager.safe_file_copy(self.test_file, dest_file)
        self.assertTrue(result['success'])
        self.assertTrue(os.path.exists(dest_file))
        
        # Verify content
        with open(dest_file, 'r') as f:
            content = f.read()
            self.assertIn("Test content", content)
    
    def test_temp_file_creation(self):
        """Test temporary file creation and cleanup."""
        temp_file = self.manager.create_temp_file(suffix='.txt')
        self.assertTrue(os.path.exists(temp_file))
        
        # Write to temp file
        with open(temp_file, 'w') as f:
            f.write("Temporary content")
        
        # Cleanup
        cleanup_result = self.manager.cleanup_temp_files()
        self.assertTrue(cleanup_result['success'])
        self.assertFalse(os.path.exists(temp_file))


class TestTransactionManager(unittest.TestCase):
    """Test transaction management with rollback."""
    
    def test_successful_transaction(self):
        """Test successful transaction execution."""
        transaction = TransactionManager("test_transaction")
        
        # Add steps
        def step1():
            return "step1_result"
        
        def step2():
            return "step2_result"
        
        transaction.add_step("step1", step1)
        transaction.add_step("step2", step2)
        
        result = transaction.execute()
        self.assertTrue(result['success'])
        self.assertEqual(len(result['results']), 2)
        self.assertEqual(result['results']['step1'], "step1_result")
    
    def test_transaction_rollback(self):
        """Test transaction rollback on failure."""
        transaction = TransactionManager("test_rollback")
        
        rollback_called = []
        
        def step1():
            return "step1_result"
        
        def step1_rollback(result, **kwargs):
            rollback_called.append("step1")
        
        def step2():
            raise Exception("Step 2 failed")
        
        transaction.add_step("step1", step1, step1_rollback)
        transaction.add_step("step2", step2)
        
        result = transaction.execute()
        self.assertFalse(result['success'])
        self.assertIn("step1", rollback_called)


class TestCartManager(unittest.TestCase):
    """Test enhanced cart management."""
    
    def setUp(self):
        self.cart_manager = CartManager()
    
    @patch('utils.cart_manager.get_or_create_cart')
    @patch('utils.cart_manager.get_product')
    def test_cart_validation(self, mock_get_product, mock_get_cart):
        """Test cart validation functionality."""
        # Mock cart data
        mock_cart = {
            "user_id": 1,
            "items": [
                {
                    "product_id": 1,
                    "name": "Test Product",
                    "price": 10.99,
                    "quantity": 1
                }
            ]
        }
        mock_get_cart.return_value = mock_cart
        
        # Mock product data
        mock_product = {
            "id": 1,
            "name": "Test Product",
            "price": 10.99,
            "is_line_based": False,
            "is_exclusive_single_use": False
        }
        mock_get_product.return_value = mock_product
        
        result = self.cart_manager.get_cart_with_validation(1)
        self.assertTrue(result['success'])
        self.assertEqual(result['item_count'], 1)


class TestRaceConditionFixes(unittest.TestCase):
    """Test race condition fixes in inventory management."""
    
    @patch('database.operations.products_collection')
    def test_atomic_inventory_reservation(self, mock_collection):
        """Test atomic inventory reservation prevents race conditions."""
        from database.operations import reserve_inventory_lines
        
        # Mock successful atomic update
        mock_result = Mock()
        mock_result.modified_count = 1
        mock_collection.update_one.return_value = mock_result
        
        # Mock product data
        mock_product = {
            "_id": "product_id",
            "is_line_based": True,
            "available_lines": 10
        }
        
        with patch('database.operations.get_product', return_value=mock_product):
            result = reserve_inventory_lines("product_id", 5)
            self.assertTrue(result)
            
            # Verify atomic operation was called
            mock_collection.update_one.assert_called_once()
            call_args = mock_collection.update_one.call_args
            
            # Check that the update uses atomic operations
            update_query = call_args[0][0]
            update_operation = call_args[0][1]
            
            self.assertIn("available_lines", update_query)
            self.assertIn("$gte", update_query["available_lines"])
            self.assertIn("$inc", update_operation)


class TestSecurityFixes(unittest.TestCase):
    """Test security vulnerability fixes."""
    
    def test_path_traversal_prevention(self):
        """Test path traversal attack prevention."""
        from utils.exclusive_product_manager import ExclusiveProductManager
        
        manager = ExclusiveProductManager()
        
        # Test malicious path
        malicious_path = "../../../etc/passwd"
        result = manager.validate_file_type(malicious_path)
        
        self.assertFalse(result['valid'])
        self.assertIn('security_issue', result)
    
    def test_input_sanitization(self):
        """Test input sanitization in product validation."""
        validator = ProductValidator()
        
        # Test XSS attempt in product name
        xss_name = "Product<script>alert('xss')</script>"
        result = validator.validate_product_name(xss_name)
        
        self.assertFalse(result['valid'])
        self.assertIn('invalid characters', result['error'])


if __name__ == '__main__':
    # Create test suite
    test_suite = unittest.TestSuite()
    
    # Add test cases
    test_classes = [
        TestPriceCalculator,
        TestProductValidator,
        TestFileResourceManager,
        TestTransactionManager,
        TestCartManager,
        TestRaceConditionFixes,
        TestSecurityFixes
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    # Print summary
    print(f"\n{'='*50}")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")
    print(f"{'='*50}")
