"""
Test file handling flow for product creation and delivery.
Tests the complete flow from product creation with file upload through to successful file delivery.
"""

import pytest
import os
import tempfile
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from datetime import datetime

# Import the modules we need to test
from database.operations import add_product
from database.models import Product
from utils.digital_delivery import DigitalDeliveryManager
from utils.cart_item_factory import CartItemFactory


class TestFileHandlingFlow:
    """Test the complete file handling flow for regular products."""

    def setup_method(self):
        """Set up test fixtures."""
        self.test_file_content = b"This is test file content for product delivery"
        self.test_filename = "test_product_file.pdf"
        self.test_mime_type = "application/pdf"
        
    def create_test_file(self):
        """Create a temporary test file."""
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.pdf')
        temp_file.write(self.test_file_content)
        temp_file.close()
        return temp_file.name

    def test_product_model_file_metadata_fields(self):
        """Test that Product model includes file metadata fields."""
        product_data = {
            "id": 1,
            "name": "Test Product",
            "description": "Test Description",
            "price": 9.99,
            "file_link": "/path/to/file.pdf",
            "file_name": "test_file.pdf",
            "file_size": 1024,
            "file_type": "document",
            "file_path": "/uploads/files/test_file.pdf",
            "file_mime_type": "application/pdf"
        }
        
        product = Product.from_dict(product_data)
        
        # Verify all file metadata fields are present
        assert product.file_name == "test_file.pdf"
        assert product.file_size == 1024
        assert product.file_type == "document"
        assert product.file_path == "/uploads/files/test_file.pdf"
        assert product.file_mime_type == "application/pdf"
        
        # Verify to_dict includes file metadata
        product_dict = product.to_dict()
        assert product_dict["file_name"] == "test_file.pdf"
        assert product_dict["file_size"] == 1024
        assert product_dict["file_type"] == "document"
        assert product_dict["file_path"] == "/uploads/files/test_file.pdf"
        assert product_dict["file_mime_type"] == "application/pdf"

    @patch('database.operations.products_collection')
    def test_add_product_with_file_metadata(self, mock_collection):
        """Test that add_product properly stores file metadata."""
        mock_collection.find_one.return_value = {"id": 0}
        mock_collection.insert_one.return_value = Mock(acknowledged=True, inserted_id="test_id")
        
        product_data = {
            "name": "Test Product with File",
            "description": "Test Description",
            "price": 19.99,
            "file_link": "/uploads/files/test_file.pdf",
            "file_name": "test_file.pdf",
            "file_size": 2048,
            "file_type": "document",
            "file_path": "/uploads/files/test_file.pdf",
            "file_mime_type": "application/pdf"
        }
        
        result = add_product(product_data)
        
        # Verify the product was inserted with file metadata
        assert mock_collection.insert_one.called
        inserted_data = mock_collection.insert_one.call_args[0][0]
        
        assert inserted_data["file_name"] == "test_file.pdf"
        assert inserted_data["file_size"] == 2048
        assert inserted_data["file_type"] == "document"
        assert inserted_data["file_path"] == "uploads/files/test_file.pdf"
        assert inserted_data["file_mime_type"] == "application/pdf"

    def test_cart_item_factory_includes_file_metadata(self):
        """Test that cart item factory includes file metadata."""
        product_data = {
            "id": 1,
            "name": "Test Product",
            "description": "Test Description",
            "price": 9.99,
            "file_link": "/uploads/files/test_file.pdf",
            "file_name": "test_file.pdf",
            "file_size": 1024,
            "file_type": "document",
            "file_path": "/uploads/files/test_file.pdf",
            "file_mime_type": "application/pdf"
        }
        
        cart_item = CartItemFactory.create_cart_item(product_data, 1, 1)
        
        # Verify file metadata is included in cart item
        assert cart_item["file_name"] == "test_file.pdf"
        assert cart_item["file_size"] == 1024
        assert cart_item["file_type"] == "document"
        assert cart_item["file_path"] == "/uploads/files/test_file.pdf"
        assert cart_item["file_mime_type"] == "application/pdf"

    @pytest.mark.asyncio
    async def test_file_delivery_with_file_path(self):
        """Test file delivery when product has file_path."""
        # Create a test file
        test_file_path = self.create_test_file()
        
        try:
            # Mock bot
            mock_bot = AsyncMock()
            delivery_manager = DigitalDeliveryManager(mock_bot)
            
            # Mock the deliver_file method
            delivery_manager.deliver_file = AsyncMock(return_value={
                "success": True,
                "product_name": "Test Product",
                "delivery_type": "file"
            })
            
            cart_item = {
                "name": "Test Product",
                "file_link": "some_link",
                "file_path": test_file_path,
                "file_name": "test_file.pdf",
                "file_size": len(self.test_file_content),
                "file_type": "document",
                "file_mime_type": "application/pdf"
            }
            
            result = await delivery_manager.deliver_product_consolidated(
                user_id=12345,
                cart_item=cart_item,
                order_number=1001
            )
            
            # Verify file delivery was called with correct path
            delivery_manager.deliver_file.assert_called_once_with(
                12345, test_file_path, "Test Product", 1, 1001
            )
            
            assert result["success"] is True
            assert result["delivery_type"] == "file"
            
        finally:
            # Clean up test file
            if os.path.exists(test_file_path):
                os.unlink(test_file_path)

    @pytest.mark.asyncio
    async def test_file_delivery_with_file_id(self):
        """Test file delivery when product has file_id format."""
        mock_bot = AsyncMock()
        delivery_manager = DigitalDeliveryManager(mock_bot)
        
        cart_item = {
            "name": "Test Product",
            "file_link": "file_id:BAADBAADrwADBREAAcaXBwABag",
            "file_name": "test_file.pdf",
            "file_size": 1024,
            "file_type": "document",
            "file_mime_type": "application/pdf"
        }
        
        # Mock successful file send
        mock_bot.send_document = AsyncMock()
        
        result = await delivery_manager._deliver_regular_product_content(
            user_id=12345,
            file_link="file_id:BAADBAADrwADBREAAcaXBwABag",
            product_name="Test Product",
            quantity=1,
            order_number=1001
        )
        
        # Verify bot.send_document was called
        mock_bot.send_document.assert_called_once()
        call_args = mock_bot.send_document.call_args
        
        assert call_args[1]["chat_id"] == 12345
        assert call_args[1]["document"] == "BAADBAADrwADBREAAcaXBwABag"
        assert "Test Product" in call_args[1]["caption"]
        
        assert result["success"] is True
        assert result["delivery_type"] == "file"

    def test_file_metadata_validation(self):
        """Test that file metadata is properly validated."""
        # Test with valid file metadata
        valid_product = {
            "name": "Valid Product",
            "description": "Test",
            "price": 9.99,
            "file_name": "document.pdf",
            "file_size": 1024,
            "file_type": "document",
            "file_mime_type": "application/pdf"
        }
        
        product = Product.from_dict(valid_product)
        assert product.file_name == "document.pdf"
        assert product.file_size == 1024
        assert product.file_type == "document"
        assert product.file_mime_type == "application/pdf"
        
        # Test with None values (should be handled gracefully)
        product_with_none = {
            "name": "Product with None",
            "description": "Test",
            "price": 9.99,
            "file_name": None,
            "file_size": None,
            "file_type": None,
            "file_mime_type": None
        }
        
        product = Product.from_dict(product_with_none)
        assert product.file_name is None
        assert product.file_size is None
        assert product.file_type is None
        assert product.file_mime_type is None

    def teardown_method(self):
        """Clean up after tests."""
        pass


if __name__ == "__main__":
    pytest.main([__file__])
