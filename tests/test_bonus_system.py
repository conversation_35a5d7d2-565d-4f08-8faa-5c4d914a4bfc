"""
Unit tests for the bonus reward system.
Tests bonus calculation logic, database operations, and integration points.
"""

import unittest
import sys
import os
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.bonus_calculator import BonusCalculator, BonusCalculationError
from database.models import BonusTier


class TestBonusTier(unittest.TestCase):
    """Test the BonusTier model."""
    
    def test_bonus_tier_creation(self):
        """Test creating a BonusTier instance."""
        tier = BonusTier(
            threshold=100.0,
            bonus_percentage=0.15,
            description="Test tier"
        )
        
        self.assertEqual(tier.threshold, 100.0)
        self.assertEqual(tier.bonus_percentage, 0.15)
        self.assertEqual(tier.description, "Test tier")
        self.assertTrue(tier.is_active)
    
    def test_bonus_tier_from_dict(self):
        """Test creating BonusTier from dictionary."""
        data = {
            "_id": "507f1f77bcf86cd799439011",
            "threshold": 50.0,
            "bonus_percentage": 0.10,
            "is_active": True,
            "description": "Test tier",
            "created_at": datetime.now(),
            "updated_at": datetime.now()
        }
        
        tier = BonusTier.from_dict(data)
        
        self.assertEqual(tier.threshold, 50.0)
        self.assertEqual(tier.bonus_percentage, 0.10)
        self.assertTrue(tier.is_active)
        self.assertEqual(tier.description, "Test tier")
    
    def test_bonus_tier_to_dict(self):
        """Test converting BonusTier to dictionary."""
        tier = BonusTier(
            threshold=75.0,
            bonus_percentage=0.12,
            description="Test tier",
            created_by=123
        )
        
        tier_dict = tier.to_dict()
        
        self.assertEqual(tier_dict["threshold"], 75.0)
        self.assertEqual(tier_dict["bonus_percentage"], 0.12)
        self.assertEqual(tier_dict["description"], "Test tier")
        self.assertEqual(tier_dict["created_by"], 123)
        self.assertTrue(tier_dict["is_active"])
    
    def test_calculate_bonus_amount(self):
        """Test bonus amount calculation."""
        tier = BonusTier(
            threshold=100.0,
            bonus_percentage=0.20,
            is_active=True
        )
        
        # Test qualifying purchase
        bonus = tier.calculate_bonus_amount(150.0)
        self.assertEqual(bonus, 30.0)  # 150 * 0.20
        
        # Test non-qualifying purchase
        bonus = tier.calculate_bonus_amount(50.0)
        self.assertEqual(bonus, 0.0)
        
        # Test inactive tier
        tier.is_active = False
        bonus = tier.calculate_bonus_amount(150.0)
        self.assertEqual(bonus, 0.0)


class TestBonusCalculator(unittest.TestCase):
    """Test the BonusCalculator class."""
    
    @patch('utils.bonus_calculator.calculate_bonus_amount')
    def test_calculate_purchase_bonus(self, mock_calculate):
        """Test purchase bonus calculation."""
        # Mock the database function
        mock_calculate.return_value = {
            "bonus_amount": 15.0,
            "total_amount": 115.0,
            "tier_used": {
                "_id": "507f1f77bcf86cd799439011",
                "threshold": 100.0,
                "bonus_percentage": 0.15
            },
            "original_amount": 100.0
        }
        
        result = BonusCalculator.calculate_purchase_bonus(100.0)
        
        self.assertTrue(result["has_bonus"])
        self.assertEqual(result["bonus_amount"], 15.0)
        self.assertEqual(result["total_amount"], 115.0)
        self.assertEqual(result["original_amount"], 100.0)
    
    @patch('utils.bonus_calculator.calculate_bonus_amount')
    def test_calculate_purchase_bonus_no_bonus(self, mock_calculate):
        """Test purchase bonus calculation with no applicable bonus."""
        # Mock the database function
        mock_calculate.return_value = {
            "bonus_amount": 0.0,
            "total_amount": 25.0,
            "tier_used": None,
            "original_amount": 25.0
        }
        
        result = BonusCalculator.calculate_purchase_bonus(25.0)
        
        self.assertFalse(result["has_bonus"])
        self.assertEqual(result["bonus_amount"], 0.0)
        self.assertEqual(result["total_amount"], 25.0)
    
    def test_calculate_purchase_bonus_invalid_amount(self):
        """Test purchase bonus calculation with invalid amount."""
        result = BonusCalculator.calculate_purchase_bonus(-10.0)
        
        self.assertFalse(result["has_bonus"])
        self.assertEqual(result["bonus_amount"], 0.0)
        self.assertIn("error", result)
    
    @patch('utils.bonus_calculator.get_user_balance')
    @patch('utils.bonus_calculator.update_user_balance')
    @patch('utils.bonus_calculator.add_transaction')
    @patch('utils.bonus_calculator.calculate_bonus_amount')
    def test_apply_bonus_to_wallet(self, mock_calculate, mock_add_transaction, 
                                   mock_update_balance, mock_get_balance):
        """Test applying bonus to user wallet."""
        # Setup mocks
        mock_calculate.return_value = {
            "bonus_amount": 20.0,
            "total_amount": 120.0,
            "tier_used": {
                "_id": "507f1f77bcf86cd799439011",
                "threshold": 100.0,
                "bonus_percentage": 0.20
            },
            "original_amount": 100.0,
            "has_bonus": True
        }
        mock_get_balance.return_value = 50.0
        mock_update_balance.return_value = True
        mock_add_transaction.return_value = {"_id": "transaction_id"}
        
        result = BonusCalculator.apply_bonus_to_wallet(
            user_id=123,
            purchase_amount=100.0,
            order_id="ORDER123"
        )
        
        self.assertTrue(result["success"])
        self.assertEqual(result["bonus_amount"], 20.0)
        self.assertEqual(result["new_balance"], 70.0)  # 50 + 20
        self.assertIsNotNone(result["tier_used"])
        self.assertEqual(result["transaction_id"], "transaction_id")
    
    @patch('utils.bonus_calculator.calculate_bonus_amount')
    def test_apply_bonus_to_wallet_no_bonus(self, mock_calculate):
        """Test applying bonus when no bonus is applicable."""
        # Setup mocks
        mock_calculate.return_value = {
            "bonus_amount": 0.0,
            "total_amount": 25.0,
            "tier_used": None,
            "original_amount": 25.0,
            "has_bonus": False
        }
        
        with patch('utils.bonus_calculator.get_user_balance', return_value=100.0):
            result = BonusCalculator.apply_bonus_to_wallet(
                user_id=123,
                purchase_amount=25.0
            )
        
        self.assertTrue(result["success"])
        self.assertEqual(result["bonus_amount"], 0.0)
        self.assertEqual(result["new_balance"], 100.0)
        self.assertIsNone(result["tier_used"])
        self.assertIsNone(result["transaction_id"])
    
    @patch('utils.bonus_calculator.calculate_bonus_amount')
    def test_get_bonus_preview(self, mock_calculate):
        """Test getting bonus preview."""
        # Mock with bonus
        mock_calculate.return_value = {
            "bonus_amount": 25.0,
            "total_amount": 125.0,
            "tier_used": {
                "threshold": 100.0,
                "bonus_percentage": 0.25,
                "description": "Premium tier"
            },
            "original_amount": 100.0,
            "has_bonus": True
        }
        
        result = BonusCalculator.get_bonus_preview(100.0)
        
        self.assertTrue(result["has_bonus"])
        self.assertEqual(result["bonus_amount"], 25.0)
        self.assertEqual(result["total_amount"], 125.0)
        self.assertEqual(result["bonus_percentage"], 25.0)
        self.assertEqual(result["tier_threshold"], 100.0)
        self.assertIn("🎉 Bonus:", result["preview_text"])
    
    @patch('utils.bonus_calculator.calculate_bonus_amount')
    def test_get_bonus_preview_no_bonus(self, mock_calculate):
        """Test getting bonus preview with no bonus."""
        # Mock without bonus
        mock_calculate.return_value = {
            "bonus_amount": 0.0,
            "total_amount": 30.0,
            "tier_used": None,
            "original_amount": 30.0,
            "has_bonus": False
        }
        
        result = BonusCalculator.get_bonus_preview(30.0)
        
        self.assertFalse(result["has_bonus"])
        self.assertEqual(result["bonus_amount"], 0.0)
        self.assertEqual(result["total_amount"], 30.0)
        self.assertEqual(result["preview_text"], "No bonus available for this amount")


class TestBonusDatabaseOperations(unittest.TestCase):
    """Test bonus tier database operations."""
    
    @patch('database.operations.bonus_tiers_collection')
    def test_create_bonus_tier_success(self, mock_collection):
        """Test successful bonus tier creation."""
        from database.operations import create_bonus_tier
        
        # Mock successful insertion
        mock_result = Mock()
        mock_result.acknowledged = True
        mock_result.inserted_id = "507f1f77bcf86cd799439011"
        mock_collection.insert_one.return_value = mock_result
        mock_collection.find_one.return_value = None  # No existing tier
        
        result = create_bonus_tier(
            threshold=100.0,
            bonus_percentage=0.15,
            created_by=123,
            description="Test tier"
        )
        
        self.assertIsNotNone(result)
        self.assertEqual(result["threshold"], 100.0)
        self.assertEqual(result["bonus_percentage"], 0.15)
        self.assertEqual(result["created_by"], 123)
    
    @patch('database.operations.bonus_tiers_collection')
    def test_create_bonus_tier_duplicate_threshold(self, mock_collection):
        """Test bonus tier creation with duplicate threshold."""
        from database.operations import create_bonus_tier
        
        # Mock existing tier with same threshold
        mock_collection.find_one.return_value = {"threshold": 100.0}
        
        result = create_bonus_tier(
            threshold=100.0,
            bonus_percentage=0.15,
            created_by=123
        )
        
        self.assertIsNone(result)
    
    @patch('database.operations.bonus_tiers_collection')
    def test_get_applicable_bonus_tier(self, mock_collection):
        """Test getting applicable bonus tier."""
        from database.operations import get_applicable_bonus_tier
        
        # Mock tiers
        mock_tiers = [
            {"threshold": 100.0, "bonus_percentage": 0.20, "is_active": True},
            {"threshold": 50.0, "bonus_percentage": 0.10, "is_active": True}
        ]
        mock_collection.find.return_value.sort.return_value = mock_tiers
        
        result = get_applicable_bonus_tier(150.0)
        
        # Should return the highest threshold tier (100.0)
        self.assertIsNotNone(result)
        self.assertEqual(result["threshold"], 100.0)
        self.assertEqual(result["bonus_percentage"], 0.20)


if __name__ == "__main__":
    # Configure logging to reduce noise during tests
    import logging
    logging.getLogger().setLevel(logging.CRITICAL)
    
    unittest.main()
