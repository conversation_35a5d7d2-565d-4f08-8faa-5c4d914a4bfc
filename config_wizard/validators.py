"""
Configuration Wizard Validators

This module provides validation functions for different types of configuration values
including specialized validators for Telegram tokens, MongoDB URIs, etc.
"""

import re
from typing import Tuple, Any, Optional


def validate_by_type(
    value: str, var_type: str, param_name: Optional[str] = None
) -> Tuple[bool, str, Any]:
    """
    Validates a value based on its expected type.

    Args:
        value: The value to validate
        var_type: Type of validation to perform
        param_name: Optional parameter name for error message

    Returns:
        Tuple containing:
        - bool: Whether validation passed
        - str: Error message if validation failed, empty string if successful
        - Any: The converted value if applicable, or original value
    """
    # Handle None or empty string for non-required fields
    if value is None or value == "":
        return True, "", value

    # Handle different types
    if var_type == "string":
        return True, "", value

    elif var_type == "int":
        try:
            int_value = int(value)
            return True, "", int_value
        except ValueError:
            return False, f"Value must be an integer", value

    elif var_type == "float":
        try:
            float_value = float(value)
            return True, "", float_value
        except ValueError:
            return False, f"Value must be a number", value

    elif var_type == "bool":
        lower_value = value.lower()
        if lower_value in ["true", "1", "yes", "y"]:
            return True, "", True
        elif lower_value in ["false", "0", "no", "n"]:
            return True, "", False
        else:
            return False, f"Value must be a boolean (true/false, yes/no, 1/0)", value

    elif var_type == "telegram_token":
        # Validate Telegram bot token format (numbers:letters)
        pattern = r"^\d+:[A-Za-z0-9_-]+$"
        if re.match(pattern, value):
            return True, "", value
        else:
            return (
                False,
                "Invalid Telegram token format. Should be in the format NUMBER:ALPHANUMERIC",
                value,
            )

    elif var_type == "mongodb_uri":
        # Basic validation for MongoDB URI
        pattern = r"^mongodb(\+srv)?:\/\/"
        if re.match(pattern, value):
            return True, "", value
        else:
            return (
                False,
                "Invalid MongoDB URI format. Should start with mongodb:// or mongodb+srv://",
                value,
            )

    elif var_type == "email":
        # Simple email validation
        pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
        if re.match(pattern, value):
            return True, "", value
        else:
            return False, "Invalid email address format", value

    elif var_type == "url":
        # URL validation - simplified to be more permissive
        pattern = r"^https?:\/\/[a-zA-Z0-9][-a-zA-Z0-9@:%._\+~#=]{0,256}\.[a-zA-Z0-9()]{1,6}([-a-zA-Z0-9()@:%_\+.~#?&//=]*)$"
        if re.match(pattern, value):
            return True, "", value
        else:
            return (
                False,
                "Invalid URL format. Should start with http:// or https:// followed by a valid domain",
                value,
            )

    elif var_type == "port":
        try:
            port = int(value)
            if 1 <= port <= 65535:
                return True, "", port
            else:
                return False, "Port must be between 1 and 65535", value
        except ValueError:
            return False, "Port must be an integer", value

    # Add any other specific validations here

    # Default case - assume it's valid
    return True, "", value
