import os
import time
import shutil
from typing import Dict, Any, List, Optional

# Convert relative imports to absolute imports
import sys

# Add parent directory to path if needed
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Use absolute imports instead of relative ones
from config_wizard.manager import Config<PERSON>anager
from config_wizard.validators import validate_by_type
from config_wizard.utils import normalize_env_path
from config_wizard.cli import console, THEME
import config_wizard.cli as cli


def create_env_example_if_missing(required_vars: List[str]) -> None:
    """
    Create a .env.example file if it doesn't exist.

    Args:
        required_vars: List of required variable names
    """
    # This function might be called dynamically or used in ways Vulture can't detect
    env_example_path = normalize_env_path(".env.example")
    if os.path.exists(env_example_path):
        return

    cli.print_info("Creating .env.example template file...")

    with open(env_example_path, "w") as f:
        f.write("# Configuration Template\n")
        f.write(
            "# This file provides a template for the required environment variables\n\n"
        )

        for var in required_vars:
            f.write(f"# {var} - Required\n")
            f.write(f"{var}=\n\n")


def backup_existing_env() -> bool:
    """
    Backup existing .env file if it exists.

    Returns:
        bool: True if backed up, False otherwise
    """
    env_file_path = normalize_env_path(".env")
    if os.path.exists(env_file_path):
        # Create a timestamp with a format that's safe for filenames
        timestamp = time.strftime("%Y%m%d_%H%M%S")
        backup_file = normalize_env_path(f".env.backup_{timestamp}")

        # Ensure the backup directory exists
        backup_dir = os.path.dirname(backup_file)
        os.makedirs(backup_dir, exist_ok=True)

        try:
            # Copy the file with error handling
            shutil.copyfile(env_file_path, backup_file)

            # Verify the backup was created successfully
            if os.path.exists(backup_file) and os.path.getsize(backup_file) > 0:
                cli.print_info(f"Existing configuration backed up to {backup_file}")
                return True
            else:
                cli.print_error(
                    "Backup file was created but appears to be empty or invalid"
                )
                return False
        except Exception as e:
            cli.print_error(f"Failed to create backup: {e}")
            return False

    return False


def mask_sensitive_value(value):
    """
    Create a masked version of a sensitive value for display.

    Args:
        value (str): The sensitive value to mask

    Returns:
        str: The masked value with only the first few characters visible
    """
    if not value:
        return ""

    visible_part = value[:2] if len(value) > 4 else ""
    masked_part = (
        "\u2022" * (len(value) - 2) if len(value) > 4 else "\u2022" * len(value)
    )
    return f"{visible_part}{masked_part}"


def fallback_input(
    prompt_text, var_type, required, display_default, default, is_sensitive=False
):
    """
    Handle input through a sleek fallback mechanism when rich input fails.
    Provides a premium console experience even in fallback mode.

    Args:
        prompt_text: The formatted prompt text
        var_type: The variable type (string, int, etc.)
        required: Whether the field is required
        display_default: The default value to display
        default: The actual default value to use
        is_sensitive: Whether this is sensitive data

    Returns:
        str: The user input, validated
    """
    # Add elegant visual styling to the prompt with improved color palette
    styled_prompt = f"\033[38;5;75m?\033[0m {prompt_text}"

    # Show default if available with subtle styling
    if display_default:
        prompt_display = (
            f"{styled_prompt} \033[38;5;245m(default: {display_default})\033[0m: "
        )
    else:
        prompt_display = f"{styled_prompt}: "

    # Indicate if field is required with gentle color indicator
    if required:
        print(f"\033[38;5;208m✱\033[0m Field is required")

    # Handle password/sensitive input with enhanced security
    if is_sensitive:
        import getpass

        try:
            # Attempt to use getpass for truly invisible input
            print(prompt_display, end="", flush=True)
            value = getpass.getpass("")
        except (EOFError, KeyboardInterrupt):
            # User canceled the input
            print("\n\033[38;5;203m✗ Input canceled\033[0m")
            return fallback_input(
                prompt_text, var_type, required, display_default, default, is_sensitive
            )
        except Exception:
            # Getpass failed, fallback to regular input with a warning
            print(
                "\033[38;5;208m⚠ Secure input not available, characters will be visible\033[0m"
            )
            value = input(prompt_display)
    else:
        # Standard input for non-sensitive fields
        value = input(prompt_display)

    # Use default if input is empty and default exists
    if not value and default is not None:
        return str(default)

    # Skip validation for empty non-required fields
    if not value and not required:
        print("\033[38;5;245m○ Field left empty (optional)\033[0m")
        return value

    # Import validation here to ensure it's available
    import sys
    import os

    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)

    from config_wizard.validators import validate_by_type

    # Validate the input with elegant feedback
    is_valid, error, validated_value = validate_by_type(value, var_type)

    if is_valid:
        # Show confirmation of the successful input with premium styling
        if is_sensitive:
            # For sensitive data, show tastefully masked confirmation
            masked_value = mask_sensitive_value(value)
            print(
                f"\033[38;5;78m✓\033[0m Value securely stored: \033[38;5;245m{masked_value}\033[0m"
            )
        else:
            # For regular data, show the value with elegant styling
            print(f"\033[38;5;78m✓\033[0m Value set: \033[38;5;75m{value}\033[0m")

        # For types that have conversion, use the converted value as a string
        if var_type in ["int", "float", "bool"]:
            return str(validated_value)
        return value
    else:
        # Show error and try again with refined styling
        print(f"\033[38;5;203m✗ Error: {error}\033[0m")
        print("\033[38;5;245m→ Please try again\033[0m")
        return fallback_input(
            prompt_text, var_type, required, display_default, default, is_sensitive
        )


def prompt_for_value(
    var_name: str, var_info: Dict[str, Any], current_value: Optional[str] = None
) -> str:
    """
    Display an elegant interactive input prompt for configuration values with premium styling,
    sophisticated validation, and intelligent guidance.

    Args:
        var_name: The variable name
        var_info: The variable metadata
        current_value: The current value, if any

    Returns:
        str: The validated user input
    """
    # Use the enhanced interactive input function if available
    try:
        from .cli import interactive_value_input

        while True:  # Loop until we get valid input
            # Get the value from interactive input
            value = interactive_value_input(var_name, var_info, current_value)

            # Apply strict validation from validators module
            var_type = var_info.get("type", "string")
            is_required = var_info.get("required", False)

            # Skip validation for empty non-required fields
            if not value and not is_required:
                cli.print_info("Field left empty (optional).")
                return value

            # Don't allow empty values for required fields
            if not value and is_required:
                cli.print_error("This field is required. Please try again.")
                continue

            # Validate the input
            is_valid, error_message, validated_value = validate_by_type(value, var_type)

            if is_valid:
                # For types that have conversion, use the converted value as a string
                if var_type in ["int", "float", "bool"]:
                    return str(validated_value)
                return value
            else:
                # Show error and prompt again
                cli.print_error(f"Validation error: {error_message}")
                cli.print_info("Please try again with a valid value.")
                # Continue loop to get new input

    except ImportError:
        # Fall back to the premium implementation if there's an issue
        var_type = var_info.get("type", "string")
        default = current_value or var_info.get("default", "")
        required = var_info.get("required", False)
        example = var_info.get("example", "")

        # For sensitive data, prepare sophisticated visual styling
        is_sensitive = any(
            s in var_name.lower() for s in ["password", "secret", "key", "token", "api"]
        )

        # Apply premium styling based on input type
        input_style = "gold1" if is_sensitive else THEME["accent"]
        symbol = "🔐" if is_sensitive else "✦"

        # Build the prompt text (this will be used by cli.ask_input, not displayed here)
        prompt_text = f"Enter value"  # Simplified since name is already displayed

        # Add type information with elegant formatting
        type_info = f"[{var_type}]"
        if example:
            type_info += f" e.g. {var_info['example']}"
        prompt_text += f" {type_info}"

        # Add required indicator with premium styling
        if required:
            prompt_text += f" [bold {THEME['error']}](required)[/]"

        # Show all default values with elegant styling
        display_default = default

        # Use the updated input handling with premium fallback and visual cues
        try:
            # First try the rich prompt for better UI with sophisticated indicators
            try:
                # Print a more informative header for sensitive fields with premium styling
                if is_sensitive:
                    console.print(
                        f"[dim]{symbol} [gold1]Secure field[/] - input will be masked for security[/]"
                    )

                # Add intelligent suggestions for common field types with elegant styling
                if var_type == "telegram_token" and not default:
                    console.print(f"[dim]💡 Get this from @BotFather on Telegram[/]")
                elif var_type == "url" and not default:
                    console.print(f"[dim]💡 Should start with http:// or https://[/]")
                elif var_type == "mongodb_uri" and not default:
                    console.print(
                        f"[dim]💡 Format: ********************************:port/database[/]"
                    )
                elif var_type == "email" and not default:
                    console.print(f"[dim]💡 Enter a valid email address[/]")
                elif var_type == "port" and not default:
                    console.print(
                        f"[dim]💡 Port numbers typically range from 1-65535[/]"
                    )

                # Loop until we get valid input
                while True:
                    # Set password parameter to True for sensitive fields to mask input with premium handling
                    value = cli.ask_input(
                        f"[bold {input_style}]{prompt_text}[/]",
                        default=display_default,
                        password=is_sensitive,
                    )

                    # Don't allow empty values for required fields with elegant error handling
                    if not value and required:
                        cli.print_error("This field is required")
                        continue

                    # Skip validation for empty non-required fields with subtle feedback
                    if not value and not required:
                        console.print("[dim]○ Field left empty (optional)[/]")
                        return value

                    # Validate the input with sophisticated error handling using validators module
                    is_valid, error, validated_value = validate_by_type(value, var_type)

                    if is_valid:
                        # For types that have conversion, use the converted value as a string
                        if var_type in ["int", "float", "bool"]:
                            return str(validated_value)
                        return value
                    else:
                        cli.print_error(error)
                        cli.print_info("Please provide a valid value.")
                        # Continue loop to get new input

            except Exception as e:
                cli.print_error(f"Input error: {str(e)}")
                # Fall back to premium input if rich input fails
                return fallback_input(
                    prompt_text,
                    var_type,
                    required,
                    display_default,
                    default,
                    is_sensitive=is_sensitive,
                )

        except KeyboardInterrupt:
            console.print("\n[dim]✓ Input interrupted[/]")
            raise


def setup_wizard() -> bool:
    """
    Run the setup wizard to configure the application.

    Returns:
        bool: True if setup was successful, False otherwise
    """
    try:
        # Welcome message
        cli.print_welcome()

        # Initialize the configuration manager
        console.print("[dim]✓ Loading configuration system[/]")
        config = ConfigManager()

        # Load any existing configuration
        with console.status(
            f"[{THEME['accent']}]Scanning for existing configuration...",
            spinner="line",
        ):
            current_config = config.load_current_env()

        # Parse the template with metadata
        with console.status(
            f"[{THEME['accent']}]Analyzing configuration requirements...",
            spinner="dots",
        ):
            var_metadata = config.parse_template_with_metadata()

        # Define the configuration structure as per requirements
        if not var_metadata:
            cli.print_warning(
                "No configuration template found. Using predefined configuration structure."
            )

            # Create the configuration structure as per the requirements
            var_metadata = {
                # Bot Configuration
                "API_TOKEN": {
                    "default": "7625560158:AAEUrJ7faRxzuSpRtSQfbmiO7VxnytNBhgw",
                    "required": True,
                    "type": "telegram_token",
                    "description": "Telegram Bot API Token",
                    "section": "Bot Configuration",
                    "help": "Get this from @BotFather when creating a new bot",
                    "example": "7625560158:AAEUrJ7faRxzuSpRtSQfbmiO7VxnytNBhgw",
                },
                "ADMIN_ID": {
                    "default": "7845929154",
                    "required": False,
                    "type": "int",
                    "description": "Telegram User ID for the bot administrator",
                    "section": "Bot Configuration",
                    "help": "Find this using @userinfobot on Telegram",
                    "example": "7845929154",
                },
                "OWNER_ID": {
                    "default": "7845929154",
                    "required": False,
                    "type": "int",
                    "description": "Telegram User ID for the bot owner",
                    "section": "Bot Configuration",
                    "help": "Find this using @userinfobot on Telegram",
                    "example": "7845929154",
                },
                # MongoDB Configuration
                "MONGO_URI": {
                    "default": "mongodb+srv://alpha:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0",
                    "required": True,
                    "type": "mongodb_uri",
                    "description": "MongoDB Connection URI",
                    "section": "MongoDB Configuration",
                    "help": "URL for connecting to your MongoDB database",
                    "example": "mongodb+srv://username:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0",
                },
                "MONGO_DB": {
                    "default": "telegram_shop_bot1",
                    "required": True,
                    "type": "string",
                    "description": "MongoDB Database Name",
                    "section": "MongoDB Configuration",
                    "help": "Name of the database to use for this bot",
                    "example": "telegram_shop_bot1",
                },
                # MongoDB connection settings
                "MONGO_CONNECT_TIMEOUT_MS": {
                    "default": "30000",
                    "required": False,
                    "type": "int",
                    "description": "MongoDB connection timeout in milliseconds",
                    "section": "MongoDB Configuration",
                    "help": "Timeout for MongoDB connection attempts",
                    "example": "30000",
                },
                "MONGO_SOCKET_TIMEOUT_MS": {
                    "default": "120000",
                    "required": False,
                    "type": "int",
                    "description": "MongoDB socket timeout in milliseconds",
                    "section": "MongoDB Configuration",
                    "help": "Timeout for MongoDB socket operations",
                    "example": "120000",
                },
                "MONGO_SERVER_SELECTION_TIMEOUT_MS": {
                    "default": "30000",
                    "required": False,
                    "type": "int",
                    "description": "MongoDB server selection timeout in milliseconds",
                    "section": "MongoDB Configuration",
                    "help": "Timeout for MongoDB server selection",
                    "example": "30000",
                },
                # Payment Gateway Settings
                "OXA_PAY_API_KEY": {
                    "default": "U3TN4U-GEM145-WKZKRR-S4E67B",
                    "required": True,
                    "type": "string",
                    "description": "OXA Pay API Key",
                    "section": "Payment Gateway Settings",
                    "help": "API key for OXA payment processing",
                    "example": "U3TN4U-GEM145-WKZKRR-S4E67B",
                },
                "OXA_PAY_CALLBACK_URL": {
                    "default": "",
                    "required": False,
                    "type": "url",
                    "description": "OXA Pay Callback URL (Optional)",
                    "section": "Payment Gateway Settings",
                    "help": "URL where payment notifications will be sent. If not provided, the system will automatically generate a callback URL using the server's IP address and port.",
                    "example": "https://your-domain.com/callback",
                },
                "HTTP_TIMEOUT": {
                    "default": "30",
                    "required": False,
                    "type": "int",
                    "description": "HTTP Timeout for payment gateway requests (seconds)",
                    "section": "Payment Gateway Settings",
                    "help": "Timeout for HTTP requests to payment gateway",
                    "example": "30",
                },
                "PAYMENT_API_TIMEOUT": {
                    "default": "60",
                    "required": False,
                    "type": "int",
                    "description": "Timeout for payment API requests (seconds)",
                    "section": "Payment Gateway Settings",
                    "help": "Timeout for payment API requests",
                    "example": "60",
                },
                # Support Contact Information
                "SUPPORT_USERNAME": {
                    "default": "support_username",
                    "required": False,
                    "type": "string",
                    "description": "Telegram username for support contact",
                    "section": "Support Contact Information",
                    "help": "Telegram username without @ symbol",
                    "example": "support_username",
                },
                "SUPPORT_EMAIL": {
                    "default": "<EMAIL>",
                    "required": False,
                    "type": "email",
                    "description": "Email address for support contact",
                    "section": "Support Contact Information",
                    "help": "Email address where users can contact support",
                    "example": "<EMAIL>",
                },
                "SUPPORT_WEBSITE": {
                    "default": "https://example.com/support",
                    "required": False,
                    "type": "url",
                    "description": "Website URL for support",
                    "section": "Support Contact Information",
                    "help": "URL of the support website",
                    "example": "https://example.com/support",
                },
                # System Settings
                "DEBUG_MODE": {
                    "default": "True",
                    "required": False,
                    "type": "bool",
                    "description": "Enable debug mode",
                    "section": "System Settings",
                    "help": "Set to True to enable debug logging and features",
                    "example": "True",
                },
                "MAINTENANCE_MODE": {
                    "default": "False",
                    "required": False,
                    "type": "bool",
                    "description": "Enable maintenance mode",
                    "section": "System Settings",
                    "help": "Set to True to put the bot in maintenance mode",
                    "example": "False",
                },
                "MAINTENANCE_MESSAGE": {
                    "default": "The bot is currently under maintenance and will be back soon.",
                    "required": False,
                    "type": "string",
                    "description": "Message to show during maintenance",
                    "section": "System Settings",
                    "help": "Message to display when maintenance mode is enabled",
                    "example": "The bot is currently under maintenance and will be back soon.",
                },
                # Logging
                "LOG_CHANNEL_ID": {
                    "default": "-1002324489401",
                    "required": False,
                    "type": "int",
                    "description": "Telegram channel ID for logging",
                    "section": "System Settings",
                    "help": "Channel ID where logs will be sent (must start with -100)",
                    "example": "-1002324489401",
                },
            }

        # Group variables by section
        sections = {}
        for var_name, var_info in var_metadata.items():
            section = var_info.get("section", "General")
            if section not in sections:
                sections[section] = []
            sections[section].append((var_name, var_info))

        # Check if backup is needed
        if config.check_config_exists():
            cli.print_info("Existing configuration detected")

            # Ask if user wants to modify
            if cli.ask_yes_no(
                "Would you like to modify your existing configuration?", default=True
            ):
                # Backup current config
                with console.status(
                    f"[{THEME['accent']}]Creating backup of existing configuration...",
                    spinner="dots",
                ):
                    backup_result = backup_existing_env()

                if backup_result:
                    cli.print_success("Backup created successfully")
            else:
                cli.print_info("Keeping existing configuration")
                return True

        # Map section names to emojis
        section_emojis = {
            "General": "🔧",
            "Bot Configuration": "🤖",
            "MongoDB Configuration": "💾",
            "Payment Gateway Settings": "💸",
            "Support Contact Information": "📞",
            "System Settings": "🔧",
        }

        # Show overview of sections to configure
        cli.console.rule("[bold]Configuration Wizard[/]")
        cli.print_info(f"We'll configure {len(sections)} sections:")

        # Show sections
        for section_name in sections.keys():
            emoji = section_emojis.get(section_name, "🔧")
            required_count = sum(
                1 for _, info in sections[section_name] if info.get("required", False)
            )
            total_count = len(sections[section_name])

            # Required indicator
            if required_count > 0:
                required_indicator = f"[bold red]{required_count}[/] required"
            else:
                required_indicator = "[dim]all optional[/]"

            cli.console.print(
                f"  {emoji} [bold]{section_name}[/] "
                f"([cyan]{total_count}[/] settings, {required_indicator})"
            )

        cli.console.print()  # Add spacing

        # Process each section
        for i, (section_name, section_vars) in enumerate(sections.items(), 1):
            # Get emoji for this section
            emoji = section_emojis.get(section_name, "🔧")

            # Print section header
            cli.console.rule(f"{emoji} {section_name}")
            cli.print_step(i, len(sections), f"Configuring {section_name}")

            # Process each variable in the section
            for var_name, var_info in section_vars:
                current_value = current_config.get(var_name, None)

                # Get the value
                value = prompt_for_value(var_name, var_info, current_value)
                current_config[var_name] = value

                # Determine field sensitivity
                is_sensitive = any(
                    s in var_name.lower()
                    for s in ["password", "secret", "key", "token", "api"]
                )
                is_required = var_info.get("required", False)

                # Determine appropriate icon and status
                if not value:
                    if is_required:
                        icon = "⚠️"  # Warning for empty required field
                        status = "[yellow bold]WARNING: Empty required field![/]"
                    else:
                        icon = "⚪"  # Empty optional field
                        status = "[dim]skipped (optional)[/]"
                elif is_sensitive:
                    icon = "🔒"  # Sensitive information
                    status = "[green]secured successfully[/]"
                else:
                    icon = "✅"  # Regular field
                    status = "[green]validated successfully[/]"

                # Display value with masking for sensitive fields
                if is_sensitive and value:
                    visible_part = value[:2] if len(value) > 4 else ""
                    masked_part = (
                        "\u2022" * (len(value) - 2)
                        if len(value) > 4
                        else "\u2022" * len(value)
                    )
                    display_value = f"{visible_part}{masked_part}"
                    cli.console.print(
                        f"  {icon} Set to: [dim]{display_value}[/] {status}"
                    )
                else:
                    if value:
                        cli.console.print(
                            f"  {icon} Set to: [cyan]'{value}'[/] {status}"
                        )
                    else:
                        cli.console.print(
                            f"  {icon} Set to: [dim italic]empty[/] {status}"
                        )

            # Show completion for this section
            cli.console.print(f"\n[bold green]✓ {section_name}[/] section complete!")

            # Preview next section if not the last
            if i < len(sections):
                sections_list = list(sections.keys())
                next_section = sections_list[i]
                cli.console.print(
                    f"\n[bold]Coming up next:[/] {section_emojis.get(next_section, '🔧')} "
                    f"[bold]{next_section}[/]\n"
                )

        # Show a preview of the configuration
        cli.print_section_header("Configuration Preview")
        cli.print_config_preview(current_config)

        # Confirm and save
        if cli.ask_yes_no("Save this configuration?", default=True):
            # Save the configuration
            with console.status(
                f"[{THEME['accent']}]Saving configuration to disk...", spinner="dots"
            ):
                success = config.create_config_file(current_config)

            if success:
                cli.print_completion()
                cli.print_success("Configuration has been saved successfully")
                cli.print_info("✨ Application is ready to start! ✨")
                return True
            else:
                cli.print_error("Failed to save configuration")
                return False
        else:
            cli.print_warning("Configuration not saved")
            return False

    except KeyboardInterrupt:
        cli.print_warning("\n⚠ Setup wizard was interrupted. Configuration not saved.")
        return False
    except Exception as e:
        cli.print_error(f"⚠ An error occurred during setup: {str(e)}")
        return False


# Simplified API that can be called from main.py
def check_and_setup_env(required_vars: List[str] = None) -> bool:
    """
    Check if the environment is configured and run the setup wizard if needed.

    Args:
        required_vars: List of required variable names

    Returns:
        bool: True if the environment is properly configured
    """
    # Initialize with default paths, normalizing them
    config = ConfigManager(
        env_file=normalize_env_path(".env"),
        env_template=normalize_env_path(".env.example"),
    )

    # If no specific requirements, use general existence check
    if not config.check_config_exists():
        cli.print_warning("No configuration file found.")
        return setup_wizard()

    # If specific requirements provided, check them
    if required_vars:
        missing = config.get_missing_required_vars(required_vars)
        if missing:
            cli.print_warning(f"Missing required configuration: {', '.join(missing)}")
            return setup_wizard()

    # Configuration exists and meets requirements
    return True
