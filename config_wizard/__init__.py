"""
Configuration Wizard Package for Dynamic Environment Setup

This package provides tools for detecting, validating, and interactively
setting up application configuration through an engaging terminal interface.

Main components:
- manager: Core configuration operations (loading, saving, validation)
- cli: Terminal interface with colorama/rich for user interaction
- validators: Input validation for various data types
- setup_wizard: Interactive step-by-step configuration wizard
- utils: Helper functions for configuration management
"""

# Use absolute imports instead of relative imports
import sys
import os
from .validators import validate_by_type

# Add parent directory to path if needed
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

from config_wizard.manager import ConfigManager
from config_wizard.setup_wizard import setup_wizard

__all__ = ["ConfigManager", "setup_wizard", "validate_by_type"]
