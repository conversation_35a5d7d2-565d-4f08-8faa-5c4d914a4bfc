"""
Configuration Wizard Utility Functions

This module provides utility functions for the configuration wizard,
including input handling, formatting, and environment variable operations.
"""

import os
import re
import sys
import time
from typing import Dict, Any, Optional, List, Tuple, Union


def parse_env_file(file_path: str) -> Dict[str, str]:
    """
    Parse an environment file (.env or .env.example) into a dictionary.

    Args:
        file_path: Path to the environment file

    Returns:
        Dict[str, str]: Dictionary of environment variables
    """
    if not os.path.exists(file_path):
        return {}

    env_vars = {}
    current_comment = []

    with open(file_path, "r") as f:
        for line in f:
            line = line.strip()

            # Skip empty lines
            if not line:
                continue

            # Handle comments
            if line.startswith("#"):
                current_comment.append(line)
                continue

            # Handle variable definitions
            if "=" in line:
                key, value = line.split("=", 1)
                key = key.strip()
                value = value.strip().strip("'\"")

                # Store comments with the variable
                if current_comment:
                    env_vars[f"#{key}"] = "\n".join(current_comment)
                    current_comment = []

                env_vars[key] = value

    return env_vars


def merge_env_files(env_file: str, env_example: str) -> Dict[str, str]:
    """
    Merge .env and .env.example files, prioritizing .env values.

    Args:
        env_file: Path to the .env file
        env_example: Path to the .env.example file

    Returns:
        Dict[str, str]: Merged environment variables
    """
    example_vars = parse_env_file(env_example)
    env_vars = parse_env_file(env_file)

    # Merge, prioritizing env_vars
    result = example_vars.copy()
    result.update(env_vars)

    return result


def load_env_from_file(file_path: str) -> Dict[str, str]:
    """
    Load environment variables from a file into a dictionary.
    Does not set the environment variables in the process.

    Args:
        file_path: Path to the environment file

    Returns:
        Dict[str, str]: Dictionary of environment variables
    """
    return parse_env_file(file_path)


def get_input_with_timeout(
    prompt: str, timeout: int = 30, default: Optional[str] = None
) -> Optional[str]:
    """
    Get user input with a timeout.

    Args:
        prompt: The prompt to display
        timeout: Timeout in seconds
        default: Default value if timeout occurs

    Returns:
        str: User input or default value
    """
    # If timeout is disabled, use standard input directly
    if timeout <= 0:
        return input(prompt)

    # Check if this is a sensitive input (password, token, etc.)
    is_sensitive = any(
        s in prompt.lower() for s in ["password", "secret", "key", "token"]
    )

    # Enhanced visual prompt with styling
    styled_prompt = prompt
    if not styled_prompt.endswith(": "):
        styled_prompt += ": "

    # Add visual indicators for sensitive fields
    if is_sensitive:
        print("\033[33m🔒 Secure input field\033[0m")  # Yellow lock icon

    # Cross-platform input timeout handling
    try:
        # For Windows
        if os.name == "nt":
            import msvcrt
            import threading
            import queue

            # Use a queue to safely pass data between threads
            input_queue = queue.Queue()

            # Print the prompt with styling
            print("\033[36m?\033[0m " + styled_prompt, end="", flush=True)

            def input_thread():
                try:
                    # Get input directly without relying on the input() function
                    # which can sometimes block on Windows
                    line = ""
                    cursor_pos = 0

                    while True:
                        if msvcrt.kbhit():
                            char = msvcrt.getwch()
                            if char == "\r":  # Enter key
                                print("")  # Move to the next line
                                break
                            elif char == "\x08":  # Backspace
                                if line and cursor_pos > 0:
                                    # Remove character from the line
                                    line = line[: cursor_pos - 1] + line[cursor_pos:]
                                    cursor_pos -= 1

                                    # Redraw the line with proper masking for sensitive data
                                    print(
                                        "\r"
                                        + " " * len(styled_prompt)
                                        + " " * len(line)
                                        + 1,
                                        end="\r",
                                        flush=True,
                                    )
                                    print(styled_prompt, end="", flush=True)

                                    if is_sensitive:
                                        # Show bullet characters for password
                                        print(
                                            "\u2022" * len(line[:cursor_pos]),
                                            end="",
                                            flush=True,
                                        )
                                    else:
                                        # Show actual text
                                        print(line[:cursor_pos], end="", flush=True)
                            elif char == "\xe0":  # Arrow key prefix
                                arrow = msvcrt.getwch()  # Get the actual arrow key code
                                if arrow == "K" and cursor_pos > 0:  # Left arrow
                                    cursor_pos -= 1
                                    print("\b", end="", flush=True)
                                elif arrow == "M" and cursor_pos < len(
                                    line
                                ):  # Right arrow
                                    cursor_pos += 1
                                    if is_sensitive:
                                        print("\u2022", end="", flush=True)
                                    else:
                                        print(line[cursor_pos - 1], end="", flush=True)
                            else:
                                # Insert character at cursor position
                                line = line[:cursor_pos] + char + line[cursor_pos:]
                                cursor_pos += 1

                                # Display according to sensitivity
                                if is_sensitive:
                                    # Show bullet for sensitive data
                                    print("\u2022", end="", flush=True)
                                else:
                                    # Show actual character
                                    print(char, end="", flush=True)

                    input_queue.put(line)
                except Exception as e:
                    input_queue.put(None)
                    print(f"\n\033[31mInput error: {str(e)}\033[0m")  # Red error text

            thread = threading.Thread(target=input_thread, daemon=True)
            thread.start()

            # Wait for input or timeout
            thread.join(timeout)

            if thread.is_alive():
                # Thread is still alive, timeout occurred
                print(
                    "\n\033[33mTimeout reached, using default value.\033[0m"
                )  # Yellow timeout message
                # We need to ensure the thread doesn't block indefinitely
                # Just return the default value and let the thread continue in the background
                return default

            # Check if we got input
            try:
                if not input_queue.empty():
                    user_input = input_queue.get(block=False)
                    return user_input
                return default
            except queue.Empty:
                return default

        # For Unix-like systems
        else:
            import termios
            import fcntl
            import select

            # Save terminal settings
            fd = sys.stdin.fileno()
            oldterm = termios.tcgetattr(fd)
            newattr = termios.tcgetattr(fd)
            newattr[3] = newattr[3] & ~termios.ICANON & ~termios.ECHO
            termios.tcsetattr(fd, termios.TCSANOW, newattr)

            oldflags = fcntl.fcntl(fd, fcntl.F_GETFL)
            fcntl.fcntl(fd, fcntl.F_SETFL, oldflags | os.O_NONBLOCK)

            try:
                # Print the prompt with styling
                print("\033[36m?\033[0m " + styled_prompt, end="", flush=True)
                result = ""
                cursor_pos = 0
                start_time = time.time()

                while True:
                    if time.time() - start_time > timeout:
                        print(
                            "\n\033[33mTimeout reached, using default value.\033[0m"
                        )  # Yellow timeout message
                        return default

                    try:
                        # Check if data is available to read
                        if select.select([sys.stdin], [], [], 0.1)[0]:
                            c = sys.stdin.read(1)
                            if c:
                                if c == "\n":
                                    break
                                elif c == "\x7f":  # Backspace
                                    if result and cursor_pos > 0:
                                        # Remove character at cursor position
                                        result = (
                                            result[: cursor_pos - 1]
                                            + result[cursor_pos:]
                                        )
                                        cursor_pos -= 1

                                        # Redraw the line
                                        print(
                                            "\r"
                                            + " " * len(styled_prompt)
                                            + " " * len(result)
                                            + 1,
                                            end="\r",
                                            flush=True,
                                        )
                                        print(styled_prompt, end="", flush=True)

                                        if is_sensitive:
                                            # Show bullet characters for password
                                            print(
                                                "\u2022" * len(result[:cursor_pos]),
                                                end="",
                                                flush=True,
                                            )
                                        else:
                                            # Show actual text
                                            print(
                                                result[:cursor_pos], end="", flush=True
                                            )
                                elif c == "\x1b":  # Escape sequence (for arrow keys)
                                    # Read the next two characters
                                    if sys.stdin.read(1) == "[":
                                        arrow = sys.stdin.read(1)
                                        if (
                                            arrow == "D" and cursor_pos > 0
                                        ):  # Left arrow
                                            cursor_pos -= 1
                                            print("\b", end="", flush=True)
                                        elif arrow == "C" and cursor_pos < len(
                                            result
                                        ):  # Right arrow
                                            cursor_pos += 1
                                            if is_sensitive:
                                                print("\u2022", end="", flush=True)
                                            else:
                                                print(
                                                    result[cursor_pos - 1],
                                                    end="",
                                                    flush=True,
                                                )
                                else:
                                    # Insert character at cursor position
                                    result = (
                                        result[:cursor_pos] + c + result[cursor_pos:]
                                    )
                                    cursor_pos += 1

                                    # Display appropriately
                                    if is_sensitive:
                                        # Show bullet character for sensitive data
                                        print("\u2022", end="", flush=True)
                                    else:
                                        # Show actual character
                                        print(c, end="", flush=True)
                    except IOError:
                        pass
                    except KeyboardInterrupt:
                        return None

                    time.sleep(0.01)

                print("", flush=True)
                return result
            finally:
                # Restore terminal settings
                termios.tcsetattr(fd, termios.TCSAFLUSH, oldterm)
                fcntl.fcntl(fd, fcntl.F_SETFL, oldflags)

    except Exception as e:
        # Log the error for debugging
        print(f"\n\033[31mInput handling error: {str(e)}\033[0m")  # Red error text

        # Fallback to standard input without timeout
        if is_sensitive:
            import getpass

            print(
                "\n\033[33mFalling back to secure input method.\033[0m"
            )  # Yellow fallback message
            try:
                # Use getpass for sensitive input
                value = getpass.getpass(prompt=styled_prompt)
                return value if value else default
            except Exception as inner_e:
                print(
                    f"\n\033[31mSecure input error: {str(inner_e)}\033[0m"
                )  # Red error text
                # Fall back to regular input as last resort
                return input(styled_prompt) or default
        else:
            return input(styled_prompt) or default


def is_comment_required(comment: str) -> bool:
    """
    Check if a comment indicates that a variable is required.

    Args:
        comment: The comment string

    Returns:
        bool: True if the variable is required, False otherwise
    """
    return re.search(r"required", comment, re.IGNORECASE) is not None


def extract_type_from_comment(comment: str) -> Optional[str]:
    """
    Extract the type information from a comment.

    Args:
        comment: The comment string

    Returns:
        Optional[str]: The type string or None
    """
    type_match = re.search(r"type:\s*(\w+)", comment, re.IGNORECASE)
    if type_match:
        return type_match.group(1).lower()
    return None


def format_env_file(env_vars: Dict[str, str], include_comments: bool = True) -> str:
    """
    Format environment variables into a string suitable for a .env file.

    Args:
        env_vars: Dictionary of environment variables
        include_comments: Whether to include comments

    Returns:
        str: Formatted string
    """
    lines = []

    for key, value in env_vars.items():
        # Handle comments
        if key.startswith("#") and include_comments:
            lines.append(value)  # value is the comment text
        elif not key.startswith("#"):
            lines.append(f"{key}={value}")

    return "\n".join(lines)


def normalize_env_path(file_path: str) -> str:
    """
    Normalize a path for environment file.

    Args:
        file_path: The file path to normalize

    Returns:
        str: The normalized path
    """
    if os.path.isabs(file_path):
        return file_path
    return os.path.join(
        os.path.dirname(os.path.dirname(os.path.abspath(__file__))), file_path
    )
