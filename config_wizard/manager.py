"""
Configuration Manager Module

This module provides the ConfigManager class for handling configuration operations
including loading from environment variables, validating inputs, and generating
new configuration files.
"""

import os
import json
import logging
from typing import Dict, Any, List

logger = logging.getLogger(__name__)


class ConfigManager:
    """
    Configuration Manager for handling application settings.

    This class provides a unified interface for working with both environment
    variable based configuration (.env) and JSON configuration (config.json).

    Features:
    - Load configurations from .env files
    - Check for missing required configuration
    - Save configurations to .env file with proper formatting
    - Support both required and optional configuration values
    - Maintain backwards compatibility with existing config usage
    """

    def __init__(
        self,
        env_file: str = ".env",
        env_template: str = ".env.example",
        json_config_file: str = "config.json",
    ):
        """
        Initialize the ConfigManager.

        Args:
            env_file: Path to the .env file
            env_template: Path to the .env template file
            json_config_file: Path to the JSON configuration file
        """
        self.env_file = env_file
        self.env_template = env_template
        self.json_config_file = json_config_file
        self.current_config = {}
        self.template_config = {}

        # Load configuration templates if they exist
        self._load_template_config()

    def _load_template_config(self) -> None:
        """Load the template configuration from the .env.example file if it exists."""
        if os.path.exists(self.env_template):
            with open(self.env_template, "r") as f:
                for line in f:
                    line = line.strip()
                    # Skip comments and empty lines
                    if not line or line.startswith("#"):
                        continue

                    # Parse variable definition (VAR_NAME=default_value)
                    if "=" in line:
                        key, value = line.split("=", 1)
                        self.template_config[key.strip()] = value.strip().strip("'\"")

    def check_config_exists(self) -> bool:
        """
        Check if the configuration file exists.

        Returns:
            bool: True if the configuration file exists, False otherwise
        """
        return os.path.exists(self.env_file)

    def load_current_env(self) -> Dict[str, str]:
        """
        Load the current environment variables from the .env file.

        Returns:
            Dict[str, str]: Dictionary of environment variables
        """
        env_vars = {}

        if not os.path.exists(self.env_file):
            return env_vars

        with open(self.env_file, "r") as f:
            for line in f:
                line = line.strip()
                # Skip comments and empty lines
                if not line or line.startswith("#"):
                    continue

                # Parse variable definition
                if "=" in line:
                    key, value = line.split("=", 1)
                    env_vars[key.strip()] = value.strip().strip("'\"")

        self.current_config = env_vars
        return env_vars

    def get_missing_required_vars(self, required_vars: List[str]) -> List[str]:
        """
        Get a list of required variables that are missing from the configuration.

        Args:
            required_vars: List of required variable names

        Returns:
            List[str]: List of missing required variables
        """
        if not self.current_config:
            self.load_current_env()

        missing_vars = []
        for var in required_vars:
            # Check if the variable exists in the current config
            if var not in self.current_config:
                missing_vars.append(var)
            # Check if the variable exists but is empty
            elif not self.current_config[var].strip():
                missing_vars.append(var)

        return missing_vars

    def create_config_file(
        self, config_vars: Dict[str, str], include_comments: bool = True
    ) -> bool:
        """
        Create a new configuration file with the provided variables.

        Args:
            config_vars: Dictionary of configuration variables
            include_comments: Whether to include comments from the template

        Returns:
            bool: True if the file was created successfully, False otherwise
        """
        try:
            # Create the directory if it doesn't exist
            os.makedirs(os.path.dirname(os.path.abspath(self.env_file)), exist_ok=True)

            # Load the template for comments if available
            template_lines = []
            if os.path.exists(self.env_template) and include_comments:
                with open(self.env_template, "r") as f:
                    template_lines = f.readlines()

            # Create a new .env file
            with open(self.env_file, "w") as f:
                if include_comments and template_lines:
                    # Try to preserve comments and format from template
                    processed_keys = set()

                    for line in template_lines:
                        original_line = line
                        line = line.strip()

                        # Handle empty lines
                        if not line:
                            f.write("\n")
                            continue

                        # Handle section headers and comments
                        if line.startswith("#"):
                            # Write comments as is
                            f.write(f"{original_line}\n")
                            continue

                        # Handle variable definitions
                        if "=" in line:
                            # Replace value in template
                            key = line.split("=", 1)[0].strip()
                            if key in config_vars:
                                value = config_vars[key]
                                processed_keys.add(key)
                                f.write(f"{key}={value}\n")
                            else:
                                # Keep the original line if key not in config_vars
                                f.write(f"{original_line}\n")

                    # Add any keys that weren't in the template
                    if processed_keys:
                        remaining_keys = set(config_vars.keys()) - processed_keys
                        if remaining_keys:
                            f.write("\n# Additional Configuration\n")
                            for key in remaining_keys:
                                f.write(f"{key}={config_vars[key]}\n")
                else:
                    # Simple key=value format with sections
                    sections = {}

                    # Group variables by section (if we can determine it)
                    for key, value in config_vars.items():
                        section = "General"

                        # Try to determine section from key name
                        if "MONGO" in key:
                            section = "MongoDB Configuration"
                        elif "API_TOKEN" in key or "ADMIN" in key or "OWNER" in key:
                            section = "Bot Configuration"
                        elif "OXA_PAY" in key:
                            section = "Payment Gateway Settings"
                        elif "SUPPORT" in key:
                            section = "Support Contact Information"
                        elif any(mode in key for mode in ["MODE", "DEBUG"]):
                            section = "System Settings"

                        if section not in sections:
                            sections[section] = []
                        sections[section].append((key, value))

                    # Write sections with headers
                    for section, vars in sections.items():
                        f.write(f"\n# --- {section} ---\n")
                        for key, value in vars:
                            f.write(f"{key}={value}\n")

            logger.info(f"Created configuration file at {self.env_file}")
            return True
        except Exception as e:
            logger.error(f"Failed to create configuration file: {e}")
            return False

    def _update_config(self, key: str, value: str) -> bool:
        """
        Update a single configuration value in the .env file.

        Args:
            key: The configuration key
            value: The new value

        Returns:
            bool: True if the update was successful, False otherwise
        """
        # This method might be called dynamically or used in ways Vulture can't detect
        try:
            # Load current config
            current_config = self.load_current_env()

            # Update the value
            current_config[key] = value

            # Save back to file
            return self.create_config_file(current_config)
        except Exception as e:
            logger.error(f"Failed to update configuration: {e}")
            return False

    def _save_json_config(self, config_data: Dict[str, Any]) -> bool:
        """
        Save configuration to JSON file.

        Args:
            config_data: Configuration data to save

        Returns:
            bool: True if saved successfully, False otherwise
        """
        # This method might be called dynamically or used in ways Vulture can't detect
        try:
            with open(self.json_config_file, "w") as f:
                json.dump(config_data, f, indent=4)
            return True
        except Exception as e:
            logger.error(f"Failed to save JSON configuration: {e}")
            return False

    def _load_json_config(self) -> Dict[str, Any]:
        """
        Load configuration from JSON file.

        Returns:
            Dict[str, Any]: Configuration data
        """
        # This method might be called dynamically or used in ways Vulture can't detect
        try:
            if os.path.exists(self.json_config_file):
                with open(self.json_config_file, "r") as f:
                    return json.load(f)
            return {}
        except Exception as e:
            logger.error(f"Failed to load JSON configuration: {e}")
            return {}

    def _get_template_vars(self) -> Dict[str, str]:
        """
        Get configuration variables from the template.

        Returns:
            Dict[str, str]: Dictionary of variable names and default values
        """
        # This method might be called dynamically or used in ways Vulture can't detect
        return self.template_config

    def _is_config_complete(self, required_vars: List[str]) -> bool:
        """
        Check if all required configuration variables are present.

        Args:
            required_vars: List of required variable names

        Returns:
            bool: True if all required variables are present, False otherwise
        """
        # This method might be called dynamically or used in ways Vulture can't detect
        if not self.current_config:
            self.load_current_env()

        return all(var in self.current_config for var in required_vars)

    def parse_template_with_metadata(self) -> Dict[str, Dict[str, Any]]:
        """
        Parse the .env.example template file with metadata from comments.

        Returns:
            Dict[str, Dict[str, Any]]: Dictionary of variables with metadata
        """
        if not os.path.exists(self.env_template):
            return {}

        result = {}
        current_section = "General"
        current_comment = []

        with open(self.env_template, "r") as f:
            lines = f.readlines()

        for i, line in enumerate(lines):
            line = line.strip()

            # Handle section headers (comments with specific format)
            if line.startswith("# --- ") and line.endswith(" ---"):
                current_section = line[6:-4].strip()
                continue

            # Collect comments
            if line.startswith("#"):
                comment_text = line[1:].strip()
                current_comment.append(comment_text)
                continue

            # Skip empty lines
            if not line:
                current_comment = []  # Reset comment if we hit empty line
                continue

            # Handle variable definition
            if "=" in line:
                key, value = line.split("=", 1)
                key = key.strip()
                value = value.strip().strip("'\"")

                # Parse metadata from comments
                required = False
                var_type = "string"
                description = " ".join(current_comment) if current_comment else ""
                example = value

                for comment in current_comment:
                    lower_comment = comment.lower()
                    if "required" in lower_comment:
                        required = True
                    if any(
                        type_marker in lower_comment
                        for type_marker in ["type:", "type ="]
                    ):
                        for t in [
                            "string",
                            "int",
                            "bool",
                            "float",
                            "url",
                            "email",
                            "path",
                        ]:
                            if t in lower_comment:
                                var_type = t
                                break

                # Store the variable with its metadata
                result[key] = {
                    "default": value,
                    "required": required,
                    "type": var_type,
                    "description": description,
                    "section": current_section,
                    "example": example,
                }

                # Reset comment collection
                current_comment = []

        return result
