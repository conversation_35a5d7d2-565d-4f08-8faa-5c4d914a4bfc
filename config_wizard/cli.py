"""
Configuration Wizard CLI Interface

This module provides user interface elements for the configuration wizard
using colorama and rich for a pleasant terminal experience.
"""

import sys
import os
from typing import List, Dict, Any, Optional, Callable, Tuple
import colorama

from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm
from rich.table import Table
from rich.align import Align
from rich import box

from config_wizard.validators import validate_by_type

# Add parent directory to path if needed
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Initialize colorama for cross-platform colored terminal output
colorama.init(autoreset=True)

# Initialize rich console
console = Console()

# Theme colors for consistent UI
THEME = {
    "primary": "#1E90FF",  # Dodger Blue
    "secondary": "#9370DB",  # Medium Purple
    "accent": "#00C08B",  # Jade Green
    "warning": "#FFB900",  # Amber
    "error": "#FF5252",  # Red Coral
    "info": "#5DADE2",  # <PERSON> Blue
    "success": "#4CAF50",  # Material Green
    "header_box": box.ROUNDED,
    "content_box": box.SIMPLE,
    "section_box": box.ROUNDED,
    "muted": "#A0A0A0",  # For less important text
    "highlight": "#F0F0F0",  # For highlighted text
    "border": "#7A7A7A",  # For borders
}

# Standard spinners that work well in most terminals
_SPINNERS = [
    "dots",
    "line",
    "dots12",
    "point",
]  # Renamed with underscore to indicate it's reserved for future use


def _print_header(
    title: str, subtitle: Optional[str] = None, style: str = "primary"
) -> None:
    """
    Print a styled header with optional subtitle.

    Args:
        title: The main title text
        subtitle: Optional subtitle text
        style: The color style to use
    """
    pass  # Renamed with underscore to indicate it's reserved for future use


def print_section_header(text: str) -> None:
    """
    Print a section header with styling.

    Args:
        text: The section header text
    """
    console.print("\n")
    console.print(
        Panel(
            Align.center(f"[bold white]{text}[/]"),
            border_style=THEME["primary"],
            box=THEME["section_box"],
            width=60,
            title=f"[{THEME['secondary']}]··· {text.upper()} ···[/]",
            title_align="center",
        )
    )
    console.print("\n")


def print_success(message: str) -> None:
    """
    Print a success message.

    Args:
        message: The success message
    """
    console.print(f"[bold {THEME['success']}]✓[/] {message}")


def print_error(message: str) -> None:
    """
    Print an error message.

    Args:
        message: The error message
    """
    console.print(f"[bold {THEME['error']}]✗[/] {message}")


def print_warning(message: str) -> None:
    """
    Print a warning message.

    Args:
        message: The warning message
    """
    console.print(f"[bold {THEME['warning']}]![/] {message}")


def print_info(message: str) -> None:
    """
    Print an informational message.

    Args:
        message: The informational message
    """
    console.print(f"[bold {THEME['info']}]ℹ[/] {message}")


def print_step(step: int, total: int, message: str) -> None:
    """
    Print a step indicator with progress.

    Args:
        step: The current step number
        total: The total number of steps
        message: The step message
    """
    progress_percent = int((step / total) * 100)
    progress_bar = "━" * int(progress_percent / 5) + "░" * (
        20 - int(progress_percent / 5)
    )
    step_emoji = "🏁" if step == total else "🔶"

    console.print("\n")
    console.print(
        Panel(
            f"[bold {THEME['primary']}]{step_emoji} Step {step}/{total} ({progress_percent}%)[/]\n\n"
            f"[{THEME['accent']}]{progress_bar}[/]\n\n"
            f"[white]{message}[/]",
            border_style=THEME["accent"],
            padding=(1, 2),
            title=f"[{THEME['secondary']}]⟡ {message} ⟡[/]",
            title_align="center",
            box=box.ROUNDED,
        )
    )


def print_welcome() -> None:
    """
    Print a welcome message for the configuration wizard.
    """
    console.print("\n")
    console.print(
        Panel(
            Align.center(
                f"[bold white]Welcome to the Configuration Wizard[/]\n\n"
                f"[white]This wizard will guide you through setting up\n"
                f"all necessary configuration parameters for your bot.[/]\n\n"
                f"[{THEME['info']}]Follow the prompts and provide the requested information.[/]"
            ),
            border_style=THEME["primary"],
            box=THEME["header_box"],
            padding=(1, 4),
            title=f"[{THEME['secondary']}]⟡ Bot Configuration Wizard ⟡[/]",
            title_align="center",
        )
    )
    console.print("\n")


def print_completion() -> None:
    """
    Print a completion message when the configuration is successfully saved.
    """
    console.print("\n")
    console.print(
        Panel(
            Align.center(
                f"[{THEME['success']}]✅ Configuration Completed Successfully ✅[/]\n\n"
                f"[white]Your bot configuration has been saved and is ready to use.[/]\n"
                f"[{THEME['info']}]You can now start your bot with the new settings.[/]"
            ),
            border_style=THEME["success"],
            box=THEME["header_box"],
            padding=(1, 4),
            title=f"[{THEME['secondary']}]⟡ Setup Complete ⟡[/]",
            title_align="center",
        )
    )
    console.print("\n")


def print_config_preview(config: Dict[str, str]) -> None:
    """
    Print a preview of the configuration with formatted display.

    Args:
        config: The configuration dictionary
    """
    table = Table(
        title="Configuration Preview",
        box=THEME["content_box"],
        highlight=True,
        border_style=THEME["primary"],
        header_style=f"bold {THEME['secondary']}",
    )

    # Add columns
    table.add_column("Variable", style=f"bold {THEME['primary']}")
    table.add_column("Value", style="white")
    table.add_column("Type", style=f"dim {THEME['accent']}")

    # Process config entries
    for key, value in config.items():
        # Skip comments
        if key.startswith("#"):
            continue

        # Detect type for display purposes
        display_type = "string"
        if value.isdigit():
            display_type = "int"
        elif value.lower() in ["true", "false"]:
            display_type = "bool"
        elif ":" in value and len(value) > 10 and "@" not in value:
            display_type = "token"
        elif value.startswith(("mongodb://", "mongodb+srv://")):
            display_type = "mongodb_uri"

        # Mask sensitive values
        display_value = value
        if any(s in key.lower() for s in ["password", "secret", "key", "token", "api"]):
            if value:
                visible_part = value[:2] if len(value) > 4 else ""
                masked_part = (
                    "\u2022" * (len(value) - 2)
                    if len(value) > 4
                    else "\u2022" * len(value)
                )
                display_value = f"{visible_part}{masked_part}"

        # Add row to table
        table.add_row(key, display_value, display_type)

    # Print the table
    console.print(table)


def ask_input(
    prompt: str,
    default: str = "",
    password: bool = False,
    choices: List[str] = None,
) -> str:
    """
    Ask for user input with a styled prompt.

    Args:
        prompt: The prompt text
        default: The default value
        password: Whether this is a password input
        choices: Optional list of autocomplete suggestions

    Returns:
        str: The user input
    """
    try:
        from rich.prompt import Prompt

        # Basic prompt with rich
        return Prompt.ask(
            f"[bold {THEME['accent']}]?[/] [bold]{prompt}[/]",
            default=default,
            password=password,
            choices=choices,
            show_choices=bool(choices),
        )
    except Exception as e:
        # Fallback to basic input
        console.print(f"[bold {THEME['error']}]Error with interactive prompt: {e}[/]")

        # Simple text-based fallback
        default_text = f" (default: {default})" if default else ""
        choices_text = f" [{'/'.join(choices)}]" if choices else ""

        # Always use regular input, never hide text
        response = input(f"{prompt}{default_text}{choices_text}: ")

        if not response and default:
            return default
        return response


def ask_yes_no(question: str, default: bool = False) -> bool:
    """
    Ask a yes/no question with a default value.

    Args:
        question: The question to ask
        default: The default answer (True for yes, False for no)

    Returns:
        bool: True for yes, False for no
    """
    try:
        # Try using rich's Confirm
        return Confirm.ask(
            f"[bold {THEME['accent']}]?[/] [bold]{question}[/]",
            default=default,
        )
    except Exception as e:
        # Fallback to basic input
        console.print(f"[bold {THEME['error']}]Error with interactive prompt: {e}[/]")

        # Format yes/no options based on default
        if default:
            options = "[Y/n]"
        else:
            options = "[y/N]"

        # Basic text-based yes/no prompt
        while True:
            response = input(f"{question} {options}: ").strip().lower()

            if not response:
                return default

            if response in ["y", "yes"]:
                return True
            elif response in ["n", "no"]:
                return False

            print("Please answer 'yes' or 'no'")


def ask_selection(prompt: str, options: List[str], default: int = 0) -> int:
    """
    Display a selection menu for the user to choose from.

    Args:
        prompt: The prompt text
        options: A list of options to choose from
        default: The default selected index

    Returns:
        int: The index of the selected option
    """
    console.print(f"[bold {THEME['accent']}]?[/] [bold]{prompt}[/]")
    for i, option in enumerate(options, start=1):
        console.print(f"  [dim]{i}.[/] {option}")

    while True:
        try:
            choice = input(
                f"Enter your choice [1-{len(options)}] (default: {default + 1}): "
            ).strip()
            if not choice:
                return default
            choice_index = int(choice) - 1
            if 0 <= choice_index < len(options):
                return choice_index
        except ValueError:
            pass
        console.print(
            f"[bold {THEME['error']}]Invalid choice. Please enter a number between 1 and {len(options)}.[/]"
        )


def interactive_value_input(
    var_name: str,
    var_info: Dict[str, Any],
    current_value: Optional[str] = None,
) -> str:
    """
    Display an interactive input prompt with validation and appropriate
    input method based on the variable type.

    Args:
        var_name: The variable name
        var_info: The variable metadata
        current_value: The current value, if any

    Returns:
        str: The validated user input
    """
    var_type = var_info.get("type", "string")
    default = current_value or var_info.get("default", "")
    required = var_info.get("required", False)
    description = var_info.get("description", "")
    example = var_info.get("example", "")
    possible_values = var_info.get("values", [])
    category = var_info.get("category", "")
    help_text = var_info.get("help", "")

    # Determine if this is a sensitive field
    is_sensitive = any(
        s in var_name.lower() for s in ["password", "secret", "key", "token", "api"]
    )

    # Display field header and description
    console.print("\n")
    header_style = THEME["error"] if required else THEME["primary"]
    importance_icon = "🔒" if is_sensitive else "✱" if required else "○"
    category_tag = f"[{THEME['secondary']}][{category}][/]" if category else ""

    # Show variable header with rich formatting
    console.print(
        f"[bold {header_style}]{importance_icon} {var_name}[/] "
        f"[{THEME['accent']}]<{var_type}>[/] {category_tag}"
    )

    # Show description if available
    if description:
        console.print(f"  [dim]{description}[/]")

    # Show help text if available
    if help_text:
        console.print(f"  [dim]💡 {help_text}[/]")

    # Show information about sensitive fields
    if is_sensitive:
        console.print(f"  [dim]🔒 This is a sensitive field - input will be masked[/]")

    # Show different inputs based on the variable type
    if possible_values and len(possible_values) > 0:
        # For enumerated values, show a selection menu
        console.print("\n  [dim]Please select from the available options:[/]")

        # Find current value in possible values
        default_index = 0
        if current_value:
            try:
                default_index = possible_values.index(current_value)
            except ValueError:
                pass

        selected_index = ask_selection(
            f"Select a value for {var_name}", possible_values, default=default_index
        )
        value = possible_values[selected_index]
        return value  # No validation needed for selection from predefined options

    elif var_type == "bool":
        # For boolean values, show a yes/no prompt
        default_bool = False
        if default:
            default_bool = default.lower() in ["true", "yes", "1", "t", "y"]

        result = ask_yes_no(f"Set {var_name} to True?", default=default_bool)
        value = "true" if result else "false"
        return value  # Boolean values are already validated

    else:
        # For regular text and specialized types
        placeholders = {
            "telegram_token": "123456789:AbCdEfGhIjKlMnOpQrStUvWxYz",
            "mongodb_uri": "********************************:port/database",
            "url": "https://example.com",
            "email": "<EMAIL>",
        }

        suggestions = None
        if example and example != default:
            suggestions = [example]

        while True:
            # Get input value
            value = ask_input(
                f"Enter value for {var_name}",
                default=default,
                password=is_sensitive,
                choices=suggestions,
            )

            # Don't allow empty values for required fields
            if not value and required:
                console.print(f"  [bold {THEME['error']}]✗[/] This field is required")
                continue

            # Skip validation for empty non-required fields
            if not value and not required:
                console.print(f"  [dim]○[/] Field left empty (optional)")
                return value

            # Import validation here to ensure it's available in this module's scope
            from config_wizard.validators import validate_by_type

            # Validate the input properly using our validators
            is_valid, error_message, validated_value = validate_by_type(value, var_type)

            if is_valid:
                # Display confirmation of the input
                if is_sensitive:
                    # Mask sensitive values
                    visible_part = value[:2] if len(value) > 4 else ""
                    masked_part = (
                        "\u2022" * (len(value) - 2)
                        if len(value) > 4
                        else "\u2022" * len(value)
                    )
                    display_value = f"[dim]{visible_part}{masked_part}[/]"
                    console.print(
                        f"  [bold {THEME['success']}]✓[/] Value securely stored 🔒"
                    )
                else:
                    # Show regular values
                    display_value = f"[cyan]'{value}'[/]"
                    console.print(
                        f"  [bold {THEME['success']}]✓[/] Set to: {display_value}"
                    )

                # For types that have conversion, use the converted value as a string
                if var_type in ["int", "float", "bool"]:
                    return str(validated_value)
                return value
            else:
                # Show error and continue the loop for new input
                console.print(f"  [bold {THEME['error']}]✗[/] Error: {error_message}")
                console.print(f"  [dim]→[/] Please try again")
                # Loop continues for new input

    # Add a separator
    console.print("─" * 40, style="dim")
