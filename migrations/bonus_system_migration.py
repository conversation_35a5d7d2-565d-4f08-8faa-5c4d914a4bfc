"""
Database migration script for the bonus reward system.
Initializes the bonus_tiers collection and creates default bonus configurations.
"""

import logging
import sys
import os
from datetime import datetime

# Add the parent directory to the path so we can import from the project
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from database.operations import (
    create_bonus_tier,
    get_all_bonus_tiers,
    initialize_indexes,
)
from database.connection import bonus_tiers_collection

logger = logging.getLogger(__name__)


def run_bonus_system_migration():
    """
    Run the bonus system migration.
    
    This migration:
    1. Ensures the bonus_tiers collection exists
    2. Creates indexes for the collection
    3. Optionally creates default bonus tiers
    """
    try:
        logger.info("Starting bonus system migration...")
        
        # Step 1: Ensure indexes are created
        logger.info("Creating database indexes...")
        initialize_indexes()
        logger.info("Database indexes created successfully")
        
        # Step 2: Check if bonus tiers already exist
        existing_tiers = get_all_bonus_tiers()
        if existing_tiers:
            logger.info(f"Found {len(existing_tiers)} existing bonus tiers. Skipping default tier creation.")
            return True
        
        # Step 3: Create default bonus tiers
        logger.info("Creating default bonus tiers...")
        
        default_tiers = [
            {
                "threshold": 50.0,
                "bonus_percentage": 0.10,  # 10%
                "description": "Standard bonus for medium deposits",
                "is_active": True
            },
            {
                "threshold": 100.0,
                "bonus_percentage": 0.20,  # 20%
                "description": "Premium bonus for large deposits",
                "is_active": True
            },
            {
                "threshold": 250.0,
                "bonus_percentage": 0.30,  # 30%
                "description": "VIP bonus for very large deposits",
                "is_active": True
            }
        ]
        
        created_count = 0
        for tier_config in default_tiers:
            try:
                tier = create_bonus_tier(
                    threshold=tier_config["threshold"],
                    bonus_percentage=tier_config["bonus_percentage"],
                    created_by=0,  # System created
                    description=tier_config["description"],
                    is_active=tier_config["is_active"]
                )
                
                if tier:
                    created_count += 1
                    logger.info(
                        f"Created bonus tier: ${tier_config['threshold']:.2f} -> "
                        f"{tier_config['bonus_percentage']*100:.1f}% bonus"
                    )
                else:
                    logger.error(f"Failed to create bonus tier: ${tier_config['threshold']:.2f}")
                    
            except Exception as e:
                logger.error(f"Error creating bonus tier ${tier_config['threshold']:.2f}: {e}")
        
        logger.info(f"Created {created_count} default bonus tiers")
        
        # Step 4: Verify migration
        final_tiers = get_all_bonus_tiers()
        logger.info(f"Migration complete. Total bonus tiers: {len(final_tiers)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Bonus system migration failed: {e}")
        return False


def rollback_bonus_system_migration():
    """
    Rollback the bonus system migration.
    
    WARNING: This will delete all bonus tiers!
    """
    try:
        logger.warning("Starting bonus system migration rollback...")
        
        # Delete all bonus tiers
        result = bonus_tiers_collection.delete_many({})
        deleted_count = result.deleted_count
        
        logger.warning(f"Rollback complete. Deleted {deleted_count} bonus tiers.")
        return True
        
    except Exception as e:
        logger.error(f"Bonus system migration rollback failed: {e}")
        return False


def check_migration_status():
    """
    Check the current status of the bonus system migration.
    """
    try:
        tiers = get_all_bonus_tiers()
        active_tiers = [t for t in tiers if t.get("is_active", True)]
        
        print(f"Bonus System Migration Status:")
        print(f"  Total bonus tiers: {len(tiers)}")
        print(f"  Active bonus tiers: {len(active_tiers)}")
        print(f"  Inactive bonus tiers: {len(tiers) - len(active_tiers)}")
        
        if tiers:
            print(f"\nConfigured tiers:")
            for i, tier in enumerate(tiers, 1):
                status = "Active" if tier.get("is_active", True) else "Inactive"
                threshold = tier.get("threshold", 0)
                percentage = tier.get("bonus_percentage", 0) * 100
                description = tier.get("description", "No description")
                
                print(f"  {i}. ${threshold:.2f} -> {percentage:.1f}% ({status})")
                print(f"     Description: {description}")
        else:
            print(f"\nNo bonus tiers configured.")
        
        return True
        
    except Exception as e:
        logger.error(f"Error checking migration status: {e}")
        return False


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    import argparse
    parser = argparse.ArgumentParser(description="Bonus System Migration Tool")
    parser.add_argument(
        "action", 
        choices=["migrate", "rollback", "status"],
        help="Action to perform"
    )
    parser.add_argument(
        "--force", 
        action="store_true",
        help="Force the action without confirmation (for rollback)"
    )
    
    args = parser.parse_args()
    
    if args.action == "migrate":
        print("Running bonus system migration...")
        success = run_bonus_system_migration()
        if success:
            print("✅ Migration completed successfully!")
        else:
            print("❌ Migration failed!")
            sys.exit(1)
            
    elif args.action == "rollback":
        if not args.force:
            confirm = input("⚠️  This will delete ALL bonus tiers! Are you sure? (yes/no): ")
            if confirm.lower() != "yes":
                print("Rollback cancelled.")
                sys.exit(0)
        
        print("Running bonus system migration rollback...")
        success = rollback_bonus_system_migration()
        if success:
            print("✅ Rollback completed successfully!")
        else:
            print("❌ Rollback failed!")
            sys.exit(1)
            
    elif args.action == "status":
        print("Checking bonus system migration status...")
        success = check_migration_status()
        if not success:
            print("❌ Status check failed!")
            sys.exit(1)
