import os
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

# Application mode
DEVELOPMENT_MODE = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"

# Bot configuration
API_TOKEN = os.getenv("API_TOKEN")
if not API_TOKEN:
    raise ValueError(
        "API_TOKEN environment variable not set. Check your .env file or environment variables."
    )

# Admin and owner IDs
ADMIN_ID = int(os.getenv("ADMIN_ID", "0"))
OWNER_ID = int(os.getenv("OWNER_ID", "0"))

# Maintenance mode configuration - default to False if not set
MAINTENANCE_MODE = os.getenv("MAINTENANCE_MODE", "false").lower() == "true"

# MongoDB configuration
MONGO_URI = os.getenv("MONGO_URI")
if not MONGO_URI:
    raise ValueError("MONGO_URI environment variable not set")

MONGO_DB = os.getenv("MONGO_DB", "telegram_shop_bot1")
DB_NAME = MONGO_DB  # For backward compatibility

# MongoDB connection settings
MONGO_CONNECT_TIMEOUT_MS = int(
    os.getenv("MONGO_CONNECT_TIMEOUT_MS", "30000")
)  # 30 seconds
MONGO_SOCKET_TIMEOUT_MS = int(
    os.getenv("MONGO_SOCKET_TIMEOUT_MS", "120000")
)  # 2 minutes
MONGO_SERVER_SELECTION_TIMEOUT_MS = int(
    os.getenv("MONGO_SERVER_SELECTION_TIMEOUT_MS", "30000")
)  # 30 seconds

# Payment gateway configuration
OXA_PAY_API_KEY = os.getenv("OXA_PAY_API_KEY")
if not OXA_PAY_API_KEY:
    raise ValueError("OXA_PAY_API_KEY environment variable not set")

OXA_PAY_CALLBACK_URL = os.getenv("OXA_PAY_CALLBACK_URL")
# OXA_PAY_CALLBACK_URL is now optional - if not set, the system will use dynamic URL generation

SUPPORT_USERNAME = os.getenv("SUPPORT_USERNAME", "support")
SUPPORT_EMAIL = os.getenv("SUPPORT_EMAIL", "<EMAIL>")
SUPPORT_WEBSITE = os.getenv("SUPPORT_WEBSITE", "www.example.com")

# Payment processing configuration
PAYMENT_FEE_RATE = float(os.getenv("PAYMENT_FEE_RATE", "0.015"))  # Default 1.5%
PAYMENT_MIN_AMOUNT = float(os.getenv("PAYMENT_MIN_AMOUNT", "0.01"))  # Minimum payment amount
PAYMENT_MAX_AMOUNT = float(os.getenv("PAYMENT_MAX_AMOUNT", "10000.0"))  # Maximum payment amount

# Validate fee rate
if not (0.0 <= PAYMENT_FEE_RATE <= 1.0):
    raise ValueError(f"PAYMENT_FEE_RATE must be between 0.0 and 1.0, got {PAYMENT_FEE_RATE}")

# Validate payment amounts
if PAYMENT_MIN_AMOUNT < 0:
    raise ValueError(f"PAYMENT_MIN_AMOUNT must be non-negative, got {PAYMENT_MIN_AMOUNT}")

if PAYMENT_MAX_AMOUNT <= PAYMENT_MIN_AMOUNT:
    raise ValueError(f"PAYMENT_MAX_AMOUNT ({PAYMENT_MAX_AMOUNT}) must be greater than PAYMENT_MIN_AMOUNT ({PAYMENT_MIN_AMOUNT})")
