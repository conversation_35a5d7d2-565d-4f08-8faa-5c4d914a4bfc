import asyncio
import logging
import os
import sys
import traceback
from aiogram import <PERSON><PERSON>, Dispatcher
from aiogram.fsm.storage.memory import MemoryStorage
from aiogram.enums import ParseMode
from aiogram.client.default import DefaultBotProperties
from aiogram.types import BotCommand
from aiogram.utils.token import TokenValidationError
from utils.helpers import ensure_upload_directories

# Import colorama and rich for terminal output styling
try:
    from colorama import Fore, Back, Style, init as colorama_init
    from rich.console import Console

    # Initialize colorama for cross-platform color support
    colorama_init(autoreset=True)

    # Create console for rich output
    console = Console()
    HAS_RICH = True
except ImportError:
    print("Colorama or Rich packages not found, using standard logging")
    HAS_RICH = False

# Add the project root directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# Custom logger configuration with colors
class ColoredFormatter(logging.Formatter):
    """Custom formatter for the bot logger with colorama colors"""

    # Store previous level to detect changes
    previous_level = None

    def format(self, record):
        # Store the original levelname
        original_levelname = record.levelname

        # Add a divider if the level changed
        divider = ""
        if (
            ColoredFormatter.previous_level is not None
            and ColoredFormatter.previous_level != record.levelno
        ):
            divider = f"\n{'-' * 70}\n"

        # Store the current level for the next message
        ColoredFormatter.previous_level = record.levelno

        # Get mode prefix for development mode
        from config import DEVELOPMENT_MODE

        mode_prefix = (
            f"{Back.MAGENTA}{Fore.WHITE} DEV {Style.RESET_ALL} "
            if DEVELOPMENT_MODE
            else ""
        )

        # Colorize just the levelname based on level - with background color
        if record.levelno >= logging.ERROR:
            record.levelname = f"{mode_prefix}{Back.RED}{Fore.WHITE} {record.levelname} {Style.RESET_ALL}"
        elif record.levelno >= logging.WARNING:
            record.levelname = f"{mode_prefix}{Back.YELLOW}{Fore.BLACK} {record.levelname} {Style.RESET_ALL}"
        elif record.levelno >= logging.INFO:
            record.levelname = f"{mode_prefix}{Back.BLUE}{Fore.WHITE} {record.levelname} {Style.RESET_ALL}"
        else:
            record.levelname = f"{mode_prefix}{Back.CYAN}{Fore.BLACK} {record.levelname} {Style.RESET_ALL}"

        # Format the record with the colored levelname
        result = divider + super().format(record)

        # Restore the original levelname for future handlers
        record.levelname = original_levelname

        return result


# Configure logging
def setup_logger():
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)

    # Clear any existing handlers
    if logger.handlers:
        for handler in logger.handlers:
            logger.removeHandler(handler)

    # Create file handler with UTF-8 encoding
    file_handler = logging.FileHandler("bot.log", encoding='utf-8')
    file_formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    file_handler.setFormatter(file_formatter)

    # Create console handler with colors and UTF-8 support
    console_handler = logging.StreamHandler(sys.stdout)

    # Try to configure stdout for UTF-8 encoding
    try:
        if hasattr(sys.stdout, 'reconfigure'):
            sys.stdout.reconfigure(encoding='utf-8', errors='replace')
    except (AttributeError, OSError):
        # Fallback: console handler will use system default
        pass

    if HAS_RICH:
        console_handler.setFormatter(
            ColoredFormatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        )
    else:
        console_handler.setFormatter(
            logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        )

    # Add handlers to logger
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)

    return logger


# Setup the logger
logger = setup_logger()

# Add the current directory to Python path to make absolute imports work
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Check if configuration exists, run setup wizard if needed
try:
    from config_wizard.setup_wizard import check_and_setup_env

    # List of required environment variables
    required_vars = ["API_TOKEN", "MONGO_URI", "OXA_PAY_API_KEY"]

    # Run configuration wizard if needed
    if not check_and_setup_env(required_vars):
        logger.critical("Configuration setup was cancelled or failed. Exiting...")
        sys.exit(1)

    logger.info("Configuration validated successfully")
except ImportError:
    logger.warning("Configuration wizard not available, continuing with existing setup")
except Exception as e:
    logger.error(f"Error during configuration setup: {e}")
    logger.error(traceback.format_exc())

# Import configuration after wizard has potentially created/updated it
from config import API_TOKEN, MAINTENANCE_MODE
from database.connection import check_database_connection
from middlewares.error_middleware import ErrorHandlingMiddleware
from middlewares.maintenance_middleware import MaintenanceModeMiddleware
from handlers.register_handlers import register_all_handlers


async def reconnect_database():
    """Attempt to reconnect to the database after a connection failure."""
    try:
        logger.info("Attempting to reconnect to the database...")
        connection_result = await check_database_connection()
        if connection_result:
            logger.info("Successfully reconnected to the database")
            return True
        else:
            logger.error("Failed to reconnect to the database")
            return False
    except Exception as e:
        logger.error(f"Error during database reconnection: {e}")
        return False


# All routers are now imported and registered in handlers/register_handlers.py

# Initialize bot with HTML parsing as default and improved connection settings
# Using the latest aiogram features for better connection management
try:
    bot = Bot(
        token=API_TOKEN,
        default=DefaultBotProperties(
            parse_mode=ParseMode.HTML,
        ),
        timeout=60,  # Increase overall request timeout to 60 seconds
        connections_limit=100,  # Increase connection pool size
    )
except TokenValidationError as e:
    logger.critical(f"Invalid bot token: {e}")
    sys.exit(1)


async def initialize_database():
    """Initialize the database connection and schema."""
    max_retries = 3
    retry_delay = 2  # seconds

    for attempt in range(1, max_retries + 1):
        try:
            # Check connection first
            is_connected = await check_database_connection()
            if not is_connected:
                logger.warning(
                    f"Database connection check failed (attempt {attempt}/{max_retries}), attempting to reconnect"
                )
                reconnect_result = await reconnect_database()
                if not reconnect_result:
                    if attempt < max_retries:
                        wait_time = retry_delay * attempt
                        logger.info(
                            f"Waiting {wait_time} seconds before retrying connection..."
                        )
                        await asyncio.sleep(wait_time)
                        continue
                    logger.error("All connection attempts failed")
                    return False

            # Now that we have confirmed connection, initialize the database
            from database.database import init_db

            init_db()
            logger.info("Database initialized successfully")
            return True

        except Exception as e:
            logger.error(
                f"Database initialization error (attempt {attempt}/{max_retries}): {e}"
            )
            logger.error(traceback.format_exc())

            if attempt < max_retries:
                wait_time = retry_delay * attempt
                logger.info(f"Waiting {wait_time} seconds before retrying...")
                await asyncio.sleep(wait_time)
            else:
                logger.critical("Failed to initialize database after all attempts")

    return False


async def set_commands(bot: Bot):
    """Set bot commands in the interface"""
    commands = [
        BotCommand(command="start", description="Start the bot"),
        BotCommand(command="help", description="Show help information"),
        BotCommand(command="balance", description="Check your balance"),
        BotCommand(command="deposit", description="Add funds to your account"),
        BotCommand(command="products", description="Browse available products"),
        BotCommand(command="support", description="Contact support"),
        BotCommand(command="settings", description="Change user settings"),
    ]
    await bot.set_my_commands(commands)


async def main():
    """Initialize and start the bot with all handlers and middlewares"""
    # Check environment variables directly first
    env_dev_mode = os.getenv("DEVELOPMENT_MODE", "false").lower() == "true"
    ensure_upload_directories()

    # Then import from config
    from config import DEVELOPMENT_MODE

    # Log the development mode status from both sources
    logger.info(f"Development mode from environment: {env_dev_mode}")
    logger.info(f"Development mode from config: {DEVELOPMENT_MODE}")

    # Use the environment variable if it's set to true, otherwise use the config value
    actual_dev_mode = env_dev_mode

    # Log application startup
    if actual_dev_mode:
        logger.info("Starting the bot application in DEVELOPMENT MODE")
        # Set logging level to DEBUG in development mode
        logger.setLevel(logging.DEBUG)
        logging.getLogger("aiogram").setLevel(logging.DEBUG)

        # Set pymongo logger to INFO level to avoid excessive debug logs
        logging.getLogger("pymongo").setLevel(logging.INFO)

        # Set motor logger to INFO level (async MongoDB driver)
        logging.getLogger("motor").setLevel(logging.INFO)

        logger.info("MongoDB debug logs suppressed for cleaner output")
    else:
        logger.info("Starting the bot application in PRODUCTION MODE")
        # Use INFO level in production
        logger.setLevel(logging.INFO)
        logging.getLogger("aiogram").setLevel(logging.INFO)

    # Log maintenance mode status
    if MAINTENANCE_MODE:
        logger.warning(
            "Bot starting in MAINTENANCE MODE - only admins will have access"
        )
    else:
        logger.info("Bot starting in normal operational mode")

    # Initialize database
    db_initialized = await initialize_database()
    if not db_initialized:
        logger.critical("Failed to initialize database, exiting...")
        return

    # Initialize Dispatcher with memory storage for FSM
    dp = Dispatcher(storage=MemoryStorage())

    # Register middlewares
    # Apply maintenance middleware to ALL update types explicitly
    dp.message.middleware(MaintenanceModeMiddleware())
    dp.callback_query.middleware(MaintenanceModeMiddleware())
    dp.update.middleware(ErrorHandlingMiddleware())  # type: ignore

    # Register all handlers using the centralized registration function
    try:
        register_all_handlers(dp)
        logger.info("All handlers registered successfully")
    except Exception as e:
        logger.error(f"Failed to register handlers: {e}")
        logger.error(traceback.format_exc())

    # Set bot commands
    try:
        await set_commands(bot)
    except Exception as e:
        logger.error(f"Failed to set bot commands: {e}")

    # Initialize digital delivery manager for line-based products
    try:
        from utils.digital_delivery import initialize_delivery_manager
        initialize_delivery_manager(bot)
        logger.info("Digital delivery manager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize digital delivery manager: {e}")

    # Development mode welcome message
    if DEVELOPMENT_MODE:
        logger.debug("=" * 50)
        logger.debug("DEVELOPMENT MODE ACTIVE")
        logger.debug("Enhanced logging and debugging features are enabled")
        logger.debug("=" * 50)

    try:
        # Start polling with improved parameters for better stability
        await dp.start_polling(
            bot,
            allowed_updates=[
                "message",
                "edited_message",
                "callback_query",
                "inline_query",
                "chat_member",
            ],
            polling_timeout=30,  # Reasonable timeout for polling
            polling_max_wait=(
                5.0 if not DEVELOPMENT_MODE else 2.0
            ),  # Faster polling in dev mode
            polling_retries=(
                5 if not DEVELOPMENT_MODE else 10
            ),  # More retries in dev mode
        )
    except Exception as e:
        logger.error(f"Critical error in polling: {e}")
        logger.error(traceback.format_exc())


if __name__ == "__main__":
    # Print the current environment variables
    print(
        f"DEVELOPMENT_MODE environment variable: {os.getenv('DEVELOPMENT_MODE', 'not set')}"
    )

    # Import config and print the development mode status
    import config

    print(f"DEVELOPMENT_MODE in config: {config.DEVELOPMENT_MODE}")

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Bot stopped manually")
    except Exception as e:
        logger.error(f"Bot stopped due to error: {e}")
        traceback.print_exc()
