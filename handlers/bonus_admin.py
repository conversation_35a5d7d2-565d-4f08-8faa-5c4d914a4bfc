"""
Admin interface for bonus tier management.
Provides comprehensive tools for managing bonus reward tiers.
"""

import logging
from datetime import datetime
from typing import Optional

from aiogram import Router, F
from aiogram.types import CallbackQuery, Message, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.fsm.context import FSMContext

from database.operations import (
    is_owner,
    get_all_bonus_tiers,
    get_bonus_tier_by_id,
    create_bonus_tier,
    update_bonus_tier,
    delete_bonus_tier,
)
from handlers.sys_db import is_privileged
from utils.telegram_helpers import safe_edit_message
from utils.template_helpers import format_text
from states.states import BonusManagementStates

logger = logging.getLogger(__name__)
router = Router()


def format_bonus_tier_info(tier: dict) -> str:
    """Format bonus tier information for display using unified UI components."""
    from utils.bonus_tier_ui import tier_ui
    return tier_ui.format_tier_info(tier, detailed=True)


def bonus_management_keyboard() -> InlineKeyboardMarkup:
    """Create keyboard for bonus management main menu using unified UI components."""
    from utils.bonus_tier_ui import tier_ui
    return tier_ui.create_tier_management_keyboard()


def bonus_tier_keyboard(tier_id: str) -> InlineKeyboardMarkup:
    """Create keyboard for individual bonus tier management using unified UI components."""
    from utils.bonus_tier_ui import tier_ui
    return tier_ui.create_tier_action_keyboard(tier_id)


def bonus_edit_keyboard(tier_id: str, tier: dict = None) -> InlineKeyboardMarkup:
    """Create keyboard for editing bonus tier using unified UI components."""
    from utils.bonus_tier_ui import tier_ui
    return tier_ui.create_edit_options_keyboard(tier_id, tier)





@router.callback_query(F.data == "bonus_management")
async def bonus_management_main(callback_query: CallbackQuery):
    """Main bonus management interface."""
    user_id = callback_query.from_user.id
    
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return
    
    await callback_query.answer()
    
    # Get tier count for display
    from utils.bonus_tier_ui import tier_ui

    tiers = get_all_bonus_tiers()
    active_tiers = [t for t in tiers if t.get("is_active", True)]

    # Use unified UI formatting
    header = tier_ui.format_header("BONUS TIER MANAGEMENT", "REWARD SYSTEM")

    message_text = (
        f"{header}\n"
        f"{tier_ui.EMOJIS['view']} <b>Total Tiers:</b> {len(tiers)}\n"
        f"{tier_ui.EMOJIS['active']} <b>Active Tiers:</b> {len(active_tiers)}\n"
        f"{tier_ui.EMOJIS['inactive']} <b>Inactive Tiers:</b> {len(tiers) - len(active_tiers)}\n\n"
        "<i>Manage bonus tiers that reward users with extra credits based on their deposit amounts.</i>\n\n"
        f"{tier_ui.EMOJIS['info']} <b>Quick Actions:</b> View, Add, Edit, or Delete bonus tiers"
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=bonus_management_keyboard(),
        parse_mode="HTML"
    )


@router.callback_query(F.data == "bonus_view_all")
async def bonus_view_all_tiers(callback_query: CallbackQuery):
    """View all bonus tiers."""
    user_id = callback_query.from_user.id
    
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return
    
    await callback_query.answer()
    
    tiers = get_all_bonus_tiers()
    
    if not tiers:
        message_text = (
            "🎁 <b>• BONUS TIERS •</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>NO TIERS CONFIGURED</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            "<i>No bonus tiers have been created yet. Add your first tier to start rewarding customers!</i>"
        )
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="➕ Add First Tier", callback_data="bonus_add_new")],
            [InlineKeyboardButton(text="🔙 Back", callback_data="bonus_management")]
        ])
    else:
        message_text = (
            "🎁 <b>• BONUS TIERS •</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>CONFIGURED TIERS</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
        )
        
        keyboard_buttons = []
        
        for i, tier in enumerate(tiers, 1):
            status_emoji = "🟢" if tier.get("is_active", True) else "🔴"
            threshold = tier.get("threshold", 0)
            bonus_type = tier.get("bonus_type", "percentage")

            # Format bonus display based on type
            if bonus_type == "fixed":
                fixed_amount = tier.get("bonus_fixed_amount", 0)
                bonus_display = f"${fixed_amount:.2f} fixed"
                button_text = f"{status_emoji} ${threshold:.2f} → ${fixed_amount:.2f}"
            else:
                percentage = tier.get("bonus_percentage", 0) * 100
                bonus_display = f"{percentage:.1f}%"
                button_text = f"{status_emoji} ${threshold:.2f} → {percentage:.1f}%"

            message_text += (
                f"{status_emoji} <b>{i}. ${threshold:.2f} → {bonus_display} bonus</b>\n"
                f"   <i>{tier.get('description', 'No description')}</i>\n\n"
            )

            keyboard_buttons.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"bonus_view_tier:{tier['_id']}"
                )
            ])
        
        keyboard_buttons.extend([
            [InlineKeyboardButton(text="➕ Add New Tier", callback_data="bonus_add_new")],
            [InlineKeyboardButton(text="🔙 Back", callback_data="bonus_management")]
        ])
        
        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)
    
    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("bonus_view_tier:"))
async def bonus_view_tier_details(callback_query: CallbackQuery):
    """View details of a specific bonus tier."""
    user_id = callback_query.from_user.id
    
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return
    
    tier_id = callback_query.data.split(":", 1)[1]
    tier = get_bonus_tier_by_id(tier_id)
    
    if not tier:
        await callback_query.answer("❌ Tier not found", show_alert=True)
        return
    
    await callback_query.answer()
    
    message_text = (
        "🎁 <b>• BONUS TIER DETAILS •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>TIER CONFIGURATION</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"{format_bonus_tier_info(tier)}\n"
        "<i>Select an action to modify this tier.</i>"
    )
    
    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=bonus_tier_keyboard(tier_id),
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("bonus_toggle:"))
async def bonus_toggle_tier(callback_query: CallbackQuery):
    """Toggle active status of a bonus tier."""
    user_id = callback_query.from_user.id
    
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return
    
    tier_id = callback_query.data.split(":", 1)[1]
    tier = get_bonus_tier_by_id(tier_id)
    
    if not tier:
        await callback_query.answer("❌ Tier not found", show_alert=True)
        return
    
    new_status = not tier.get("is_active", True)
    
    if update_bonus_tier(tier_id, is_active=new_status):
        status_text = "activated" if new_status else "deactivated"
        await callback_query.answer(f"✅ Tier {status_text}")
        
        # Refresh the tier details view
        await bonus_view_tier_details(callback_query)
    else:
        await callback_query.answer("❌ Failed to update tier", show_alert=True)


@router.callback_query(F.data == "bonus_add_new")
async def bonus_add_new_tier(callback_query: CallbackQuery, state: FSMContext):
    """Start the process of adding a new bonus tier with enhanced security."""
    user_id = callback_query.from_user.id

    # Enhanced privilege checking
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        logger.warning(f"Unauthorized tier creation attempt by user {user_id}")
        return

    # Check rate limiting
    from database.operations import check_tier_operation_rate_limit, log_tier_operation

    rate_limit_check = check_tier_operation_rate_limit(user_id, "create_tier", window_minutes=5, max_operations=5)
    if rate_limit_check["rate_limited"]:
        await callback_query.answer(
            f"🚫 Rate limit exceeded. Please wait {rate_limit_check['window_minutes']} minutes before creating more tiers.",
            show_alert=True
        )
        log_tier_operation("create_tier_attempt", user_id, success=False, error="Rate limited")
        return

    await callback_query.answer()
    await state.set_state(BonusManagementStates.waiting_for_threshold)

    # Use unified UI components for consistent styling
    from utils.bonus_tier_ui import tier_ui

    header = tier_ui.format_header("ADD NEW BONUS TIER")
    progress = tier_ui.format_step_progress(1, 4, "THRESHOLD")
    prompt = tier_ui.format_input_prompt(
        "minimum deposit amount (threshold)",
        examples=["50.00 (for $50 minimum deposit)", "100 (for $100 minimum deposit)"],
        constraints=["Must be greater than $0", "Maximum allowed: $100,000", "Enter only the number, without the $ symbol"]
    )

    message_text = f"{header}\n{progress}\n{prompt}"
    
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="❌ Cancel", callback_data="bonus_management")]
    ])
    
    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(BonusManagementStates.waiting_for_threshold)
async def process_bonus_threshold(message: Message, state: FSMContext):
    """Process the threshold input for new bonus tier with enhanced validation."""
    from utils.unified_validation import UnifiedValidation

    # Enhanced validation with improved error handling
    from utils.bonus_tier_errors import tier_error_handler

    try:
        validation_result = UnifiedValidation.validate_bonus_tier_threshold(message.text.strip())

        if not validation_result["valid"]:
            # Create a validation error for better handling
            error = ValueError(validation_result["error"])
            error_info = tier_error_handler.handle_tier_operation_error(
                "threshold validation", error, user_id=message.from_user.id
            )

            await message.reply(error_info["user_message"], parse_mode="HTML")
            return

    except Exception as e:
        error_info = tier_error_handler.handle_tier_operation_error(
            "threshold validation", e, user_id=message.from_user.id
        )
        await message.reply(error_info["user_message"], parse_mode="HTML")
        return

    threshold = validation_result["sanitized_threshold"]

    # Check if tier with this threshold already exists (with tolerance)
    existing_tiers = get_all_bonus_tiers()
    for tier in existing_tiers:
        existing_threshold = tier.get("threshold", 0)
        if abs(existing_threshold - threshold) < 0.01:  # Allow for floating point precision
            await message.reply(
                f"❌ <b>Threshold already exists!</b>\n\n"
                f"A bonus tier with threshold ${threshold:.2f} already exists.\n"
                "Please choose a different amount:",
                parse_mode="HTML"
            )
            return

    await state.update_data(threshold=threshold)
    await state.set_state(BonusManagementStates.waiting_for_bonus_type)

    # Use unified UI components for consistent styling
    from utils.bonus_tier_ui import tier_ui

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text=f"{tier_ui.EMOJIS['percentage']} Percentage Bonus", callback_data="bonus_type_percentage")],
        [InlineKeyboardButton(text=f"{tier_ui.EMOJIS['fixed']} Fixed Amount Bonus", callback_data="bonus_type_fixed")],
        [InlineKeyboardButton(text=f"{tier_ui.EMOJIS['error']} Cancel", callback_data="bonus_management")]
    ])

    header = tier_ui.format_header("ADD NEW BONUS TIER")
    progress = tier_ui.format_step_progress(2, 4, "BONUS TYPE")

    await message.reply(
        f"{header}\n{progress}\n"
            "<b>STEP 2: BONUS TYPE</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            f"💰 <b>Threshold:</b> ${threshold:.2f} ✅\n\n"
            "🎯 <b>Choose the bonus type for this tier:</b>\n\n"
            "📈 <b>Percentage Bonus:</b> Bonus calculated as a percentage of the deposit amount\n"
            "   <i>Example: 10% of $100 = $10 bonus</i>\n\n"
            "💵 <b>Fixed Amount Bonus:</b> Fixed bonus amount regardless of deposit size\n"
            "   <i>Example: $15 bonus for any qualifying deposit</i>",
            parse_mode="HTML",
            reply_markup=keyboard
        )


@router.message(BonusManagementStates.waiting_for_percentage)
async def process_bonus_percentage(message: Message, state: FSMContext):
    """Process the percentage input for new bonus tier with enhanced validation."""
    from utils.unified_validation import UnifiedValidation

    # Enhanced validation with improved error handling
    from utils.bonus_tier_errors import tier_error_handler

    try:
        validation_result = UnifiedValidation.validate_bonus_percentage(message.text.strip())

        if not validation_result["valid"]:
            error = ValueError(validation_result["error"])
            error_info = tier_error_handler.handle_tier_operation_error(
                "percentage validation", error, user_id=message.from_user.id
            )
            await message.reply(error_info["user_message"], parse_mode="HTML")
            return

    except Exception as e:
        error_info = tier_error_handler.handle_tier_operation_error(
            "percentage validation", e, user_id=message.from_user.id
        )
        await message.reply(error_info["user_message"], parse_mode="HTML")
        return

    percentage_display = validation_result["sanitized_percentage"]
    bonus_percentage = validation_result["decimal_value"]

    await state.update_data(bonus_percentage=bonus_percentage, percentage_display=percentage_display)
    await state.set_state(BonusManagementStates.waiting_for_description)

    # Get data for display
    data = await state.get_data()
    threshold = data.get("threshold")

    await message.reply(
        "🎁 <b>• ADD NEW BONUS TIER •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>STEP 4: DESCRIPTION</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f} ✅\n"
        f"🎯 <b>Type:</b> Percentage Bonus ✅\n"
        f"📈 <b>Bonus:</b> {percentage_display:.1f}% ✅\n\n"
        "📝 <b>Enter a description for this tier (optional):</b>\n\n"
        "<i>Examples:</i>\n"
        "• <code>Standard bonus for medium deposits</code>\n"
        "• <code>Premium tier for loyal customers</code>\n"
        "• <code>Holiday special bonus</code>\n\n"
        "💡 <i>You can also type 'skip' to proceed without a description.</i>",
        parse_mode="HTML"
    )


@router.message(BonusManagementStates.waiting_for_description)
async def process_bonus_description(message: Message, state: FSMContext):
    """Process the description input for new bonus tier with enhanced validation."""
    from utils.unified_validation import UnifiedValidation

    # Enhanced validation and sanitization
    validation_result = UnifiedValidation.validate_bonus_tier_description(message.text)

    if not validation_result["valid"]:
        await message.reply(
            f"❌ <b>Invalid description!</b>\n\n"
            f"{validation_result['error']}\n\n"
            "Please enter a valid description:\n"
            "• Maximum 200 characters\n"
            "• Type 'skip' to proceed without description\n"
            "• HTML/script content will be sanitized",
            parse_mode="HTML"
        )
        return

    description = validation_result["sanitized_description"]
    await state.update_data(description=description)
    await state.set_state(BonusManagementStates.confirm_tier)

    data = await state.get_data()
    threshold = data.get("threshold")
    bonus_type = data.get("bonus_type", "percentage")
    description = data.get("description")

    # Format bonus information based on type
    if bonus_type == "fixed":
        fixed_amount = data.get("bonus_fixed_amount", 0)
        bonus_display = f"${fixed_amount:.2f} fixed bonus"
        example_total = threshold + fixed_amount
        example_bonus = fixed_amount
    else:
        # Handle both percentage_display (from user input) and bonus_percentage (stored value)
        percentage_display = data.get("percentage_display")
        if percentage_display is None:
            # Fallback: calculate from bonus_percentage if percentage_display not set
            bonus_percentage = data.get("bonus_percentage", 0)
            percentage_display = bonus_percentage * 100

        bonus_display = f"{percentage_display:.1f}% bonus"
        bonus_amount = threshold * data.get("bonus_percentage", 0)
        example_total = threshold + bonus_amount
        example_bonus = bonus_amount

    # Create confirmation message
    message_text = (
        "🎁 <b>• CONFIRM NEW BONUS TIER •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>TIER SUMMARY</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f}\n"
        f"🎁 <b>Bonus:</b> {bonus_display}\n"
        f"🎯 <b>Type:</b> {bonus_type.title()}\n"
        f"📝 <b>Description:</b> {description or 'No description'}\n"
        f"📊 <b>Status:</b> Active\n\n"
        "<b>Example:</b>\n"
        f"• User deposits ${threshold:.2f} → receives ${example_total:.2f}\n"
        f"• Bonus amount: ${example_bonus:.2f}\n\n"
        "❓ <b>Create this bonus tier?</b>"
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(text="✅ Create Tier", callback_data="bonus_confirm_create"),
            InlineKeyboardButton(text="❌ Cancel", callback_data="bonus_cancel_create")
        ]
    ])

    await message.reply(message_text, reply_markup=keyboard, parse_mode="HTML")


@router.callback_query(F.data == "bonus_type_percentage")
async def bonus_select_percentage_type(callback_query: CallbackQuery, state: FSMContext):
    """Handle selection of percentage bonus type."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    data = await state.get_data()
    threshold = data.get("threshold", 0)

    await state.update_data(bonus_type="percentage")
    await state.set_state(BonusManagementStates.waiting_for_percentage)
    await callback_query.answer()

    message_text = (
        "🎁 <b>• ADD NEW BONUS TIER •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>STEP 3: PERCENTAGE</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f} ✅\n"
        f"🎯 <b>Type:</b> Percentage Bonus ✅\n\n"
        "📈 <b>Enter the bonus percentage for this tier:</b>\n\n"
        "<i>Examples:</i>\n"
        "• <code>10</code> for 10% bonus\n"
        "• <code>15.5</code> for 15.5% bonus\n"
        "• <code>25</code> for 25% bonus\n\n"
        "⚠️ <i>Enter only the percentage number (without % symbol).</i>"
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="❌ Cancel", callback_data="bonus_management")]
    ])

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "bonus_type_fixed")
async def bonus_select_fixed_type(callback_query: CallbackQuery, state: FSMContext):
    """Handle selection of fixed amount bonus type."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    data = await state.get_data()
    threshold = data.get("threshold", 0)

    await state.update_data(bonus_type="fixed")
    await state.set_state(BonusManagementStates.waiting_for_fixed_amount)
    await callback_query.answer()

    message_text = (
        "🎁 <b>• ADD NEW BONUS TIER •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>STEP 3: FIXED AMOUNT</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f} ✅\n"
        f"🎯 <b>Type:</b> Fixed Amount Bonus ✅\n\n"
        "💵 <b>Enter the fixed bonus amount for this tier:</b>\n\n"
        "<i>Examples:</i>\n"
        "• <code>10.00</code> for $10 fixed bonus\n"
        "• <code>25.50</code> for $25.50 fixed bonus\n"
        "• <code>100</code> for $100 fixed bonus\n\n"
        "⚠️ <i>Enter only the dollar amount (without $ symbol).</i>"
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="❌ Cancel", callback_data="bonus_management")]
    ])

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.message(BonusManagementStates.waiting_for_fixed_amount)
async def process_bonus_fixed_amount(message: Message, state: FSMContext):
    """Process the fixed amount input for new bonus tier with enhanced validation."""
    from utils.unified_validation import UnifiedValidation

    # Enhanced validation
    validation_result = UnifiedValidation.validate_bonus_fixed_amount(message.text.strip())

    if not validation_result["valid"]:
        await message.reply(
            f"❌ <b>Invalid fixed amount!</b>\n\n"
            f"{validation_result['error']}\n\n"
            "Please enter a valid fixed amount:\n"
            "• Must be greater than $0\n"
            "• Maximum allowed: $10,000\n"
            "• Example: <code>25.00</code>",
            parse_mode="HTML"
        )
        return

    fixed_amount = validation_result["sanitized_amount"]
    await state.update_data(bonus_fixed_amount=fixed_amount)
    await state.set_state(BonusManagementStates.waiting_for_description)

    # Get data for display
    data = await state.get_data()
    threshold = data.get("threshold", 0)

    message_text = (
        "🎁 <b>• ADD NEW BONUS TIER •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>STEP 4: DESCRIPTION</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f} ✅\n"
        f"🎯 <b>Type:</b> Fixed Amount Bonus ✅\n"
        f"💵 <b>Fixed Amount:</b> ${fixed_amount:.2f} ✅\n\n"
        "📝 <b>Enter a description for this bonus tier:</b>\n\n"
        "<i>Examples:</i>\n"
        "• <code>Standard fixed bonus for medium deposits</code>\n"
        "• <code>Premium fixed reward tier</code>\n\n"
        "💡 <i>Type 'skip' to use a default description.</i>"
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="❌ Cancel", callback_data="bonus_management")]
    ])

    await message.reply(message_text, reply_markup=keyboard, parse_mode="HTML")


@router.callback_query(F.data == "bonus_confirm_create")
async def bonus_confirm_create_tier(callback_query: CallbackQuery, state: FSMContext):
    """Confirm and create the new bonus tier with enhanced security."""
    user_id = callback_query.from_user.id

    # Enhanced privilege checking
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        logger.warning(f"Unauthorized tier creation confirmation by user {user_id}")
        return

    # Import security functions
    from database.operations import (
        create_bonus_tier_atomic, validate_tier_operation_safety,
        log_tier_operation, check_tier_operation_rate_limit
    )

    # Final rate limit check
    rate_limit_check = check_tier_operation_rate_limit(user_id, "create_tier", window_minutes=5, max_operations=5)
    if rate_limit_check["rate_limited"]:
        await callback_query.answer("🚫 Rate limit exceeded", show_alert=True)
        await state.clear()
        return

    data = await state.get_data()
    threshold = data.get("threshold")
    bonus_type = data.get("bonus_type", "percentage")
    bonus_percentage = data.get("bonus_percentage")
    bonus_fixed_amount = data.get("bonus_fixed_amount")
    description = data.get("description")

    # Prepare tier data for atomic creation
    tier_data = {
        "threshold": threshold,
        "bonus_type": bonus_type,
        "bonus_percentage": bonus_percentage,
        "bonus_fixed_amount": bonus_fixed_amount,
        "description": description,
        "created_by": user_id,
        "is_active": True
    }

    # Validate operation safety
    safety_check = validate_tier_operation_safety("create", threshold=threshold)
    if not safety_check["safe"]:
        error_msg = "; ".join(safety_check["errors"])
        await callback_query.answer(f"❌ Cannot create tier: {error_msg}", show_alert=True)
        log_tier_operation("create_tier", user_id, tier_data=tier_data, success=False, error=error_msg)
        await state.clear()
        return

    # Create the bonus tier using atomic operation
    tier = create_bonus_tier_atomic(tier_data)

    await state.clear()

    if tier:
        # Log successful creation
        log_tier_operation("create_tier", user_id, tier_data=tier_data, success=True)

        await callback_query.answer("✅ Bonus tier created successfully!")

        message_text = (
            "🎉 <b>• BONUS TIER CREATED •</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>SUCCESS</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            f"✅ New bonus tier has been created successfully!\n\n"
            f"{format_bonus_tier_info(tier)}\n"
            "<i>The tier is now active and will be applied to qualifying deposits.</i>\n\n"
            f"🔒 <b>Security:</b> Operation logged for audit trail"
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="📊 View All Tiers", callback_data="bonus_view_all")],
            [InlineKeyboardButton(text="➕ Add Another", callback_data="bonus_add_new")],
            [InlineKeyboardButton(text="🔙 Back to Management", callback_data="bonus_management")]
        ])

    else:
        # Enhanced error handling for failed creation
        from utils.bonus_tier_errors import tier_error_handler, TierErrorCategory

        # Log failed creation
        log_tier_operation("create_tier", user_id, tier_data=tier_data, success=False, error="Atomic creation failed")

        # Create comprehensive error response
        creation_error = Exception("Tier creation failed during atomic operation")
        error_info = tier_error_handler.handle_tier_operation_error(
            "create_tier", creation_error, user_id=user_id, additional_context=tier_data
        )

        await callback_query.answer("❌ Failed to create bonus tier", show_alert=True)

        # Get recovery suggestions
        recovery_buttons = tier_error_handler.create_recovery_keyboard("create_tier", retry_allowed=True)
        alternative_actions = tier_error_handler.suggest_alternative_actions("create_tier", TierErrorCategory.SYSTEM)

        message_text = (
            f"{error_info['user_message']}\n\n"
            "🔧 <b>Alternative Actions:</b>\n"
        )

        for action in alternative_actions[:3]:  # Limit to 3 suggestions
            message_text += f"• {action}\n"

        message_text += f"\n🔒 <b>Security:</b> Failed attempt logged for audit"

        # Convert recovery buttons to InlineKeyboardMarkup
        keyboard_buttons = []
        for row in recovery_buttons:
            button_row = []
            for button in row:
                button_row.append(InlineKeyboardButton(text=button["text"], callback_data=button["callback_data"]))
            keyboard_buttons.append(button_row)

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "bonus_cancel_create")
async def bonus_cancel_create_tier(callback_query: CallbackQuery, state: FSMContext):
    """Cancel the bonus tier creation process."""
    await state.clear()
    await callback_query.answer("❌ Bonus tier creation cancelled")

    # Return to bonus management
    await bonus_management_main(callback_query)


@router.callback_query(F.data.startswith("bonus_delete_confirm:"))
async def bonus_delete_confirm(callback_query: CallbackQuery):
    """Confirm deletion of a bonus tier."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    tier_id = callback_query.data.split(":", 1)[1]
    tier = get_bonus_tier_by_id(tier_id)

    if not tier:
        await callback_query.answer("❌ Tier not found", show_alert=True)
        return

    await callback_query.answer()

    threshold = tier.get("threshold", 0)
    percentage = tier.get("bonus_percentage", 0) * 100

    message_text = (
        "⚠️ <b>• CONFIRM DELETION •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>DANGER ZONE</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"You are about to delete the bonus tier:\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f}\n"
        f"📈 <b>Bonus:</b> {percentage:.1f}%\n\n"
        "⚠️ <b>This action cannot be undone!</b>\n\n"
        "❓ <b>Are you sure you want to delete this tier?</b>"
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(text="🗑️ Yes, Delete", callback_data=f"bonus_delete_confirmed:{tier_id}"),
            InlineKeyboardButton(text="❌ Cancel", callback_data=f"bonus_view_tier:{tier_id}")
        ]
    ])

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("bonus_delete_confirmed:"))
async def bonus_delete_confirmed(callback_query: CallbackQuery):
    """Actually delete the bonus tier with enhanced security."""
    user_id = callback_query.from_user.id

    # Enhanced privilege checking
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        logger.warning(f"Unauthorized tier deletion attempt by user {user_id}")
        return

    # Import security functions
    from database.operations import (
        delete_bonus_tier, validate_tier_operation_safety,
        log_tier_operation, check_tier_operation_rate_limit, get_bonus_tier_by_id
    )

    tier_id = callback_query.data.split(":", 1)[1]

    # Rate limiting check
    rate_limit_check = check_tier_operation_rate_limit(user_id, "delete_tier", window_minutes=10, max_operations=3)
    if rate_limit_check["rate_limited"]:
        await callback_query.answer("🚫 Rate limit exceeded for deletions", show_alert=True)
        log_tier_operation("delete_tier", user_id, tier_id=tier_id, success=False, error="Rate limited")
        return

    # Get tier info before deletion for logging
    tier_info = get_bonus_tier_by_id(tier_id)

    # Validate operation safety
    safety_check = validate_tier_operation_safety("delete", tier_id=tier_id)
    if not safety_check["safe"]:
        error_msg = "; ".join(safety_check["errors"])
        await callback_query.answer(f"❌ Cannot delete tier: {error_msg}", show_alert=True)
        log_tier_operation("delete_tier", user_id, tier_id=tier_id, success=False, error=error_msg)
        return

    # Perform deletion
    deletion_success = delete_bonus_tier(tier_id)

    if deletion_success:
        # Log successful deletion
        log_tier_operation("delete_tier", user_id, tier_id=tier_id, success=True)

        await callback_query.answer("✅ Bonus tier deleted successfully!")

        message_text = (
            "🗑️ <b>• TIER DELETED •</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>SUCCESS</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            "✅ The bonus tier has been deleted successfully.\n\n"
            "<i>This tier will no longer be applied to new deposits.</i>\n\n"
            f"🔒 <b>Security:</b> Deletion logged for audit trail"
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="📊 View All Tiers", callback_data="bonus_view_all")],
            [InlineKeyboardButton(text="🔙 Back to Management", callback_data="bonus_management")]
        ])

    else:
        # Log failed deletion
        log_tier_operation("delete_tier", user_id, tier_id=tier_id, success=False, error="Database deletion failed")

        await callback_query.answer("❌ Failed to delete tier", show_alert=True)

        message_text = (
            "❌ <b>• DELETION FAILED •</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>ERROR</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            "Failed to delete the bonus tier.\n\n"
            "<i>Please try again or contact support.</i>\n\n"
            f"🔒 <b>Security:</b> Failed attempt logged for audit"
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🔄 Try Again", callback_data=f"bonus_delete_confirm:{tier_id}")],
            [InlineKeyboardButton(text="🔙 Back", callback_data="bonus_view_all")]
        ])

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# Edit handlers for existing bonus tiers

@router.callback_query(F.data.startswith("bonus_edit:"))
async def bonus_edit_tier(callback_query: CallbackQuery):
    """Show edit options for a bonus tier."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    tier_id = callback_query.data.split(":", 1)[1]
    tier = get_bonus_tier_by_id(tier_id)

    if not tier:
        await callback_query.answer("❌ Tier not found", show_alert=True)
        return

    await callback_query.answer()

    message_text = (
        "✏️ <b>• EDIT BONUS TIER •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>CURRENT CONFIGURATION</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"{format_bonus_tier_info(tier)}\n\n"
        "<i>Select what you want to edit:</i>"
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=bonus_edit_keyboard(tier_id, tier),
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("bonus_edit_threshold:"))
async def bonus_edit_threshold(callback_query: CallbackQuery, state: FSMContext):
    """Start editing threshold of a bonus tier."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    tier_id = callback_query.data.split(":", 1)[1]
    tier = get_bonus_tier_by_id(tier_id)

    if not tier:
        await callback_query.answer("❌ Tier not found", show_alert=True)
        return

    await state.update_data(editing_tier_id=tier_id)
    await state.set_state(BonusManagementStates.editing_threshold)
    await callback_query.answer()

    current_threshold = tier.get("threshold", 0)

    message_text = (
        "✏️ <b>• EDIT THRESHOLD •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>CURRENT VALUE</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Current Threshold:</b> ${current_threshold:.2f}\n\n"
        "💰 <b>Enter the new minimum deposit amount (threshold):</b>\n\n"
        "<i>Example: 75.00 (for $75 minimum deposit)</i>\n\n"
        "⚠️ <i>Enter only the number, without the $ symbol.</i>"
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="❌ Cancel", callback_data=f"bonus_edit:{tier_id}")]
    ])

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("bonus_edit_type:"))
async def bonus_edit_type(callback_query: CallbackQuery, state: FSMContext):
    """Start editing bonus type of a bonus tier."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    tier_id = callback_query.data.split(":", 1)[1]
    tier = get_bonus_tier_by_id(tier_id)

    if not tier:
        await callback_query.answer("❌ Tier not found", show_alert=True)
        return

    await state.update_data(editing_tier_id=tier_id)
    await state.set_state(BonusManagementStates.editing_bonus_type)
    await callback_query.answer()

    current_type = tier.get("bonus_type", "percentage")
    threshold = tier.get("threshold", 0)

    if current_type == "fixed":
        current_value = f"${tier.get('bonus_fixed_amount', 0):.2f} fixed"
    else:
        current_value = f"{tier.get('bonus_percentage', 0)*100:.1f}% percentage"

    message_text = (
        "✏️ <b>• EDIT BONUS TYPE •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>CURRENT CONFIGURATION</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f}\n"
        f"🎯 <b>Current Type:</b> {current_value}\n\n"
        "🎯 <b>Choose the new bonus type:</b>\n\n"
        "📈 <b>Percentage Bonus:</b> Bonus calculated as a percentage of the deposit amount\n"
        "💵 <b>Fixed Amount Bonus:</b> Fixed bonus amount regardless of deposit size\n\n"
        "⚠️ <i>Changing the bonus type will require setting a new bonus value.</i>"
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📈 Change to Percentage", callback_data=f"bonus_change_to_percentage:{tier_id}")],
        [InlineKeyboardButton(text="💵 Change to Fixed Amount", callback_data=f"bonus_change_to_fixed:{tier_id}")],
        [InlineKeyboardButton(text="❌ Cancel", callback_data=f"bonus_edit:{tier_id}")]
    ])

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("bonus_edit_percentage:"))
async def bonus_edit_percentage(callback_query: CallbackQuery, state: FSMContext):
    """Start editing percentage of a bonus tier."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    tier_id = callback_query.data.split(":", 1)[1]
    tier = get_bonus_tier_by_id(tier_id)

    if not tier:
        await callback_query.answer("❌ Tier not found", show_alert=True)
        return

    await state.update_data(editing_tier_id=tier_id)
    await state.set_state(BonusManagementStates.editing_percentage)
    await callback_query.answer()

    current_percentage = tier.get("bonus_percentage", 0) * 100
    threshold = tier.get("threshold", 0)

    message_text = (
        "✏️ <b>• EDIT PERCENTAGE •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>CURRENT VALUE</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f}\n"
        f"📈 <b>Current Percentage:</b> {current_percentage:.1f}%\n\n"
        "📈 <b>Enter the new bonus percentage:</b>\n\n"
        "<i>Examples:</i>\n"
        "• <code>10</code> for 10% bonus\n"
        "• <code>15.5</code> for 15.5% bonus\n"
        "• <code>25</code> for 25% bonus\n\n"
        "⚠️ <i>Enter only the percentage number (without % symbol).</i>"
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="❌ Cancel", callback_data=f"bonus_edit:{tier_id}")]
    ])

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("bonus_edit_fixed:"))
async def bonus_edit_fixed_amount(callback_query: CallbackQuery, state: FSMContext):
    """Start editing fixed amount of a bonus tier."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    tier_id = callback_query.data.split(":", 1)[1]
    tier = get_bonus_tier_by_id(tier_id)

    if not tier:
        await callback_query.answer("❌ Tier not found", show_alert=True)
        return

    await state.update_data(editing_tier_id=tier_id)
    await state.set_state(BonusManagementStates.editing_fixed_amount)
    await callback_query.answer()

    current_fixed = tier.get("bonus_fixed_amount", 0)
    threshold = tier.get("threshold", 0)

    message_text = (
        "✏️ <b>• EDIT FIXED AMOUNT •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>CURRENT VALUE</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f}\n"
        f"💵 <b>Current Fixed Amount:</b> ${current_fixed:.2f}\n\n"
        "💵 <b>Enter the new fixed bonus amount:</b>\n\n"
        "<i>Examples:</i>\n"
        "• <code>10.00</code> for $10 fixed bonus\n"
        "• <code>25.50</code> for $25.50 fixed bonus\n"
        "• <code>100</code> for $100 fixed bonus\n\n"
        "⚠️ <i>Enter only the dollar amount (without $ symbol).</i>"
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="❌ Cancel", callback_data=f"bonus_edit:{tier_id}")]
    ])

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("bonus_edit_description:"))
async def bonus_edit_description(callback_query: CallbackQuery, state: FSMContext):
    """Start editing description of a bonus tier."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    tier_id = callback_query.data.split(":", 1)[1]
    tier = get_bonus_tier_by_id(tier_id)

    if not tier:
        await callback_query.answer("❌ Tier not found", show_alert=True)
        return

    await state.update_data(editing_tier_id=tier_id)
    await state.set_state(BonusManagementStates.editing_description)
    await callback_query.answer()

    current_description = tier.get("description", "No description")
    threshold = tier.get("threshold", 0)
    bonus_type = tier.get("bonus_type", "percentage")

    if bonus_type == "fixed":
        bonus_info = f"${tier.get('bonus_fixed_amount', 0):.2f} fixed"
    else:
        bonus_info = f"{tier.get('bonus_percentage', 0)*100:.1f}%"

    message_text = (
        "✏️ <b>• EDIT DESCRIPTION •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>CURRENT CONFIGURATION</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f}\n"
        f"🎁 <b>Bonus:</b> {bonus_info}\n"
        f"📝 <b>Current Description:</b> <i>{current_description}</i>\n\n"
        "📝 <b>Enter the new description:</b>\n\n"
        "<i>Examples:</i>\n"
        "• <code>Premium bonus for VIP customers</code>\n"
        "• <code>Special promotion tier</code>\n\n"
        "💡 <i>Type 'skip' to remove the description.</i>"
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="❌ Cancel", callback_data=f"bonus_edit:{tier_id}")]
    ])

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )
