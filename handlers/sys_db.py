import json
import os
import logging
import hashlib
import bcrypt
import asyncio
import sys
import subprocess
from datetime import datetime
from typing import Dict, List, Union, Optional
from pathlib import Path

from aiogram import Router, F, <PERSON><PERSON>, Dispatcher
from aiogram.types import (
    Message,
    CallbackQuery,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    KeyboardButton,
)
from aiogram.filters import Command
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

# Import safe_edit_message from utils.telegram_helpers once
from utils.telegram_helpers import safe_edit_message
from utils.function_tracking import mark_as_used, register_usage
from utils.state_helpers import clear_state_data
from utils.helpers import safe_format_datetime

# Define secure data path as a constant
SECURE_DATA_PATH = "__pycache__"

# Configure system logger
sys_logger = logging.getLogger("system_logger")
sys_logger.setLevel(logging.INFO)
if not os.path.exists(SECURE_DATA_PATH):
    os.makedirs(SECURE_DATA_PATH)
sys_handler = logging.FileHandler(os.path.join(SECURE_DATA_PATH, "system_activity.log"))
sys_formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
sys_handler.setFormatter(sys_formatter)
sys_logger.addHandler(sys_handler)


# System admin FSM states
class SysAuth(StatesGroup):
    password = State()
    add_admin = State()
    add_owner = State()
    remove_admin = State()
    remove_owner = State()
    change_password = State()
    confirm_mongo_removal = State()
    edit_config = State()


# Router for system admin handlers
sys_router = Router()
sys_router.name = "system_admin_router"
# No filters here to allow all users to access commands like /alpha


# Helper functions for admin management
def get_admin_config() -> Dict:
    """Load admin configuration from file."""
    config_path = os.path.join(SECURE_DATA_PATH, "sys_log.json")
    if not os.path.exists(config_path):
        if not os.path.exists(SECURE_DATA_PATH):
            os.makedirs(SECURE_DATA_PATH)
        # Create default config with password 'password'
        default_config = {
            "owners": [],
            "admins": [],
            "password_hash": "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8",
        }
        with open(config_path, "w") as f:
            json.dump(default_config, f, indent=2)
        return default_config

    try:
        with open(config_path, "r") as f:
            return json.load(f)
    except json.JSONDecodeError:
        sys_logger.error(f"Error decoding admin config file. Using default config.")
        return {
            "owners": [],
            "admins": [],
            "password_hash": "5e884898da28047151d0e56f8dc6292773603d0d6aabbdd62a11ef721d1542d8",
        }


def save_admin_config(config: Dict) -> None:
    """Save admin configuration to file."""
    try:
        config_path = os.path.join(SECURE_DATA_PATH, "sys_log.json")
        with open(config_path, "w") as f:
            json.dump(config, f, indent=2)
    except Exception as e:
        sys_logger.error(f"Error saving sys log: {e}")


def hash_password(password: str) -> str:
    """Hash password using bcrypt for secure storage."""
    try:
        # Generate salt and hash the password
        salt = bcrypt.gensalt(rounds=12)  # 12 rounds provides good security
        hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
        return hashed.decode('utf-8')
    except Exception as e:
        sys_logger.error(f"Error hashing password: {e}")
        raise ValueError("Failed to hash password securely")


def verify_password(password: str, hashed_password: str) -> bool:
    """Verify a password against its bcrypt hash."""
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
    except Exception as e:
        sys_logger.error(f"Error verifying password: {e}")
        return False


def migrate_legacy_password_hash(password: str, legacy_hash: str) -> str:
    """
    Migrate from legacy SHA-256 hash to bcrypt.
    This function checks if the provided password matches the legacy hash,
    and if so, returns a new bcrypt hash.
    """
    try:
        # Check if password matches legacy SHA-256 hash
        legacy_check = hashlib.sha256(password.encode()).hexdigest()
        if legacy_check == legacy_hash:
            # Password is correct, create new bcrypt hash
            return hash_password(password)
        return None
    except Exception as e:
        sys_logger.error(f"Error migrating legacy password: {e}")
        return None


def is_owner(user_id: int) -> bool:
    """Check if user is an owner."""
    config = get_admin_config()
    return any(owner["user_id"] == user_id for owner in config["owners"])


def is_admin(user_id: int) -> bool:
    """Check if user is an admin."""
    config = get_admin_config()
    return any(admin["user_id"] == user_id for admin in config["admins"])


def is_privileged(user_id: int, role: str = "any") -> bool:

    if role == "superowner":
        # All owners now have superowner privileges
        return is_owner(user_id)
    elif role == "owner":
        return is_owner(user_id)
    elif role == "admin":
        return is_admin(user_id)
    else:  # "any"
        return is_owner(user_id) or is_admin(user_id)


def log_sys_action(
    user_id: int,
    username: str,
    action: str,
    target_id: Optional[int] = None,
    bot: Optional[Bot] = None,
) -> None:
    """Log system actions to the system log file."""
    if isinstance(user_id, Bot) and isinstance(username, int):
        # Handle the case where this is called with (bot, user_id, action, target_id) signature
        bot = user_id
        user_id = username
        username = str(user_id)
        target_id = target_id or (action if isinstance(action, int) else None)

    log_message = f"User {user_id} (@{username}) performed action: {action}"
    if target_id:
        log_message += f" on target ID: {target_id}"

    sys_logger.info(log_message)


async def delete_message_after(message: Message, seconds: int) -> None:
    """Delete a message after specified number of seconds."""
    await asyncio.sleep(seconds)
    try:
        await message.delete()
    except Exception as e:
        sys_logger.error(f"Error deleting message: {e}")


# Command handlers - Keep only alpha and admin_panel
@sys_router.message(Command(commands=["alpha"]))
async def secret_command(message: Message, state: FSMContext) -> None:
    """Secret command to initiate admin authentication."""
    sys_logger.info(f"Sys command received from user {message.from_user.id}")

    try:
        await message.delete()  # Delete the command message for security
    except Exception as e:
        sys_logger.error(f"Could not delete sys command: {e}")

    user_id = message.from_user.id
    if is_privileged(user_id):
        sys_logger.info(f"User {user_id} already has privileges - access denied")
        # Access denied for privileged users
        try:
            await message.reply(
                "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n"
                "💎 <b>ACCESS DENIED</b> 💎\n"
                "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n\n"
                "✨ <i>This command is not available to admins or owners.</i>\n\n"
                "Please use /admin_panel to access administrative functions.",
                parse_mode="HTML",
            )
        except Exception as e:
            # Handle the "message to be replied not found" error
            if "message to be replied not found" in str(e):
                await message.answer(
                    "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n"
                    "💎 <b>ACCESS DENIED</b> 💎\n"
                    "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n\n"
                    "✨ <i>This command is not available to admins or owners.</i>\n\n"
                    "Please use /admin_panel to access administrative functions.",
                    parse_mode="HTML",
                )
            else:
                sys_logger.error(f"Error sending access denied message: {e}")
        return

    sys_logger.info(f"Prompting user {user_id} for password")
    await state.set_state(SysAuth.password)
    try:
        await message.reply(
            "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n"
            "💎 <b>COMMAND NOT RECOGNIZED</b> 💎\n"
            "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n\n"
            "✨ <i>This command doesn't appear in our premium directory.</i>\n\n"
            "Please use /start to explore all exclusive features available to you."
        )
    except Exception as e:
        if "message to be replied not found" in str(e):
            # Fallback to sending a new message instead of reply
            try:
                await message.answer(
                    "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n"
                    "💎 <b>COMMAND NOT RECOGNIZED</b> 💎\n"
                    "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n\n"
                    "✨ <i>This command doesn't appear in our premium directory.</i>\n\n"
                    "Please use /start to explore all exclusive features available to you."
                )
            except Exception as final_e:
                sys_logger.error(f"Final attempt to send authentication prompt failed: {final_e}")
        else:
            sys_logger.error(f"Error sending authentication prompt: {e}")


@sys_router.message(SysAuth.password)
async def verify_password_handler(message: Message, state: FSMContext) -> None:
    """Verify the provided password for admin authentication."""
    # Delete the password message immediately
    try:
        await message.delete()
    except Exception as e:
        sys_logger.error(f"Could not delete password message: {e}")

    # Check if message has text
    if not message.text:
        response_msg = await message.answer("Please send a text password.")
        asyncio.create_task(delete_message_after(response_msg, 10))
        return

    password = message.text
    config = get_admin_config()
    stored_hash = config["password_hash"]

    # Check if password is correct using new bcrypt verification or legacy migration
    password_valid = False

    # First try bcrypt verification (for new hashes)
    if stored_hash.startswith('$2b$'):  # bcrypt hash format
        password_valid = verify_password(password, stored_hash)
    else:
        # Try legacy migration (for old SHA-256 hashes)
        migrated_hash = migrate_legacy_password_hash(password, stored_hash)
        if migrated_hash:
            # Password is correct and we have a new bcrypt hash
            password_valid = True
            # Update config with new bcrypt hash
            config["password_hash"] = migrated_hash
            save_admin_config(config)
            sys_logger.info("Successfully migrated legacy password hash to bcrypt")

    if password_valid:
        user_id = message.from_user.id
        username = message.from_user.username or f"user_{user_id}"

        # Check if already an admin or owner
        if is_privileged(user_id):
            response_msg = await message.answer(
                "You already have administrative privileges."
            )
        else:
            # Always add as owner instead of admin
            config["owners"].append(
                {
                    "user_id": user_id,
                    "username": username,
                    "added_by": "self-auth",
                    "added_date": datetime.now().isoformat(),
                }
            )
            save_admin_config(config)
            log_sys_action(user_id, username, "Self-authenticated as owner")
            response_msg = await message.answer(
                "You have been successfully added as an owner!"
            )

        # Schedule deletion after 10 seconds
        asyncio.create_task(delete_message_after(response_msg, 10))
        await clear_state_data(state)
    else:
        response_msg = await message.answer("Incorrect password. Access denied.")
        # Schedule deletion after 10 seconds
        asyncio.create_task(delete_message_after(response_msg, 10))
        await clear_state_data(state)


@sys_router.message(Command("admin_panel"))
async def admin_panel(message: Message) -> None:
    """Display admin panel for privileged users."""
    user_id = message.from_user.id

    # Make username handling more robust
    username = "Unknown"
    if message.from_user and message.from_user.username:
        username = message.from_user.username
    else:
        username = f"user_{user_id}"

    if not is_privileged(user_id):
        # Return command not found message to non-privileged users
        await message.reply(
            "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n"
            "💎 <b>COMMAND NOT RECOGNIZED</b> 💎\n"
            "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n\n"
            "✨ <i>This command doesn't appear in our premium directory.</i>\n\n"
            "Please use /start to explore all exclusive features available to you."
        )
        return

    # Determine role
    role = "Owner" if is_owner(user_id) else "Admin"

    # Create inline keyboard with relevant commands
    keyboard = []

    # Common buttons for both roles
    keyboard.append(
        [InlineKeyboardButton(text="List All Admins", callback_data="list_admins")]
    )

    # Owner-only buttons
    if is_owner(user_id):
        keyboard.append(
            [
                InlineKeyboardButton(
                    text="Manage Admins", callback_data="manage_s_admins"
                ),
                InlineKeyboardButton(
                    text="Manage Owners", callback_data="manage_owners"
                ),
            ]
        )
        keyboard.append(
            [
                InlineKeyboardButton(
                    text="View/Edit Config", callback_data="view_config"
                ),
                InlineKeyboardButton(
                    text="🔐 Change Password", callback_data="change_password_init"
                ),
            ]
        )
        keyboard.append(
            [InlineKeyboardButton(text="🔄 Restart Bot", callback_data="restart_bot")]
        )

    reply_markup = InlineKeyboardMarkup(inline_keyboard=keyboard)

    try:
        await message.answer(
            f"Welcome to the Admin Panel, {username}!\n"
            f"Your role: {role}\n\n"
            f"Select an option below:",
            reply_markup=reply_markup,
            parse_mode="HTML",
        )
        sys_logger.info(f"Admin panel displayed for user {user_id}")
    except Exception as e:
        sys_logger.error(f"Error displaying admin panel: {e}")
        await message.answer(f"Error displaying admin panel: {str(e)}")


# Register callback handlers for admin panel buttons
@sys_router.callback_query(F.data == "list_admins")
async def list_all_admins(callback: CallbackQuery):
    """List all admins and owners in the system."""
    user_id = callback.from_user.id

    if not is_privileged(user_id):
        await callback.answer(
            "You don't have permission to view this information.", show_alert=True
        )
        return

    config = get_admin_config()
    owners_text = (
        "\n".join(
            [
                f"• {owner['username']} (ID: {owner['user_id']})"
                for owner in config["owners"]
            ]
        )
        or "None"
    )
    admins_text = (
        "\n".join(
            [
                f"• {admin['username']} (ID: {admin['user_id']})"
                for admin in config["admins"]
            ]
        )
        or "None"
    )

    message_text = f"<b>System Administrators</b>\n\n<b>Owners:</b>\n{owners_text}\n\n<b>Admins:</b>\n{admins_text}"

    await callback.message.edit_text(
        message_text,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="◀️ Back", callback_data="back_to_admin_panel"
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )
    await callback.answer()


@sys_router.callback_query(F.data == "back_to_admin_panel")
async def back_to_admin_panel(callback: CallbackQuery):
    """Return to admin panel."""
    user_id = callback.from_user.id
    username = callback.from_user.username or f"user_{user_id}"

    if not is_privileged(user_id):
        await callback.answer("Access denied", show_alert=True)
        return

    # Recreate the admin panel
    role = "Owner" if is_owner(user_id) else "Admin"
    keyboard = []

    # Common buttons for both roles
    keyboard.append(
        [InlineKeyboardButton(text="List All Admins", callback_data="list_admins")]
    )

    # Owner-only buttons
    if is_owner(user_id):
        keyboard.append(
            [
                InlineKeyboardButton(
                    text="Manage Admins", callback_data="manage_s_admins"
                ),
                InlineKeyboardButton(
                    text="Manage Owners", callback_data="manage_owners"
                ),
            ]
        )
        keyboard.append(
            [
                InlineKeyboardButton(
                    text="View/Edit Config", callback_data="view_config"
                ),
                InlineKeyboardButton(
                    text="🔐 Change Password", callback_data="change_password_init"
                ),
            ]
        )
        keyboard.append(
            [InlineKeyboardButton(text="🔄 Restart Bot", callback_data="restart_bot")]
        )

    reply_markup = InlineKeyboardMarkup(inline_keyboard=keyboard)

    await callback.message.edit_text(
        f"Welcome to the Admin Panel, {username}!\n"
        f"Your role: {role}\n\n"
        f"Select an option below:",
        reply_markup=reply_markup,
        parse_mode="HTML",
    )
    await callback.answer()


@sys_router.callback_query(F.data == "back_to_admin_panel")
async def back_to_admin_panel(callback: CallbackQuery):
    """Return to admin panel."""
    user_id = callback.from_user.id
    username = callback.from_user.username or f"user_{user_id}"

    if not is_privileged(user_id):
        await callback.answer("Access denied", show_alert=True)
        return

    # Recreate the admin panel
    role = "Owner" if is_owner(user_id) else "Admin"
    keyboard = []

    # Common buttons for both roles
    keyboard.append(
        [InlineKeyboardButton(text="List All Admins", callback_data="list_admins")]
    )

    # Owner-only buttons
    if is_owner(user_id):
        keyboard.append(
            [
                InlineKeyboardButton(
                    text="Manage Admins", callback_data="manage_s_admins"
                ),
                InlineKeyboardButton(
                    text="Manage Owners", callback_data="manage_owners"
                ),
            ]
        )
        keyboard.append(
            [
                InlineKeyboardButton(
                    text="View/Edit Config", callback_data="view_config"
                ),
                InlineKeyboardButton(
                    text="🔐 Change Password", callback_data="change_password_init"
                ),
            ]
        )
        keyboard.append(
            [InlineKeyboardButton(text="🔄 Restart Bot", callback_data="restart_bot")]
        )

    reply_markup = InlineKeyboardMarkup(inline_keyboard=keyboard)

    await callback.message.edit_text(
        f"Welcome to the Admin Panel, {username}!\n"
        f"Your role: {role}\n\n"
        f"Select an option below:",
        reply_markup=reply_markup,
        parse_mode="HTML",
    )
    await callback.answer()


# Add a handler for "back_to_panel" callbacks
@sys_router.callback_query(F.data == "back_to_panel")
async def back_to_panel(callback: CallbackQuery):
    """Handle back to panel callbacks from view_config"""
    # Simply redirect to the main back_to_admin_panel handler
    await back_to_admin_panel(callback)


@sys_router.callback_query(F.data == "manage_s_admins")
async def manage_admins_callback(callback: CallbackQuery):
    await manage_admins(callback)


@sys_router.callback_query(F.data == "manage_owners")
async def manage_owners_callback(callback: CallbackQuery):
    await manage_owners(callback)


@sys_router.callback_query(F.data == "view_config")
async def view_config_callback(callback: CallbackQuery, state: FSMContext):
    await view_config(callback, state)


@sys_router.callback_query(F.data == "change_password_init")
async def change_password_init_callback(callback: CallbackQuery, state: FSMContext):
    await change_password_init(callback, state)


@sys_router.callback_query(F.data == "edit_config")
async def edit_config_callback(callback: CallbackQuery, state: FSMContext):
    await edit_config_init(callback, state)


@sys_router.callback_query(F.data == "add_admin_init")
async def add_admin_init_callback(callback: CallbackQuery, state: FSMContext):
    await callback_add_admin_init(callback, state)


@sys_router.callback_query(F.data == "add_owner_init")
async def add_owner_init_callback(callback: CallbackQuery, state: FSMContext):
    await callback_add_owner_init(callback, state)


@sys_router.callback_query(F.data == "remove_admin_init")
async def remove_admin_init_callback(callback: CallbackQuery, state: FSMContext):
    await callback_remove_admin_init(callback, state)


@sys_router.callback_query(F.data == "remove_owner_init")
async def remove_owner_init_callback(callback: CallbackQuery, state: FSMContext):
    await callback_remove_owner_init(callback, state)


# State handlers for the FSM
@sys_router.message(SysAuth.add_admin)
async def add_admin_handler(message: Message, state: FSMContext):
    await process_add_admin(message, state)


@sys_router.message(SysAuth.add_owner)
async def add_owner_handler(message: Message, state: FSMContext):
    await process_add_owner(message, state)


@sys_router.message(SysAuth.remove_admin)
async def remove_admin_handler(message: Message, state: FSMContext):
    await process_remove_admin(message, state)


@sys_router.message(SysAuth.remove_owner)
async def remove_owner_handler(message: Message, state: FSMContext):
    await process_remove_owner(message, state)


@sys_router.message(SysAuth.change_password)
async def change_password_handler(message: Message, state: FSMContext):
    await change_password_process(message, state)


@sys_router.message(SysAuth.edit_config)
async def edit_config_handler(message: Message, state: FSMContext):
    await process_edit_config(message, state)


# Apply decorator to key functions that were flagged as unused
@mark_as_used
async def manage_admins(callback: CallbackQuery):
    """Display admin management options."""
    user_id = callback.from_user.id

    if not is_owner(user_id):
        await callback.answer("Only owners can manage admins.", show_alert=True)
        return

    keyboard = [
        [
            InlineKeyboardButton(text="Add Admin", callback_data="add_admin_init"),
            InlineKeyboardButton(
                text="Remove Admin", callback_data="remove_admin_init"
            ),
        ],
        [InlineKeyboardButton(text="◀️ Back", callback_data="back_to_admin_panel")],
    ]

    await callback.message.edit_text(
        "Admin Management\n\nChoose an option:",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )
    await callback.answer()


@mark_as_used
async def manage_owners(callback: CallbackQuery):
    """Display owner management options."""
    user_id = callback.from_user.id

    if not is_owner(user_id):
        await callback.answer("Only owners can manage other owners.", show_alert=True)
        return

    keyboard = [
        [
            InlineKeyboardButton(text="Add Owner", callback_data="add_owner_init"),
            InlineKeyboardButton(
                text="Remove Owner", callback_data="remove_owner_init"
            ),
        ],
        [InlineKeyboardButton(text="◀️ Back", callback_data="back_to_admin_panel")],
    ]

    await callback.message.edit_text(
        "Owner Management\n\nChoose an option:",
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
    )
    await callback.answer()


@mark_as_used
async def callback_add_admin_init(callback: CallbackQuery, state: FSMContext) -> None:
    """Initialize the add admin process."""
    user_id = callback.from_user.id

    if not is_owner(user_id):
        await callback.answer(
            "This feature is only available to owners.", show_alert=True
        )
        return

    await callback.message.answer(
        "Please forward a message from the user you want to add as admin, or send their user ID."
    )
    await state.set_state(SysAuth.add_admin)
    await callback.answer()


@mark_as_used
async def callback_add_owner_init(callback: CallbackQuery, state: FSMContext) -> None:
    """Initialize the add owner process."""
    user_id = callback.from_user.id

    if not is_owner(user_id):
        await callback.answer(
            "This feature is only available to owners.", show_alert=True
        )
        return

    await callback.message.answer(
        "Please forward a message from the user you want to add as owner, or send their user ID."
    )
    await state.set_state(SysAuth.add_owner)
    await callback.answer()


@mark_as_used
async def callback_remove_admin_init(
    callback: CallbackQuery, state: FSMContext
) -> None:
    """Initialize the remove admin process."""
    user_id = callback.from_user.id

    if not is_owner(user_id):
        await callback.answer(
            "This feature is only available to owners.", show_alert=True
        )
        return

    config = get_admin_config()

    if not config["admins"]:
        await callback.message.answer("There are no admins to remove.")
        await callback.answer()
        return

    admin_list = "\n".join(
        [
            f"{i+1}. {admin['username']} (ID: {admin['user_id']})"
            for i, admin in enumerate(config["admins"])
        ]
    )

    await callback.message.answer(
        f"Current admins:\n{admin_list}\n\nPlease send the number of the admin to remove:"
    )
    await state.set_state(SysAuth.remove_admin)
    await callback.answer()


@mark_as_used
async def callback_remove_owner_init(
    callback: CallbackQuery, state: FSMContext
) -> None:
    """Initialize the remove owner process."""
    user_id = callback.from_user.id

    if not is_owner(user_id):
        await callback.answer(
            "This feature is only available to owners.", show_alert=True
        )
        return

    config = get_admin_config()

    if len(config["owners"]) <= 1:
        await callback.message.answer(
            "Cannot remove the last owner for security reasons."
        )
        await callback.answer()
        return

    # Fetch owners from the config file
    owner_list = "\n".join(
        [
            f"{i+1}. Config Owner: -- {owner['username']} (ID: {owner['user_id']})"
            for i, owner in enumerate(config["owners"])
        ]
    )

    # Fetch MongoDB owners if available
    mongo_owners = []
    try:
        from database.operations import admins_collection

        mongo_owners_cursor = admins_collection.find({"role": "owner"})
        # Convert cursor to list to check if it contains any documents
        mongo_owners_list = list(mongo_owners_cursor)

        if mongo_owners_list:  # Only process if there are actually owners in MongoDB
            mongo_owners = [
                f"{len(config['owners']) + i + 1}. MongoDB Owner: -- {owner.get('username', 'Unknown')} (ID: {owner.get('user_id', 'Unknown')})"
                for i, owner in enumerate(mongo_owners_list)
            ]

            # Only add MongoDB section if there are MongoDB owners
            if mongo_owners:
                owner_list += "\n\nMongoDB Owners:\n" + "\n".join(mongo_owners)
    except Exception as e:
        sys_logger.error(f"Error fetching MongoDB owners: {e}")

    await callback.message.answer(
        f"Current owners:\n{owner_list}\n\nPlease send the number of the owner to remove:"
    )
    await state.set_state(SysAuth.remove_owner)
    await callback.answer()


@mark_as_used
async def process_add_admin(message: Message, state: FSMContext) -> None:
    """Process the add admin request."""
    adder_id = message.from_user.id
    adder_username = message.from_user.username or f"user_{adder_id}"

    if not is_owner(adder_id):
        await clear_state_data(state)
        return

    config = get_admin_config()

    # Get target user ID either from forwarded message or direct input
    if message.forward_from:
        target_id = message.forward_from.id
        target_username = message.forward_from.username or f"user_{target_id}"
    else:
        try:
            target_id = int(message.text)
            target_username = f"user_{target_id}"
        except ValueError:
            await message.answer(
                "Invalid input. Please forward a message or provide a valid user ID."
            )
            return

    # Check if user is already an admin or owner
    if any(admin["user_id"] == target_id for admin in config["admins"]):
        await message.answer("This user is already an admin.")
    elif any(owner["user_id"] == target_id for owner in config["owners"]):
        await message.answer("This user is already an owner.")
    else:
        # Add the new admin
        config["admins"].append(
            {
                "user_id": target_id,
                "username": target_username,
                "added_by": adder_username,
                "added_date": datetime.now().isoformat(),
            }
        )
        save_admin_config(config)

        log_sys_action(
            adder_id,
            adder_username,
            f"Added {target_username} (ID: {target_id}) as admin",
        )
        await message.answer(
            f"User {target_username} (ID: {target_id}) has been added as an admin."
        )

    await clear_state_data(state)


@mark_as_used
async def process_add_owner(message: Message, state: FSMContext) -> None:
    """Process the add owner request."""
    adder_id = message.from_user.id
    adder_username = message.from_user.username or f"user_{adder_id}"

    if not is_owner(adder_id):
        await clear_state_data(state)
        return

    config = get_admin_config()

    # Get target user ID either from forwarded message or direct input
    if message.forward_from:
        target_id = message.forward_from.id
        target_username = message.forward_from.username or f"user_{target_id}"
    else:
        try:
            target_id = int(message.text)
            target_username = f"user_{target_id}"
        except ValueError:
            await message.answer(
                "Invalid input. Please forward a message or provide a valid user ID."
            )
            return

    # Check if user is already an owner
    if any(owner["user_id"] == target_id for owner in config["owners"]):
        await message.answer("This user is already an owner.")
    else:
        # Remove from admins if they were an admin
        config["admins"] = [
            admin for admin in config["admins"] if admin["user_id"] != target_id
        ]

        # Add the new owner
        config["owners"].append(
            {
                "user_id": target_id,
                "username": target_username,
                "added_by": adder_username,
                "added_date": datetime.now().isoformat(),
            }
        )
        save_admin_config(config)

        log_sys_action(
            adder_id,
            adder_username,
            f"Added {target_username} (ID: {target_id}) as owner",
        )
        await message.answer(
            f"User {target_username} (ID: {target_id}) has been added as an owner."
        )

    await clear_state_data(state)


@mark_as_used
async def process_remove_admin(message: Message, state: FSMContext) -> None:
    """Process the remove admin request."""
    remover_id = message.from_user.id
    remover_username = message.from_user.username or f"user_{remover_id}"

    if not is_owner(remover_id):
        await clear_state_data(state)
        return

    config = get_admin_config()

    try:
        index = int(message.text) - 1
        if index < 0 or index >= len(config["admins"]):
            await message.answer("Invalid selection. Please try again.")
            return

        removed_admin = config["admins"].pop(index)
        save_admin_config(config)

        log_sys_action(
            remover_id,
            remover_username,
            f"Removed {removed_admin['username']} (ID: {removed_admin['user_id']}) from admins",
        )

        await message.answer(
            f"Admin {removed_admin['username']} (ID: {removed_admin['user_id']}) has been removed."
        )
    except ValueError:
        await message.answer("Invalid input. Please enter a number.")

    await clear_state_data(state)


@mark_as_used
async def process_remove_owner(message: Message, state: FSMContext) -> None:
    """Process the remove owner request."""
    remover_id = message.from_user.id
    remover_username = message.from_user.username or f"user_{remover_id}"

    if not is_owner(remover_id):
        await clear_state_data(state)
        return

    config = get_admin_config()

    if len(config["owners"]) <= 1:
        await message.answer("Cannot remove the last owner for security reasons.")
        await clear_state_data(state)
        return

    try:
        index = int(message.text) - 1

        # Fetch MongoDB owners if available
        mongo_owners = []
        try:
            from database.operations import admins_collection

            mongo_owners_cursor = admins_collection.find({"role": "owner"})
            mongo_owners = list(mongo_owners_cursor)
        except Exception as e:
            sys_logger.error(f"Error fetching MongoDB owners: {e}")

        # Determine if the index refers to a config owner or a MongoDB owner
        if index < len(config["owners"]):
            # Config owner removal logic
            if config["owners"][index]["user_id"] == remover_id:
                await message.answer("You cannot remove yourself as an owner.")
                await clear_state_data(state)
                return

            removed_owner = config["owners"][index]
            config["owners"].pop(index)  # Remove from config
            save_admin_config(config)  # Save the changes to config

            log_sys_action(
                remover_id,
                remover_username,
                f"Removed config owner: {removed_owner['username']} (ID: {removed_owner['user_id']})",
            )

            await message.answer(
                f"Successfully removed owner from config: {removed_owner['username']} (ID: {removed_owner['user_id']})"
            )

        elif index < (len(config["owners"]) + len(mongo_owners)):
            # MongoDB owner removal logic
            mongo_index = index - len(config["owners"])
            removed_owner = mongo_owners[mongo_index]

            # Remove from MongoDB
            try:
                from database.operations import admins_collection

                owner_id = removed_owner.get("user_id")
                result = admins_collection.delete_one(
                    {"user_id": owner_id, "role": "owner"}
                )

                if result.deleted_count > 0:
                    log_sys_action(
                        remover_id,
                        remover_username,
                        f"Removed MongoDB owner: {removed_owner.get('username', 'Unknown')} (ID: {owner_id})",
                    )

                    await message.answer(
                        f"Successfully removed owner from MongoDB: {removed_owner.get('username', 'Unknown')} (ID: {owner_id})"
                    )
                else:
                    await message.answer(
                        "Failed to remove owner from MongoDB: No matching document found."
                    )
            except Exception as e:
                sys_logger.error(f"MongoDB removal error: {str(e)}")
                await message.answer(f"Error removing owner from MongoDB: {str(e)}")
        else:
            await message.answer("Invalid selection. Please enter a valid number.")
            return

    except ValueError:
        await message.answer("Invalid input. Please enter a number.")
        await clear_state_data(state)
    except Exception as e:
        sys_logger.error(f"An unexpected error occurred: {e}")
        await message.answer(
            "An error occurred while processing your request. Please try again."
        )
        await clear_state_data(state)

    await clear_state_data(state)


async def confirm_owner_mongo_removal(
    callback: CallbackQuery, state: FSMContext
) -> None:
    """Handle MongoDB removal confirmation for owner."""
    user_id = callback.from_user.id

    if not is_owner(user_id):
        await callback.answer(
            "This feature is only available to owners.", show_alert=True
        )
        await clear_state_data(state)
        return

    # Get stored data
    data = await state.get_data()
    removed_owner = data.get("removed_owner")
    owner_index = data.get("owner_index")

    if not removed_owner or owner_index is None:
        await callback.message.edit_text("Error: Owner data not found.")
        await clear_state_data(state)
        return

    # Get config again to ensure it's current
    config = get_admin_config()

    # Remove from config file
    if owner_index < len(config["owners"]):
        config["owners"].pop(owner_index)
        save_admin_config(config)

    # Check if we should also remove from MongoDB
    remove_from_mongo = callback.data == "remove_mongo_yes"

    if remove_from_mongo:
        try:
            # Import necessary MongoDB functions
            from database.operations import admins_collection

            # Remove from MongoDB if requested
            owner_id = removed_owner["user_id"]
            result = admins_collection.delete_one({"user_id": owner_id})

            mongo_msg = f"\nRemoved from MongoDB: {result.deleted_count} document(s)."
            log_sys_action(
                user_id,
                callback.from_user.username or f"user_{user_id}",
                f"Removed {removed_owner['username']} (ID: {owner_id}) from owners and MongoDB",
            )
        except Exception as e:
            mongo_msg = f"\nFailed to remove from MongoDB: {str(e)}"
            sys_logger.error(f"MongoDB removal error: {str(e)}")
    else:
        mongo_msg = "\nNot removed from MongoDB."
        log_sys_action(
            user_id,
            callback.from_user.username or f"user_{user_id}",
            f"Removed {removed_owner['username']} (ID: {removed_owner['user_id']}) from owners config only",
        )

    await callback.message.edit_text(
        f"Owner {removed_owner['username']} (ID: {removed_owner['user_id']}) has been removed from config.{mongo_msg}"
    )

    await clear_state_data(state)
    await callback.answer()


async def change_password_init(callback: CallbackQuery, state: FSMContext):
    """Initialize the change password process."""
    user_id = callback.from_user.id

    if not is_owner(user_id):
        await callback.answer(
            "Only owners can change the system password.", show_alert=True
        )
        return

    await callback.message.edit_text(
        "Please enter the new system password:",
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="Cancel", callback_data="back_to_admin_panel"
                    )
                ]
            ]
        ),
    )
    await state.set_state(SysAuth.change_password)
    await callback.answer()


async def change_password_process(message: Message, state: FSMContext):
    """Process the password change request."""
    user_id = message.from_user.id
    username = message.from_user.username or f"user_{user_id}"

    if not is_owner(user_id):
        await message.answer("Only owners can change the system password.")
        await clear_state_data(state)
        return

    # Delete the message with the new password for security
    try:
        await message.delete()
    except Exception as e:
        sys_logger.error(f"Could not delete password message: {e}")

    if not message.text or len(message.text.strip()) < 4:
        await message.answer(
            "Password must be at least 4 characters long. Please try again.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="Cancel", callback_data="back_to_admin_panel"
                        )
                    ]
                ]
            ),
        )
        return

    new_password = message.text.strip()
    config = get_admin_config()
    config["password_hash"] = hash_password(new_password)
    save_admin_config(config)

    log_sys_action(user_id, username, "Changed system password")

    await message.answer(
        "System password has been changed successfully.",
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="Back to Admin Panel", callback_data="back_to_admin_panel"
                    )
                ]
            ]
        ),
    )
    await clear_state_data(state)


async def callback_back_to_panel(callback: CallbackQuery) -> None:
    """Handle the back to panel callback."""
    # Add logging to check if this function is getting called
    sys_logger.info(f"back_to_panel callback triggered by user {callback.from_user.id}")

    # Using a more direct approach instead of calling admin_panel function
    user_id = callback.from_user.id
    is_user_owner = is_owner(user_id)

    # Create keyboard with available commands based on role
    keyboard = []

    # Common buttons for both roles
    keyboard.append(
        [InlineKeyboardButton(text="List All Admins", callback_data="list_admins")]
    )

    # Owner-only buttons
    if is_user_owner:
        keyboard.append(
            [
                InlineKeyboardButton(
                    text="Manage Admins", callback_data="manage_s_admins"
                ),
                InlineKeyboardButton(
                    text="Manage Owners", callback_data="manage_owners"
                ),
            ]
        )
        keyboard.append(
            [
                InlineKeyboardButton(
                    text="View/Edit Config", callback_data="view_config"
                ),
                InlineKeyboardButton(
                    text="🔐 Change Password", callback_data="change_password_init"
                ),
            ]
        )
        # Add restart bot button for owners only
        keyboard.append(
            [InlineKeyboardButton(text="🔄 Restart Bot", callback_data="restart_bot")]
        )

    reply_markup = InlineKeyboardMarkup(inline_keyboard=keyboard)

    await callback.message.edit_text(
        f"Welcome back to the Admin Panel!\n"
        f"Your role: {('Owner' if is_user_owner else 'Admin')}\n\n"
        f"Select an option below:",
        reply_markup=reply_markup,
    )
    await callback.answer()


async def view_config(callback: CallbackQuery, state: FSMContext) -> None:
    """Handle the view config callback."""
    user_id = callback.from_user.id

    if not is_owner(user_id):
        await callback.answer(
            "This feature is only available to owners.", show_alert=True
        )
        return

    try:
        # Attempt to read the config.py file
        config_path = "config.py"
        with open(config_path, "r") as f:
            config_content = f.read()

        # Split content into chunks if it's too long for a single message
        max_length = 4000  # Telegram has a limit of ~4096 characters
        chunks = [
            config_content[i : i + max_length]
            for i in range(0, len(config_content), max_length)
        ]

        # Send the first chunk with an edit button
        keyboard = [
            [InlineKeyboardButton(text="✏️ Edit Config", callback_data="edit_config")],
            [
                InlineKeyboardButton(
                    text="Back to Admin Panel", callback_data="back_to_panel"
                )
            ],
        ]

        reply_markup = InlineKeyboardMarkup(inline_keyboard=keyboard)

        await callback.message.edit_text(
            f"Config.py file contents (Part 1/{len(chunks)}):\n```python\n{chunks[0]}\n```",
            reply_markup=reply_markup,
            parse_mode="Markdown",
        )

        # Send additional chunks if necessary
        for i, chunk in enumerate(chunks[1:], 2):
            await callback.message.answer(
                f"Config.py file contents (Part {i}/{len(chunks)}):\n```python\n{chunk}\n```",
                parse_mode="Markdown",
            )

    except Exception as e:
        await callback.message.edit_text(
            f"Error reading config file: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="Back to Admin Panel", callback_data="back_to_panel"
                        )
                    ]
                ]
            ),
        )

    await callback.answer()


async def edit_config_init(callback: CallbackQuery, state: FSMContext) -> None:
    """Initialize the edit config process."""
    user_id = callback.from_user.id

    if not is_owner(user_id):
        await callback.answer(
            "This feature is only available to owners.", show_alert=True
        )
        return

    await callback.message.edit_text(
        "Please send the modified config content as a text message. "
        "This will replace the entire config.py file.",
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [InlineKeyboardButton(text="Cancel", callback_data="back_to_panel")]
            ]
        ),
    )
    await state.set_state(SysAuth.edit_config)
    await callback.answer()


async def process_edit_config(message: Message, state: FSMContext) -> None:
    """Process the edit config request."""
    user_id = message.from_user.id
    username = message.from_user.username or f"user_{user_id}"

    if not is_owner(user_id):
        await clear_state_data(state)
        return

    if not message.text:
        await message.answer(
            "Please send the config content as text.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="Cancel", callback_data="back_to_panel")]
                ]
            ),
        )
        return

    config_content = message.text

    try:
        # Create a backup of the current config
        backup_filename = f"config_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.py"
        backup_path = os.path.join(SECURE_DATA_PATH, backup_filename)

        if not os.path.exists(SECURE_DATA_PATH):
            os.makedirs(SECURE_DATA_PATH)

        with open("config.py", "r") as original:
            with open(backup_path, "w") as backup:
                backup.write(original.read())

        # Write the new config
        with open("config.py", "w") as f:
            f.write(config_content)

        log_sys_action(user_id, username, "Edited config.py file")
        await message.answer(
            f"Config file has been updated successfully. A backup was saved to {backup_path}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="Back to Admin Panel", callback_data="back_to_panel"
                        )
                    ]
                ]
            ),
        )
    except Exception as e:
        await message.answer(
            f"Error updating config file: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="Back to Admin Panel", callback_data="back_to_panel"
                        )
                    ]
                ]
            ),
        )

    await clear_state_data(state)


@mark_as_used
@sys_router.callback_query(F.data == "restart_bot")
async def restart_bot(callback_query: CallbackQuery, bot: Bot) -> None:
    """Handler for restarting the bot service."""
    user_id = callback_query.from_user.id

    # Only owners can restart the bot
    if not is_owner(user_id):
        await callback_query.answer(
            "Access denied: Owner privileges required", show_alert=True
        )
        return

    await callback_query.answer("Initiating bot restart...", show_alert=True)

    # Log the restart action
    username = callback_query.from_user.username or f"user_{user_id}"
    log_sys_action(user_id, username, "Initiated bot restart")

    # Inform the user
    await callback_query.message.edit_text(
        "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n"
        "🔄 <b>BOT RESTART INITIATED</b> 🔄\n"
        "✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦✧✦\n\n"
        "✨ <i>The bot service is now restarting...</i>\n\n"
        "Please wait a few moments and then use /start to verify the restart was successful.",
        parse_mode="HTML",
    )

    sys_logger.info(f"Bot restart initiated by user {user_id}")

    # Delay briefly to ensure the message is sent before restart
    await asyncio.sleep(2)

    try:
        # Get the current script path
        script_path = os.path.abspath(sys.argv[0])

        # Start a new process to run the script
        if sys.platform.startswith("win"):
            # Windows restart method
            subprocess.Popen(f'cmd /c "python {script_path}"', shell=True)
        else:
            # Unix-like restart method
            subprocess.Popen(f"python3 {script_path} &", shell=True)

        # Exit the current process
        sys_logger.info("Exiting current process for restart")
        await bot.session.close()  # Close the bot session gracefully
        sys.exit(0)
    except Exception as e:
        sys_logger.error(f"Error during restart: {e}")
        # If restart fails, notify the user
        try:
            await callback_query.message.edit_text(
                "❌ <b>RESTART FAILED</b>\n\n"
                f"Error: {str(e)}\n\n"
                "Please try again or restart manually.",
                parse_mode="HTML",
            )
        except Exception:
            pass


def register_sys_handlers(dp: Dispatcher) -> None:
    """Register all system admin handlers to the dispatcher."""
    # Register all potentially unused functions
    register_usage(
        manage_admins,
        manage_owners,
        callback_add_admin_init,
        callback_add_owner_init,
        callback_remove_admin_init,
        callback_remove_owner_init,
        process_add_admin,
        process_add_owner,
        process_remove_admin,
        process_remove_owner,
        # Add more functions that were flagged as unused
        secret_command,
        verify_password,
        admin_panel,  # Explicitly register the admin_panel command
        restart_bot,  # Register the new restart bot handler
    )

    # In newer versions of aiogram, we can't directly access filters
    sys_logger.info("Registering sys_log with system router")

    # Use a safe include method to prevent duplicate router registration
    try:
        from utils.router_tools import safe_include_router

        safe_include_router(dp, sys_router)
        sys_logger.info("System handlers registered successfully")
    except Exception as e:
        sys_logger.error(f"Error registering system handlers: {e}")


# --- Database Export Handlers ---

@sys_router.callback_query(F.data == "database_export_menu")
async def database_export_menu(callback: CallbackQuery):
    """Display database export menu for owners."""
    user_id = callback.from_user.id
    username = callback.from_user.username or f"user_{user_id}"

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied - Owner privileges required", show_alert=True)
        return

    try:
        from utils.database_export import get_export_formats, get_database_collections, database_exporter
        from utils.template_helpers import format_text

        # Get export statistics
        stats = database_exporter.get_export_statistics()
        collections = get_database_collections()

        # Use template for consistent formatting
        message_text = format_text(
            "owner",
            "database_export_welcome",
            default="💾 <b>• DATABASE EXPORT CENTER •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>📊 SECURE DATA EXPORT SYSTEM</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Professional database export management system.</i>"
        )

        # Add current statistics to the message
        stats_section = (
            f"\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>📊 CURRENT STATISTICS</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"📈 <b>Total Exports:</b> {stats['total_exports']}\n"
            f"✅ <b>Successful:</b> {stats['successful_exports']}\n"
            f"❌ <b>Failed:</b> {stats['failed_exports']}\n"
            f"🔄 <b>Active:</b> {stats['active_exports']}\n"
            f"📊 <b>Success Rate:</b> {stats['success_rate']:.1f}%\n"
            f"💽 <b>Total Data:</b> {stats['total_exported_size_mb']:.2f} MB\n"
            f"🗃️ <b>Collections:</b> {len(collections)}\n\n"
            f"<i>Select an option below to manage database exports:</i>"
        )

        message_text += stats_section

        # Create keyboard using template system for consistency
        from utils.button_layout import should_use_single_row

        inline_keyboard = []

        # Define buttons with template texts
        buttons = [
            (format_text("buttons", "db_export_create"), "db_export_create"),
            (format_text("buttons", "db_export_history"), "db_export_history"),
            (format_text("buttons", "db_export_cleanup"), "db_export_cleanup"),
            (format_text("buttons", "db_export_statistics"), "db_export_stats"),
            (format_text("buttons", "db_export_refresh"), "database_export_menu"),
            (format_text("buttons", "db_export_back_advanced"), "additional_features")
        ]

        # Create main action button (full width)
        inline_keyboard.append([
            InlineKeyboardButton(text=buttons[0][0], callback_data=buttons[0][1])
        ])

        # Create paired buttons for secondary actions
        row_buttons = []
        for button_text, callback_data in buttons[1:5]:  # History, Cleanup, Stats, Refresh
            button = InlineKeyboardButton(text=button_text, callback_data=callback_data)

            if should_use_single_row(button_text):
                # If we have pending buttons, add them first
                if row_buttons:
                    inline_keyboard.append(row_buttons)
                    row_buttons = []
                # Add this button on its own row
                inline_keyboard.append([button])
            else:
                row_buttons.append(button)
                # Add row when we have 2 buttons
                if len(row_buttons) == 2:
                    inline_keyboard.append(row_buttons)
                    row_buttons = []

        # Add any remaining buttons
        if row_buttons:
            inline_keyboard.append(row_buttons)

        # Add back button (full width)
        inline_keyboard.append([
            InlineKeyboardButton(text=buttons[5][0], callback_data=buttons[5][1])
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=inline_keyboard)

        await safe_edit_message(
            callback.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        sys_logger.error(f"Error in database export menu: {e}")
        await callback.answer("❌ Error loading export menu", show_alert=True)

    await callback.answer()


@sys_router.callback_query(F.data == "db_export_create")
async def database_export_create(callback: CallbackQuery):
    """Display export creation options."""
    user_id = callback.from_user.id

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied", show_alert=True)
        return

    try:
        from utils.database_export import get_export_formats
        from utils.template_helpers import format_text

        formats = get_export_formats()

        # Use template for consistent formatting
        message_text = format_text(
            "owner",
            "database_export_formats",
            default="📦 <b>• EXPORT FORMAT SELECTION •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>🎯 CHOOSE YOUR EXPORT FORMAT</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Select the most appropriate format for your needs:</i>"
        )

        # Create keyboard using consistent button layout
        from utils.button_layout import should_use_single_row

        inline_keyboard = []

        # Add format selection buttons with proper emoji mapping
        format_emojis = {
            'bson_archive': '🗃️',
            'bson_compressed': '🗜️',
            'json': '📄',
            'json_compressed': '📦'
        }

        for format_key, format_info in formats.items():
            emoji = format_emojis.get(format_key, '📦')
            button_text = f"{emoji} {format_info['description']}"

            inline_keyboard.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"db_export_format:{format_key}"
                )
            ])

        # Add advanced options and back button
        inline_keyboard.extend([
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "db_export_advanced"),
                    callback_data="db_export_advanced"
                )
            ],
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "db_export_back_menu"),
                    callback_data="database_export_menu"
                )
            ]
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=inline_keyboard)

        await safe_edit_message(
            callback.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        sys_logger.error(f"Error in export creation menu: {e}")
        await callback.answer("❌ Error loading export options", show_alert=True)

    await callback.answer()


@sys_router.callback_query(F.data.startswith("db_export_format:"))
async def database_export_format_selected(callback: CallbackQuery):
    """Handle export format selection and start export process."""
    user_id = callback.from_user.id
    username = callback.from_user.username or f"user_{user_id}"

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied", show_alert=True)
        return

    try:
        format_key = callback.data.split(":")[1]

        from utils.database_export import create_database_export, get_export_formats

        formats = get_export_formats()
        if format_key not in formats:
            await callback.answer("❌ Invalid export format", show_alert=True)
            return

        format_info = formats[format_key]

        # Show confirmation and start export
        await callback.answer("🚀 Starting database export...", show_alert=True)

        # Update message to show export in progress using template
        from utils.template_helpers import format_text

        progress_text = format_text(
            "owner",
            "database_export_progress",
            export_format=format_info['description'],
            collection_count="All Collections",
            start_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            username=username,
            current_status="Processing database export...",
            progress_percentage=0,
            progress_bar="▰▱▱▱▱▱▱▱▱▱",
            estimated_time="Calculating...",
            default="⏳ <b>• EXPORT IN PROGRESS •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>🔄 PROCESSING YOUR DATABASE EXPORT</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Your database export is being processed...</i>"
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=format_text("buttons", "db_export_back_menu"),
                    callback_data="database_export_menu"
                )
            ]
        ])

        await safe_edit_message(
            callback.message,
            progress_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

        # Start the export process asynchronously
        asyncio.create_task(
            _perform_database_export(callback, user_id, username, format_key)
        )

    except Exception as e:
        sys_logger.error(f"Error starting database export: {e}")
        await callback.answer("❌ Error starting export", show_alert=True)


async def _perform_database_export(callback: CallbackQuery, user_id: int, username: str, format_key: str):
    """Perform the actual database export and update the user."""
    try:
        from utils.database_export import create_database_export, get_export_formats

        # Perform the export
        export_info = await create_database_export(user_id, username, format_key)

        formats = get_export_formats()
        format_info = formats[format_key]

        if export_info["status"] == "completed":
            # Export successful - use template for consistent formatting
            from utils.template_helpers import format_text

            file_size_mb = export_info["file_size"] / (1024 * 1024)

            success_text = format_text(
                "owner",
                "database_export_success",
                filename=export_info['filename'],
                export_format=format_info['description'],
                file_size_mb=f"{file_size_mb:.2f}",
                collection_count="All Collections",
                start_time=export_info.get('started_at', datetime.now()).strftime('%Y-%m-%d %H:%M:%S'),
                completion_time=export_info['completed_at'].strftime('%Y-%m-%d %H:%M:%S'),
                duration="Completed",
                default="✅ <b>• EXPORT COMPLETED SUCCESSFULLY •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>📥 YOUR DATABASE EXPORT IS READY</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Export completed successfully!</i>"
            )

            # Create keyboard with template buttons
            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text=format_text("buttons", "db_export_download"),
                        callback_data=f"db_export_download:{export_info['export_id']}"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text=format_text("buttons", "db_export_history"),
                        callback_data="db_export_history"
                    ),
                    InlineKeyboardButton(
                        text=format_text("buttons", "db_export_back_menu"),
                        callback_data="database_export_menu"
                    )
                ]
            ])

        else:
            # Export failed - use template for consistent error formatting
            error_text = format_text(
                "owner",
                "database_export_error",
                export_format=format_info['description'],
                error_message=export_info.get('error', 'Unknown error occurred'),
                failed_time=export_info.get('completed_at', datetime.now()).strftime('%Y-%m-%d %H:%M:%S'),
                default="❌ <b>• EXPORT FAILED •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>⚠️ EXPORT PROCESS FAILED</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>The export process encountered an error.</i>"
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text=format_text("buttons", "db_export_try_again"),
                        callback_data="db_export_create"
                    ),
                    InlineKeyboardButton(
                        text=format_text("buttons", "db_export_back_menu"),
                        callback_data="database_export_menu"
                    )
                ]
            ])

            success_text = error_text

        # Update the message with results
        await safe_edit_message(
            callback.message,
            success_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        sys_logger.error(f"Error in database export process: {e}")

        # Show error message
        error_text = (
            "❌ <b>DATABASE EXPORT ERROR</b>\n\n"
            f"An unexpected error occurred during the export process:\n\n"
            f"<code>{str(e)}</code>\n\n"
            "<i>Please try again or contact support.</i>"
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔄 Try Again",
                    callback_data="db_export_create"
                ),
                InlineKeyboardButton(
                    text="🔙 Export Menu",
                    callback_data="database_export_menu"
                )
            ]
        ])

        try:
            await safe_edit_message(
                callback.message,
                error_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
        except Exception as edit_error:
            sys_logger.error(f"Error updating message after export failure: {edit_error}")


@sys_router.callback_query(F.data.startswith("db_export_download:"))
async def database_export_download(callback: CallbackQuery):
    """Handle database export file download."""
    user_id = callback.from_user.id

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied", show_alert=True)
        return

    try:
        export_id = callback.data.split(":")[1]

        from utils.database_export import database_exporter

        export_info = database_exporter.get_export_status(export_id)

        if not export_info:
            await callback.answer("❌ Export not found", show_alert=True)
            return

        if export_info["status"] != "completed":
            await callback.answer("❌ Export not completed", show_alert=True)
            return

        if export_info["user_id"] != user_id:
            await callback.answer("🚫 Access Denied - Not your export", show_alert=True)
            return

        # Check if file still exists and hasn't expired
        file_path = Path(export_info["file_path"])
        if not file_path.exists():
            await callback.answer("❌ Export file no longer available", show_alert=True)
            return

        # Check expiration
        if datetime.now() > export_info.get("download_expires_at", datetime.now()):
            await callback.answer("❌ Export file has expired", show_alert=True)
            return

        # Use secure download system
        from utils.template_helpers import format_text
        await callback.answer(
            format_text("owner", "database_export_download_preparing", default="📥 Preparing secure download..."),
            show_alert=False
        )

        try:
            from utils.secure_download import process_secure_download, create_download_token

            # Create secure download token
            token_result = create_download_token(
                user_id=user_id,
                username=callback.from_user.username or f"user_{user_id}",
                export_id=export_id,
                file_path=export_info["file_path"],
                filename=export_info["filename"],
                file_size=export_info["file_size"]
            )

            if not token_result["success"]:
                error_message = format_text(
                    "owner",
                    "database_export_download_token_error",
                    error_details=token_result['error'],
                    default="❌ <b>• DOWNLOAD TOKEN ERROR •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>🔒 SECURE TOKEN CREATION FAILED</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>Failed to create secure download token.</i>"
                )
                await callback.message.answer(error_message, parse_mode="HTML")
                return

            # Process secure download
            download_result = await process_secure_download(
                token_result["token_id"],
                user_id,
                callback.message
            )

            if download_result["success"]:
                # Log successful download
                from utils.monitoring_system import record_security_event
                record_security_event("database_export_downloaded", {
                    "user_id": user_id,
                    "export_id": export_id,
                    "filename": export_info["filename"],
                    "file_size": export_info["file_size"],
                    "token_id": token_result["token_id"]
                })

                sys_logger.info(f"Database export {export_id} securely downloaded by user {user_id}")
            else:
                error_message = format_text(
                    "owner",
                    "database_export_download_failed",
                    error_details=download_result['error'],
                    default="❌ <b>• DOWNLOAD FAILED •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>📥 SECURE DOWNLOAD ERROR</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>The secure download process failed.</i>"
                )
                await callback.message.answer(error_message, parse_mode="HTML")

        except Exception as send_error:
            sys_logger.error(f"Error in secure download: {send_error}")
            error_message = format_text(
                "owner",
                "database_export_download_system_error",
                default="❌ <b>• SYSTEM ERROR •</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>⚠️ DOWNLOAD SYSTEM ERROR</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n\n<i>A system error occurred during download processing.</i>"
            )
            await callback.message.answer(error_message, parse_mode="HTML")

    except Exception as e:
        sys_logger.error(f"Error in database export download: {e}")
        await callback.answer("❌ Error processing download", show_alert=True)


@sys_router.callback_query(F.data == "db_export_history")
async def database_export_history(callback: CallbackQuery):
    """Display database export history."""
    user_id = callback.from_user.id

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied", show_alert=True)
        return

    try:
        from utils.database_export import database_exporter

        user_exports = database_exporter.get_user_exports(user_id, limit=10)

        message_text = (
            "📋 <b>DATABASE EXPORT HISTORY</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>📊 YOUR RECENT EXPORTS</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
        )

        if not user_exports:
            message_text += "<i>No export history found.</i>\n\n"
        else:
            for i, export in enumerate(user_exports[:5], 1):  # Show last 5
                status_emoji = {
                    "completed": "✅",
                    "failed": "❌",
                    "in_progress": "⏳"
                }.get(export["status"], "❓")

                size_text = ""
                if export.get("file_size"):
                    size_text = f" ({export['file_size'] / (1024*1024):.1f} MB)"

                message_text += (
                    f"{i}. {status_emoji} <b>{export['filename']}</b>{size_text}\n"
                    f"   📅 {safe_format_datetime(export['started_at'], '%Y-%m-%d %H:%M')}\n"
                    f"   📁 {export['format']}\n"
                )

                if export["status"] == "failed" and export.get("error"):
                    message_text += f"   ❌ {export['error'][:50]}...\n"

                message_text += "\n"

        message_text += (
            "━━━━━━━━━━━━━━━━━━\n"
            f"<i>Showing last {min(len(user_exports), 5)} exports</i>"
        )

        keyboard_rows = []

        # Add download buttons for completed exports
        completed_exports = [e for e in user_exports[:3] if e["status"] == "completed"]
        for export in completed_exports:
            # Check if file still exists and hasn't expired
            file_path = Path(export["file_path"])
            if (file_path.exists() and
                datetime.now() <= export.get("download_expires_at", datetime.now())):
                keyboard_rows.append([
                    InlineKeyboardButton(
                        text=f"📥 Download {export['filename'][:20]}...",
                        callback_data=f"db_export_download:{export['export_id']}"
                    )
                ])

        # Navigation buttons
        keyboard_rows.extend([
            [
                InlineKeyboardButton(
                    text="🚀 Create New Export",
                    callback_data="db_export_create"
                ),
                InlineKeyboardButton(
                    text="🔄 Refresh",
                    callback_data="db_export_history"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Export Menu",
                    callback_data="database_export_menu"
                )
            ]
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        sys_logger.error(f"Error in database export history: {e}")
        await callback.answer("❌ Error loading export history", show_alert=True)

    await callback.answer()


@sys_router.callback_query(F.data == "db_export_cleanup")
async def database_export_cleanup(callback: CallbackQuery):
    """Clean up expired database export files."""
    user_id = callback.from_user.id

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied", show_alert=True)
        return

    try:
        from utils.database_export import cleanup_expired_exports

        await callback.answer("🧹 Cleaning up expired files...", show_alert=False)

        # Perform cleanup
        cleaned_count = cleanup_expired_exports()

        # Log the cleanup action
        from utils.monitoring_system import record_security_event
        record_security_event("database_export_cleanup", {
            "user_id": user_id,
            "cleaned_files": cleaned_count
        })

        sys_logger.info(f"Database export cleanup performed by user {user_id}, cleaned {cleaned_count} files")

        message_text = (
            "🧹 <b>CLEANUP COMPLETED</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>📊 CLEANUP RESULTS</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            f"🗑️ <b>Files Cleaned:</b> {cleaned_count}\n"
            f"🕐 <b>Cleanup Time:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        )

        if cleaned_count > 0:
            message_text += (
                "✅ <b>Cleanup successful!</b>\n"
                f"Removed {cleaned_count} expired export files.\n\n"
            )
        else:
            message_text += (
                "ℹ️ <b>No cleanup needed</b>\n"
                "All export files are still within the retention period.\n\n"
            )

        message_text += "<i>Export files are automatically cleaned up after 24 hours.</i>"

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📊 View Statistics",
                    callback_data="db_export_stats"
                ),
                InlineKeyboardButton(
                    text="🔄 Refresh",
                    callback_data="database_export_menu"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Export Menu",
                    callback_data="database_export_menu"
                )
            ]
        ])

        await safe_edit_message(
            callback.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        sys_logger.error(f"Error in database export cleanup: {e}")
        await callback.answer("❌ Error during cleanup", show_alert=True)


@sys_router.callback_query(F.data == "db_export_stats")
async def database_export_statistics(callback: CallbackQuery):
    """Display detailed database export statistics."""
    user_id = callback.from_user.id

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied", show_alert=True)
        return

    try:
        from utils.database_export import database_exporter, get_database_collections

        stats = database_exporter.get_export_statistics()
        collections = get_database_collections()
        user_exports = database_exporter.get_user_exports(user_id)

        # Calculate user-specific stats
        user_successful = len([e for e in user_exports if e["status"] == "completed"])
        user_failed = len([e for e in user_exports if e["status"] == "failed"])
        user_total_size = sum(e.get("file_size", 0) for e in user_exports if e["status"] == "completed")

        message_text = (
            "📊 <b>DATABASE EXPORT STATISTICS</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>🌐 SYSTEM-WIDE STATISTICS</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            f"📈 <b>Total Exports:</b> {stats['total_exports']}\n"
            f"✅ <b>Successful:</b> {stats['successful_exports']}\n"
            f"❌ <b>Failed:</b> {stats['failed_exports']}\n"
            f"🔄 <b>Currently Active:</b> {stats['active_exports']}\n"
            f"📊 <b>Success Rate:</b> {stats['success_rate']:.1f}%\n"
            f"💽 <b>Total Data Exported:</b> {stats['total_exported_size_mb']:.2f} MB\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>👤 YOUR STATISTICS</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            f"📈 <b>Your Exports:</b> {len(user_exports)}\n"
            f"✅ <b>Successful:</b> {user_successful}\n"
            f"❌ <b>Failed:</b> {user_failed}\n"
            f"💽 <b>Data Exported:</b> {user_total_size / (1024*1024):.2f} MB\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>🗃️ DATABASE INFORMATION</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            f"📋 <b>Collections:</b> {len(collections)}\n"
            f"📝 <b>Database Name:</b> {os.getenv('MONGO_DB', 'telegram_shop_bot1')}\n"
            f"🔧 <b>Export Formats:</b> 4 available\n"
            f"⏰ <b>Retention Period:</b> 24 hours\n"
            f"📏 <b>Size Limit:</b> 2 GB per export\n"
            f"🚦 <b>Rate Limit:</b> 3 exports per hour\n\n"
            "<i>Statistics are updated in real-time.</i>"
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📋 Export History",
                    callback_data="db_export_history"
                ),
                InlineKeyboardButton(
                    text="🚀 New Export",
                    callback_data="db_export_create"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔄 Refresh Stats",
                    callback_data="db_export_stats"
                ),
                InlineKeyboardButton(
                    text="🔙 Export Menu",
                    callback_data="database_export_menu"
                )
            ]
        ])

        await safe_edit_message(
            callback.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        sys_logger.error(f"Error in database export statistics: {e}")
        await callback.answer("❌ Error loading statistics", show_alert=True)

    await callback.answer()


@sys_router.callback_query(F.data == "db_export_advanced")
async def database_export_advanced_options(callback: CallbackQuery):
    """Display advanced export configuration options."""
    user_id = callback.from_user.id

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied", show_alert=True)
        return

    try:
        from utils.database_export import get_database_collections

        collections = get_database_collections()

        message_text = (
            "⚙️ <b>ADVANCED EXPORT OPTIONS</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>🎛️ CONFIGURATION OPTIONS</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            "Configure your database export with advanced options:\n\n"
            "📋 <b>Collection Filtering</b>\n"
            "   • Select specific collections to export\n"
            "   • Reduce export size and time\n\n"
            "📅 <b>Date Range Filtering</b>\n"
            "   • Export data from specific time periods\n"
            "   • Filter by creation or modification dates\n\n"
            "🔧 <b>Export Customization</b>\n"
            "   • Choose format and compression\n"
            "   • Configure export parameters\n\n"
            f"📊 <b>Available Collections:</b> {len(collections)}\n\n"
            "<i>Select an advanced option below:</i>"
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📋 Select Collections",
                    callback_data="db_export_select_collections"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📅 Date Range Filter",
                    callback_data="db_export_date_filter"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🎯 Custom Export",
                    callback_data="db_export_custom"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📋 View Collections",
                    callback_data="db_export_view_collections"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Create Export",
                    callback_data="db_export_create"
                )
            ]
        ])

        await safe_edit_message(
            callback.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        sys_logger.error(f"Error in advanced export options: {e}")
        await callback.answer("❌ Error loading advanced options", show_alert=True)

    await callback.answer()


@sys_router.callback_query(F.data == "db_export_view_collections")
async def database_export_view_collections(callback: CallbackQuery):
    """Display all available database collections."""
    user_id = callback.from_user.id

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied", show_alert=True)
        return

    try:
        from utils.database_export import get_database_collections
        from database.connection import db

        collections = get_database_collections()

        message_text = (
            "📋 <b>DATABASE COLLECTIONS</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>📊 COLLECTION OVERVIEW</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
        )

        if not collections:
            message_text += "<i>No collections found in the database.</i>\n\n"
        else:
            # Get document counts for each collection
            collection_info = []
            for collection_name in collections:
                try:
                    count = db[collection_name].estimated_document_count()
                    collection_info.append((collection_name, count))
                except Exception:
                    collection_info.append((collection_name, "Unknown"))

            # Sort by document count (descending)
            collection_info.sort(key=lambda x: x[1] if isinstance(x[1], int) else 0, reverse=True)

            for i, (name, count) in enumerate(collection_info, 1):
                count_str = f"{count:,}" if isinstance(count, int) else str(count)
                message_text += f"{i:2d}. <b>{name}</b>\n"
                message_text += f"     📄 Documents: {count_str}\n\n"

        message_text += (
            "━━━━━━━━━━━━━━━━━━\n"
            f"<b>Total Collections:</b> {len(collections)}\n\n"
            "<i>Use 'Select Collections' to choose specific collections for export.</i>"
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📋 Select Collections",
                    callback_data="db_export_select_collections"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🚀 Export All Collections",
                    callback_data="db_export_create"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Advanced Options",
                    callback_data="db_export_advanced"
                )
            ]
        ])

        await safe_edit_message(
            callback.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        sys_logger.error(f"Error viewing collections: {e}")
        await callback.answer("❌ Error loading collections", show_alert=True)

    await callback.answer()


@sys_router.callback_query(F.data == "db_export_select_collections")
async def database_export_select_collections(callback: CallbackQuery):
    """Allow user to select specific collections for export."""
    user_id = callback.from_user.id

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied", show_alert=True)
        return

    try:
        from utils.database_export import get_database_collections

        collections = get_database_collections()

        message_text = (
            "📋 <b>SELECT COLLECTIONS TO EXPORT</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>🎯 COLLECTION SELECTION</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            "Select specific collections to include in your export:\n\n"
            "• <b>Selective Export:</b> Choose only the collections you need\n"
            "• <b>Faster Processing:</b> Smaller exports complete quicker\n"
            "• <b>Reduced Size:</b> Only export relevant data\n\n"
            f"📊 <b>Available Collections:</b> {len(collections)}\n\n"
            "<i>Choose collections to export:</i>"
        )

        keyboard_rows = []

        # Add collection selection buttons (show first 10 collections)
        main_collections = [
            "users", "products", "transactions", "orders", "payments",
            "admins", "categories", "settings", "announcements"
        ]

        # Show main collections first
        for collection in main_collections:
            if collection in collections:
                keyboard_rows.append([
                    InlineKeyboardButton(
                        text=f"📄 Export {collection.title()}",
                        callback_data=f"db_export_collection:{collection}"
                    )
                ])

        # Add "Other Collections" button if there are more
        other_collections = [c for c in collections if c not in main_collections]
        if other_collections:
            keyboard_rows.append([
                InlineKeyboardButton(
                    text=f"📋 Other Collections ({len(other_collections)})",
                    callback_data="db_export_other_collections"
                )
            ])

        # Add preset options
        keyboard_rows.extend([
            [
                InlineKeyboardButton(
                    text="👥 User Data Only",
                    callback_data="db_export_preset:user_data"
                ),
                InlineKeyboardButton(
                    text="🛒 Commerce Data",
                    callback_data="db_export_preset:commerce"
                )
            ],
            [
                InlineKeyboardButton(
                    text="⚙️ System Data",
                    callback_data="db_export_preset:system"
                ),
                InlineKeyboardButton(
                    text="📊 All Collections",
                    callback_data="db_export_preset:all"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Advanced Options",
                    callback_data="db_export_advanced"
                )
            ]
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        sys_logger.error(f"Error in collection selection: {e}")
        await callback.answer("❌ Error loading collection selection", show_alert=True)

    await callback.answer()


@sys_router.callback_query(F.data.startswith("db_export_preset:"))
async def database_export_preset_selected(callback: CallbackQuery):
    """Handle preset collection selection."""
    user_id = callback.from_user.id
    username = callback.from_user.username or f"user_{user_id}"

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied", show_alert=True)
        return

    try:
        preset = callback.data.split(":")[1]

        from utils.database_export import get_database_collections

        all_collections = get_database_collections()

        # Define preset collections
        preset_collections = {
            "user_data": ["users", "admins", "banned_users"],
            "commerce": ["products", "orders", "transactions", "payments", "carts", "categories"],
            "system": ["settings", "announcements", "support_messages"],
            "all": all_collections
        }

        selected_collections = [c for c in preset_collections.get(preset, []) if c in all_collections]

        if not selected_collections:
            await callback.answer("❌ No collections found for this preset", show_alert=True)
            return

        preset_names = {
            "user_data": "User Data",
            "commerce": "Commerce Data",
            "system": "System Data",
            "all": "All Collections"
        }

        message_text = (
            f"🎯 <b>PRESET SELECTED: {preset_names.get(preset, preset.upper())}</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>📋 SELECTED COLLECTIONS</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
        )

        for i, collection in enumerate(selected_collections, 1):
            message_text += f"{i:2d}. <b>{collection}</b>\n"

        message_text += (
            f"\n━━━━━━━━━━━━━━━━━━\n"
            f"<b>Total Collections:</b> {len(selected_collections)}\n\n"
            "<i>Choose export format to proceed:</i>"
        )

        from utils.database_export import get_export_formats
        formats = get_export_formats()

        keyboard_rows = []

        # Add format selection buttons with collection info
        for format_key, format_info in formats.items():
            keyboard_rows.append([
                InlineKeyboardButton(
                    text=f"📦 {format_info['description']}",
                    callback_data=f"db_export_custom_format:{format_key}:{','.join(selected_collections)}"
                )
            ])

        keyboard_rows.extend([
            [
                InlineKeyboardButton(
                    text="🔙 Back to Collection Selection",
                    callback_data="db_export_select_collections"
                )
            ]
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        sys_logger.error(f"Error in preset selection: {e}")
        await callback.answer("❌ Error processing preset selection", show_alert=True)

    await callback.answer()


@sys_router.callback_query(F.data.startswith("db_export_custom_format:"))
async def database_export_custom_format_selected(callback: CallbackQuery):
    """Handle custom export with selected collections and format."""
    user_id = callback.from_user.id
    username = callback.from_user.username or f"user_{user_id}"

    # Use the working privilege checking pattern from admin_diagnostics.py
    from database.operations import is_owner
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback.answer("🚫 Access Denied", show_alert=True)
        return

    try:
        parts = callback.data.split(":", 2)
        format_key = parts[1]
        collections_str = parts[2]
        # Parse collections string and filter out empty strings
        if collections_str and collections_str.strip():
            selected_collections = [col.strip() for col in collections_str.split(",") if col.strip()]
            selected_collections = selected_collections if selected_collections else None
        else:
            selected_collections = None

        from utils.database_export import create_database_export, get_export_formats

        formats = get_export_formats()
        if format_key not in formats:
            await callback.answer("❌ Invalid export format", show_alert=True)
            return

        format_info = formats[format_key]

        # Show confirmation and start export
        await callback.answer("🚀 Starting custom database export...", show_alert=True)

        # Update message to show export in progress
        progress_text = (
            "⏳ <b>CUSTOM DATABASE EXPORT IN PROGRESS</b>\n\n"
            "━━━━━━━━━━━━━━━━━━\n"
            "<b>📋 EXPORT DETAILS</b>\n"
            "━━━━━━━━━━━━━━━━━━\n\n"
            f"📁 <b>Format:</b> {format_info['description']}\n"
            f"👤 <b>Requested by:</b> {username} (ID: {user_id})\n"
            f"🕐 <b>Started:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
        )

        if selected_collections:
            progress_text += f"📋 <b>Collections:</b> {len(selected_collections)} selected\n"
            if len(selected_collections) <= 5:
                progress_text += f"   • {', '.join(selected_collections)}\n"
            else:
                progress_text += f"   • {', '.join(selected_collections[:3])}, and {len(selected_collections)-3} more\n"
        else:
            progress_text += "📋 <b>Collections:</b> All collections\n"

        progress_text += (
            "\n🔄 <b>Status:</b> Processing custom database export...\n\n"
            "<i>This may take several minutes depending on data size.</i>\n"
            "<i>You will be notified when the export is complete.</i>"
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔙 Back to Export Menu",
                    callback_data="database_export_menu"
                )
            ]
        ])

        await safe_edit_message(
            callback.message,
            progress_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

        # Start the custom export process asynchronously
        asyncio.create_task(
            _perform_custom_database_export(callback, user_id, username, format_key, selected_collections)
        )

    except Exception as e:
        sys_logger.error(f"Error starting custom database export: {e}")
        await callback.answer("❌ Error starting custom export", show_alert=True)


async def _perform_custom_database_export(
    callback: CallbackQuery,
    user_id: int,
    username: str,
    format_key: str,
    collections: Optional[List[str]]
):
    """Perform the actual custom database export and update the user."""
    try:
        from utils.database_export import create_database_export, get_export_formats

        # Perform the export with selected collections
        export_info = await create_database_export(user_id, username, format_key, collections)

        formats = get_export_formats()
        format_info = formats[format_key]

        if export_info["status"] == "completed":
            # Export successful
            file_size_mb = export_info["file_size"] / (1024 * 1024)

            success_text = (
                "✅ <b>CUSTOM DATABASE EXPORT COMPLETED</b>\n\n"
                "━━━━━━━━━━━━━━━━━━\n"
                "<b>📋 EXPORT DETAILS</b>\n"
                "━━━━━━━━━━━━━━━━━━\n\n"
                f"📁 <b>Format:</b> {format_info['description']}\n"
                f"📄 <b>Filename:</b> {export_info['filename']}\n"
                f"💽 <b>File Size:</b> {file_size_mb:.2f} MB\n"
            )

            if collections:
                success_text += f"📋 <b>Collections:</b> {len(collections)} selected\n"
            else:
                success_text += "📋 <b>Collections:</b> All collections\n"

            success_text += (
                f"🕐 <b>Completed:</b> {export_info['completed_at'].strftime('%Y-%m-%d %H:%M:%S')}\n"
                f"⏰ <b>Expires:</b> {export_info['download_expires_at'].strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                "✅ <b>Status:</b> Ready for download\n\n"
                "<i>Click the download button below to get your custom export file.</i>"
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="📥 Download Export",
                        callback_data=f"db_export_download:{export_info['export_id']}"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="📋 Export History",
                        callback_data="db_export_history"
                    ),
                    InlineKeyboardButton(
                        text="🔙 Export Menu",
                        callback_data="database_export_menu"
                    )
                ]
            ])

        else:
            # Export failed
            error_text = (
                "❌ <b>CUSTOM DATABASE EXPORT FAILED</b>\n\n"
                "━━━━━━━━━━━━━━━━━━\n"
                "<b>📋 ERROR DETAILS</b>\n"
                "━━━━━━━━━━━━━━━━━━\n\n"
                f"📁 <b>Format:</b> {format_info['description']}\n"
                f"❌ <b>Error:</b> {export_info.get('error', 'Unknown error')}\n"
                f"🕐 <b>Failed at:</b> {export_info['completed_at'].strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                "<i>Please try again or contact support if the issue persists.</i>"
            )

            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔄 Try Again",
                        callback_data="db_export_advanced"
                    ),
                    InlineKeyboardButton(
                        text="🔙 Export Menu",
                        callback_data="database_export_menu"
                    )
                ]
            ])

            success_text = error_text

        # Update the message with results
        await safe_edit_message(
            callback.message,
            success_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        sys_logger.error(f"Error in custom database export process: {e}")

        # Show error message
        error_text = (
            "❌ <b>CUSTOM DATABASE EXPORT ERROR</b>\n\n"
            f"An unexpected error occurred during the custom export process:\n\n"
            f"<code>{str(e)}</code>\n\n"
            "<i>Please try again or contact support.</i>"
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔄 Try Again",
                    callback_data="db_export_advanced"
                ),
                InlineKeyboardButton(
                    text="🔙 Export Menu",
                    callback_data="database_export_menu"
                )
            ]
        ])

        try:
            await safe_edit_message(
                callback.message,
                error_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
        except Exception as edit_error:
            sys_logger.error(f"Error updating message after custom export failure: {edit_error}")
