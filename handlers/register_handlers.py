from aiogram import Dispatcher
import logging

# Import router tools for safe registration
from utils.router_tools import safe_include_router
from utils.function_tracking import register_usage

# Setup logger
logger = logging.getLogger(__name__)

# Import all router modules
from handlers.user import router as user_router
from handlers.admin import router as admin_router
from handlers.owner import router as owner_router

from handlers.deposit import router as deposit_router
from handlers.products import router as products_router
from handlers.shop import router as shop_router
from handlers.common_callbacks import router as common_callbacks_router
from handlers.support_admin import router as support_admin_router
from handlers.cancel_handlers import router as cancel_router
from handlers.callbacks import router as callbacks_router
from handlers.welcome_message import router as welcome_message_router
from handlers.sys_db import register_sys_handlers

# Import template handlers register function
from templates.template_handlers import register_template_handlers

# Import diagnostic handlers
from handlers.admin_diagnostics import router as admin_diagnostics_router

# Import bonus management handlers
from handlers.bonus_admin import router as bonus_admin_router
from handlers.bonus_admin_edit import router as bonus_admin_edit_router


# Register keyboard functions from across the application to prevent false positives
def register_all_keyboards():
    """Register all keyboard functions to prevent them from being flagged as unused."""
    from keyboards.deposit_kb import deposit_pay_keyboard, custom_amount_cancel_keyboard
    from keyboards.product_kb import (
        back_to_file_options_keyboard,
        back_to_edit_file_options_keyboard,
        product_preview_keyboard,
        done_editing_keyboard,
        confirm_delete_product_keyboard,
        back_to_product_management_keyboard,
        product_deleted_keyboard,
        product_edit_field_success_keyboard,
        product_edit_options_keyboard,
        category_selection_keyboard,
    )

    register_usage(
        deposit_pay_keyboard,
        custom_amount_cancel_keyboard,
        back_to_file_options_keyboard,
        back_to_edit_file_options_keyboard,
        product_preview_keyboard,
        done_editing_keyboard,
        confirm_delete_product_keyboard,
        back_to_product_management_keyboard,
        product_deleted_keyboard,
        product_edit_field_success_keyboard,
        product_edit_options_keyboard,
        category_selection_keyboard,
    )


def register_all_handlers(dp: Dispatcher):
    """Register all handlers with the dispatcher in the correct order."""
    try:
        # Initialize database indexes for exclusive products
        try:
            from utils.exclusive_product_db_indexes import initialize_exclusive_product_indexes
            initialize_exclusive_product_indexes()
            logger.info("[OK] Exclusive product database indexes initialized")
        except Exception as e:
            logger.warning(f"[WARNING] Failed to initialize exclusive product indexes: {e}")

        # Register all keyboard functions to prevent false positives in static analysis
        register_all_keyboards()

        # Register system handlers first to ensure highest priority for admin commands
        register_sys_handlers(dp)
        logger.info("[OK] System handlers registered")

        # Then register cancel handlers to ensure they work in any state
        safe_include_router(dp, cancel_router)
        logger.info("[OK] Cancel handlers registered")

        # Register common callback handlers
        safe_include_router(dp, common_callbacks_router)
        logger.info("[OK] Common callback handlers registered")

        # Register shop handlers
        safe_include_router(dp, shop_router)
        logger.info("[OK] Shop handlers registered")

        # Register products handlers
        safe_include_router(dp, products_router)
        logger.info("[OK] Products handlers registered")

        # Then register payment verification handlers
        from handlers.payment_verification import register_payment_verification_handlers

        register_payment_verification_handlers(dp)
        logger.info("[OK] Payment verification handlers registered")

        # Register specific callback handlers
        safe_include_router(dp, callbacks_router)
        logger.info("[OK] Callback handlers registered")

        # Register owner handlers (highest privilege)
        safe_include_router(dp, owner_router)
        logger.info("[OK] Owner handlers registered")

        # Register welcome message handlers
        safe_include_router(dp, welcome_message_router)
        logger.info("[OK] Welcome message handlers registered")

        # Register admin handlers
        safe_include_router(dp, admin_router)
        logger.info("[OK] Admin handlers registered")

        # Register admin diagnostic handlers
        safe_include_router(dp, admin_diagnostics_router)
        logger.info("[OK] Admin diagnostic handlers registered")

        # Register bonus admin handlers
        safe_include_router(dp, bonus_admin_router)
        logger.info("[OK] Bonus admin handlers registered")

        # Register bonus admin edit handlers
        safe_include_router(dp, bonus_admin_edit_router)
        logger.info("[OK] Bonus admin edit handlers registered")

        # Register deposit handlers
        safe_include_router(dp, deposit_router)
        logger.info("[OK] Deposit handlers registered")

        # Register template handlers
        register_template_handlers(dp)
        logger.info("[OK] Template handlers registered")

        # Register system database handlers
        register_sys_handlers(dp)
        logger.info("[OK] System database handlers registered")

        # Register user handlers
        safe_include_router(dp, user_router)
        logger.info("[OK] User handlers registered")

        # Register support admin handlers AFTER user handlers to ensure proper message flow
        safe_include_router(dp, support_admin_router)
        logger.info("[OK] Support admin handlers registered")

        # Register exclusive product admin handlers
        try:
            from handlers.exclusive_admin import register_exclusive_admin_handlers
            register_exclusive_admin_handlers(dp)
            logger.info("[OK] Exclusive product admin handlers registered")
        except Exception as e:
            logger.warning(f"[WARNING] Failed to register exclusive admin handlers: {e}")

        # Register unified product admin handlers
        try:
            from handlers.unified_product_admin import register_unified_product_admin_handlers
            register_unified_product_admin_handlers(dp)
            logger.info("[OK] Unified product admin handlers registered")
        except Exception as e:
            logger.warning(f"[WARNING] Failed to register unified product admin handlers: {e}")

        logger.info("[OK] All handlers registered successfully!")
    except Exception as e:
        logger.error(f"[ERROR] Error registering handlers: {e}")
        # Re-raise the exception to propagate it up
        raise
