from aiogram import Router, F, types
from aiogram.fsm.context import FSMContext
from aiogram.types import (
    Callback<PERSON><PERSON><PERSON>,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    Message,
)
from aiogram.filters import Command
import logging

# This import is correct - ensure it stays this way
from keyboards.main_menu import main_menu_keyboard
from keyboards.user_kb import balance_keyboard
from keyboards.support_kb import support_keyboard
from keyboards.admin_kb import admin_main_keyboard
from keyboards.common_kb import help_keyboard, back_to_menu_keyboard
from database.operations import (
    get_user_balance,
    get_user_role,
    get_or_create_cart,
    clear_cart,
)

# Add the import for register_router_once utility
from utils.router_tools import register_router_once
from utils.helpers import is_maintenance_mode_for_user
from utils.telegram_helpers import (
    safe_edit_message,
)  # Added for the return_to_main_menu handler
from utils.template_helpers import format_text
from utils.state_helpers import clear_state_data

router = Router()
router.name = "common_callbacks"

# Initialize logger
logger = logging.getLogger(__name__)


async def check_maintenance_mode(callback_query: CallbackQuery) -> bool:
    """Check if user is subject to maintenance mode, and respond accordingly.

    Args:
        callback_query: The callback query to check

    Returns:
        bool: True if maintenance mode is active for this user and response was sent
    """
    user_id = callback_query.from_user.id

    if is_maintenance_mode_for_user(user_id):
        await callback_query.answer("🚧 Maintenance mode is active")

        # Edit the message to show maintenance mode
        # Use the template from user.json
        maintenance_text = format_text("user", "maintenance_active")

        await safe_edit_message(
            callback_query.message,
            maintenance_text,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text=format_text("buttons", "maintenance_check_status"),
                            callback_data="check_maintenance",
                        )
                    ]
                ]
            ),
        )
        return True

    return False


# Add a universal return to main menu handler in common callbacks
@router.callback_query(F.data.startswith("return_to_main"))
async def return_to_main_menu(callback_query: CallbackQuery, state: FSMContext):
    """Return to main menu, clearing state."""
    await callback_query.answer()
    await clear_state_data(state)  # Always clear state when returning to main

    user_id = callback_query.from_user.id
    role = get_user_role(user_id)  # Fetch role again if needed

    try:
        if role in ("owner", "admin"):
            from keyboards.admin_kb import admin_main_keyboard

            is_owner = role == "owner"
            keyboard = admin_main_keyboard(is_owner=is_owner)
            # Get admin welcome message from template
            admin_text = format_text(
                "admin" if role == "admin" else "owner",
                "admin_welcome",
                admin_name=callback_query.from_user.full_name,
                default=(
                    f"{'👑' if role == 'owner' else '🔧'} <b>\u2022 {role.upper()} PANEL \u2022</b>\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>ADMINISTRATION TOOLS</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"<i>Welcome back to the {role.upper()} dashboard!</i>\n\n"
                    f"\u2022 Manage users and permissions\n"
                    f"\u2022 View system statistics\n"
                    f"\u2022 Configure bot settings\n"
                    f"\u2022 Access support tools\n\n"
                    f"<i>Select an option below to continue.</i>"
                ),
            )
            await safe_edit_message(
                callback_query.message,
                admin_text,
                reply_markup=keyboard,
                parse_mode="HTML",
            )
        else:
            # Get welcome message settings
            from database.operations import get_welcome_message_async

            welcome_settings = await get_welcome_message_async()
            is_welcome_enabled = welcome_settings.get("enabled", False)
            welcome_message_text = welcome_settings.get("message_text", "")
            welcome_image_path = welcome_settings.get("image_path")

            # Get the standard main menu text
            main_menu_text = format_text(
                "user",
                "main_menu_message",
                user_name=callback_query.from_user.full_name,
            )

            # If welcome message is enabled, combine it with the main menu text
            if is_welcome_enabled:
                combined_message = (
                    f"{welcome_message_text}\n\n{main_menu_text}"
                    if welcome_message_text
                    else main_menu_text
                )

                # If there's a welcome image, send it with the combined message
                if welcome_image_path:
                    # Get the full path to the image
                    import os

                    base_dir = os.path.dirname(
                        os.path.dirname(os.path.abspath(__file__))
                    )
                    full_path = os.path.join(base_dir, "uploads", welcome_image_path)

                    if os.path.exists(full_path):
                        # Check if it's a GIF file
                        is_gif = full_path.lower().endswith(".gif")
                        from aiogram.types import FSInputFile

                        # Delete the original message to avoid "message to edit not found" errors
                        try:
                            await callback_query.message.delete()
                        except Exception as delete_error:
                            logger.warning(
                                f"Could not delete original message: {delete_error}"
                            )

                        # Send a new message with the image
                        if is_gif:
                            # Send as animation for GIFs
                            try:
                                await callback_query.message.answer_animation(
                                    animation=FSInputFile(full_path),
                                    caption=combined_message,
                                    parse_mode="HTML",
                                    reply_markup=main_menu_keyboard(),
                                )
                                return  # Exit early after sending the message
                            except Exception as e:
                                logger.warning(
                                    f"Failed to send welcome GIF as animation: {e}"
                                )
                                # Fall through to the next option if this fails
                        else:
                            # Send as photo for other image types
                            try:
                                await callback_query.message.answer_photo(
                                    photo=FSInputFile(full_path),
                                    caption=combined_message,
                                    parse_mode="HTML",
                                    reply_markup=main_menu_keyboard(),
                                )
                                return  # Exit early after sending the message
                            except Exception as e:
                                logger.warning(f"Failed to send welcome image: {e}")
                                # Fall through to the next option if this fails
                    else:
                        logger.warning(f"Welcome image not found at path: {full_path}")
                        # Fall through to edit the message without an image

                # If we get here, either there's no image or sending the image failed
                await safe_edit_message(
                    callback_query.message,
                    combined_message,
                    reply_markup=main_menu_keyboard(),
                    parse_mode="HTML",
                )
            else:
                # Welcome message is disabled, just edit with the main menu text
                await safe_edit_message(
                    callback_query.message,
                    main_menu_text,
                    reply_markup=main_menu_keyboard(),
                    parse_mode="HTML",
                )
    except Exception as e:
        # Fallback if edit fails (e.g., message too old)
        logger.error(f"Error returning to main menu via edit: {e}")
        # Sending a new message as fallback
        try:
            if role in ("owner", "admin"):
                # Send new admin welcome
                pass  # Or resend welcome message
            else:
                # Get welcome message settings
                from database.operations import get_welcome_message_async

                welcome_settings = await get_welcome_message_async()
                is_welcome_enabled = welcome_settings.get("enabled", False)
                welcome_message_text = welcome_settings.get("message_text", "")
                welcome_image_path = welcome_settings.get("image_path")

                # Get the standard main menu text
                main_menu_text = format_text(
                    "user",
                    "main_menu_message",
                    user_name=callback_query.from_user.full_name,
                )

                # If welcome message is enabled, combine it with the main menu text
                if is_welcome_enabled:
                    combined_message = (
                        f"{welcome_message_text}\n\n{main_menu_text}"
                        if welcome_message_text
                        else main_menu_text
                    )

                    # If there's a welcome image, send it with the combined message
                    if welcome_image_path:
                        # Get the full path to the image
                        import os

                        base_dir = os.path.dirname(
                            os.path.dirname(os.path.abspath(__file__))
                        )
                        full_path = os.path.join(
                            base_dir, "uploads", welcome_image_path
                        )

                        if os.path.exists(full_path):
                            # Check if it's a GIF file
                            is_gif = full_path.lower().endswith(".gif")
                            from aiogram.types import FSInputFile

                            # Send a new message with the image
                            if is_gif:
                                # Send as animation for GIFs
                                await callback_query.message.answer_animation(
                                    animation=FSInputFile(full_path),
                                    caption=combined_message,
                                    parse_mode="HTML",
                                    reply_markup=main_menu_keyboard(),
                                )
                                return  # Exit early after sending the message
                            else:
                                # Send as photo for other image types
                                await callback_query.message.answer_photo(
                                    photo=FSInputFile(full_path),
                                    caption=combined_message,
                                    parse_mode="HTML",
                                    reply_markup=main_menu_keyboard(),
                                )
                                return  # Exit early after sending the message
                        else:
                            # Image file not found, just send combined text
                            await callback_query.message.answer(
                                combined_message,
                                reply_markup=main_menu_keyboard(),
                                parse_mode="HTML",
                            )
                    else:
                        # No image, just send combined text
                        await callback_query.message.answer(
                            combined_message,
                            reply_markup=main_menu_keyboard(),
                            parse_mode="HTML",
                        )
                else:
                    # Welcome message disabled, just send main menu text
                    await callback_query.message.answer(
                        main_menu_text,
                        reply_markup=main_menu_keyboard(),
                        parse_mode="HTML",
                    )
        except Exception as send_err:
            logger.error(f"Failed to send new main menu message: {send_err}")


@router.callback_query(F.data == "check_maintenance")
async def check_maintenance_status(callback_query: CallbackQuery):
    """Handler for the check maintenance status button."""
    user_id = callback_query.from_user.id
    await callback_query.answer("Checking maintenance status...")

    # Check if maintenance mode is active for this user
    if is_maintenance_mode_for_user(user_id):
        # Maintenance mode is still active
        # Use the template from user.json
        maintenance_text = format_text("user", "maintenance_active")

        await safe_edit_message(
            callback_query.message,
            maintenance_text,
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text=format_text("buttons", "maintenance_check_again"),
                            callback_data="check_maintenance",
                        )
                    ]
                ]
            ),
        )
    else:
        # Maintenance mode is no longer active
        # Use the template from user.json
        completed_text = format_text("user", "maintenance_completed")

        await safe_edit_message(
            callback_query.message,
            completed_text,
            parse_mode="HTML",
            reply_markup=main_menu_keyboard(),
        )


@router.callback_query(F.data == "view_balance")
async def view_balance_callback(callback_query: CallbackQuery):
    """Primary handler for view_balance."""
    await callback_query.answer()

    # Check maintenance mode first
    if await check_maintenance_mode(callback_query):
        return

    user_id = callback_query.from_user.id
    # Get the user's balance from MongoDB
    balance = get_user_balance(user_id)

    # Check for pending payments and add BTC warnings if needed
    from database.operations import get_user_pending_payments
    from handlers.payment_verification import is_btc_payment

    pending_payments = get_user_pending_payments(user_id)
    btc_pending_warning = ""

    if pending_payments:
        btc_payments = []
        other_payments = []

        for payment in pending_payments:
            # Try to get currency from payment data if available
            payment_data = payment.get("api_response_data", {})
            if isinstance(payment_data, dict):
                currency = payment_data.get("currency", "")
                if is_btc_payment(currency):
                    btc_payments.append(payment)
                else:
                    other_payments.append(payment)
            else:
                other_payments.append(payment)

        if btc_payments:
            btc_pending_warning = (
                "\n\n⚠️ <b>PENDING BITCOIN PAYMENTS</b>\n"
                f"• {len(btc_payments)} BTC payment(s) awaiting confirmation\n"
                "• Bitcoin requires 2 network confirmations\n"
                "• Your balance will update after confirmation completes"
            )

    # Use the template from user.json
    balance_text = format_text("user", "balance_message", balance=balance)

    # Add BTC warning if present
    if btc_pending_warning:
        balance_text += btc_pending_warning

    # Use safe_edit_message instead of edit_text to handle messages without text
    await safe_edit_message(
        callback_query.message,
        balance_text,
        reply_markup=balance_keyboard(),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "clear_cart")
async def clear_cart_redirect(callback_query: CallbackQuery):
    """Primary handler for clear_cart - redirects to the products module."""
    # Check maintenance mode first
    if await check_maintenance_mode(callback_query):
        return

    from handlers.products import clear_cart_handler

    await clear_cart_handler(callback_query)


@router.callback_query(F.data.in_(["cancel_checkout", "cancel_purchase"]))
async def handle_cancel_actions(callback_query: CallbackQuery, state: FSMContext):
    """Handle cancel actions from checkout or purchase."""
    await callback_query.answer()

    # Check maintenance mode first
    if await check_maintenance_mode(callback_query):
        return

    # Different text depending on which action was cancelled
    if callback_query.data == "cancel_checkout":
        action_text = "Checkout cancelled."
    else:
        action_text = "Purchase cancelled."

    await callback_query.message.edit_text(
        f"✅ {action_text}", reply_markup=back_to_menu_keyboard()
    )

    await clear_state_data(state)


@router.callback_query(F.data == "browse_products")
async def browse_products_redirect(callback_query: CallbackQuery):
    """Primary handler for browse_products - redirects to the products module."""
    # Check maintenance mode first
    if await check_maintenance_mode(callback_query):
        return

    from handlers.products import browse_products

    await browse_products(callback_query)


# Make sure view_cart is imported only after it exists
@router.callback_query(F.data == "view_cart")
async def view_cart_redirect(callback_query: CallbackQuery):
    """Primary handler for view_cart - redirects to the products module."""
    # Check maintenance mode first
    if await check_maintenance_mode(callback_query):
        return

    try:
        from handlers.products import view_cart_callback

        # Call the correct function
        await view_cart_callback(callback_query)
    except ImportError:
        # Fallback if the function doesn't exist yet
        await callback_query.answer(
            "Cart viewing is currently unavailable. Please try again later."
        )
        await callback_query.message.edit_text(
            "Cart feature is being updated. Please check back later.",
            reply_markup=back_to_menu_keyboard(),
        )


@router.callback_query(F.data == "help_button")
async def help_button_callback(callback_query: CallbackQuery):
    """Handle the help button callback."""
    try:
        await callback_query.answer()

        # Check maintenance mode first
        if await check_maintenance_mode(callback_query):
            return

        # Use the template from user.json
        help_text = format_text("user", "help_message")

        # Use safe_edit_message instead of edit_text to handle messages without text
        await safe_edit_message(
            callback_query.message,
            help_text,
            parse_mode="HTML",
            reply_markup=help_keyboard(),
        )
    except Exception as e:
        print(f"ERROR in help_button_callback: {e}")
        await callback_query.message.reply(
            "An error occurred. Please try again or use /help command."
        )


@router.callback_query(F.data == "support_button")
async def support_button_callback(callback_query: CallbackQuery):
    """Handle the support button callback."""
    try:
        await callback_query.answer()

        # Check maintenance mode first
        if await check_maintenance_mode(callback_query):
            return

        from handlers.user import contact_support

        await contact_support(callback_query, None)
    except Exception as e:
        print(f"ERROR in support_button_callback: {e}")
        await callback_query.message.reply(
            "An error occurred. Please try again or use /support command."
        )
