# -*- coding: utf-8 -*-
import logging
import time
from datetime import datetime

from aiogram import Router, F, Bot
from aiogram.fsm.context import FSMContext
from aiogram.types import (
    CallbackQuery,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    Message,
)
from utils.logger import log_payment
from utils.telegram_helpers import safe_edit_message
from aiogram.filters import StateFilter
from aiogram.fsm.state import State, StatesGroup
from payments.oxa_verify import check_oxapay_payment
from payments.currency_converter import (
    process_payment_with_conversion,
    format_conversion_display,
    get_usdt_equivalent_amount,
)
from utils.performance_monitor import PerformanceTimer, log_performance_metric
from utils.template_helpers import format_text
from utils.state_helpers import clear_state_data
from database.operations import (
    get_user_balance,
    update_user_balance,
    add_transaction,
    get_payment_by_track_id,
    update_payment_status,
    get_latest_payment,
)

# Set up logger
# Basic config moved higher to ensure logger works early
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Create router
router = Router()
router.name = "payment_verification_router"


# Define states for payment verification
class PaymentVerificationStates(StatesGroup):
    waiting_for_track_id = State()


# --- Helper Functions ---


def is_track_id_already_verified(track_id: str) -> tuple[bool, dict | None]:
    """
    Check if a track ID has already been successfully verified and processed.

    Args:
        track_id: The track ID to check.

    Returns:
        tuple: (bool, dict | None) - (is_verified, payment_info)
    """
    try:
        payment = get_payment_by_track_id(track_id)
        if payment and payment.get("status") == "completed":
            return True, payment
    except Exception as e:
        logger.error(f"Database error checking track ID {track_id}: {e}", exc_info=True)
    return False, None


async def _send_already_verified_message(
    target: Message | CallbackQuery, track_id: str, payment_info: dict
):
    """Sends the 'already verified' message."""
    # Extract user ID correctly based on object type
    if isinstance(target, CallbackQuery):
        user_id = target.from_user.id
    else:  # Message type
        # Try to get the user ID from the replied message if available
        if target.reply_to_message and target.reply_to_message.from_user:
            reply_user = target.reply_to_message.from_user
            user_id = reply_user.id
            logger.info(f"Using reply user ID {user_id} for verification")
        else:
            # Fallback to the original message sender if not a reply
            user_id = target.from_user.id

    paid_to_user = payment_info.get("user_id")

    # Simplify user ID comparison
    try:
        # Convert both IDs to strings for comparison
        user_id_str = str(user_id)
        paid_to_user_str = str(paid_to_user)

        # Remove any bot prefix if present
        user_id_str = user_id_str.replace("bot", "")
        paid_to_user_str = paid_to_user_str.replace("bot", "")

        is_same_user = user_id_str == paid_to_user_str

        logger.info(
            f"Payment {track_id} verification: User match result: {is_same_user} "
            f"(Current: {user_id_str}, Payment: {paid_to_user_str})"
        )
    except Exception as e:
        logger.error(f"Error comparing user IDs: {e}", exc_info=True)
        is_same_user = False

    # Get already processed payment message from template
    from utils.template_helpers import format_text

    payment_status = (
        "✅ <i>This payment was already credited to your account.</i>"
        if is_same_user
        else "⚠️ <i>This payment was credited to another user.</i>"
    )

    text = format_text(
        "payment",
        "already_processed_payment",
        track_id=track_id,
        payment_status=payment_status,
        default=(
            "⚠️ <b>\u2022 ALREADY PROCESSED PAYMENT \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>VERIFICATION RESULT</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 <b>Track ID:</b> <code>{track_id}</code>\n\n"
            f"{payment_status}\n\n"
            f"<i>Each payment can only be credited once.</i>"
        ),
    )
    markup = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="💎 Make New Deposit", callback_data="deposit_funds"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🏠 Return to Menu", callback_data="return_to_main"
                )
            ],
        ]
    )
    target_message = target.message if isinstance(target, CallbackQuery) else target
    if isinstance(target, CallbackQuery):
        await safe_edit_message(
            target_message, text, reply_markup=markup, parse_mode="HTML"
        )
    elif isinstance(target, Message):
        # Use reply for messages (e.g., manual input)
        await target.reply(text, reply_markup=markup, parse_mode="HTML")


# Import payment configuration
from config import PAYMENT_FEE_RATE

# Constants for payment processing
FEE_RATE = PAYMENT_FEE_RATE  # Use configurable fee rate
DEFAULT_CURRENCY = "USDT"


def format_crypto_amount(amount, currency=""):
    """
    Format cryptocurrency amount to avoid scientific notation for small values.

    Args:
        amount: The amount to format (float, int, or string)
        currency: The cryptocurrency code (optional)

    Returns:
        str: Properly formatted amount
    """
    try:
        # Convert to float first
        float_amount = float(amount)

        # For very small values (like BTC), use more decimal places
        if float_amount < 0.0001:
            # Format with up to 10 decimal places, removing trailing zeros
            formatted = f"{float_amount:.10f}".rstrip("0").rstrip(".")
        elif float_amount < 0.01:
            # For small values, use 8 decimal places
            formatted = f"{float_amount:.8f}".rstrip("0").rstrip(".")
        elif float_amount < 1:
            # For medium values, use 6 decimal places
            formatted = f"{float_amount:.6f}".rstrip("0").rstrip(".")
        else:
            # For larger values (like USDT), use 2 decimal places
            formatted = f"{float_amount:.2f}"

        # Add currency code if provided
        if currency:
            return f"{formatted} {currency}"
        return formatted

    except (ValueError, TypeError):
        # Return original value if conversion fails
        if currency:
            return f"{amount} {currency}"
        return str(amount)


async def _parse_payment_amount_and_currency_with_conversion(
    payment_data: dict,
) -> tuple[float, str, dict]:
    """
    Enhanced function to parse payment amount and currency with automatic conversion to USDT.

    This function uses the currency converter to handle all types of payments:
    - Direct USDT payments
    - Auto-converted payments from OXA Pay
    - Manual conversions for non-USDT payments

    Args:
        payment_data: The 'data' part of the OxaPay API response.

    Returns:
        tuple: (float usdt_amount, str currency_code, dict conversion_info)
    """
    track_id = payment_data.get("track_id", "N/A")

    # Early return if payment_data is empty or invalid
    if not payment_data or not isinstance(payment_data, dict):
        logger.error(f"Invalid payment data for {track_id}: {type(payment_data)}")
        return 0.0, DEFAULT_CURRENCY, {}

    try:
        # Use the enhanced currency converter to process the payment
        conversion_result = await process_payment_with_conversion(
            payment_data, track_id, "USDT"
        )

        converted_amount = conversion_result.get("converted_amount", 0.0)
        currency = conversion_result.get("currency", "USDT")

        # Apply fee adjustment (configurable fee rate that needs to be added back)
        if converted_amount > 0:
            fee_rate = PAYMENT_FEE_RATE  # Use configurable fee rate
            final_amount = converted_amount / (1 - fee_rate)

            logger.info(
                f"Applied fee adjustment for {track_id}: "
                f"{converted_amount:.2f} → {final_amount:.2f} {currency} "
                f"(adding back {fee_rate*100}% fee)"
            )

            converted_amount = final_amount

        # Log conversion errors if any
        conversion_errors = conversion_result.get("conversion_errors", [])
        if conversion_errors:
            logger.warning(f"Conversion errors for {track_id}: {conversion_errors}")

        logger.info(
            f"Payment processing completed for {track_id}: "
            f"Final amount: {converted_amount:.2f} {currency}, "
            f"Conversions: {len(conversion_result.get('conversion_details', []))}"
        )

        return converted_amount, currency, conversion_result

    except Exception as e:
        logger.error(
            f"Error processing payment with conversion for {track_id}: {e}",
            exc_info=True,
        )

        # Fallback to original parsing method
        try:
            amount, currency = _parse_payment_amount_and_currency_fallback(payment_data)
            return amount, currency, {"fallback_used": True, "error": str(e)}
        except Exception as fallback_error:
            logger.error(
                f"Fallback parsing also failed for {track_id}: {fallback_error}"
            )
            return (
                0.0,
                DEFAULT_CURRENCY,
                {"error": str(e), "fallback_error": str(fallback_error)},
            )


def _parse_payment_amount_and_currency_fallback(
    payment_data: dict,
) -> tuple[float, str]:
    """
    Fallback function for parsing payment amount when currency conversion fails.
    This is the original parsing logic kept as a safety net.

    Args:
        payment_data: The 'data' part of the OxaPay API response.

    Returns:
        tuple: (float amount, str currency_code)
    """
    currency = payment_data.get("currency", DEFAULT_CURRENCY)
    track_id = payment_data.get("track_id", "N/A")

    # Early return if payment_data is empty or invalid
    if not payment_data or not isinstance(payment_data, dict):
        logger.error(f"Invalid payment data for {track_id}: {type(payment_data)}")
        return 0.0, DEFAULT_CURRENCY

    try:
        # Process transactions if available (most accurate source)
        transactions = payment_data.get("txs", [])
        if transactions:
            return _process_transactions(transactions, track_id, currency)

        # Fallback to direct fields if no transactions
        return _process_fallback_fields(payment_data, track_id, currency)

    except Exception as e:
        logger.error(f"Error parsing payment data for {track_id}: {e}", exc_info=True)
        return 0.0, currency


def _process_transactions(
    transactions: list, track_id: str, default_currency: str
) -> tuple[float, str]:
    """
    Process transaction data to extract amount and currency.
    Optimized helper function for _parse_payment_amount_and_currency.

    Args:
        transactions: List of transaction objects
        track_id: Payment track ID for logging
        default_currency: Default currency to use if not determined from transactions

    Returns:
        tuple: (float amount, str currency)
    """
    total_amount = 0.0
    currencies = set()
    original_currencies = set()
    is_auto_converted = False

    # Process each transaction
    for tx in transactions:
        tx_currency = tx.get("currency", DEFAULT_CURRENCY)
        original_currencies.add(tx_currency)
        tx_amount = 0.0

        # Handle auto-converted payments
        if "auto_convert_amount" in tx and tx.get("auto_convert_amount") is not None:
            try:
                tx_amount = float(tx.get("auto_convert_amount", 0))
                auto_convert_currency = tx.get(
                    "auto_convert_currency", DEFAULT_CURRENCY
                )

                # Special handling for USDT auto-conversions where auto_convert_amount might be 0
                # but the original transaction is already in USDT
                if tx_amount == 0 and tx_currency.upper() == "USDT":
                    # For USDT "auto-conversions", use the sent_amount instead
                    tx_amount = float(tx.get("sent_amount", 0))
                    logger.info(
                        f"USDT auto-conversion fix for {track_id}: "
                        f"Using sent_amount {tx_amount} USDT instead of auto_convert_amount 0.0"
                    )

                currencies.add(auto_convert_currency)
                is_auto_converted = True

                # Format amounts properly to avoid scientific notation
                sent_amount_formatted = format_crypto_amount(tx.get("sent_amount", 0))
                tx_amount_formatted = format_crypto_amount(tx_amount)
                logger.info(
                    f"Auto-converted payment: {sent_amount_formatted} {tx_currency} → "
                    f"{tx_amount_formatted} {auto_convert_currency} for {track_id}"
                )
            except (ValueError, TypeError):
                # Fallback to value field
                tx_amount = _safe_float(tx.get("value", 0))
                currencies.add(tx_currency)
        else:
            # Direct payment - check multiple possible amount fields
            tx_amount = 0.0
            amount_fields = ["amount", "value", "received_amount", "sent_amount"]
            for field in amount_fields:
                if field in tx and tx.get(field) is not None:
                    tx_amount = _safe_float(tx.get(field, 0))
                    if tx_amount > 0:
                        break
            currencies.add(tx_currency)

            # Format amount properly to avoid scientific notation
            tx_amount_formatted = format_crypto_amount(tx_amount)
            logger.info(
                f"Direct payment in {tx_currency}: {tx_amount_formatted} for {track_id}"
            )

        # Add back transaction fee
        original_amount = tx_amount / (1 - FEE_RATE)
        total_amount += original_amount

    # Determine currency - use the common currency if all transactions use the same one
    final_currency = list(currencies)[0] if len(currencies) == 1 else default_currency

    # Log summary
    # Format total amount properly to avoid scientific notation
    total_amount_formatted = format_crypto_amount(total_amount)
    if is_auto_converted:
        logger.info(
            f"Auto-converted payment(s) for {track_id}: {total_amount_formatted} {final_currency} "
            f"(after fee adjustment)"
        )
    else:
        logger.info(
            f"Processed {len(transactions)} transaction(s) for {track_id}: "
            f"{total_amount_formatted} {final_currency} (after fee adjustment)"
        )

    return total_amount, final_currency


def _process_fallback_fields(
    payment_data: dict, track_id: str, default_currency: str
) -> tuple[float, str]:
    """
    Process fallback fields when no transactions are available.
    Optimized helper function for _parse_payment_amount_and_currency.

    Args:
        payment_data: Payment data dictionary
        track_id: Payment track ID for logging
        default_currency: Default currency to use

    Returns:
        tuple: (float amount, str currency)
    """
    logger.warning(f"No transactions found for {track_id}, using fallback fields")

    # Try value field first (preferred)
    if "value" in payment_data:
        amount = _safe_float(payment_data.get("value"))
        if amount > 0:
            logger.info(
                f"Using 'value' field for {track_id}: {amount} {default_currency}"
            )
            return amount, default_currency

    # Try receivedAmount next
    if "receivedAmount" in payment_data:
        amount = _safe_float(payment_data.get("receivedAmount"))
        if amount > 0:
            logger.info(
                f"Using 'receivedAmount' field for {track_id}: {amount} {default_currency}"
            )
            return amount, default_currency

    # Last resort: try amount field (usually the requested amount, not actual paid amount)
    if "amount" in payment_data:
        amount = _safe_float(payment_data.get("amount"))
        if amount > 0:
            logger.info(
                f"Using 'amount' field for {track_id}: {amount} {default_currency}"
            )
            return amount, default_currency

    logger.warning(f"No valid amount fields found for {track_id}")
    return 0.0, default_currency


def _safe_float(value) -> float:
    """Safely convert a value to float, returning 0.0 on failure."""
    try:
        return float(value)
    except (ValueError, TypeError):
        return 0.0


async def _process_completed_payment(
    target: Message | CallbackQuery,
    user_id: int,
    track_id: str,
    payment_data: dict,
    actual_paid_amount: float,
    currency: str,
    bot: Bot,
):
    """
    Optimized function to handle completed payment processing.
    Uses batched database operations and reduces redundant calculations.
    """
    # Performance timing
    start_time = time.time()

    # Double-check it's not already marked completed in DB (single DB call)
    is_verified, payment_info = is_track_id_already_verified(track_id)
    if is_verified:
        logger.warning(f"Payment {track_id} already processed in database")
        await _send_already_verified_message(target, track_id, payment_info)
        return

    # Get payment details (reuse existing DB call result if available)
    payment_db_record = get_payment_by_track_id(track_id)
    requested_amount = (
        float(payment_db_record.get("amount", 0)) if payment_db_record else 0.0
    )
    order_id = payment_data.get("orderId") or (
        payment_db_record.get("order_id") if payment_db_record else None
    )

    # Extract transaction data
    txs = payment_data.get("txs", [])

    # Log payment details
    logger.info(
        f"Processing payment {track_id} for user {user_id}: "
        f"Requested=${requested_amount:.2f}, Received=${actual_paid_amount:.2f} {currency}, "
        f"Transactions={len(txs)}"
    )

    # Extract auto-conversion info (single pass through transactions)
    conversion_info = _extract_conversion_info(txs)
    is_auto_converted = conversion_info["is_auto_converted"]
    original_currency = conversion_info["original_currency"]
    auto_convert_amount = conversion_info["auto_convert_amount"]

    # Prepare transaction log data
    pay_log_data = {
        "track_id": track_id,
        "payment_status": payment_data.get("status", "completed").lower(),
        "verification_time": datetime.now().isoformat(),
        "currency": currency,
        "requested_amount": requested_amount,
        "actual_paid_amount": actual_paid_amount,
        "response_data": payment_data,
        "order_id": order_id,
        "transaction_count": len(txs),
        "is_auto_converted": is_auto_converted,
        "original_currency": original_currency,
        "auto_convert_amount": auto_convert_amount,
        "processing_time_ms": int((time.time() - start_time) * 1000),
    }

    # Initialize bonus_result outside try block
    bonus_result = None

    # Process database operations in a batched manner
    if actual_paid_amount > 0:
        try:
            # 1. Update payment status
            update_payment_status(
                track_id,
                "completed",
                actual_paid_amount=actual_paid_amount,
                payment_verified=True,
                api_response_data=payment_data,
            )

            # 2. Get current balance (single DB call)
            current_balance = get_user_balance(user_id)
            current_balance_float = (
                float(current_balance) if current_balance is not None else 0.0
            )
            new_balance = current_balance_float + actual_paid_amount

            # 3. Update user balance
            update_user_balance(user_id, new_balance)

            # 4. Add transaction record
            add_transaction(
                user_id,
                "deposit",
                actual_paid_amount,
                track_id=track_id,
                pay_data=pay_log_data,
            )

            # 5. Apply bonus for deposit (if applicable)
            bonus_result = None
            try:
                logger.info(f"Attempting to apply bonus for deposit {track_id}, amount: ${actual_paid_amount:.2f}")
                from utils.bonus_calculator import BonusCalculator

                bonus_result = BonusCalculator.apply_bonus_to_wallet(
                    user_id=user_id,
                    deposit_amount=actual_paid_amount,
                    track_id=track_id
                )
                logger.info(f"Bonus application result: {bonus_result}")

                if bonus_result.get("success") and bonus_result.get("bonus_amount", 0) > 0:
                    new_balance = bonus_result.get("new_balance", new_balance)
                    logger.info(
                        f"Applied deposit bonus to user {user_id}: +${bonus_result['bonus_amount']:.2f} "
                        f"for deposit {track_id}"
                    )
                else:
                    logger.debug(f"No bonus applied for deposit {track_id} (amount: ${actual_paid_amount:.2f})")

            except Exception as bonus_e:
                logger.error(f"Error applying deposit bonus for {track_id}: {bonus_e}")
                # Don't fail the deposit if bonus application fails
                bonus_result = None

            # Format amounts properly for logging
            current_formatted = format_crypto_amount(current_balance_float)
            new_formatted = format_crypto_amount(new_balance)
            amount_formatted = format_crypto_amount(actual_paid_amount)

            # Include bonus information in logging if applicable
            bonus_info = ""
            if bonus_result and bonus_result.get("success") and bonus_result.get("bonus_amount", 0) > 0:
                bonus_amount = bonus_result.get("bonus_amount", 0)
                bonus_info = f" + ${bonus_amount:.2f} bonus"

            logger.info(
                f"User {user_id} balance updated: ${current_formatted} -> ${new_formatted} "
                f"(+${amount_formatted}{bonus_info})"
            )

            # Log payment to admins/channels
            await log_payment(bot, user_id, actual_paid_amount, track_id)

        except Exception as db_err:
            logger.error(
                f"Database error processing payment {track_id}: {db_err}",
                exc_info=True,
            )

        # Send success message
        await _send_success_message(
            target, track_id, actual_paid_amount, currency, bonus_result=bonus_result
        )
    else:
        logger.warning(
            f"Payment amount is zero or negative: ${actual_paid_amount:.2f}. User balance not updated."
        )

        # Still update payment status even if amount is zero
        try:
            update_payment_status(
                track_id,
                "completed",
                actual_paid_amount=actual_paid_amount,
                payment_verified=True,
                api_response_data=payment_data,
            )
        except Exception as e:
            logger.error(f"Failed to update payment status for {track_id}: {e}")


async def _process_completed_payment_with_conversion(
    target: Message | CallbackQuery,
    user_id: int,
    track_id: str,
    payment_data: dict,
    actual_paid_amount_usdt: float,
    currency: str,
    conversion_info: dict,
    bot: Bot,
):
    """
    Enhanced function to handle completed payment processing with currency conversion support.
    All amounts are expected to be in USDT equivalent.
    """
    # Performance timing
    start_time = time.time()

    # Double-check it's not already marked completed in DB (single DB call)
    is_verified, payment_info = is_track_id_already_verified(track_id)
    if is_verified:
        logger.warning(f"Payment {track_id} already processed in database")
        await _send_already_verified_message(target, track_id, payment_info)
        return

    # Get payment details (reuse existing DB call result if available)
    payment_db_record = get_payment_by_track_id(track_id)
    requested_amount = (
        float(payment_db_record.get("amount", 0)) if payment_db_record else 0.0
    )
    order_id = payment_data.get("orderId") or (
        payment_db_record.get("order_id") if payment_db_record else None
    )

    # Extract transaction data
    txs = payment_data.get("txs", [])

    # Log payment details with conversion info
    conversion_details = conversion_info.get("conversion_details", [])
    original_payments = []
    for detail in conversion_details:
        if detail.get("method") in ["auto_converted", "manual_converted"]:
            original_payments.append(
                f"{detail['original_amount']:.8f} {detail['original_currency']}"
            )

    conversion_summary = ""
    if original_payments:
        conversion_summary = f" (Original: {', '.join(original_payments)})"

    logger.info(
        f"Processing payment {track_id} for user {user_id} with conversion: "
        f"Requested=${requested_amount:.2f} USDT, Received=${actual_paid_amount_usdt:.2f} USDT, "
        f"Transactions={len(txs)}{conversion_summary}"
    )

    # Prepare enhanced payment log data with conversion information
    pay_log_data = {
        "track_id": track_id,
        "user_id": user_id,
        "amount": actual_paid_amount_usdt,
        "currency": "USDT",  # Always USDT for internal storage
        "status": "completed",
        "txs": txs,
        "order_id": order_id,
        "conversion_info": conversion_info,
        "original_currency": currency,
        "processing_time": time.time() - start_time,
    }

    # Initialize bonus_result outside try block
    bonus_result = None

    # Process database operations in a batched manner
    if actual_paid_amount_usdt > 0:
        try:
            # 1. Update payment status with conversion data
            update_payment_status(
                track_id,
                "completed",
                actual_paid_amount=actual_paid_amount_usdt,
                payment_verified=True,
                api_response_data=payment_data,
                conversion_data=conversion_info,
            )

            # 2. Get current balance (single DB call)
            current_balance = get_user_balance(user_id)
            current_balance_float = (
                float(current_balance) if current_balance is not None else 0.0
            )
            new_balance = current_balance_float + actual_paid_amount_usdt

            # 3. Update user balance (always in USDT)
            update_user_balance(user_id, new_balance)

            # 4. Add transaction record with conversion details
            add_transaction(
                user_id,
                "deposit",
                actual_paid_amount_usdt,  # Store USDT equivalent
                track_id=track_id,
                pay_data=pay_log_data,
            )

            # 5. Apply bonus for deposit (if applicable)
            bonus_result = None
            try:
                logger.info(f"Attempting to apply bonus for conversion deposit {track_id}, amount: ${actual_paid_amount_usdt:.2f}")
                from utils.bonus_calculator import BonusCalculator

                bonus_result = BonusCalculator.apply_bonus_to_wallet(
                    user_id=user_id,
                    deposit_amount=actual_paid_amount_usdt,
                    track_id=track_id
                )
                logger.info(f"Bonus application result: {bonus_result}")

                if bonus_result.get("success") and bonus_result.get("bonus_amount", 0) > 0:
                    new_balance = bonus_result.get("new_balance", new_balance)
                    logger.info(
                        f"Applied deposit bonus to user {user_id}: +${bonus_result['bonus_amount']:.2f} "
                        f"for deposit {track_id}"
                    )
                else:
                    logger.debug(f"No bonus applied for deposit {track_id} (amount: ${actual_paid_amount_usdt:.2f})")

            except Exception as bonus_e:
                logger.error(f"Error applying deposit bonus for {track_id}: {bonus_e}")
                # Don't fail the deposit if bonus application fails
                bonus_result = None

            # Format amounts properly for logging
            current_formatted = format_crypto_amount(current_balance_float)
            new_formatted = format_crypto_amount(new_balance)
            amount_formatted = format_crypto_amount(actual_paid_amount_usdt)

            # Include bonus information in logging if applicable
            bonus_info = ""
            if bonus_result and bonus_result.get("success") and bonus_result.get("bonus_amount", 0) > 0:
                bonus_amount = bonus_result.get("bonus_amount", 0)
                bonus_info = f" + ${bonus_amount:.2f} bonus"

            logger.info(
                f"User {user_id} balance updated: ${current_formatted} -> ${new_formatted} "
                f"(+${amount_formatted} USDT{bonus_info}){conversion_summary}"
            )

            # Log payment to admins/channels
            await log_payment(bot, user_id, actual_paid_amount_usdt, track_id)

        except Exception as db_err:
            logger.error(
                f"Database error processing payment with conversion {track_id}: {db_err}",
                exc_info=True,
            )
    else:
        logger.warning(
            f"Payment amount is zero or negative: ${actual_paid_amount_usdt:.2f} USDT. User balance not updated."
        )

        # Still update payment status even if amount is zero
        try:
            update_payment_status(
                track_id,
                "completed",
                actual_paid_amount=actual_paid_amount_usdt,
                payment_verified=True,
                api_response_data=payment_data,
                conversion_data=conversion_info,
            )
        except Exception as e:
            logger.error(f"Failed to update payment status for {track_id}: {e}")

    # Send success message with conversion details
    await _send_success_message_with_conversion(
        target, track_id, actual_paid_amount_usdt, new_balance, conversion_info, bonus_result
    )


def _extract_conversion_info(txs: list) -> dict:
    """
    Extract auto-conversion information from transaction data.
    Returns a dictionary with conversion details.
    """
    result = {
        "is_auto_converted": False,
        "original_currency": None,
        "auto_convert_amount": None,
    }

    if not txs:
        return result

    # Check first transaction for auto-conversion data
    tx = txs[0]
    if "auto_convert_amount" in tx and tx.get("auto_convert_amount") is not None:
        result["is_auto_converted"] = True
        result["original_currency"] = tx.get("currency", "Unknown")
        result["auto_convert_amount"] = tx.get("auto_convert_amount")

        # Special handling for USDT auto-conversions where auto_convert_amount might be 0
        # but the original transaction is already in USDT
        if (
            result["auto_convert_amount"] == 0
            and result["original_currency"].upper() == "USDT"
        ):
            # For USDT "auto-conversions", use the sent_amount instead
            result["auto_convert_amount"] = tx.get("sent_amount", 0)
            logger.info(
                f"USDT auto-conversion fix in _extract_conversion_info: "
                f"Using sent_amount {result['auto_convert_amount']} USDT instead of auto_convert_amount 0.0"
            )

        # Format amounts properly to avoid scientific notation
        sent_amount_formatted = format_crypto_amount(tx.get("sent_amount"))
        convert_amount_formatted = format_crypto_amount(result["auto_convert_amount"])
        logger.info(
            f"Auto-converted payment: {sent_amount_formatted} {result['original_currency']} → "
            f"{convert_amount_formatted} USDT"
        )

    return result


# This function is called by _handle_completed_payment and _check_for_underpayment
# It's not directly accessed by name, which is why the IDE shows a warning
async def _send_success_message(
    target: Message | CallbackQuery,
    track_id: str,
    actual_paid_amount: float,
    currency: str,
    requested_amount: float = 0.0,  # Default value to avoid unused parameter warning
    is_auto_converted: bool = False,
    original_currency: str = None,
    bonus_result: dict = None,
):
    """Send success message to user with payment details."""
    # Get transaction data
    payment_record = get_payment_by_track_id(track_id)
    txs = []
    auto_convert_amount = None

    if payment_record and "response_data" in payment_record:
        response_data = payment_record.get("response_data", {})
        if isinstance(response_data, dict) and "txs" in response_data:
            txs = response_data.get("txs", [])

    # Extract transaction hash if available
    tx_hash = ""
    if txs:
        tx_hash = txs[0].get("tx_hash", "")
        if is_auto_converted:
            auto_convert_amount = txs[0].get("auto_convert_amount")

    # Format the success message
    tx_hash_display = (
        f"\u2022 <b>TX:</b> <code>{tx_hash[:8]}...{tx_hash[-6:]}</code>\n"
        if tx_hash and len(tx_hash) > 14
        else ""
    )

    # Add auto-conversion information if applicable
    auto_convert_display = ""
    if is_auto_converted and original_currency:
        # Get the original sent amount
        sent_amount = txs[0].get("sent_amount", "?") if txs else "?"

        # Format amounts properly to avoid scientific notation
        if sent_amount != "?":
            sent_amount = format_crypto_amount(sent_amount)

        # Format the auto-converted amount
        converted_amount = auto_convert_amount or actual_paid_amount
        converted_amount_formatted = format_crypto_amount(converted_amount)

        auto_convert_display = (
            f"\u2022 <b>Original Payment:</b> <code>{sent_amount} {original_currency}</code>\n"
            f"\u2022 <b>Auto-converted to:</b> <code>{converted_amount_formatted} USDT</code>\n"
        )

    # Format the credited amount properly
    amount_formatted = format_crypto_amount(actual_paid_amount)

    # Use enhanced payment confirmation format for consistency with Flask server
    from payments.flask_server import create_enhanced_payment_confirmation

    # Get current balance for display
    from database.operations import get_user_balance
    try:
        current_balance = get_user_balance(user_id)
    except Exception as e:
        logger.error(f"Error getting user balance for confirmation message: {e}")
        current_balance = 0.0

    # Create enhanced payment confirmation message
    text = create_enhanced_payment_confirmation(
        amount_usdt=actual_paid_amount,
        track_id=track_id,
        new_balance=current_balance,
        bonus_result=bonus_result,
        conversion_result=None,  # Manual verification doesn't have conversion details
        conversion_display=""
    )

    markup = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="💰 View Balance", callback_data="view_balance"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🏠 Return to Menu", callback_data="return_to_main"
                )
            ],
        ]
    )

    target_message = target.message if isinstance(target, CallbackQuery) else target
    await safe_edit_message(
        target_message, text, reply_markup=markup, parse_mode="HTML"
    )


async def _send_pending_message(
    target: Message | CallbackQuery, track_id: str, pay_status: str, currency: str = None
):
    """Sends a message indicating the payment is pending/in progress."""
    # Get status details based on payment status
    status_upper = pay_status.upper().replace("_", " ")
    message_details = ""
    if pay_status == "new":
        message_details = "⏳ Payment initiated, waiting for action..."
    elif pay_status == "paying":
        message_details = "⏳ Payment attempt in progress..."
    elif pay_status in ["pending", "confirming", "waiting", "manual_accept"]:
        message_details = "⏳ Processing payment, please wait..."
    else:
        message_details = "⏳ Payment is processing..."

    # Add BTC-specific warning if this is a Bitcoin payment
    btc_warning = ""
    if is_btc_payment(currency):
        btc_warning = (
            "\n\n⚠️ <b>BITCOIN PAYMENT NOTICE</b>\n"
            "• BTC transactions require 2 network confirmations\n"
            "• Balance updates may be delayed due to network congestion\n"
            "• Processing time varies based on Bitcoin network activity"
        )

    # Get payment in progress message from template
    from utils.template_helpers import format_text

    text = format_text(
        "payment",
        "payment_in_progress",
        track_id=track_id,
        status=status_upper,
        message_details=message_details,
        btc_warning=btc_warning,
        default=(
            f"⏳ <b>\u2022 PAYMENT IN PROGRESS \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>TRANSACTION STATUS</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 <b>Track ID:</b> <code>{track_id}</code>\n"
            f"\u2022 <b>Status:</b> <code>{status_upper}</code>\n\n"
            f"{message_details}{btc_warning}\n\n"
            f"<i>You can check again in a few minutes using the Refresh button.</i>"
        ),
    )
    markup = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔄 Refresh Status", callback_data=f"verify_oxa:{track_id}"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🏠 Return to Menu", callback_data="return_to_main"
                )
            ],
        ]
    )
    target_message = target.message if isinstance(target, CallbackQuery) else target
    await safe_edit_message(
        target_message, text, reply_markup=markup, parse_mode="HTML"
    )


async def _send_failed_message(
    target: Message | CallbackQuery,
    track_id: str,
    pay_status: str,
    reason: str | None = None,
):
    """Sends a message indicating the payment failed, expired, or was underpaid."""
    # Prepare payment failed message details
    from utils.template_helpers import format_text

    status_upper = pay_status.upper().replace("_", " ")
    reason_text = reason or "We couldn't confirm this transaction."
    title = "❌ Payment Failed"
    icon = "🔴"
    buttons = [
        [
            InlineKeyboardButton(
                text="💎 Try New Deposit", callback_data="deposit_funds"
            )
        ],
        [
            InlineKeyboardButton(
                text="🛟 Contact Support", callback_data="contact_support"
            )
        ],
        [
            InlineKeyboardButton(
                text="🏠 Return to Menu", callback_data="return_to_main"
            )
        ],
    ]

    if pay_status == "expired":
        title = "❌ Payment Expired"
        reason_text = "This payment request has timed out. Please create a new one."
    elif pay_status == "underpaid":
        title = "⚠️ Payment Underpaid"
        icon = "🟠"
        reason_text = "The amount received was less than required. Your account has not been credited for this transaction."
        buttons = [
            [
                InlineKeyboardButton(
                    text="🔄 Refresh Status", callback_data=f"verify_oxa:{track_id}"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🛟 Contact Support", callback_data="contact_support"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🏠 Return to Menu", callback_data="return_to_main"
                )
            ],
        ]
    elif pay_status in ["canceled", "cancelled", "invalid"]:
        title = "❌ Payment Cancelled/Invalid"
        reason_text = "The payment was cancelled or marked as invalid."

    # Get payment failed message from template
    text = format_text(
        "payment",
        "payment_failed",
        title=title,
        track_id=track_id,
        status=status_upper,
        icon=icon,
        reason=reason_text,
        default=(
            f"<b>\u2022 {title} \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>TRANSACTION ERROR</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 <b>Track ID:</b> <code>{track_id}</code>\n"
            f"\u2022 <b>Status:</b> <code>{status_upper}</code>\n\n"
            f"<i>{reason_text}</i>"
        ),
    )

    markup = InlineKeyboardMarkup(inline_keyboard=buttons)
    target_message = target.message if isinstance(target, CallbackQuery) else target
    await safe_edit_message(
        target_message, text, reply_markup=markup, parse_mode="HTML"
    )


async def _send_error_message(
    target: Message | CallbackQuery, track_id: str | None, error_message_detail: str
):
    """Sends a generic error message for API or system issues."""
    # Use a standard user-facing message
    user_friendly_error = "A technical issue occurred during verification."
    # Log the detailed error passed in error_message_detail
    logger.error(
        f"Sending error message. TrackID: {track_id}, Detail: {error_message_detail}"
    )

    # Get verification error message from template
    from utils.template_helpers import format_text

    track_id_text = (
        f"\u2022 <b>Track ID:</b> <code>{track_id}</code>\n" if track_id else ""
    )

    text = format_text(
        "payment",
        "verification_error",
        track_id_text=track_id_text,
        error_message=user_friendly_error,
        default=(
            f"❌ <b>\u2022 VERIFICATION ERROR \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>SYSTEM ISSUE</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"{track_id_text}\n"
            f"<i>{user_friendly_error}</i>\n\n"
            f"<i>Please try again later. If the problem persists, contact our support team.</i>"
        ),
    )
    buttons = []
    if track_id:
        buttons.append(
            [
                InlineKeyboardButton(
                    text="🔄 Try Again", callback_data=f"verify_oxa:{track_id}"
                )
            ]
        )
    buttons.extend(
        [
            [
                InlineKeyboardButton(
                    text="🛟 Contact Support", callback_data="contact_support"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🏠 Return to Menu", callback_data="return_to_main"
                )
            ],
        ]
    )
    markup = InlineKeyboardMarkup(inline_keyboard=buttons)

    target_message = target.message if isinstance(target, CallbackQuery) else target
    # Ensure target_message is valid before trying to edit
    if target_message and isinstance(target_message, Message):
        await safe_edit_message(
            target_message, text, reply_markup=markup, parse_mode="HTML"
        )
    else:
        logger.error(
            f"Cannot send error message: Invalid target_message for TrackID {track_id}"
        )


async def _send_underpaid_message(
    target: Message | CallbackQuery,
    track_id: str,
    received_amount: float,
    required_amount: float,
    remaining_amount: float,
):
    """Sends a message indicating the payment is underpaid with a button to complete payment."""
    from utils.template_helpers import format_text
    from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

    # Get the payment record from the database to retrieve the original payment URL and additional details
    payment_record = get_payment_by_track_id(track_id)

    # Get payment URL from the payment record or fallback to constructing one
    payment_url = None
    if payment_record and "payment_url" in payment_record:
        payment_url = payment_record["payment_url"]
        logger.info(f"Using original payment URL from database for track_id {track_id}")
    else:
        logger.warning(f"Payment URL not found in database for {track_id}")

    # Calculate underpayment percentage
    underpayment_percent = ((required_amount - received_amount) / required_amount) * 100

    # Get transaction details if available
    txs = []
    original_currencies = set()
    is_auto_converted = False
    original_payment_details = []

    if payment_record and "response_data" in payment_record:
        response_data = payment_record.get("response_data", {})
        if isinstance(response_data, dict) and "txs" in response_data:
            txs = response_data.get("txs", [])

            # Extract original payment details for better display
            for tx in txs:
                tx_currency = tx.get("currency", "Unknown")
                original_currencies.add(tx_currency)

                # Check for auto-converted payments
                if (
                    "auto_convert_amount" in tx
                    and tx.get("auto_convert_amount") is not None
                ):
                    is_auto_converted = True
                    # For auto-converted payments, get the original sent amount
                    original_amount = tx.get("sent_amount", 0)
                    converted_amount = tx.get("auto_convert_amount", 0)

                    # Special handling for USDT auto-conversions where auto_convert_amount might be 0
                    if converted_amount == 0 and tx_currency.upper() == "USDT":
                        converted_amount = original_amount

                    original_payment_details.append(
                        {
                            "original_amount": original_amount,
                            "original_currency": tx_currency,
                            "converted_amount": converted_amount,
                            "converted_currency": "USDT",
                            "method": "auto_converted",
                        }
                    )
                else:
                    # For direct payments, use the transaction amount
                    tx_amount = (
                        tx.get("amount", 0)
                        or tx.get("value", 0)
                        or tx.get("received_amount", 0)
                    )
                    original_payment_details.append(
                        {
                            "original_amount": tx_amount,
                            "original_currency": tx_currency,
                            "converted_amount": (
                                tx_amount if tx_currency.upper() == "USDT" else 0
                            ),
                            "converted_currency": "USDT",
                            "method": "direct",
                        }
                    )

    # Create detailed payment breakdown
    payment_breakdown = ""
    if original_payment_details:
        payment_breakdown += "\n<b>💰 PAYMENT BREAKDOWN</b>\n"
        payment_breakdown += "<b>━━━━━━━━━━━━━━━━━━</b>\n"

        for i, detail in enumerate(original_payment_details, 1):
            original_formatted = format_crypto_amount(
                detail["original_amount"], detail["original_currency"]
            )

            if detail["method"] == "auto_converted":
                converted_formatted = format_crypto_amount(detail["converted_amount"])
                payment_breakdown += f"<b>Payment {i}:</b>\n"
                payment_breakdown += (
                    f"  🔸 <b>Paid:</b> <code>{original_formatted}</code>\n"
                )
                payment_breakdown += (
                    f"  🔸 <b>Converted:</b> <code>{converted_formatted} USDT</code>\n"
                )
            else:
                payment_breakdown += (
                    f"<b>Payment {i}:</b> <code>{original_formatted}</code>\n"
                )
        payment_breakdown += "\n"

    # Prepare additional details for the message
    additional_details = ""
    if is_auto_converted:
        additional_details += (
            f"\u2022 <b>Auto-converted Payment:</b> <code>Yes</code>\n"
        )

    if len(original_currencies) > 0:
        currencies_str = ", ".join(original_currencies)
        additional_details += (
            f"\u2022 <b>Payment Currency:</b> <code>{currencies_str}</code>\n"
        )

    if len(txs) > 1:
        additional_details += (
            f"\u2022 <b>Transaction Count:</b> <code>{len(txs)}</code>\n"
        )

    # Format amounts properly to avoid scientific notation
    received_formatted = format_crypto_amount(received_amount)
    required_formatted = format_crypto_amount(required_amount)
    remaining_formatted = format_crypto_amount(remaining_amount)

    text = format_text(
        "user",
        "payment_underpaid",
        track_id=track_id,
        received=received_formatted,
        required=required_formatted,
        remaining=remaining_formatted,
        underpayment_percent=f"{underpayment_percent:.1f}",
        additional_details=additional_details,
        payment_breakdown=payment_breakdown,
        default=(
            f"⚠️ <b>\u2022 PAYMENT UNDERPAID \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>ADDITIONAL PAYMENT REQUIRED</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 <b>Track ID:</b> <code>{track_id}</code>\n"
            f"\u2022 <b>Amount Received (USDT):</b> <code>${received_formatted}</code>\n"
            f"\u2022 <b>Amount Required (USDT):</b> <code>${required_formatted}</code>\n"
            f"\u2022 <b>Amount Remaining (USDT):</b> <code>${remaining_formatted}</code>\n"
            f"\u2022 <b>Underpayment:</b> <code>{underpayment_percent:.1f}%</code>\n"
            f"{additional_details}"
            f"{payment_breakdown}"
            f"<i>💡 The payment is incomplete. Please pay the remaining amount to complete your transaction.</i>"
        ),
    )

    buttons = []
    if payment_url:
        buttons.append(
            [InlineKeyboardButton(text="💰 Complete Payment", url=payment_url)]
        )

    buttons.extend(
        [
            [
                InlineKeyboardButton(
                    text="🔄 Refresh Status", callback_data=f"verify_oxa:{track_id}"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🛟 Contact Support", callback_data="contact_support"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🏠 Return to Menu", callback_data="return_to_main"
                )
            ],
        ]
    )

    markup = InlineKeyboardMarkup(inline_keyboard=buttons)
    target_message = target.message if isinstance(target, CallbackQuery) else target
    await safe_edit_message(
        target_message, text, reply_markup=markup, parse_mode="HTML"
    )


async def _send_success_message_with_conversion(
    target: Message | CallbackQuery,
    track_id: str,
    actual_paid_amount_usdt: float,
    new_balance: float,
    conversion_info: dict,
    bonus_result: dict = None,
):
    """Send success message to user with currency conversion details."""
    from utils.template_helpers import format_text

    # Format the credited amount properly
    amount_formatted = format_crypto_amount(actual_paid_amount_usdt)
    balance_formatted = format_crypto_amount(new_balance)

    # Generate conversion display with fee rate
    conversion_display = format_conversion_display(conversion_info, PAYMENT_FEE_RATE)

    # Extract transaction hash if available
    tx_hash_display = ""
    conversion_details = conversion_info.get("conversion_details", [])
    if conversion_details:
        for detail in conversion_details:
            tx_hash = detail.get("tx_hash", "")
            if tx_hash and len(tx_hash) > 14:
                tx_hash_display = (
                    f"\u2022 <b>TX:</b> <code>{tx_hash[:8]}...{tx_hash[-6:]}</code>\n"
                )
                break

    # Use enhanced payment confirmation format for consistency with Flask server
    from payments.flask_server import create_enhanced_payment_confirmation
    from payments.currency_converter import format_conversion_display

    # Create conversion display if applicable
    conversion_display_formatted = ""
    conversion_result_data = None
    if conversion_info.get("is_converted", False):
        # Create a conversion result structure for the enhanced display
        conversion_result_data = {
            "is_converted": True,
            "conversion_details": conversion_info.get("conversion_details", [])
        }
        conversion_display_formatted = format_conversion_display(conversion_result_data, 0.0)  # No fee for manual verification

    # Create enhanced payment confirmation message
    text = create_enhanced_payment_confirmation(
        amount_usdt=actual_paid_amount_usdt,
        track_id=track_id,
        new_balance=new_balance,
        bonus_result=bonus_result,
        conversion_result=conversion_result_data,
        conversion_display=conversion_display_formatted
    )

    markup = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="💰 View Balance", callback_data="view_balance"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🏠 Return to Menu", callback_data="return_to_main"
                )
            ],
        ]
    )

    target_message = target.message if isinstance(target, CallbackQuery) else target
    await safe_edit_message(
        target_message, text, reply_markup=markup, parse_mode="HTML"
    )


async def _send_underpaid_message_with_conversion(
    target: Message | CallbackQuery,
    track_id: str,
    received_amount_usdt: float,
    required_amount_usdt: float,
    remaining_amount_usdt: float,
    conversion_info: dict,
):
    """Send underpaid message with currency conversion details."""
    from utils.template_helpers import format_text
    from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

    # Get the payment record from the database
    payment_record = get_payment_by_track_id(track_id)

    # Get payment URL from the payment record
    payment_url = None
    if payment_record and "payment_url" in payment_record:
        payment_url = payment_record["payment_url"]
        logger.info(f"Using original payment URL from database for track_id {track_id}")
    else:
        logger.warning(f"Payment URL not found in database for {track_id}")

    # Calculate underpayment percentage
    underpayment_percent = (
        (required_amount_usdt - received_amount_usdt) / required_amount_usdt
    ) * 100

    # Extract original payment details from conversion info
    conversion_details = conversion_info.get("conversion_details", [])
    original_payment_breakdown = ""
    remaining_breakdown = ""

    if conversion_details:
        original_payment_breakdown += "\n<b>💰 PAYMENT BREAKDOWN</b>\n"
        original_payment_breakdown += "<b>━━━━━━━━━━━━━━━━━━</b>\n"

        total_original_amounts = {}  # Track amounts by currency

        for i, detail in enumerate(conversion_details, 1):
            original_amount = detail.get("original_amount", 0)
            original_currency = detail.get("original_currency", "Unknown")
            converted_amount = detail.get("converted_amount", 0)
            method = detail.get("method", "unknown")

            # Track total amounts by currency for remaining calculation
            if original_currency not in total_original_amounts:
                total_original_amounts[original_currency] = 0
            total_original_amounts[original_currency] += original_amount

            original_formatted = format_crypto_amount(
                original_amount, original_currency
            )
            converted_formatted = format_crypto_amount(converted_amount)

            original_payment_breakdown += f"<b>Payment {i}:</b>\n"
            original_payment_breakdown += (
                f"  🔸 <b>Paid:</b> <code>{original_formatted}</code>\n"
            )
            original_payment_breakdown += (
                f"  🔸 <b>Converted:</b> <code>{converted_formatted} USDT</code>\n"
            )

            if method == "auto_converted":
                original_payment_breakdown += (
                    f"  🔸 <b>Method:</b> <code>Auto-converted</code>\n"
                )
            elif method == "manual_converted":
                original_payment_breakdown += (
                    f"  🔸 <b>Method:</b> <code>Live Rate Conversion</code>\n"
                )

        # Calculate remaining amounts in original currencies
        if total_original_amounts and remaining_amount_usdt > 0:
            remaining_breakdown += "\n<b>💸 REMAINING PAYMENT NEEDED</b>\n"
            remaining_breakdown += "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            remaining_breakdown += f"<b>USDT:</b> <code>{format_crypto_amount(remaining_amount_usdt)} USDT</code>\n"

            # Try to estimate remaining amounts in original currencies
            # This is an approximation based on the conversion rates used
            for currency, total_paid in total_original_amounts.items():
                if currency.upper() != "USDT":
                    # Find the conversion rate from the details
                    for detail in conversion_details:
                        if detail.get("original_currency") == currency:
                            original_amount = detail.get("original_amount", 0)
                            converted_amount = detail.get("converted_amount", 0)
                            if original_amount > 0 and converted_amount > 0:
                                rate = converted_amount / original_amount
                                remaining_in_original = remaining_amount_usdt / rate
                                remaining_formatted = format_crypto_amount(
                                    remaining_in_original, currency
                                )
                                remaining_breakdown += f"<b>{currency}:</b> <code>~{remaining_formatted}</code> <i>(estimated)</i>\n"
                                break

        original_payment_breakdown += "\n"
        if remaining_breakdown:
            remaining_breakdown += "\n"

    # Generate conversion display with fee rate
    conversion_display = format_conversion_display(conversion_info, PAYMENT_FEE_RATE)

    # Prepare conversion summary for the message
    conversion_summary = ""
    if conversion_info.get("is_converted", False):
        conversion_summary = f"\n\n{conversion_display}\n"

    # Format amounts properly
    received_formatted = format_crypto_amount(received_amount_usdt)
    required_formatted = format_crypto_amount(required_amount_usdt)
    remaining_formatted = format_crypto_amount(remaining_amount_usdt)

    text = format_text(
        "user",
        "payment_underpaid_with_conversion",
        track_id=track_id,
        received=received_formatted,
        required=required_formatted,
        remaining=remaining_formatted,
        underpayment_percent=f"{underpayment_percent:.1f}",
        conversion_summary=conversion_summary,
        original_payment_breakdown=original_payment_breakdown,
        remaining_breakdown=remaining_breakdown,
        default=(
            f"⚠️ <b>\u2022 PAYMENT UNDERPAID \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>ADDITIONAL PAYMENT REQUIRED</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 <b>Track ID:</b> <code>{track_id}</code>\n"
            f"\u2022 <b>Amount Received (USDT):</b> <code>${received_formatted}</code>\n"
            f"\u2022 <b>Amount Required (USDT):</b> <code>${required_formatted}</code>\n"
            f"\u2022 <b>Amount Remaining (USDT):</b> <code>${remaining_formatted}</code>\n"
            f"\u2022 <b>Underpayment:</b> <code>{underpayment_percent:.1f}%</code>\n"
            f"{original_payment_breakdown}"
            f"{remaining_breakdown}"
            f"<i>💡 The payment is incomplete. Please pay the remaining amount to complete your transaction.</i>"
        ),
    )

    buttons = []
    if payment_url:
        buttons.append(
            [InlineKeyboardButton(text="💰 Complete Payment", url=payment_url)]
        )

    buttons.extend(
        [
            [
                InlineKeyboardButton(
                    text="🔄 Refresh Status", callback_data=f"verify_oxa:{track_id}"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🛟 Contact Support", callback_data="contact_support"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🏠 Return to Menu", callback_data="return_to_main"
                )
            ],
        ]
    )

    markup = InlineKeyboardMarkup(inline_keyboard=buttons)
    target_message = target.message if isinstance(target, CallbackQuery) else target
    await safe_edit_message(
        target_message, text, reply_markup=markup, parse_mode="HTML"
    )


# Payment status categories for clearer processing
COMPLETED_STATUSES = ["completed", "confirmed", "success", "paid", "manual_accept"]
PENDING_STATUSES = ["pending", "confirming", "waiting", "new", "paying"]
FAILED_STATUSES = ["failed", "canceled", "cancelled", "expired", "invalid", "error"]
UNDERPAID_STATUS = "underpaid"

# Minimum confirmations required for completed payments
MIN_CONFIRMATIONS = 1

# Bitcoin-specific confirmation requirements
BTC_MIN_CONFIRMATIONS = 2

# Default underpayment threshold (85% of required amount)
DEFAULT_UNDERPAYMENT_THRESHOLD = 0.85

# Threshold for treating slightly underpaid payments as completed (99% of required)
SLIGHT_UNDERPAYMENT_THRESHOLD = 0.99


def is_btc_payment(currency: str) -> bool:
    """
    Check if the payment currency is Bitcoin (BTC).

    Args:
        currency: The currency code to check

    Returns:
        bool: True if the currency is BTC, False otherwise
    """
    if not currency:
        return False
    return currency.upper() == "BTC"


def get_required_confirmations(currency: str) -> int:
    """
    Get the required number of confirmations for a given currency.

    Args:
        currency: The currency code

    Returns:
        int: Number of confirmations required
    """
    if is_btc_payment(currency):
        return BTC_MIN_CONFIRMATIONS
    return MIN_CONFIRMATIONS


async def _process_verification_result(
    result: dict, target: Message | CallbackQuery, user_id: int, track_id: str, bot: Bot
):
    """
    Optimized central function to process the result from check_oxapay_payment.
    Handles all payment verification logic with improved performance.
    """
    # Extract and validate API response data
    data_to_parse = _extract_payment_data(result, track_id)
    if not data_to_parse:
        await _send_error_message(
            target, track_id, "API communication failed, no data received."
        )
        return

    # Get payment record from database (single DB call)
    payment_record = get_payment_by_track_id(track_id)
    required_amount = payment_record.get("amount", 0) if payment_record else 0

    # Extract payment status and transactions
    pay_status = str(data_to_parse.get("status", "")).lower()
    txs = data_to_parse.get("txs", [])

    # Get payment amount and currency with conversion (enhanced function)
    actual_paid_amount, currency, conversion_info = (
        await _parse_payment_amount_and_currency_with_conversion(data_to_parse)
    )

    # Log payment details with properly formatted amounts
    amount_formatted = format_crypto_amount(actual_paid_amount)
    required_formatted = format_crypto_amount(required_amount)
    logger.info(
        f"Payment verification for {track_id}: Status={pay_status}, "
        f"Amount=${amount_formatted}, Currency={currency}, "
        f"Required=${required_formatted}, Transactions={len(txs)}"
    )

    # Check for underpayment (except for failed payments)
    # Now using USDT equivalent amounts for accurate comparison
    if pay_status not in FAILED_STATUSES and required_amount > 0:
        # Convert required amount to USDT if needed for comparison
        required_amount_usdt = (
            required_amount  # Assuming stored amounts are already in USDT
        )

        underpayment_result = await _check_for_underpayment_with_conversion(
            target,
            user_id,
            track_id,
            data_to_parse,
            payment_record,
            actual_paid_amount,  # This is now in USDT equivalent
            required_amount_usdt,
            txs,
            currency,
            conversion_info,
            bot,
        )
        if underpayment_result:
            return  # Underpayment was handled, exit early

    # Special handling for "paying" status
    if pay_status == "paying":
        if actual_paid_amount >= required_amount:
            logger.info(
                f"Payment {track_id} has sufficient funds but still in 'paying' status"
            )
            pay_status = "confirming"  # Treat as confirming
        else:
            # If payment is in 'paying' status but doesn't have sufficient funds,
            # check if it's significantly underpaid
            underpayment_threshold = _get_underpayment_threshold(
                data_to_parse, payment_record
            )
            if actual_paid_amount < required_amount * underpayment_threshold:
                logger.warning(
                    f"Payment {track_id} is in 'paying' status but significantly underpaid: "
                    f"Received ${actual_paid_amount:.2f}, Required ${required_amount:.2f}"
                )
                # Handle as underpaid
                remaining_amount = max(0.01, required_amount - actual_paid_amount)
                try:
                    update_payment_status(
                        track_id,
                        "underpaid",
                        actual_paid_amount=actual_paid_amount,
                        required_amount=required_amount,
                        remaining_amount=remaining_amount,
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to update underpaid status for {track_id}: {e}"
                    )

                await _send_underpaid_message(
                    target,
                    track_id,
                    actual_paid_amount,
                    required_amount,
                    remaining_amount,
                )
                return  # Exit early as we've handled the underpayment
            else:
                # Not significantly underpaid, treat as confirming
                logger.info(
                    f"Payment {track_id} is in 'paying' status with partial funds: "
                    f"Received ${actual_paid_amount:.2f}, Required ${required_amount:.2f}"
                )
                pay_status = "confirming"  # Treat as confirming

    # Process based on payment status category
    if pay_status in COMPLETED_STATUSES:
        await _handle_completed_payment(
            target,
            user_id,
            track_id,
            data_to_parse,
            actual_paid_amount,
            currency,
            txs,
            bot,
        )
    elif pay_status in PENDING_STATUSES:
        await _handle_pending_payment(target, track_id, pay_status, currency)
    elif pay_status in FAILED_STATUSES:
        await _handle_failed_payment(
            target, track_id, pay_status, result.get("message", "")
        )
    elif pay_status == UNDERPAID_STATUS:
        await _handle_underpaid_payment(target, track_id)
    else:
        await _handle_unknown_status(
            target, track_id, pay_status, result.get("message", "")
        )


def _extract_payment_data(result: dict, track_id: str) -> dict:
    """Extract and validate payment data from API response."""
    # Check if result contains a nested data object
    if "data" in result and isinstance(result["data"], dict):
        data = result["data"]
    else:
        data = result  # Use the result directly as fallback

    # Validate data
    if not data or not isinstance(data, dict):
        logger.error(f"Invalid payment data for {track_id}: {type(data)}")
        return {}

    return data


async def _check_for_underpayment_with_conversion(
    target,
    user_id,
    track_id,
    data,
    payment_record,
    actual_paid_amount_usdt,
    required_amount_usdt,
    txs,
    currency,
    conversion_info,
    bot,
) -> bool:
    """
    Enhanced underpayment check that works with currency conversion.
    All amounts are expected to be in USDT equivalent for accurate comparison.
    Returns True if underpayment was handled, False otherwise.
    """
    if not txs or actual_paid_amount_usdt <= 0:
        return False  # No transactions or zero amount, not an underpayment

    # Get underpayment threshold from payment data or record
    underpayment_threshold = _get_underpayment_threshold(data, payment_record)

    # Check if payment is underpaid (all amounts now in USDT)
    if actual_paid_amount_usdt < required_amount_usdt * underpayment_threshold:
        # Calculate remaining amount
        remaining_amount = max(0.01, required_amount_usdt - actual_paid_amount_usdt)

        # Log conversion details if available
        conversion_details = conversion_info.get("conversion_details", [])
        conversion_summary = ""
        if conversion_details:
            original_payments = []
            for detail in conversion_details:
                if detail.get("method") in ["auto_converted", "manual_converted"]:
                    original_payments.append(
                        f"{detail['original_amount']:.8f} {detail['original_currency']}"
                    )
            if original_payments:
                conversion_summary = f" (Original: {', '.join(original_payments)})"

        logger.warning(
            f"Underpayment for {track_id}: Received ${actual_paid_amount_usdt:.2f} USDT, "
            f"Required ${required_amount_usdt:.2f} USDT, Remaining ${remaining_amount:.2f} USDT"
            f"{conversion_summary}"
        )

        # Check if payment is just slightly underpaid (within 1%)
        if (
            actual_paid_amount_usdt
            >= required_amount_usdt * SLIGHT_UNDERPAYMENT_THRESHOLD
        ):
            logger.info(
                f"Payment {track_id} is only slightly underpaid (within 1%). "
                f"Processing as completed payment."
            )
            await _process_completed_payment_with_conversion(
                target,
                user_id,
                track_id,
                data,
                actual_paid_amount_usdt,
                currency,
                conversion_info,
                bot,
            )
            return True

        # Update status to underpaid
        try:
            update_payment_status(
                track_id,
                "underpaid",
                actual_paid_amount=actual_paid_amount_usdt,
                required_amount=required_amount_usdt,
                remaining_amount=remaining_amount,
                conversion_data=conversion_info,
            )
        except Exception as e:
            logger.error(f"Failed to update underpaid status for {track_id}: {e}")

        # Send underpaid notification with conversion details
        await _send_underpaid_message_with_conversion(
            target,
            track_id,
            actual_paid_amount_usdt,
            required_amount_usdt,
            remaining_amount,
            conversion_info,
        )
        return True

    return False  # Not underpaid


async def _check_for_underpayment(
    target,
    user_id,
    track_id,
    data,
    payment_record,
    actual_paid_amount,
    required_amount,
    txs,
    currency,
    bot,
) -> bool:
    """
    Legacy underpayment check function - kept for backward compatibility.
    Returns True if underpayment was handled, False otherwise.
    """
    if not txs or actual_paid_amount <= 0:
        return False  # No transactions or zero amount, not an underpayment

    # Get underpayment threshold from payment data or record
    underpayment_threshold = _get_underpayment_threshold(data, payment_record)

    # Check if payment is underpaid
    if actual_paid_amount < required_amount * underpayment_threshold:
        # Calculate remaining amount
        remaining_amount = max(0.01, required_amount - actual_paid_amount)

        logger.warning(
            f"Underpayment for {track_id}: Received ${actual_paid_amount:.2f}, "
            f"Required ${required_amount:.2f}, Remaining ${remaining_amount:.2f}"
        )

        # Check if payment is just slightly underpaid (within 1%)
        if actual_paid_amount >= required_amount * SLIGHT_UNDERPAYMENT_THRESHOLD:
            logger.info(
                f"Payment {track_id} is only slightly underpaid (within 1%). "
                f"Processing as completed payment."
            )
            await _process_completed_payment(
                target, user_id, track_id, data, actual_paid_amount, currency, bot
            )
            return True

        # Update status to underpaid
        try:
            update_payment_status(
                track_id,
                "underpaid",
                actual_paid_amount=actual_paid_amount,
                required_amount=required_amount,
                remaining_amount=remaining_amount,
            )
        except Exception as e:
            logger.error(f"Failed to update underpaid status for {track_id}: {e}")

        # Send underpaid notification
        await _send_underpaid_message(
            target, track_id, actual_paid_amount, required_amount, remaining_amount
        )
        return True

    return False  # Not underpaid


def _get_underpayment_threshold(data: dict, payment_record: dict) -> float:
    """Get underpayment threshold from payment data or record."""
    under_paid_coverage = None

    # Try to get from API response first
    if isinstance(data, dict):
        under_paid_coverage = data.get("under_paid_coverage")

    # If not found in API response, check payment record
    if (
        under_paid_coverage is None
        and payment_record
        and isinstance(payment_record, dict)
    ):
        under_paid_coverage = payment_record.get("under_paid_coverage")

    # Convert coverage percentage to threshold value
    try:
        if under_paid_coverage is not None:
            # Convert percentage to decimal (e.g. 2.5% -> 0.975)
            return 1.0 - (float(under_paid_coverage) / 100.0)
    except (ValueError, TypeError) as e:
        logger.warning(f"Invalid under_paid_coverage value: {under_paid_coverage}, {e}")

    # Default threshold (85%)
    return DEFAULT_UNDERPAYMENT_THRESHOLD


async def _handle_completed_payment(
    target, user_id, track_id, data, amount, currency, txs, bot
):
    """Handle completed payment status."""
    # Check confirmations with currency-specific requirements
    if txs and len(txs) > 0:
        confirmations = min(int(tx.get("confirmations", 0)) for tx in txs)
        required_confirmations = get_required_confirmations(currency)
        if confirmations < required_confirmations:
            logger.warning(
                f"Payment {track_id} has only {confirmations} confirmations, "
                f"minimum required is {required_confirmations} for {currency}"
            )
            await _send_pending_message(target, track_id, "confirming", currency)
            return

    # Process completed payment with conversion support
    logger.info(f"Processing completed payment for {track_id}")

    # Check if we have conversion info available
    if hasattr(data, "conversion_info") or "conversion_info" in locals():
        # Use enhanced processing with conversion info
        conversion_info = getattr(data, "conversion_info", {})
        await _process_completed_payment_with_conversion(
            target, user_id, track_id, data, amount, currency, conversion_info, bot
        )
    else:
        # Fallback to original processing
        await _process_completed_payment(
            target, user_id, track_id, data, amount, currency, bot
        )


async def _handle_pending_payment(target, track_id, pay_status, currency=None):
    """Handle pending payment status."""
    logger.info(f"Payment {track_id} is pending: '{pay_status}' (currency: {currency})")
    try:
        # For 'paying' status, we need to be careful as it might be underpaid
        # The underpayment check is already done in _process_verification_result
        # So here we just update the status and send the message
        update_payment_status(track_id, pay_status)
    except Exception as e:
        logger.error(f"Failed to update pending status for {track_id}: {e}")
    await _send_pending_message(target, track_id, pay_status, currency)


async def _handle_failed_payment(target, track_id, pay_status, error_message):
    """Handle failed payment status."""
    logger.warning(f"Payment {track_id} failed: '{pay_status}'")
    try:
        update_payment_status(track_id, "failed")
    except Exception as e:
        logger.error(f"Failed to update failed status for {track_id}: {e}")
    await _send_failed_message(target, track_id, pay_status, error_message)


async def _handle_underpaid_payment(target, track_id):
    """Handle underpaid payment status."""
    logger.warning(f"Payment {track_id} is underpaid")
    try:
        update_payment_status(track_id, UNDERPAID_STATUS)
    except Exception as e:
        logger.error(f"Failed to update underpaid status for {track_id}: {e}")
    await _send_failed_message(target, track_id, UNDERPAID_STATUS)


async def _handle_unknown_status(target, track_id, pay_status, error_message):
    """Handle unknown payment status."""
    error_msg = error_message or f"Unknown payment status: '{pay_status}'"
    logger.error(f"Unhandled payment status for {track_id}: '{pay_status}'")

    # Mark as failed in DB
    try:
        update_payment_status(track_id, "failed")
    except Exception as e:
        logger.error(f"Failed to update status to failed for {track_id}: {e}")

    await _send_error_message(target, track_id, error_msg)


async def verify_payment_status(
    track_id: str, user_id: int, bot: Bot, target: Message | CallbackQuery
) -> None:
    """
    Verify payment status and process it if completed.

    Args:
        track_id: Payment tracking ID
        user_id: User ID of the requester
        bot: Bot instance for sending messages
        target: Target message or callback query
    """
    # Start performance timing
    start_time = time.time()

    # Validate track_id format first
    if not track_id or not isinstance(track_id, str) or len(track_id) < 5:
        logger.warning(f"Invalid track_id format: {track_id}")
        await _send_error_message(target, None, "Invalid payment ID format.")
        return

    # Get target message for updates
    target_message = target.message if isinstance(target, CallbackQuery) else target
    if not target_message:
        logger.error(f"Invalid target message for track_id: {track_id}")
        return

    # Show verifying message first
    try:
        verifying_message = format_text(
            "user",
            "verifying_payment",
            track_id=track_id,
            default=(
                f"⏳ <b>\u2022 VERIFYING PAYMENT \u2022</b>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>PROCESSING REQUEST</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"\u2022 <b>Track ID:</b> <code>{track_id}</code>\n\n"
                f"<i>Please wait while we verify your payment...</i>"
            ),
        )

        await safe_edit_message(
            target_message,
            verifying_message,
            parse_mode="HTML",
            reply_markup=None,  # Remove buttons while verifying
        )
    except Exception as e:
        logger.error(f"Error displaying verification message: {e}", exc_info=True)
        # Continue with verification even if displaying message fails

    # Check if payment exists in database
    try:
        payment_db = get_payment_by_track_id(track_id)
        if not payment_db:
            logger.warning(f"Verification requested for unknown trackId: {track_id}")
            await _send_error_message(
                target, track_id, "Payment record not found in our system."
            )
            return

        # Check if payment belongs to requesting user
        if payment_db.get("user_id") != user_id:
            logger.warning(
                f"User {user_id} trying to verify track_id {track_id} belonging to user {payment_db.get('user_id')}"
            )
            await _send_error_message(
                target, track_id, "This payment ID is not associated with your account."
            )
            return

        # Check if payment has already been processed
        if payment_db.get("status") == "completed":
            logger.info(
                f"Payment {track_id} already processed, sending already verified message"
            )
            await _send_already_verified_message(target, track_id, payment_db)
            return
    except Exception as db_err:
        logger.error(
            f"Database error checking payment {track_id}: {db_err}", exc_info=True
        )
        await _send_error_message(
            target, track_id, "Error accessing payment information."
        )
        return

    # Make API call to check payment status
    try:
        with PerformanceTimer(f"payment_verification_api_{track_id}", "api_call"):
            result = await check_oxapay_payment(track_id)

        api_duration = time.time() - start_time  # Total time so far

        processing_start_time = time.time()
        await _process_verification_result(result, target, user_id, track_id, bot)
        processing_duration = time.time() - processing_start_time

        total_duration = time.time() - start_time

        # Log performance metrics
        logger.info(
            f"Payment verification completed for {track_id}: "
            f"API call: {api_duration:.2f}s, Processing: {processing_duration:.2f}s, "
            f"Total: {total_duration:.2f}s"
        )

    except Exception as e:
        total_duration = time.time() - start_time
        # Log the full exception but don't expose details to user
        logger.error(
            f"Unexpected error during verification API call for {track_id} after {total_duration:.2f}s: {e}",
            exc_info=True,
        )
        await _send_error_message(
            target,
            track_id,
            "A system error occurred while verifying your payment. Please try again later.",
        )


# --- Callback Handlers ---


@router.callback_query(F.data == "verify_latest_payment")
async def verify_latest_payment_options(callback_query: CallbackQuery):
    """Show options to verify payment."""
    await callback_query.answer()
    # Get payment verification options message from template
    from utils.template_helpers import format_text

    verification_options = format_text(
        "user",
        "payment_verification_options",
        default=(
            "✅ <b>\u2022 PAYMENT VERIFICATION \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>VERIFICATION OPTIONS</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            "<i>Choose how to verify your payment:</i>\n\n"
            "\u2022 <b>Automatic:</b> Check your most recent deposit\n"
            "\u2022 <b>Manual:</b> Enter a specific payment Track ID\n\n"
            "<i>Select an option below to proceed.</i>"
        ),
    )

    await safe_edit_message(
        callback_query.message,
        verification_options,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔄 Verify Latest Payment",
                        callback_data="auto_verify_payment",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔢 Enter Payment Track ID", callback_data="enter_track_id"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🏠 Return to Menu", callback_data="return_to_main"
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )


@router.callback_query(F.data.startswith("verify_oxa:"))
async def handle_verify_callback(callback_query: CallbackQuery, bot: Bot):
    """Handle verification initiated by callback (e.g., from deposit message or refresh button)."""
    await callback_query.answer(text="✨ Verifying payment...", show_alert=False)
    user_id = callback_query.from_user.id

    try:
        track_id = callback_query.data.split(":", 1)[1]
        if not track_id:
            raise IndexError("Empty track ID")
    except IndexError:
        logger.error(
            f"Invalid verify_oxa callback data for user {user_id}: {callback_query.data}"
        )
        if callback_query.message:
            await safe_edit_message(
                callback_query.message, "❌ Error: Invalid verification request format."
            )
        return

    # Immediately update the message to show verification is in progress
    if callback_query.message:
        await safe_edit_message(
            callback_query.message,
            "⏳ <b>Verifying Payment...</b>\n\n"
            f"🔢 Track ID: <code>{track_id}</code>\n"
            "📡 Checking with payment gateway...\n\n"
            "<i>This may take up to 15 seconds</i>",
            reply_markup=None,
        )

    await verify_payment_status(track_id, user_id, bot, callback_query)


@router.callback_query(F.data == "auto_verify_payment")
async def handle_auto_verify(callback_query: CallbackQuery, bot: Bot):
    """Automatically verify the user's latest payment."""
    await callback_query.answer(text="✨ Checking latest payment...", show_alert=False)
    user_id = callback_query.from_user.id
    target_message = callback_query.message

    if not target_message:
        logger.error("Cannot perform auto-verify: target_message is None.")
        return

    # Get latest payment track_id from DB
    try:
        logger.info(f"Auto-verify: Fetching latest payment for user_id: {user_id}")
        payment = get_latest_payment(user_id)

        if not payment or "track_id" not in payment:
            # No recent payments found
            no_payments_message = format_text(
                "user",
                "no_recent_payments",
                default=(
                    "❌ <b>\u2022 NO RECENT PAYMENTS \u2022</b>\n\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    "<b>VERIFICATION FAILED</b>\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    "<i>We couldn't find any recent deposits to verify automatically.</i>\n\n"
                    "<i>Please make a deposit first or try manual verification if you have a Track ID.</i>"
                ),
            )

            await safe_edit_message(
                target_message,
                no_payments_message,
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="💎 Make Deposit", callback_data="deposit_funds"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🔢 Enter Track ID", callback_data="enter_track_id"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Return to Menu", callback_data="return_to_main"
                            )
                        ],
                    ]
                ),
                parse_mode="HTML",
            )
            return

        track_id = payment["track_id"]

        # Validate track ID format
        min_len, max_len = 5, 64
        if (
            not track_id
            or not isinstance(track_id, str)
            or not (min_len <= len(track_id) <= max_len)
        ):
            logger.error(f"Auto-verify: Invalid track_id format: {track_id}")
            await safe_edit_message(
                target_message,
                "❌ Error: Invalid payment ID format in your latest payment.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔢 Enter Track ID Manually",
                                callback_data="enter_track_id",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏠 Return to Menu", callback_data="return_to_main"
                            )
                        ],
                    ]
                ),
            )
            return

        logger.info(
            f"Auto-verification requested for latest track_id: {track_id} by user: {user_id}"
        )
        await verify_payment_status(track_id, user_id, bot, callback_query)

    except Exception as e:
        logger.error(
            f"Database error getting latest payment for user {user_id}: {e}",
            exc_info=True,
        )
        await safe_edit_message(
            target_message,
            "❌ Error accessing payment history.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Try Again", callback_data="verify_latest_payment"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🏠 Return to Menu", callback_data="return_to_main"
                        )
                    ],
                ]
            ),
        )
        return


# --- Manual Track ID Input Handlers ---


@router.callback_query(F.data == "enter_track_id")
async def request_manual_track_id(callback_query: CallbackQuery, state: FSMContext):
    """Ask user to enter a track ID."""
    await callback_query.answer()
    await state.set_state(PaymentVerificationStates.waiting_for_track_id)
    if callback_query.message:  # Ensure message exists
        await safe_edit_message(
            callback_query.message,
            "🔢 <b>\u2022 PAYMENT TRACK ID \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>MANUAL VERIFICATION</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            "\u2022 <b>Instructions:</b> Please reply with your payment Track ID\n"
            "\u2022 <b>Format:</b> A string of 5-64 characters\n\n"
            "<i>You can find your Track ID in the payment receipt when the payment link was generated. "
            "It typically starts with 'OXA-' followed by numbers and letters.</i>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel", callback_data="cancel_verification"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
    else:
        logger.error("Cannot request manual track id: callback_query.message is None.")


@router.callback_query(
    F.data == "cancel_verification",
    StateFilter(PaymentVerificationStates.waiting_for_track_id),
)
async def cancel_manual_verification_callback(
    callback_query: CallbackQuery, state: FSMContext
):
    """Cancel manual verification via callback button while waiting for input."""
    await callback_query.answer("Cancelled.")
    current_state = await state.get_state()
    logger.info(
        f"Cancelling manual verification from state {current_state} for user {callback_query.from_user.id}"
    )
    await clear_state_data(state)
    if callback_query.message:  # Check message exists
        await safe_edit_message(
            callback_query.message,
            "✅ <b>\u2022 VERIFICATION CANCELLED \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>OPERATION ABORTED</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            "<i>Manual verification request has been cancelled.</i>\n"
            "<i>Please select an option below to continue.</i>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Verify Latest Payment",
                            callback_data="auto_verify_payment",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔢 Try Manual Verification Again",
                            callback_data="enter_track_id",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🏠 Return to Menu", callback_data="return_to_main"
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )


@router.message(StateFilter(PaymentVerificationStates.waiting_for_track_id), F.text)
async def process_manual_track_id_input(message: Message, state: FSMContext, bot: Bot):
    """Process the manually entered track ID."""
    track_id_input = message.text.strip()
    user_id = message.from_user.id

    # Validate track ID format
    min_len, max_len = 5, 64
    if not track_id_input or not (min_len <= len(track_id_input) <= max_len):
        await message.reply(
            "❌ <b>\u2022 INVALID TRACK ID \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>VERIFICATION ERROR</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 <b>Issue:</b> The Track ID format is invalid\n"
            f"\u2022 <b>Required:</b> {min_len}-{max_len} characters\n\n"
            "<i>Please ensure you've copied the complete Track ID from your payment receipt.</i>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Try Again",
                            callback_data="enter_track_id",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🏠 Return to Menu", callback_data="return_to_main"
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )
        # Clear state only if validation fails
        await clear_state_data(state)
        return

    # Clear state after successful validation
    await clear_state_data(state)

    track_id = track_id_input
    logger.info(
        f"Manual verification requested for track_id: {track_id} by user: {user_id}"
    )

    try:
        # Display verifying message first to provide immediate feedback
        verifying_msg = await message.reply(
            "⏳ <b>\u2022 VERIFYING PAYMENT \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>PROCESSING REQUEST</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 <b>Track ID:</b> <code>{track_id}</code>\n\n"
            "<i>Please wait while we communicate with the payment gateway...</i>",
            parse_mode="HTML",
            reply_markup=None,
        )

        # Process the payment verification
        await verify_payment_status(track_id, user_id, bot, verifying_msg)

    except Exception as e:
        logger.error(
            f"Error processing manual track ID verification for {track_id}, user {user_id}: {e}",
            exc_info=True,
        )
        # Send error message to user
        await message.reply(
            "❌ <b>\u2022 VERIFICATION ERROR \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>SYSTEM ISSUE</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            "\u2022 <b>Issue:</b> Error processing your verification request\n"
            "\u2022 <b>Details:</b> A technical problem occurred during verification\n\n"
            "<i>Our team has been notified. Please try again later or contact support.</i>",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Try Again", callback_data="enter_track_id"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🛟 Contact Support", callback_data="contact_support"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🏠 Return to Menu", callback_data="return_to_main"
                        )
                    ],
                ]
            ),
        )


# --- Registration ---


def register_payment_verification_handlers(dp):
    """Register payment verification handlers with the dispatcher."""
    try:
        dp.include_router(router)
        logger.info("Payment verification router included successfully.")
    except Exception as e:
        logger.error(
            f"Failed to include payment verification router: {e}", exc_info=True
        )
