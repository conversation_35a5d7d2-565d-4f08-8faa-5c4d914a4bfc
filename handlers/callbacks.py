"""
Payment verification callbacks module.

This module focuses specifically on payment verification callbacks.
Other handlers have been moved to their respective modules:
- Common actions -> common_callbacks.py
- User-specific actions -> user.py
- Admin-specific actions -> admin.py
"""

from aiogram import Router, types, F
from aiogram.fsm.context import FSMContext
from aiogram.types import (
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    CallbackQuery,
    Message,
)
from datetime import datetime
import asyncio
import json
import logging
from typing import Union, Dict, Any, Tuple, Optional

# Import the payment verification function
from payments.oxa_verify import check_oxapay_payment

# Import CallbackData for payment verification
from utils.callback_factories import PaymentVerificationCallback

from database.operations import (
    get_user_balance_async,
    update_user_balance_async,
    get_payment_by_track_id,
    update_payment_status,
    get_user_balance,
    update_user_balance,
    add_transaction,
    get_latest_payment,
)
from states.states import DepositStates
from keyboards.deposit_kb import (
    payment_verification_keyboard,
    payment_success_keyboard,
    payment_processing_keyboard,
)

router = Router()
# Set name for the router to make debugging easier
router.name = "payment_verification_router"

# ----- Constants for UI Templates -----
HEADER_DIVIDER = "<b>━━━━━━━━━━━━━━━━━━</b>"
STATUS_EMOJIS = {
    "completed": "✅",
    "pending": "⏳",
    "new": "⏳",
    "waiting": "⏳",
    "paying": "⏳",
    "expired": "⏰",
    "error": "❌",
    "verifying": "🔄",
    "unknown": "⚠️",
}

# ----- Helper Functions for Message Templates -----

def format_payment_message(title: str, amount: float, track_id: str, status_text: str,
                          additional_text: str = "", status_emoji: str = "🔄") -> str:
    """Format consistent payment status messages with optimized styling."""
    base_message = (
        f"{status_emoji} <b>{title}</b>\n\n"
        f"{HEADER_DIVIDER}\n"
        f"💵 <b>Amount:</b> <code>${amount:.2f}</code>\n"
        f"🆔 <b>Track ID:</b> <code>{track_id}</code>\n"
        f"🔄 <b>Status:</b> <code>{status_text}</code>"
    )

    if additional_text:
        base_message += f"\n\n{additional_text}"

    return base_message

def format_error_message(title: str, amount: float, error_text: str,
                        additional_text: str = "") -> str:
    """Format consistent error messages with optimized styling."""
    base_message = (
        f"❌ <b>{title}</b>\n\n"
        f"{HEADER_DIVIDER}\n"
        f"💵 <b>Amount:</b> <code>${amount:.2f}</code>\n"
        f"⚠️ <b>Issue:</b> {error_text}"
    )

    if additional_text:
        base_message += f"\n\n{additional_text}"

    return base_message

def format_success_message(amount: float, new_balance: float) -> str:
    """Format success messages with optimized styling."""
    return (
        f"✅ <b>DEPOSIT SUCCESSFUL!</b>\n\n"
        f"{HEADER_DIVIDER}\n"
        f"💵 <b>Amount:</b> <code>${amount:.2f}</code>\n"
        f"💰 <b>New Balance:</b> <code>${new_balance:.2f}</code>\n\n"
        f"✨ <i>Your funds have been added to your account and are ready to use!</i>"
    )

async def process_payment_status(
    status: str, 
    callback_query: CallbackQuery, 
    track_id: str, 
    amount: float,
    user_id: int,
    state: FSMContext
) -> None:
    """Process different payment statuses with appropriate UI updates"""
    
    # Status handlers dictionary for cleaner code organization
    status_handlers = {
        "completed": handle_completed_payment,
        "pending": handle_pending_payment,
        "new": handle_in_progress_payment,
        "waiting": handle_in_progress_payment,
        "paying": handle_in_progress_payment,
        "expired": handle_expired_payment,
    }
    
    # Get the appropriate handler or use default
    handler = status_handlers.get(status, handle_unknown_status)
    
    # Call the handler
    await handler(callback_query, track_id, amount, user_id, state, status)

# ----- Status Handler Functions -----

async def handle_completed_payment(
    callback_query: CallbackQuery, 
    track_id: str, 
    amount: float,
    user_id: int,
    state: FSMContext,
    status: str
) -> None:
    """Handle successful payment completion"""
    # Validate amount before updating
    if amount <= 0:
        await callback_query.message.edit_text(
            format_error_message(
                "Invalid Amount", 
                amount, 
                "Cannot process zero or negative amount.", 
                "Please try again with a valid payment amount."
            ),
            reply_markup=payment_verification_keyboard(),
            parse_mode="HTML",
        )
        await state.clear()
        return

    # First update UI to show we're processing the balance update
    await callback_query.message.edit_text(
        format_payment_message(
            "Payment Confirmed!", 
            amount, 
            track_id, 
            "COMPLETED", 
            "🔄 <i>Updating your account balance...</i>",
            "✅"
        ),
        parse_mode="HTML",
    )

    try:
        # Get current balance and update it
        try:
            current_balance = await get_user_balance_async(user_id)
            new_balance = current_balance + amount

            # Update user balance in database
            await update_user_balance_async(user_id, new_balance)
        except AttributeError:
            # Fallback to sync methods if async is not available
            current_balance = get_user_balance(user_id)
            new_balance = current_balance + amount
            update_user_balance(user_id, new_balance)

        # Add transaction record - make this non-blocking for faster response
        try:
            # Create a task for transaction logging but don't wait for it
            asyncio.create_task(
                _async_add_transaction(
                    user_id, amount, track_id, callback_query.bot
                )
            )
        except Exception as e:
            logging.error(f"Failed to create transaction task: {str(e)}")
            # Non-critical error, continue with the UI update

        # Send success message
        await callback_query.message.edit_text(
            format_success_message(amount, new_balance),
            reply_markup=payment_success_keyboard(),
            parse_mode="HTML",
        )

        # Record successful completion in metrics
        try:
            from utils.metrics import record_payment_completion
            asyncio.create_task(record_payment_completion(user_id, amount, "success"))
        except ImportError:
            pass

        # Clear state
        await state.clear()
        
    except Exception as e:
        logging.error(f"Error handling completed payment: {e}", exc_info=True)
        await handle_payment_error(
            callback_query, track_id, amount, 
            f"Error updating balance: {str(e)}",
            "Please contact support with your transaction ID."
        )
        await state.clear()

async def handle_pending_payment(
    callback_query: CallbackQuery, 
    track_id: str, 
    amount: float,
    user_id: int,
    state: FSMContext,
    status: str
) -> None:
    """Handle pending payment status"""
    await callback_query.message.edit_text(
        format_payment_message(
            "Payment Processing", 
            amount, 
            track_id, 
            "PENDING", 
            "Your payment is being processed. This usually takes just a few moments.\n"
            "Please check again shortly.",
            "⏳"
        ),
        reply_markup=payment_processing_keyboard(track_id),
        parse_mode="HTML",
    )
    
    # Don't clear state so the user can check again

async def handle_in_progress_payment(
    callback_query: CallbackQuery, 
    track_id: str, 
    amount: float,
    user_id: int,
    state: FSMContext,
    status: str
) -> None:
    """Handle payments that are in progress but not yet complete"""
    status_descriptions = {
        "new": "The payer has not selected the payment currency yet.",
        "waiting": "The payer has selected the payment currency. Awaiting payment.",
        "paying": "The payer is attempting to complete the invoice payment."
    }
    
    await callback_query.message.edit_text(
        format_payment_message(
            "Payment In Progress", 
            amount, 
            track_id, 
            status.upper(), 
            f"<i>{status_descriptions.get(status, '')}</i>\n\n"
            f"Please check again in a moment.",
            "⏳"
        ),
        reply_markup=payment_processing_keyboard(track_id),
        parse_mode="HTML",
    )
    
    # Don't clear state so the user can check again

async def handle_expired_payment(
    callback_query: CallbackQuery, 
    track_id: str, 
    amount: float,
    user_id: int,
    state: FSMContext,
    status: str
) -> None:
    """Handle expired payment status"""
    # Update payment status in database
    update_payment_status(track_id, "failed")
    
    await callback_query.message.edit_text(
        format_payment_message(
            "Payment Expired", 
            amount, 
            track_id, 
            "EXPIRED", 
            "This payment has expired. You can try again with a new payment.",
            "⏰"
        ),
        reply_markup=payment_verification_keyboard(),
        parse_mode="HTML",
    )
    
    # Clear state
    await state.clear()

async def handle_unknown_status(
    callback_query: CallbackQuery, 
    track_id: str, 
    amount: float,
    user_id: int,
    state: FSMContext,
    status: str
) -> None:
    """Handle unknown payment status"""
    # Update payment status in database with the actual status
    update_payment_status(track_id, status)
    
    await callback_query.message.edit_text(
        format_payment_message(
            f"Payment Status: {status.upper()}", 
            amount, 
            track_id, 
            status.upper(), 
            "Your payment is in an unknown state.\n"
            "You can try again or contact support.",
            "⚠️"
        ),
        reply_markup=payment_verification_keyboard(),
        parse_mode="HTML",
    )
    
    # Clear state
    await state.clear()

async def handle_payment_error(
    callback_query: CallbackQuery, 
    track_id: str, 
    amount: float,
    error_msg: str,
    additional_text: str = "Please try again in a moment."
) -> None:
    """Handle payment verification errors"""
    await callback_query.message.edit_text(
        format_error_message(
            "Verification Error", 
            amount, 
            error_msg, 
            additional_text
        ),
        reply_markup=payment_processing_keyboard(track_id),
        parse_mode="HTML",
    )

# Update the check_payment function to ensure it properly handles all outcomes
@router.callback_query(F.data.startswith("check_payment:"))
async def check_payment(callback_query: types.CallbackQuery, state: FSMContext):
    """Check if the payment is completed."""
    # Provide immediate feedback that we received the click
    await callback_query.answer(text="Checking payment status...", show_alert=False)

    # Extract the track ID from callback data
    track_id = callback_query.data.split(":")[1]
    user_id = callback_query.from_user.id

    # Log the verification attempt with structured data
    logging.info(
        "Payment verification attempt", 
        extra={
            "track_id": track_id, 
            "user_id": user_id, 
            "timestamp": datetime.now().isoformat()
        }
    )

    # Get payment details from database
    payment_record = get_payment_by_track_id(track_id)

    if not payment_record:
        logging.error(f"Payment record not found for track_id: {track_id}")
        await callback_query.message.edit_text(
            format_error_message(
                "Payment Record Not Found", 
                0.0, 
                "Payment not in our records.", 
                "Please try again or contact support if the issue persists."
            ),
            reply_markup=payment_verification_keyboard(),
            parse_mode="HTML",
        )
        await state.clear()
        return

    # Get amount from payment record
    amount = payment_record.get("amount", 0)

    try:
        # First immediately update the UI to show we're processing the request
        await callback_query.message.edit_text(
            format_payment_message(
                "Verifying Payment", 
                amount, 
                track_id, 
                "CHECKING", 
                "<i>Checking with payment processor...</i>",
                "🔄"
            ),
            parse_mode="HTML",
        )

        # Now perform the actual verification asynchronously
        verification_data = await check_oxapay_payment(track_id)
        logging.info(f"Verification response for {track_id}: {verification_data}")

        if verification_data.get("status") == "success":
            # Extract payment data with proper fallbacks for different API response structures
            payment_data = verification_data.get("data", {})

            # Handle nested 'data' field or direct status field
            if isinstance(payment_data.get("data"), dict):
                payment_status = payment_data.get("data", {}).get("status")
            else:
                payment_status = payment_data.get("status")

            # Convert numeric status to string for consistent comparison
            if payment_status == 200:
                payment_status = "completed"

            # Normalize status to lowercase if it's a string
            if isinstance(payment_status, str):
                payment_status = payment_status.lower()

            # Update payment status in database
            update_payment_status(track_id, payment_status)
            
            # Process the payment status using our handler functions
            await process_payment_status(
                payment_status, callback_query, track_id, amount, user_id, state
            )
                
        else:
            # API error
            error_msg = verification_data.get("message", "Unknown error")
            await handle_payment_error(
                callback_query, track_id, amount, error_msg
            )
                
    except Exception as e:
        logging.error(f"Payment verification error: {str(e)}", exc_info=True)
        await handle_payment_error(
            callback_query, track_id, amount,
            "Connection problem",
            "An error occurred while checking your payment.\nPlease try again in a moment."
        )

# Helper function to handle transaction logging asynchronously
async def _async_add_transaction(user_id, amount, track_id, bot):
    """Process transaction logging in the background"""
    try:
        # Record start time for performance monitoring
        start_time = datetime.now()
        
        # Add transaction record
        add_transaction(user_id, "deposit", amount, invoice_id=track_id)

        # Log the successful payment
        try:
            from utils.logger import log_payment

            await log_payment(
                bot=bot,
                user_id=user_id,
                amount=amount,
                invoice_id=track_id,
            )
        except ImportError:
            logging.warning(
                "Could not import log_payment function, payment logging skipped"
            )
        except Exception as e:
            logging.error(f"Error logging payment: {str(e)}")
            
        # Log performance metrics
        execution_time = (datetime.now() - start_time).total_seconds()
        logging.info(
            f"Transaction logging completed in {execution_time:.2f}s",
            extra={"track_id": track_id, "execution_time": execution_time}
        )
            
    except Exception as e:
        logging.error(
            f"Background transaction processing error: {str(e)}", 
            extra={"track_id": track_id, "user_id": user_id}
        )
        # This runs in the background, so no user-facing error handling needed

# Handler for verify_payment - more efficient implementation
@router.callback_query(F.data.startswith("verify_payment:"))
async def verify_payment(callback_query: types.CallbackQuery, state: FSMContext):
    """Verify a payment based on its identifier."""
    # Extract the track ID from callback data
    track_id = callback_query.data.split(":")[1]
    
    # Directly modify the callback_query object to avoid creating a new one
    callback_query.data = f"check_payment:{track_id}"
    
    # Call the check_payment handler directly
    await check_payment(callback_query, state)