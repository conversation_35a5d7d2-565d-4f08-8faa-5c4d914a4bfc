from aiogram import <PERSON><PERSON>, Router, F, Dispatcher
from aiogram.fsm.context import FSMContext
from aiogram.types import (
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    CallbackQuery,
    Message,
)
from aiogram.filters import Command
from utils.image_handler import (
    process_product_image,
    download_telegram_image,
    CATEGORY_IMAGES_FOLDER,
    download_file_from_telegram,
)

# Change relative imports to absolute imports
from database.operations import (
    is_owner,
    get_user_role,
    get_all_admins,
    add_admin,
    remove_admin,
    users_collection,
    transactions_collection,
    products_collection,
    admins_collection,
    categories_collection,
    save_log_channel,
    get_user,
    save_announcement,
    get_recent_announcements,
    get_all_user_ids,
    get_all_categories,
    add_category,
    get_category_by_id,
    update_category,
    delete_category,
    get_products_by_category,
    get_maintenance_mode,
    save_maintenance_mode,
)

from handlers.user import get_faqs

# Add import for is_privileged function
from handlers.sys_db import is_privileged
from keyboards.admin_kb import (
    admin_management_keyboard,
    system_stats_keyboard,
    additional_features_keyboard,
    admin_main_keyboard,
    announcement_confirmation_keyboard,
    after_announcement_keyboard,
    category_management_keyboard,
    faq_management_keyboard,
    maintenance_mode_keyboard,
)
from config import OWNER_ID
from datetime import datetime, timedelta
from states.states import (
    AdminManagementStates,
    AdditionalFeaturesStates,
    AnnouncementStates,
    CategoryManagementStates,
    FAQManagementStates,
)
from utils.logger import log_admin_action
from utils.local_file_handling import save_file, generate_file_path
from utils.template_helpers import format_text
from utils.state_helpers import safe_update_data, clear_state_data

import random
import logging
import string
import re
import os
from bson import ObjectId

# Import the template handlers from the templates package
from templates import (
    manage_templates,
    view_templates,
    edit_templates_list,
    show_template_keys,
    edit_template_key,
    process_template_value,
)
from utils.template_manager import TemplateManager

logger = logging.getLogger(__name__)
router = Router()


# Add this function to sanitize filenames
def sanitize_filename(filename):
    """
    Sanitize a filename by removing special characters and creating a short distinctive name.

    Args:
        filename (str): The original filename

    Returns:
        str: A sanitized filename with category-friendly format
    """
    # Get the file extension
    _, ext = os.path.splitext(filename)
    if not ext:
        ext = ".jpg"  # Default extension if none provided

    # Get the basename without extension
    basename = os.path.basename(filename).replace(ext, "")

    # Remove special characters, keep only alphanumeric, underscore, and hyphen
    basename = re.sub(r"[^\w\-]", "", basename)

    # Trim to a short length (max 10 chars for basename)
    if len(basename) > 10:
        basename = basename[:10]

    # Add short timestamp (just MMDD-HHMM format) for uniqueness
    timestamp = datetime.now().strftime("%m%d-%H%M")

    # Add a short random string (4 chars) for additional uniqueness
    random_suffix = "".join(random.choices(string.ascii_lowercase + string.digits, k=4))

    # Combine with extension - format: basename_MMDD-HHMM_xxxx.ext
    return f"{basename}_{timestamp}_{random_suffix}{ext.lower()}"


# Callback query handler for managing admins
@router.callback_query(F.data == "owner_manage_admins")
async def owner_manage_admins(callback_query: CallbackQuery):
    """Owner panel for admin management."""
    user_id = callback_query.from_user.id

    # Allow privileged users with owner role to access this handler
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()

    # Get all admins list
    admins = get_all_admins()
    admin_count = len(admins) if admins else 0

    # Get admin management message from template
    admin_management_message = format_text(
        "owner",
        "owner_manage_admins",
        admin_count=admin_count,
        default=(
            "👑 <b>\u2022 ADMIN MANAGEMENT \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "<b>STAFF CONTROLS</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "<i>Manage your administrative team:</i>\n\n"
            f"Current admin count: <b>{admin_count}</b>\n\n"
            "\u2022 Add new administrators to your team\n"
            "\u2022 Remove administrative access when needed\n"
            "\u2022 View complete list of system administrators\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Select an option from the menu below to proceed.</i>"
        ),
    )

    await callback_query.message.edit_text(
        admin_management_message,
        reply_markup=admin_management_keyboard(),
        parse_mode="HTML",
    )


# Add admin menu handler (this was missing in the original code)
@router.callback_query(F.data == "add_admin")
async def add_admin_menu(callback_query: CallbackQuery, state: FSMContext):
    """Menu for adding a new admin."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="🚫 <b>Only the owner can add admins</b>",
            show_alert=True,
            parse_mode="HTML",
        )
        return

    await callback_query.answer()

    # Get add admin message from template
    add_admin_message = format_text(
        "owner",
        "add_admin",
        default=(
            "👤 <b>\u2022 ADD NEW ADMIN \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "<b>GRANT PRIVILEGES</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "<i>Please enter the user ID of the person you want to make an admin.</i>\n\n"
            "\u2022 The user must have used the bot at least once\n"
            "\u2022 User ID should be a numeric value\n"
            "\u2022 New admins will have immediate access\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Type the User ID below or use the Cancel button to return.</i>"
        ),
    )

    await callback_query.message.edit_text(
        add_admin_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="❌ Cancel", callback_data="owner_manage_admins"
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )

    await state.set_state(AdminManagementStates.waiting_for_admin_id)


# Remove the duplicate handler for "manage_admins" or redirect it to the main handler
@router.callback_query(F.data == "manage_admins")
async def manage_admins_redirect(callback_query: CallbackQuery):
    """Redirect to the main admin management handler."""
    await owner_manage_admins(callback_query)


# System stats handler
@router.callback_query(F.data == "system_stats")
async def system_stats(callback_query: CallbackQuery):
    """Show system statistics to owner or admin."""
    user_id = callback_query.from_user.id

    # Check if user is owner or admin
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()

    # Collect statistics
    total_users = users_collection.count_documents({})
    total_admins = admins_collection.count_documents({})
    total_products = products_collection.count_documents({})
    total_transactions = transactions_collection.count_documents({})
    total_categories = categories_collection.count_documents({})

    # Get sales data
    sales = transactions_collection.find({"type": "purchase"})
    total_sales = sum(sale.get("amount", 0) for sale in sales)

    # Get last 24h stats
    one_day_ago = datetime.now() - timedelta(days=1)
    new_users_24h = users_collection.count_documents(
        {"created_at": {"$gte": one_day_ago}}
    )

    sales_24h = transactions_collection.find(
        {"type": "purchase", "timestamp": {"$gte": one_day_ago}}
    )
    sales_amount_24h = sum(sale.get("amount", 0) for sale in sales_24h)

    # Current time for display
    current_time = datetime.now().strftime("%Y-%m-%d %H:%M")

    # Get system stats message from template
    stats_data = {
        "total_users": f"{total_users:,}",
        "total_admins": total_admins,
        "new_users_24h": new_users_24h,
        "total_categories": total_categories,
        "total_products": total_products,
        "total_transactions": f"{total_transactions:,}",
        "total_sales": f"${total_sales:,.2f}",
        "sales_amount_24h": f"${sales_amount_24h:,.2f}",
        "current_time": current_time,
    }

    system_stats_message = format_text(
        "owner",
        "system_stats",
        **stats_data,
        default=(
            "📊 <b>\u2022 SYSTEM DASHBOARD \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "<b>USERS & COMMUNITY</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "\u2022 Total Users: <code>{total_users}</code>\n"
            "\u2022 Administrators: <code>{total_admins}</code>\n"
            "\u2022 New Users (24h): <code>{new_users_24h}</code>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "<b>SHOP STATISTICS</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "\u2022 Categories: <code>{total_categories}</code>\n"
            "\u2022 Products: <code>{total_products}</code>\n"
            "\u2022 Transactions: <code>{total_transactions}</code>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "<b>FINANCIAL SUMMARY</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "\u2022 Total Revenue: <code>{total_sales}</code>\n"
            "\u2022 Recent Sales (24h): <code>{sales_amount_24h}</code>\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Last updated: {current_time}</i>"
        ),
    )

    await callback_query.message.edit_text(
        system_stats_message,
        reply_markup=system_stats_keyboard(),
        parse_mode="HTML",
    )


# Message handler for admin ID input
@router.message(AdminManagementStates.waiting_for_admin_id)
async def process_admin_id(message: Message, state: FSMContext, bot: Bot):
    """Process the admin ID input."""
    user_id = message.from_user.id

    # Fix: Allow privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        return

    try:
        # Try to convert input to int
        new_admin_id = int(message.text.strip())

        # Check if user exists in system
        user = users_collection.find_one({"user_id": new_admin_id})
        if not user:
            await message.reply(
                "⚠️ <b>User not found</b>\n\n"
                "The user must have used the bot at least once.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Admin Management",
                                callback_data="owner_manage_admins",
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
            await clear_state_data(state)
            return

        # Check if already an admin
        if admins_collection.find_one({"user_id": new_admin_id}):
            await message.reply(
                "⚠️ <b>User is already an admin.</b>",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Admin Management",
                                callback_data="owner_manage_admins",
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
            await clear_state_data(state)
            return

        # Store in state and confirm
        await safe_update_data(state, new_admin_id=new_admin_id)

        # Get user's name and username for display
        user_name = user.get("name", f"User {new_admin_id}")
        user_username = user.get("username", "")

        # Format the display text
        display_text = f"<b>{user_name}</b>"
        if user_username:
            display_text += f" (@{user_username})"

        confirmation_message = (
            "➕ <b>\u2022 CONFIRM ADMIN ADDITION \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"Are you sure you want to add {display_text} as an admin?\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "This action will grant them administrative privileges.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Please confirm your action.</i>"
        )

        await message.reply(
            confirmation_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="✅ Confirm", callback_data="confirm_add_admin"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel", callback_data="owner_manage_admins"
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )

    except ValueError:
        # Handle invalid input
        await message.reply(
            "⚠️ <b>Invalid Input</b>\n\n" "Please enter a valid User ID (numeric only).",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel", callback_data="owner_manage_admins"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )


# Modification for confirm_add_admin function:
@router.callback_query(F.data == "confirm_add_admin")
async def confirm_add_admin(callback_query: CallbackQuery, state: FSMContext, bot: Bot):
    """Confirm adding a new admin."""
    user_id = callback_query.from_user.id

    # Fix: Allow privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()

    # Get data from state
    data = await state.get_data()
    new_admin_id = data.get("new_admin_id")

    if not new_admin_id:
        await callback_query.message.edit_text(
            "❌ <b>Error: Admin ID not found.</b>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Admin Management",
                            callback_data="owner_manage_admins",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        await clear_state_data(state)
        return

    # First check if the user exists in the database
    user_data = get_user(new_admin_id)
    if not user_data:
        await callback_query.message.edit_text(
            "❌ <b>Error: User not found.</b>\n\n"
            "The user must interact with the bot at least once before being promoted.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Admin Management",
                            callback_data="owner_manage_admins",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        await clear_state_data(state)
        return

    # Add the admin
    success = add_admin(new_admin_id, added_by=user_id)

    if not success:
        await callback_query.message.edit_text(
            "❌ <b>Error: Failed to add admin.</b>\n\n"
            "The user may already be an admin.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Admin Management",
                            callback_data="owner_manage_admins",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        await state.clear()
        return

    # Try to notify the new admin
    try:
        notification_message = (
            "💎 <b>\u2022 ADMINISTRATIVE ACCESS GRANTED \u2022</b> 💎\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "<b>SYSTEM NOTIFICATION</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "<i>You have been promoted to administrator by the owner.</i>\n\n"
            "With this role, you now have access to:\n"
            "\u2022 Administrative control panel\n"
            "\u2022 Product management capabilities\n"
            "\u2022 User management tools\n"
            "\u2022 System configuration options\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>To access your new privileges, use the /start command.</i>"
        )
        await bot.send_message(
            chat_id=new_admin_id,
            text=notification_message,
            parse_mode="HTML",
        )
    except Exception as e:
        # Log the exception but continue - don't block the admin addition if notification fails
        logger.error(
            f"Failed to notify new admin {new_admin_id} about promotion: {str(e)}"
        )

    # Clear state
    await state.clear()

    # Get the username for display if available
    display_name = user_data.get("name", f"User {new_admin_id}")
    if user_data.get("username"):
        display_name += f" (@{user_data.get('username')})"

    # Log admin action
    # Import the enhanced log_admin_action from admin handlers
    from handlers.admin import log_admin_action
    await log_admin_action(
        user_id, "add_admin", f"Added admin: {display_name}", new_admin_id
    )

    await callback_query.message.edit_text(
        f"✅ <b>Admin Added:</b> {display_name} has been successfully promoted.",
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Admin Management",
                        callback_data="owner_manage_admins",
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )


# Handler for removing admin menu
@router.callback_query(F.data == "remove_admin")
async def remove_admin_menu(callback_query: CallbackQuery, state: FSMContext):
    """Menu for removing an admin."""
    user_id = callback_query.from_user.id

    # Fix: Allow privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="🚫 <b>Only the owner can remove admins</b>",
            show_alert=True,
            parse_mode="HTML",
        )
        return

    await callback_query.answer()

    # Get all admins
    admins = get_all_admins()

    if not admins:
        await callback_query.message.edit_text(
            "❌ <b>No admins found to remove.</b>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="owner_manage_admins"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    # Create keyboard with admin options
    inline_keyboard = []

    for admin in admins:
        admin_id = admin.get("user_id")
        # Skip if this is the owner
        if admin_id == OWNER_ID:
            continue

        # Get admin name and username if available
        admin_name = admin.get("name", "Unknown")
        admin_username = admin.get("username", "")

        # Format display text
        display_text = f"{admin_name}" if admin_name != "Unknown" else f"ID: {admin_id}"
        if admin_username:
            display_text += f" (@{admin_username})"
        else:
            display_text += f" (ID: {admin_id})"

        inline_keyboard.append(
            [
                InlineKeyboardButton(
                    text=display_text,
                    callback_data=f"pre_confirm_remove_admin:{admin_id}",
                )
            ]
        )

    if not inline_keyboard:
        await callback_query.message.edit_text(
            "❌ <b>No admins to remove</b> (excluding owner).",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="owner_manage_admins"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    inline_keyboard.append(
        [InlineKeyboardButton(text="🔙 Back", callback_data="owner_manage_admins")]
    )

    keyboard = InlineKeyboardMarkup(inline_keyboard=inline_keyboard)

    message_text = (
        "➖ <b>\u2022 REMOVE ADMIN \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>SELECT ADMIN TO REVOKE PRIVILEGES</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Please select an administrator from the list below to remove their administrative rights.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Choose an admin to proceed with removal.</i>"
    )

    await callback_query.message.edit_text(
        message_text, reply_markup=keyboard, parse_mode="HTML"
    )


# Add a new handler for the pre-confirmation step
@router.callback_query(F.data.startswith("pre_confirm_remove_admin:"))
async def pre_confirm_remove_admin(callback_query: CallbackQuery, state: FSMContext):
    """Request confirmation before removing an admin."""
    user_id = callback_query.from_user.id

    # Fix: Allow privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="🚫 <b>Only the owner can remove admins</b>",
            show_alert=True,
            parse_mode="HTML",
        )
        return

    await callback_query.answer()

    # Extract admin ID from callback data
    admin_id_to_remove = int(callback_query.data.split(":")[1])  # Renamed for clarity

    # Get admin details for confirmation
    admin = get_user(admin_id_to_remove)  # Use the extracted ID
    admin_name = admin.get("name", "Unknown")
    admin_username = admin.get("username", "")

    display_text = (
        f"<b>{admin_name}</b>"
        if admin_name != "Unknown"
        else f"<b>ID: {admin_id_to_remove}</b>"
    )
    if admin_username:
        display_text += f" (@{admin_username})"

    # Store admin ID in state for the confirmation handler
    await state.update_data(admin_id_to_remove=admin_id_to_remove)

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm Removal",
                    callback_data=f"confirm_remove_admin:{admin_id_to_remove}",  # Pass the ID here
                )
            ],
            [InlineKeyboardButton(text="❌ Cancel", callback_data="remove_admin")],
        ]
    )

    confirmation_message = (
        "➖ <b>\u2022 CONFIRM ADMIN REMOVAL \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"⚠️ Are you sure you want to remove {display_text} as an admin?\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "This action is irreversible and will revoke all their administrative privileges.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Please confirm your decision.</i>"
    )

    await callback_query.message.edit_text(
        confirmation_message, reply_markup=keyboard, parse_mode="HTML"
    )


# Callback query handler for listing all admins
@router.callback_query(F.data == "list_all_admins")
async def list_all_admins(callback_query: CallbackQuery):
    """List all admins for owner view."""
    user_id = callback_query.from_user.id

    # Fix: Allow privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="🚫 <b>Only the owner can view the admin list.</b>",
            show_alert=True,
            parse_mode="HTML",
        )
        return

    await callback_query.answer()

    # Get all admins
    admins = get_all_admins()

    if not admins:
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Admin Management",
                        callback_data="owner_manage_admins",
                    )
                ]
            ]
        )

        await callback_query.message.edit_text(
            "❌ <b>No admins found in the database.</b>",
            reply_markup=keyboard,
            parse_mode="HTML",
        )
        return

    # Create admin list message
    message_text = "📋 <b>\u2022 ALL ADMINISTRATORS \u2022</b>\n\n"
    message_text += "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
    message_text += "<b>LIST OF CURRENT STAFF</b>\n"
    message_text += "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"

    for i, admin in enumerate(admins, 1):
        admin_id = admin.get("user_id")
        admin_name = admin.get("name", "")
        admin_username = admin.get("username", "")
        is_owner_text = (
            " 🌟(Owner)" if admin.get("is_owner", False) or admin_id == OWNER_ID else ""
        )

        # Check if added_at exists and is not None
        added_at = admin.get("added_at")
        if added_at is not None and isinstance(added_at, datetime):
            added_at_str = added_at.strftime("%Y-%m-%d")
        else:
            added_at_str = "🕒 Unknown"

        added_by = (
            f" by Admin ID: {admin.get('added_by')}" if admin.get("added_by") else ""
        )

        # Format with name as the primary identifier (use ID if name is empty or "Unknown")
        display_name = (
            admin_name if admin_name and admin_name != "Unknown" else "👤 Admin"
        )

        message_text += (
            f"<b>{i}. {display_name}{is_owner_text}</b>\n"
            f"   🔑 ID: <code>{admin_id}</code>\n"
        )

        if admin_username:
            message_text += f"   📱 Username: <code>@{admin_username}</code>\n"

        message_text += f"   🗓️ Added: {added_at_str}{added_by}\n"
        if i < len(admins):  # Add separator if not the last admin
            message_text += "<code>┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈</code>\n\n"
        else:
            message_text += "\n"

    message_text += "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
    message_text += "✨ <i>This is the complete list of administrators.</i>"

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔙 Back to Admin Management",
                    callback_data="owner_manage_admins",
                )
            ]
        ]
    )

    await callback_query.message.edit_text(
        message_text, reply_markup=keyboard, parse_mode="HTML"
    )


# Callback query handler for confirming admin removal
@router.callback_query(F.data.startswith("confirm_remove_admin:"))
async def confirm_remove_admin(
    callback_query: CallbackQuery, bot: Bot
):  # Removed state: FSMContext as it's not used
    """Confirm and execute admin removal."""
    user_id = callback_query.from_user.id

    # Fix: Allow privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="🚫 <b>Only the owner can remove admins</b>",
            show_alert=True,
            parse_mode="HTML",
        )
        return

    await callback_query.answer()

    # Extract admin ID
    admin_id_to_remove = int(callback_query.data.split(":")[1])  # Renamed for clarity

    # Get admin details before removal for notification
    admin_data = admins_collection.find_one({"user_id": admin_id_to_remove})

    # Remove the admin
    success = remove_admin(admin_id_to_remove)

    # Always prepare the keyboard
    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔙 Back to Admin Management",
                    callback_data="owner_manage_admins",
                )
            ]
        ]
    )

    if success:
        # Try to notify the removed admin
        try:
            if admin_data:
                notification_message = (
                    "🛡️ <b>\u2022 PRIVILEGES REVOKED \u2022</b> 🛡️\n\n"
                    "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
                    "<b>SYSTEM NOTIFICATION</b>\n"
                    "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                    "Your administrative privileges have been revoked by the shop owner.\n\n"
                    "You no longer have access to administrative functions.\n\n"
                    "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
                    "✨ <i>If you believe this is an error, please contact the owner.</i>"
                )
                await bot.send_message(
                    chat_id=admin_id_to_remove,
                    text=notification_message,
                    parse_mode="HTML",
                )
        except Exception as e:
            # Log the exception but continue
            logger.error(
                f"Failed to notify admin {admin_id_to_remove} about removal: {str(e)}"
            )

        # Display success message
        admin_name = (
            admin_data.get("name", f"ID: {admin_id_to_remove}")
            if admin_data
            else f"ID: {admin_id_to_remove}"
        )
        await callback_query.message.edit_text(
            f"✅ <b>Admin Removed:</b> {admin_name} has been successfully demoted.",
            reply_markup=keyboard,
            parse_mode="HTML",
        )
    else:
        await callback_query.message.edit_text(
            f"❌ <b>Failed to remove admin.</b>\n\nThe user might not be an admin or an error occurred.",
            reply_markup=keyboard,
            parse_mode="HTML",
        )


# Callback query handler for additional features menu
@router.callback_query(F.data == "additional_features")
async def show_additional_features(callback_query: CallbackQuery):
    """Show additional features menu to the owner."""
    user_id = callback_query.from_user.id

    # Fix: Allow privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="🚫 <b>Only the owner can access this menu</b>",
            show_alert=True,
            parse_mode="HTML",
        )
        return

    await callback_query.answer()

    message_text = (
        "🛠️ <b>\u2022 ADDITIONAL FEATURES \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>ADVANCED CONFIGURATION</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Explore further customization and management options for your bot.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Choose an option from below:</i>"
    )

    await callback_query.message.edit_text(
        message_text,
        reply_markup=additional_features_keyboard(),
        parse_mode="HTML",
    )


# Callback query handler for FAQ management
@router.callback_query(F.data == "manage_faqs")
async def manage_faqs(callback_query: CallbackQuery):
    """Show FAQ management menu."""
    user_id = callback_query.from_user.id

    # Check if user is owner or admin
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()

    message_text = (
        "❓ <b>\u2022 FAQ MANAGEMENT \u2022</b> ❓\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>KNOWLEDGE BASE CONTROL</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Manage the Frequently Asked Questions displayed to users:\n\n"
        "\u2022 View current FAQs\n"
        "\u2022 Add new FAQ entries\n"
        "\u2022 Edit existing FAQs\n"
        "\u2022 Delete FAQ entries\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Select an option below:</i>"
    )

    await callback_query.message.edit_text(
        message_text,
        reply_markup=faq_management_keyboard(),
        parse_mode="HTML",
    )


# Callback query handler for setting log channel
@router.callback_query(F.data == "set_log_channel")
async def set_log_channel(callback_query: CallbackQuery, state: FSMContext):
    """Handle setting log channel ID."""
    user_id = callback_query.from_user.id

    # Fix: Allow privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="🚫 <b>Only the owner can set the log channel</b>",
            show_alert=True,
            parse_mode="HTML",
        )
        return

    await callback_query.answer()

    message_text = (
        "📢 <b>\u2022 SET LOG CHANNEL \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>ACTIVITY LOGGING SETUP</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Please forward a message from the desired channel or enter the channel ID "
        "(e.g., <code>-1001234567890</code>) where you want logs to be sent.\n\n"
        "<b>Note:</b> Make sure the bot is an admin in the channel with permission to post messages.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Provide channel information to continue.</i>"
    )

    await callback_query.message.edit_text(
        message_text,
        parse_mode="HTML",
        # Assuming you want a cancel button here, if not, remove reply_markup
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="❌ Cancel", callback_data="additional_features"
                    )
                ]
            ]
        ),
    )

    await state.set_state(AdditionalFeaturesStates.waiting_for_log_channel)


# Message handler for channel ID input
@router.message(AdditionalFeaturesStates.waiting_for_log_channel)
async def process_channel_id(message: Message, state: FSMContext, bot: Bot):
    """Process the channel ID input."""
    owner_id = message.from_user.id

    # Fix: Allow privileged users with owner role
    if not (is_owner(owner_id) or is_privileged(owner_id, role="owner")):
        await message.reply(
            "🚫 <b>Only the owner can set the log channel</b>", parse_mode="HTML"
        )
        await clear_state_data(state)
        return

    # Try to extract channel ID
    channel_id = None

    # Check if it's a forwarded message
    if message.forward_from_chat and message.forward_from_chat.type == "channel":
        channel_id = message.forward_from_chat.id
    else:
        # Try to parse as direct ID input
        try:
            # Remove any '@' if present
            text = message.text.strip()
            if text.startswith("@"):
                await message.reply(
                    "⚠️ <b>Invalid Input</b>\n\n"
                    "Please provide the numeric channel ID instead of the username. "
                    "You can forward a message from the channel instead.",
                    parse_mode="HTML",
                )
                return
            elif text.startswith("-100") or text.startswith(
                "-"
            ):  # Allow negative IDs directly
                channel_id = int(text)
            else:
                # This logic for prepending -100 might be problematic if user enters a group ID like -12345
                # Better to expect the full -100... ID for public channels or just the negative ID for groups/private channels
                await message.reply(
                    "⚠️ <b>Invalid Channel ID Format</b>\n\n"
                    "For public channels, ID usually starts with <code>-100...</code>. "
                    "For private channels/groups, it's a negative number.\n"
                    "Please provide the full numeric ID or forward a message.",
                    parse_mode="HTML",
                )
                return

        except ValueError:
            await message.reply(
                "⚠️ <b>Invalid Channel ID Format</b>\n\n"
                "Please try again with a numeric ID or forward a message.",
                parse_mode="HTML",
            )
            return

    if not channel_id:
        await message.reply(
            "⚠️ <b>Could not identify a valid channel ID.</b>\n\n" "Please try again.",
            parse_mode="HTML",
        )
        return

    # Try to send a test message to verify the bot has access
    try:
        test_message = (
            "🔄 <b>\u2022 LOG CHANNEL TEST \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "<b>VERIFICATION MESSAGE</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "This is a test message to verify that the bot can send messages to this channel.\n"
            "If you see this message, the channel has been successfully configured as the log channel.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>No further action needed if this message is visible.</i>"
        )
        await bot.send_message(
            chat_id=channel_id,
            text=test_message,
            parse_mode="HTML",
        )

        # If successful, save the channel ID
        save_log_channel(channel_id)

        # Notify owner
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Additional Features",
                        callback_data="additional_features",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🏛️ Back to Admin Panel",
                        callback_data="back_to_admin",  # Assuming "back_to_admin" exists
                    )
                ],
            ]
        )

        await message.reply(
            f"✅ <b>Log Channel Set!</b>\n\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"<b>Channel ID:</b> <code>{channel_id}</code>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "The bot will now send important activity logs to this channel.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Configuration complete.</i>",
            reply_markup=keyboard,
            parse_mode="HTML",
        )

        # Log this action
        await log_admin_action(
            owner_id, "set_log_channel", f"Channel ID: {channel_id}"
        )

    except Exception as e:
        # Bot doesn't have permission or channel doesn't exist
        logger.error(f"Error setting log channel {channel_id}: {str(e)}")
        await message.reply(
            f"❌ <b>Error: Could not send messages to the channel.</b>\n\n"
            f"<code>Error: {str(e)}</code>\n\n"
            "Please make sure:\n"
            "1. The bot is added to the channel as an administrator.\n"
            "2. The bot has permission to post messages.\n"
            "3. The channel ID (<code>{channel_id}</code>) is correct.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Please check the settings and try again.</i>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Try Again", callback_data="set_log_channel"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Additional Features",
                            callback_data="additional_features",
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )

    await clear_state_data(state)


# Announcement handlers


@router.callback_query(F.data == "send_announcement")
async def start_announcement(callback_query: CallbackQuery, state: FSMContext):
    """Start the process of creating an announcement."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="🚫 <b>Only the owner can send announcements</b>",
            show_alert=True,
            parse_mode="HTML",
        )
        return

    await callback_query.answer()

    message_text = (
        "📣 <b>\u2022 CREATE ANNOUNCEMENT \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>DRAFT YOUR MESSAGE</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Please enter the message you want to send to all users. "
        "This will be broadcast to everyone who has interacted with the bot.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>You can use HTML formatting. Use <b>/n</b> for new lines.</i>"
    )
    await callback_query.message.edit_text(
        message_text,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="❌ Cancel", callback_data="additional_features"
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )

    await state.set_state(AnnouncementStates.waiting_for_message)


@router.message(AnnouncementStates.waiting_for_message)
async def process_announcement_message(message: Message, state: FSMContext):
    """Process the announcement message."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    announcement_text = message.html_text  # Use html_text to preserve formatting
    if (
        not announcement_text or not message.text.strip()
    ):  # Check actual text content too
        await message.reply(
            "⚠️ <b>Invalid Input</b>\n\nAnnouncement text cannot be empty.",
            parse_mode="HTML",
        )
        return

    await safe_update_data(state, announcement_text=announcement_text)
    user_count = len(get_all_user_ids())

    confirmation_message = (
        "📨 <b>\u2022 CONFIRM ANNOUNCEMENT \u2022</b>\n\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"The following message will be sent to <b>{user_count}</b> users.\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"<b>Message Preview:</b>\n{announcement_text}\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Are you sure you want to proceed? This action cannot be undone.</i>"
    )
    await message.reply(
        confirmation_message,
        reply_markup=announcement_confirmation_keyboard(),
        parse_mode="HTML",
    )
    await state.set_state(AnnouncementStates.confirm_send)


@router.callback_query(
    F.data == "confirm_announcement", AnnouncementStates.confirm_send
)
async def send_announcement_to_all(
    callback_query: CallbackQuery, state: FSMContext, bot: Bot
):
    """Send the announcement to all users."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    data = await state.get_data()
    announcement_text = data.get("announcement_text", "")

    if not announcement_text:
        error_message = (
            "❌ <b>\u2022 ANNOUNCEMENT FAILED \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "<b>Error:</b> No announcement text was found in the current session.\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "Please try creating the announcement again.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Click 'Try Again' to restart the process.</i>"
        )
        await callback_query.message.edit_text(
            error_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Try Again", callback_data="send_announcement"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel", callback_data="additional_features"
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )
        await clear_state_data(state)
        return

    user_ids = get_all_user_ids()
    unique_user_ids = set(user_ids)
    user_count = len(unique_user_ids)

    progress_text = (
        "📡 <b>\u2022 SENDING ANNOUNCEMENT \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>BROADCAST IN PROGRESS</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"Initializing broadcast to <b>{user_count}</b> users.\n"
        "Please wait..."
    )
    progress_message = await callback_query.message.edit_text(
        progress_text,
        parse_mode="HTML",
    )

    save_announcement(
        admin_id=user_id, message=announcement_text, recipients_count=user_count
    )
    await log_admin_action(
        user_id, "send_announcement", f"To {user_count} users"
    )

    success_count = 0
    error_count = 0
    user_message_text = (
        "📣 <b>\u2022 OFFICIAL ANNOUNCEMENT \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"{announcement_text}\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "✨ <i>Thank you for your attention.</i>"
    )

    for i, recipient_id in enumerate(unique_user_ids):
        try:
            await bot.send_message(
                recipient_id,
                user_message_text,
                parse_mode="HTML",
                disable_web_page_preview=True,  # Good practice for mass sends
            )
            success_count += 1
        except Exception:
            error_count += 1

        if (i + 1) % 20 == 0 or (i + 1) == user_count:  # Update every 20 or at the end
            try:
                current_progress_text = (
                    "📡 <b>\u2022 SENDING ANNOUNCEMENT \u2022</b>\n\n"
                    "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
                    "<b>BROADCAST IN PROGRESS</b>\n"
                    "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                    f"Progress: {i+1}/{user_count} users ({round((i+1)/user_count*100)}%)\n"
                    f"Sent: {success_count}, Failed: {error_count}"
                )
                await progress_message.edit_text(
                    current_progress_text,
                    parse_mode="HTML",
                )
            except Exception:  # Ignore if edit fails (e.g. message not modified)
                pass

    final_report_text = (
        "✅ <b>\u2022 ANNOUNCEMENT SENT \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>DELIVERY REPORT</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"Successfully delivered to: <b>{success_count}</b> users.\n"
        f"Failed deliveries: <b>{error_count}</b> users.\n\n"
        "The announcement has been recorded.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>You can view past announcements from the menu.</i>"
    )
    await progress_message.edit_text(
        final_report_text,
        reply_markup=after_announcement_keyboard(),
        parse_mode="HTML",
    )
    await clear_state_data(state)


@router.callback_query(F.data == "cancel_announcement", AnnouncementStates.confirm_send)
async def cancel_announcement(callback_query: CallbackQuery, state: FSMContext):
    """Cancel sending the announcement."""
    await callback_query.answer()
    cancel_message = (
        "🚫 <b>\u2022 ANNOUNCEMENT CANCELLED \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "The announcement process has been successfully cancelled.\n"
        "No messages were sent.\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>"
    )
    await callback_query.message.edit_text(
        cancel_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Additional Features",
                        callback_data="additional_features",
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )
    await clear_state_data(state)


@router.callback_query(F.data == "view_announcements")
async def view_past_announcements(callback_query: CallbackQuery):
    """View past announcements."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            text="🚫 <b>Only the owner can view announcements.</b>",
            show_alert=True,
            parse_mode="HTML",
        )
        return

    await callback_query.answer()
    announcements = get_recent_announcements(limit=10)

    if not announcements:
        empty_message = (
            "📂 <b>\u2022 ANNOUNCEMENT HISTORY \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "⚠️ <b>No past announcements found.</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "There are no recorded announcements in the system yet.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>You can create a new announcement using the button below.</i>"
        )
        await callback_query.message.edit_text(
            empty_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="📣 Create Announcement",
                            callback_data="send_announcement",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Additional Features",
                            callback_data="additional_features",
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )
        return

    announcements_text = (
        "📜 <b>\u2022 PAST ANNOUNCEMENTS \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>RECENT BROADCASTS</b> (Max 10)\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
    )
    for i, ann in enumerate(
        announcements, 1
    ):  # Renamed 'announcement' to 'ann' to avoid conflict
        timestamp_obj = ann.get("timestamp")
        if isinstance(
            timestamp_obj, str
        ):  # Basic check if it's a string from dummy data
            timestamp_str = timestamp_obj
        else:
            timestamp_str = timestamp_obj.strftime("%Y-%m-%d %H:%M")

        recipients = ann.get("recipients_count", 0)
        admin_id = ann.get("admin_id", "Unknown")
        message_preview = ann.get("message", "")
        if len(message_preview) > 70:  # Adjusted preview length
            message_preview = message_preview[:67] + "..."

        announcements_text += (
            f"<b>{i}. Announcement from {timestamp_str}</b>\n"
            f"   🗣️ By: Admin <code>{admin_id}</code>\n"
            f"   🎯 Recipients: <b>{recipients}</b>\n"
            f"   💬 Message: <i>{message_preview}</i>\n"
        )
        if i < len(announcements):
            announcements_text += "<code>┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈</code>\n\n"
        else:
            announcements_text += "\n"

    announcements_text += (
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>This is a list of the most recent announcements.</i>"
    )
    await callback_query.message.edit_text(
        announcements_text,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="📣 Create New Announcement",
                        callback_data="send_announcement",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Additional Features",
                        callback_data="additional_features",
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "manage_categories")
async def manage_categories(callback_query: CallbackQuery):
    """Admin panel for category management."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    message_text = (
        "📂 <b>\u2022 CATEGORY MANAGEMENT \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>ORGANIZE YOUR PRODUCTS</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Manage your product categories to help customers find items easily.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Select an option below:</i>"
    )
    await callback_query.message.edit_text(
        message_text,
        reply_markup=category_management_keyboard(),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "list_categories")
async def list_categories(callback_query: CallbackQuery):
    """List all product categories."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    categories = get_all_categories()

    if not categories:
        empty_message = (
            "📂 <b>\u2022 PRODUCT CATEGORIES \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "⚠️ <b>No categories found in the system.</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "You can start by adding your first category.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Click '➕ Add Category' to begin.</i>"
        )
        await callback_query.message.edit_text(
            empty_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="➕ Add Category", callback_data="add_category"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="manage_categories"
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )
        return

    text = (
        "📋 <b>\u2022 ALL CATEGORIES \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>MANAGE EXISTING & ADD NEW</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Click a category below to view/edit its details, or add a new one.\n"
        "Each button shows the category name and product count (🛍️ Count).\n"
    )
    buttons = []
    current_row = []

    for category in categories:
        category_id = category.get("_id") or category.get("id")
        category_name = category.get("name", "Unnamed")
        product_count = len(get_products_by_category(category_id))

        current_row.append(
            InlineKeyboardButton(
                text=f"📂 {category_name} (🛍️ {product_count})",
                callback_data=f"edit_category:{category_id}",  # Assuming edit_category handler exists
            )
        )
        if len(current_row) == 2 or category == categories[-1]:
            buttons.append(current_row.copy())
            current_row = []

    if current_row:  # Add any remaining button in the last row
        buttons.append(current_row)

    text += "\n▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
    text += "✨ <i>Select an action from the buttons below.</i>"

    buttons.append(
        [InlineKeyboardButton(text="➕ Add Category", callback_data="add_category")]
    )
    buttons.append(
        [InlineKeyboardButton(text="🔙 Back", callback_data="manage_categories")]
    )
    await callback_query.message.edit_text(
        text,
        reply_markup=InlineKeyboardMarkup(inline_keyboard=buttons),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "add_category")
async def add_category_start(callback_query: CallbackQuery, state: FSMContext):
    """Start the process of adding a new category."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    message_text = (
        "➕ <b>\u2022 ADD NEW CATEGORY \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "📝 <b>STEP 1: CATEGORY NAME</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Please enter a name for the new product category.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Choose a clear and concise name.</i>"
    )
    await callback_query.message.edit_text(
        message_text,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="❌ Cancel", callback_data="manage_categories"
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(CategoryManagementStates.waiting_for_category_name)


@router.message(CategoryManagementStates.waiting_for_category_name)
async def process_category_name_input(
    message: Message, state: FSMContext
):  # Renamed function
    """Process the category name input."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    category_name = message.text.strip()

    if not category_name:
        await message.reply(
            "⚠️ <b>Invalid Input</b>\n\nCategory name cannot be empty. Please try again.",
            parse_mode="HTML",
        )
        return

    categories = get_all_categories()
    if any(cat.get("name", "").lower() == category_name.lower() for cat in categories):
        await message.reply(
            "⚠️ <b>Name Exists</b>\n\nA category with this name already exists. Please use a different name.",
            parse_mode="HTML",
        )
        return

    await safe_update_data(state, category_name=category_name)
    image_prompt_message = (
        "➕ <b>\u2022 ADD NEW CATEGORY \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "🖼️ <b>STEP 2: CATEGORY IMAGE (OPTIONAL)</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"<b>Category Name:</b> {category_name}\n\n"
        "Would you like to add an image for this category?\nThis can help it stand out.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Choose an option or skip to the next step.</i>"
    )
    await message.reply(
        image_prompt_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔗 Add Image URL", callback_data="add_category_image_url"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="📷 Upload Image",
                        callback_data="add_category_image_upload",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="⏩ Skip Image", callback_data="skip_category_image"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="❌ Cancel", callback_data="manage_categories"
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )
    # Set state to wait for image option, or description if skipped
    await state.set_state(
        CategoryManagementStates.waiting_for_category_description
    )  # Assuming image options set this or skip


@router.callback_query(F.data == "add_category_image_url")
async def add_category_image_url(callback_query: CallbackQuery, state: FSMContext):
    """Add image URL for the category."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    state_data = await state.get_data()
    category_name = state_data.get("category_name", "N/A")
    message_text = (
        "🖼️ <b>\u2022 CATEGORY IMAGE: URL \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>PROVIDE IMAGE LINK</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"<b>Category Name:</b> {category_name}\n\n"
        "Please enter a direct URL for the category image.\n(e.g., https://example.com/image.jpg)\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Ensure the URL points directly to an image file (JPG, PNG, WEBP).</i>"
    )
    await callback_query.message.edit_text(
        message_text,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Options",  # More specific back
                        callback_data="back_to_image_options",
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(CategoryManagementStates.waiting_for_image_url)


@router.callback_query(F.data == "back_to_image_options")
async def back_to_image_options(callback_query: CallbackQuery, state: FSMContext):
    """Go back to image options."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    state_data = await state.get_data()
    category_name = state_data.get("category_name", "N/A")
    image_prompt_message = (
        "➕ <b>\u2022 ADD NEW CATEGORY \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "🖼️ <b>STEP 2: CATEGORY IMAGE (OPTIONAL)</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"<b>Category Name:</b> {category_name}\n\n"
        "Would you like to add an image for this category?\nThis can help it stand out.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Choose an option or skip to the next step.</i>"
    )
    await callback_query.message.edit_text(
        image_prompt_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔗 Add Image URL", callback_data="add_category_image_url"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="📷 Upload Image",
                        callback_data="add_category_image_upload",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="⏩ Skip Image", callback_data="skip_category_image"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="❌ Cancel", callback_data="manage_categories"
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )
    # Reset state to where it expects image options or description
    await state.set_state(CategoryManagementStates.waiting_for_category_description)


@router.message(CategoryManagementStates.waiting_for_image_url)
async def process_category_image_url_input(
    message: Message, state: FSMContext
):  # Renamed
    """Process the category image URL input."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    image_url = message.text.strip()
    if not image_url:
        await message.reply(
            "⚠️ <b>Invalid Input</b>\n\nImage URL cannot be empty. Please try again or skip.",
            parse_mode="HTML",
        )
        return

    if not (image_url.startswith("http://") or image_url.startswith("https://")):
        await message.reply(
            "⚠️ <b>Invalid URL Format</b>\n\nPlease enter a valid URL starting with http:// or https://.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Options",
                            callback_data="back_to_image_options",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    valid_extensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"]
    if not any(image_url.lower().endswith(ext) for ext in valid_extensions):
        await message.reply(
            "⚠️ <b>Not An Image Link</b>\n\nURL doesn't appear to be a direct image link. Ensure it ends with .jpg, .png, etc.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Options",
                            callback_data="back_to_image_options",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    await safe_update_data(state, image_type="url", image_url=image_url)
    state_data = await state.get_data()  # Get data again after update for name
    description_prompt_message = (
        "➕ <b>\u2022 ADD NEW CATEGORY \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "📝 <b>STEP 3: CATEGORY DESCRIPTION (OPTIONAL)</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"<b>Category Name:</b> {state_data.get('category_name', 'N/A')}\n"
        "<b>Image:</b> URL provided\n\n"
        "Please provide a short description for this category.\nThis can help users understand its content.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Enter a description or skip this step.</i>"
    )
    await message.reply(
        description_prompt_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="⏩ Skip Description",
                        callback_data="skip_category_description",
                    )
                ],
                [  # Added a cancel option here for better UX
                    InlineKeyboardButton(
                        text="❌ Cancel Creation", callback_data="manage_categories"
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(CategoryManagementStates.waiting_for_category_description)


@router.callback_query(F.data == "add_category_image_upload")
async def add_category_image_upload(callback_query: CallbackQuery, state: FSMContext):
    """Request image upload for the category."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    state_data = await state.get_data()
    category_name = state_data.get("category_name", "N/A")
    message_text = (
        "🖼️ <b>\u2022 CATEGORY IMAGE: UPLOAD \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>SEND IMAGE FILE</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"<b>Category Name:</b> {category_name}\n\n"
        "Please send an image file for the category (JPG, PNG, WEBP).\n"
        "<i>Note: Recommended size is square, e.g., 300x300. Max 5MB.</i>\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Attach and send the image directly in the chat.</i>"
    )
    await callback_query.message.edit_text(
        message_text,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Options",
                        callback_data="back_to_image_options",
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(CategoryManagementStates.waiting_for_image_upload)


@router.message(F.photo | F.document, CategoryManagementStates.waiting_for_image_upload)
async def process_category_image_upload_input(
    message: Message, state: FSMContext
):  # Renamed
    """Process the uploaded category image."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    try:
        if message.photo:
            # Process and save the uploaded image
            file_data = await process_product_image(message, CATEGORY_IMAGES_FOLDER)

            if not file_data:
                await message.reply("❌ Failed to process the image. Please try again.")
                return

            # Store the file info in state - url_path now contains only the path starting from uploads/
            await safe_update_data(
                state,
                image_type="file",
                image_path=file_data.get("url_path"),
                original_file_id=file_data.get("original_file_id"),
            )

        elif message.document:
            if message.document.mime_type and message.document.mime_type.startswith(
                "image/"
            ):
                # Download the file
                file_id = message.document.file_id
                file_data = await download_file_from_telegram(
                    message.bot,
                    file_id,
                    CATEGORY_IMAGES_FOLDER,
                    message.document.file_name,
                )

                if not file_data:
                    await message.reply(
                        "❌ Failed to process the image. Please try again."
                    )
                    return

                # Store the file info in state - url_path now contains only the path starting from uploads/
                await safe_update_data(
                    state,
                    image_type="file",
                    image_path=file_data.get("url_path"),
                    original_file_id=file_id,
                )
            else:
                await message.reply(
                    "⚠️ <b>Invalid File Type</b>\n\nThe file you sent is not a supported image. Please send a JPG, PNG, or WEBP file.",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Options",
                                    callback_data="back_to_image_options",
                                )
                            ]
                        ]
                    ),
                    parse_mode="HTML",
                )
                return
        else:
            await message.reply(
                "⚠️ <b>Invalid Submission</b>\n\nPlease send an image as a photo or document.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Options",
                                callback_data="back_to_image_options",
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
            return
        state_data = await state.get_data()  # Get data again after update
        description_prompt_message = (
            "➕ <b>\u2022 ADD NEW CATEGORY \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "📝 <b>STEP 3: CATEGORY DESCRIPTION (OPTIONAL)</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"<b>Category Name:</b> {state_data.get('category_name', 'N/A')}\n"
            "<b>Image:</b> Uploaded successfully\n\n"
            "Please provide a short description for this category.\nThis can help users understand its content.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Enter a description or skip this step.</i>"
        )
        await message.reply(
            description_prompt_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="⏩ Skip Description",
                            callback_data="skip_category_description",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel Creation", callback_data="manage_categories"
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )
        await state.set_state(CategoryManagementStates.waiting_for_category_description)

    except Exception as e:
        logger.error(f"Error processing category image upload: {e}")
        error_message = (
            f"❌ <b>\u2022 IMAGE UPLOAD FAILED \u2022</b>\n\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"An error occurred while saving the image: {str(e)}\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "Please try again or choose a different image/option.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Ensure the file is a valid image and not too large.</i>"
        )
        await message.reply(
            error_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Options",
                            callback_data="back_to_image_options",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )


@router.callback_query(F.data == "skip_category_image")
async def skip_category_image(callback_query: CallbackQuery, state: FSMContext):
    """Skip adding an image and proceed to description."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    await safe_update_data(
        state, image_type=None, image_url=None, image_path=None
    )  # Explicitly clear image data
    state_data = await state.get_data()
    category_name = state_data.get("category_name", "N/A")
    description_prompt_message = (
        "➕ <b>\u2022 ADD NEW CATEGORY \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "📝 <b>STEP 3: CATEGORY DESCRIPTION (OPTIONAL)</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"<b>Category Name:</b> {category_name}\n"
        "<b>Image:</b> Skipped\n\n"
        "Please provide a short description for this category.\nThis can help users understand its content.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Enter a description or skip this step.</i>"
    )
    await callback_query.message.edit_text(
        description_prompt_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="⏩ Skip Description",
                        callback_data="skip_category_description",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="❌ Cancel Creation", callback_data="manage_categories"
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(CategoryManagementStates.waiting_for_category_description)


@router.callback_query(
    F.data == "skip_category_description",
    # CategoryManagementStates.waiting_for_category_description # State check can be here or inside
)
async def skip_category_description_action(  # Renamed function
    callback_query: CallbackQuery, state: FSMContext, bot: Bot
):
    """Skip providing a category description and finalize."""
    user_id = callback_query.from_user.id
    current_state = await state.get_state()
    if current_state != CategoryManagementStates.waiting_for_category_description:
        await callback_query.answer("Invalid action for current step.", show_alert=True)
        return

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    state_data = await state.get_data()
    category_name = state_data.get("category_name", "N/A")
    image_type = state_data.get("image_type")
    db_image_data = None
    image_status_text = "Not set"

    if image_type == "url":
        db_image_data = {"type": "url", "url": state_data.get("image_url")}
        image_status_text = "URL provided"
    elif image_type == "file":
        db_image_data = {"type": "file", "path": state_data.get("image_path")}
        image_status_text = "Uploaded"

    add_category(category_name, "", db_image_data)  # Empty description
    await log_admin_action(
        user_id,
        "add_category",
        f"Added category: {category_name}, No Description, Image: {image_status_text}"
    )

    success_message = (
        "✅ <b>\u2022 CATEGORY ADDED \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>SUCCESSFULLY CREATED</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"Category '<b>{category_name}</b>' has been added successfully.\n"
        f"Description: Skipped\n"
        f"Image: {image_status_text}\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>The new category is now available.</i>"
    )
    await callback_query.message.edit_text(
        success_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add Another Category", callback_data="add_category"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="📋 View All Categories", callback_data="list_categories"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Management", callback_data="manage_categories"
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )
    await clear_state_data(state)


@router.message(CategoryManagementStates.waiting_for_category_description)
async def process_category_description_input(
    message: Message, state: FSMContext, bot: Bot
):  # Renamed
    """Process the category description input and finalize."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    category_description = message.text.strip()
    if (
        not category_description
    ):  # Optional, but good to handle if they just send spaces
        await message.reply(
            "⚠️ <b>Description seems empty.</b>\n\nIf you want to skip, use the button. Otherwise, please provide a valid description.",
            parse_mode="HTML",
        )
        return

    state_data = await state.get_data()
    category_name = state_data.get("category_name", "N/A")
    image_type = state_data.get("image_type")
    db_image_data = None
    image_status_text = "Not set"

    if image_type == "url":
        db_image_data = {"type": "url", "url": state_data.get("image_url")}
        image_status_text = "URL provided"
    elif image_type == "file":
        db_image_data = {"type": "file", "path": state_data.get("image_path")}
        image_status_text = "Uploaded"

    desc_preview = (
        category_description[:50] + "..."
        if len(category_description) > 50
        else category_description
    )

    add_category(category_name, category_description, db_image_data)
    await log_admin_action(
        user_id,
        "add_category",
        f"Added category: {category_name}, Description: {desc_preview}, Image: {image_status_text}"
    )

    success_message = (
        "✅ <b>\u2022 CATEGORY ADDED \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>SUCCESSFULLY CREATED</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"Category '<b>{category_name}</b>' has been added successfully.\n"
        f"Description: <i>{desc_preview}</i>\n"
        f"Image: {image_status_text}\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>The new category is now available.</i>"
    )
    await message.reply(
        success_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add Another Category", callback_data="add_category"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="📋 View All Categories", callback_data="list_categories"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Management", callback_data="manage_categories"
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )
    await clear_state_data(state)


# Add a "edit_category_list" handler to display all categories for editing


@router.callback_query(F.data == "edit_category")
async def show_categories_for_edit(callback_query: CallbackQuery):
    """Show a list of all categories that can be edited."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    categories = get_all_categories()

    if not categories:
        empty_message = (
            "📂 <b>\u2022 EDIT CATEGORY \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "⚠️ <b>No categories found to edit.</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "You can add a new category first.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Use the '➕ Add Category' option to create one.</i>"
        )
        await callback_query.message.edit_text(
            empty_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="➕ Add Category", callback_data="add_category"
                        ),
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="manage_categories"
                        ),
                    ],
                ]
            ),
            parse_mode="HTML",
        )
        return

    inline_keyboard = []
    row = []
    for i, category in enumerate(categories):
        category_id = category.get("_id") or category.get("id")
        category_name = category.get("name", "Unnamed")
        row.append(
            InlineKeyboardButton(
                text=f"✏️ {category_name}",
                callback_data=f"edit_category:{str(category_id)}",  # Ensure ID is string for callback
            )
        )
        if len(row) == 2 or i == len(categories) - 1:
            inline_keyboard.append(row)
            row = []

    inline_keyboard.append(
        [
            InlineKeyboardButton(
                text="🔙 Back to Management", callback_data="manage_categories"
            )
        ]
    )

    selection_message = (
        "✏️ <b>\u2022 SELECT CATEGORY TO EDIT \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>CHOOSE FROM THE LIST</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Please select a category from the list below to modify its details.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Click on a category to begin editing.</i>"
    )
    await callback_query.message.edit_text(
        selection_message,
        reply_markup=InlineKeyboardMarkup(inline_keyboard=inline_keyboard),
        parse_mode="HTML",
    )


@router.callback_query(lambda c: c.data.startswith("edit_category:"))
async def edit_category_start(callback_query: CallbackQuery, state: FSMContext):
    """Start the process of editing a category."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    category_id_str = callback_query.data.split(":")[1]

    try:
        category_id_obj = (
            ObjectId(category_id_str)
            if ObjectId.is_valid(category_id_str)
            else category_id_str
        )
    except NameError:  # ObjectId not imported (e.g. not using MongoDB)
        category_id_obj = category_id_str

    category = get_category_by_id(category_id_obj)

    if not category:
        error_message = (
            "⚠️ <b>\u2022 CATEGORY NOT FOUND \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "The selected category could not be found. It may have been deleted.\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>"
        )
        await callback_query.message.edit_text(
            error_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Categories", callback_data="edit_category"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    await safe_update_data(
        state,
        category_id=str(category_id_obj),  # Store as string in state
        category_name=category.get("name", ""),
        category_description=category.get("description", ""),
        current_image_data=category.get("image"),  # Store current image data
    )

    product_count = len(get_products_by_category(category_id_obj))
    image_info = "Not set"
    if category.get("image"):
        image_type = category.get("image", {}).get("type", "")
        if image_type == "url":
            image_info = "🔗 URL image attached"
        elif image_type == "file":
            image_info = "📸 Custom image uploaded"

    details_message = (
        "🛠️ <b>\u2022 EDIT CATEGORY DETAILS \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"<b>Category:</b> {category.get('name')}\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"🏷️ <b>Name:</b> {category.get('name')}\n"
        f"📝 <b>Description:</b> {category.get('description', 'N/A')}\n"
        f"🛍️ <b>Products Count:</b> {product_count}\n"
        f"🖼️ <b>Current Image:</b> {image_info}\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Select an option below to modify this category:</i>"
    )
    await callback_query.message.edit_text(
        details_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="✏️ Edit Name", callback_data="edit_category_name"
                    ),
                    InlineKeyboardButton(
                        text="📝 Edit Description",
                        callback_data="edit_category_description",
                    ),
                ],
                [
                    InlineKeyboardButton(
                        text="🖼️ Change Image", callback_data="edit_category_image"
                    ),
                    InlineKeyboardButton(
                        text="🗑️ Delete Category",
                        callback_data=f"delete_category:{str(category_id_obj)}",
                    ),
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Select Category", callback_data="edit_category"
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "edit_category_name")
async def edit_category_name_prompt(
    callback_query: CallbackQuery, state: FSMContext
):  # Renamed
    """Prompt for editing category name."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    state_data = await state.get_data()
    category_name = state_data.get("category_name", "N/A")
    category_id_str = state_data.get("category_id", "")

    prompt_message = (
        "✏️ <b>\u2022 EDIT CATEGORY NAME \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"<b>Current Name:</b> {category_name}\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Please enter the new name for this category.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>The name should be unique.</i>"
    )
    await callback_query.message.edit_text(
        prompt_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="❌ Cancel",
                        callback_data=f"edit_category:{category_id_str}",
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(CategoryManagementStates.waiting_for_edit_name)


@router.message(CategoryManagementStates.waiting_for_edit_name)
async def process_edit_category_name_input(
    message: Message, state: FSMContext, bot: Bot
):  # Renamed
    """Process the edited category name."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    new_name = message.text.strip()
    if not new_name:
        await message.reply(
            "⚠️ <b>Invalid Input</b>\n\nCategory name cannot be empty. Please try again.",
            parse_mode="HTML",
        )
        return

    state_data = await state.get_data()
    category_id_str = state_data.get("category_id")
    old_name = state_data.get("category_name", "N/A")

    if not category_id_str:
        await message.reply(
            "⚠️ <b>Error:</b> Category ID not found. Please restart editing.",
            parse_mode="HTML",
        )
        await clear_state_data(state)
        return

    try:
        category_id_obj = (
            ObjectId(category_id_str)
            if ObjectId.is_valid(category_id_str)
            else category_id_str
        )
    except NameError:
        category_id_obj = category_id_str

    all_categories = get_all_categories()
    if any(
        cat.get("name", "").lower() == new_name.lower()
        and str(cat.get("_id")) != category_id_str
        for cat in all_categories
    ):
        await message.reply(
            "⚠️ <b>Name Exists</b>\n\nA category with this name already exists. Please use a different name.",
            parse_mode="HTML",
        )
        return

    update_data = {"name": new_name}
    try:
        success = update_category(category_id_obj, update_data)
        if success:
            await log_admin_action(
                user_id,
                "edit_category_name",
                f"Category ID {category_id_str}: '{old_name}' -> '{new_name}'"
            )
            await safe_update_data(state, category_name=new_name)  # Update state

            success_message = (
                f"✅ <b>\u2022 NAME UPDATED \u2022</b>\n\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
                f"The category name has been successfully updated from '<b>{old_name}</b>' to '<b>{new_name}</b>'.\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "What would you like to do next for this category?\n\n"
                "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
                "✨ <i>Select an option or go back.</i>"
            )
            await message.reply(
                success_message,
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="📝 Edit Description",
                                callback_data="edit_category_description",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🖼️ Change Image",
                                callback_data="edit_category_image",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Category Details",
                                callback_data=f"edit_category:{category_id_str}",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏛️ Main Menu", callback_data="manage_categories"
                            )
                        ],  # Or main admin menu
                    ]
                ),
                parse_mode="HTML",
            )
            await state.set_state(None)  # Clear state for this specific sub-flow
        else:
            await message.reply(
                "❌ <b>\u2022 UPDATE FAILED \u2022</b>\n\nFailed to update category name. Please try again.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back",
                                callback_data=f"edit_category:{category_id_str}",
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
    except Exception as e:
        logger.error(f"Error updating category name for {category_id_str}: {e}")
        await message.reply(
            f"❌ <b>\u2022 SYSTEM ERROR \u2022</b>\n\nAn error occurred: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back",
                            callback_data=f"edit_category:{category_id_str}",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )


@router.callback_query(F.data == "edit_category_description")
async def edit_category_description_prompt(
    callback_query: CallbackQuery, state: FSMContext
):  # Renamed
    """Prompt for editing category description."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    state_data = await state.get_data()
    category_name = state_data.get("category_name", "N/A")
    category_desc = state_data.get("category_description", "N/A")
    category_id_str = state_data.get("category_id", "")

    prompt_message = (
        "📝 <b>\u2022 EDIT CATEGORY DESCRIPTION \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"<b>Category:</b> {category_name}\n"
        f"<b>Current Description:</b> <i>{category_desc}</i>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Please enter the new description for this category.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>A good description helps users.</i>"
    )
    await callback_query.message.edit_text(
        prompt_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="❌ Cancel",
                        callback_data=f"edit_category:{category_id_str}",
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(CategoryManagementStates.waiting_for_edit_description)


@router.message(CategoryManagementStates.waiting_for_edit_description)
async def process_edit_category_description_input(
    message: Message, state: FSMContext, bot: Bot
):  # Renamed
    """Process the edited category description."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    new_description = message.text.strip()
    # Description can be empty if desired by admin
    # if not new_description:
    #     await message.reply("⚠️ Description cannot be empty. Please try again.", parse_mode="HTML")
    #     return

    state_data = await state.get_data()
    category_id_str = state_data.get("category_id")
    category_name = state_data.get("category_name", "N/A")
    old_description = state_data.get("category_description", "N/A")

    if not category_id_str:
        await message.reply(
            "⚠️ <b>Error:</b> Category ID not found. Please restart editing.",
            parse_mode="HTML",
        )
        await clear_state_data(state)
        return

    try:
        category_id_obj = (
            ObjectId(category_id_str)
            if ObjectId.is_valid(category_id_str)
            else category_id_str
        )
    except NameError:
        category_id_obj = category_id_str

    update_data = {"description": new_description}
    try:
        success = update_category(category_id_obj, update_data)
        if success:
            await log_admin_action(
                user_id,
                "edit_category_description",
                f"Category ID {category_id_str} ('{category_name}') description updated."
            )
            await safe_update_data(
                state, category_description=new_description
            )  # Update state

            success_message = (
                f"✅ <b>\u2022 DESCRIPTION UPDATED \u2022</b>\n\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
                f"The description for category '<b>{category_name}</b>' has been successfully updated.\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "What would you like to do next for this category?\n\n"
                "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
                "✨ <i>Select an option or go back.</i>"
            )
            await message.reply(
                success_message,
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="✏️ Edit Name", callback_data="edit_category_name"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🖼️ Change Image",
                                callback_data="edit_category_image",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Category Details",
                                callback_data=f"edit_category:{category_id_str}",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🏛️ Main Menu", callback_data="manage_categories"
                            )
                        ],
                    ]
                ),
                parse_mode="HTML",
            )
            await state.set_state(None)  # Clear state for this specific sub-flow
        else:
            await message.reply(
                "❌ <b>\u2022 UPDATE FAILED \u2022</b>\n\nFailed to update category description. Please try again.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back",
                                callback_data=f"edit_category:{category_id_str}",
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
    except Exception as e:
        logger.error(f"Error updating category description for {category_id_str}: {e}")
        await message.reply(
            f"❌ <b>\u2022 SYSTEM ERROR \u2022</b>\n\nAn error occurred: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back",
                            callback_data=f"edit_category:{category_id_str}",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )


@router.callback_query(lambda c: c.data.startswith("delete_category:"))
async def confirm_delete_category_prompt(
    callback_query: CallbackQuery, state: FSMContext
):  # Renamed
    """Confirm category deletion."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    category_id_str = callback_query.data.split(":")[1]

    try:
        category_id_obj = (
            ObjectId(category_id_str)
            if ObjectId.is_valid(category_id_str)
            else category_id_str
        )
    except NameError:
        category_id_obj = category_id_str

    category = get_category_by_id(category_id_obj)
    if not category:
        await callback_query.message.edit_text(
            "⚠️ <b>Category not found.</b> It may have been deleted already.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Categories", callback_data="edit_category"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    product_count = len(get_products_by_category(category_id_obj))
    warning_text = ""
    if product_count > 0:
        warning_text = (
            f"⚠️ <b>This category contains {product_count} product(s).</b> "
            "Deleting it will remove their category association (they won't be deleted).\n\n"
        )

    confirmation_message = (
        f"🗑️ <b>\u2022 CONFIRM DELETION \u2022</b>\n\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"<b>Category:</b> {category.get('name', 'N/A')}\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"{warning_text}"
        "Are you absolutely sure you want to delete this category?\n"
        "<b>This action cannot be undone.</b>\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Please confirm your choice.</i>"
    )
    await callback_query.message.edit_text(
        confirmation_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="✅ Yes, Delete It",
                        callback_data=f"confirm_delete_category_action:{category_id_str}",
                    )
                ],  # Changed action name
                [
                    InlineKeyboardButton(
                        text="❌ No, Keep It",
                        callback_data=f"edit_category:{category_id_str}",
                    )
                ],  # Back to specific category edit
            ]
        ),
        parse_mode="HTML",
    )


@router.callback_query(
    lambda c: c.data.startswith("confirm_delete_category_action:")
)  # Changed action name
async def execute_delete_category_action(
    callback_query: CallbackQuery, bot: Bot
):  # Renamed
    """Execute category deletion."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    category_id_str = callback_query.data.split(":")[1]

    try:
        category_id_obj = (
            ObjectId(category_id_str)
            if ObjectId.is_valid(category_id_str)
            else category_id_str
        )
    except NameError:
        category_id_obj = category_id_str

    category = get_category_by_id(
        category_id_obj
    )  # Get details before deleting for logging
    if not category:
        await callback_query.message.edit_text(
            "⚠️ <b>Category already deleted or not found.</b>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Categories", callback_data="edit_category"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    category_name_for_log = category.get("name", f"ID: {category_id_str}")
    success = delete_category(category_id_obj)

    if success:
        await log_admin_action(
            user_id,
            "delete_category",
            f"Deleted category: '{category_name_for_log}' (ID: {category_id_str})"
        )
        success_message = (
            f"✅ <b>\u2022 CATEGORY DELETED \u2022</b>\n\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"The category '<b>{category_name_for_log}</b>' has been successfully deleted.\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>"
        )
        await callback_query.message.edit_text(
            success_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="📋 View All Categories",
                            callback_data="list_categories",
                        )
                    ],  # if list_categories exists
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Category Management",
                            callback_data="manage_categories",
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )
    else:
        error_message = (
            f"❌ <b>\u2022 DELETION FAILED \u2022</b>\n\n"
            f"Failed to delete category '<b>{category_name_for_log}</b>'. It might have already been deleted or an error occurred."
        )
        await callback_query.message.edit_text(
            error_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Categories", callback_data="edit_category"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )


@router.callback_query(F.data == "edit_category_image")
async def edit_category_image_options(
    callback_query: CallbackQuery, state: FSMContext
):  # Renamed
    """Show options for editing category image."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    state_data = await state.get_data()
    category_name = state_data.get("category_name", "N/A")
    category_id_str = state_data.get("category_id", "")
    current_image_data = state_data.get("current_image_data")

    image_status_text = "Not set"
    if current_image_data:
        if current_image_data.get("type") == "url":
            image_status_text = "🔗 URL image"
        elif current_image_data.get("type") == "file":
            image_status_text = "📸 Uploaded image"

    options_message = (
        f"🖼️ <b>\u2022 EDIT CATEGORY IMAGE \u2022</b>\n\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"<b>Category:</b> {category_name}\n"
        f"<b>Current Image:</b> {image_status_text}\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Choose an action for the category image:\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Select how you want to update the image.</i>"
    )
    keyboard_buttons = [
        [
            InlineKeyboardButton(
                text="🔗 Set/Change Image URL", callback_data="edit_category_image_url"
            )
        ],
        [
            InlineKeyboardButton(
                text="📷 Upload New Image", callback_data="edit_category_image_upload"
            )
        ],
    ]
    if current_image_data:  # Only show remove if an image exists
        keyboard_buttons.append(
            [
                InlineKeyboardButton(
                    text="🗑️ Remove Current Image",
                    callback_data="remove_category_image_action",
                )
            ]
        )  # Changed action
    keyboard_buttons.append(
        [
            InlineKeyboardButton(
                text="🔙 Back to Category Details",
                callback_data=f"edit_category:{category_id_str}",
            )
        ]
    )

    await callback_query.message.edit_text(
        options_message,
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard_buttons),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "edit_category_image_url")
async def edit_category_image_url_prompt(
    callback_query: CallbackQuery, state: FSMContext
):  # Renamed
    """Prompt for new image URL."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    state_data = await state.get_data()
    category_name = state_data.get("category_name", "N/A")

    prompt_message = (
        f"🔗 <b>\u2022 SET IMAGE URL \u2022</b>\n\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"<b>Category:</b> {category_name}\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Please enter the new direct URL for the category image (JPG, PNG, WEBP).\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>The URL should point directly to an image file.</i>"
    )
    await callback_query.message.edit_text(
        prompt_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Image Options",
                        callback_data="edit_category_image",
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(CategoryManagementStates.waiting_for_edit_image_url)


@router.message(CategoryManagementStates.waiting_for_edit_image_url)
async def process_edit_category_image_url_input(
    message: Message, state: FSMContext, bot: Bot
):  # Renamed
    """Process the edited category image URL."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    new_image_url = message.text.strip()
    if not new_image_url:
        await message.reply(
            "⚠️ <b>Image URL cannot be empty.</b> Please try again.", parse_mode="HTML"
        )
        return
    if not (
        new_image_url.startswith("http://") or new_image_url.startswith("https://")
    ):
        await message.reply(
            "⚠️ <b>Invalid URL Format.</b> Must start with http:// or https://.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="edit_category_image"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return
    valid_extensions = [".jpg", ".jpeg", ".png", ".gif", ".webp"]
    if not any(new_image_url.lower().endswith(ext) for ext in valid_extensions):
        await message.reply(
            "⚠️ <b>Not An Image Link.</b> URL must end with a valid image extension.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="edit_category_image"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    state_data = await state.get_data()
    category_id_str = state_data.get("category_id")
    category_name = state_data.get("category_name", "N/A")

    if not category_id_str:
        await message.reply(
            "⚠️ <b>Error:</b> Category ID not found. Please restart editing.",
            parse_mode="HTML",
        )
        await clear_state_data(state)
        return

    try:
        category_id_obj = (
            ObjectId(category_id_str)
            if ObjectId.is_valid(category_id_str)
            else category_id_str
        )
    except NameError:
        category_id_obj = category_id_str

    image_data = {"type": "url", "url": new_image_url}
    update_data = {"image": image_data}

    try:
        success = update_category(category_id_obj, update_data)
        if success:
            await log_admin_action(
                user_id,
                "edit_category_image_url",
                f"Category ID {category_id_str} ('{category_name}') image URL updated."
            )
            await safe_update_data(
                state, current_image_data=image_data
            )  # Update image in state

            success_message = (
                f"✅ <b>\u2022 IMAGE URL UPDATED \u2022</b>\n\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
                f"The image URL for category '<b>{category_name}</b>' has been successfully set.\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "What would you like to do next for this category?\n\n"
                "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
                "✨ <i>Select an option or go back.</i>"
            )
            await message.reply(
                success_message,
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="✏️ Edit Name", callback_data="edit_category_name"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="📝 Edit Description",
                                callback_data="edit_category_description",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Category Details",
                                callback_data=f"edit_category:{category_id_str}",
                            )
                        ],
                    ]
                ),
                parse_mode="HTML",
            )
            await state.set_state(None)
        else:
            await message.reply(
                "❌ <b>\u2022 UPDATE FAILED \u2022</b>\n\nFailed to update category image URL. Please try again.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back",
                                callback_data=f"edit_category:{category_id_str}",
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
    except Exception as e:
        logger.error(f"Error updating category image URL for {category_id_str}: {e}")
        await message.reply(
            f"❌ <b>\u2022 SYSTEM ERROR \u2022</b>\n\nAn error occurred: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back",
                            callback_data=f"edit_category:{category_id_str}",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )


@router.callback_query(F.data == "edit_category_image_upload")
async def edit_category_image_upload_prompt(
    callback_query: CallbackQuery, state: FSMContext
):  # Renamed
    """Prompt for new image upload."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    state_data = await state.get_data()
    category_name = state_data.get("category_name", "N/A")

    prompt_message = (
        f"📷 <b>\u2022 UPLOAD NEW IMAGE \u2022</b>\n\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"<b>Category:</b> {category_name}\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Please send the new image file for the category (JPG, PNG, WEBP).\n"
        "<i>Max size: 5MB. Recommended: Square aspect ratio.</i>\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Attach and send the image directly in the chat.</i>"
    )
    await callback_query.message.edit_text(
        prompt_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Image Options",
                        callback_data="edit_category_image",
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(CategoryManagementStates.waiting_for_edit_image_upload)


@router.message(
    F.photo | F.document, CategoryManagementStates.waiting_for_edit_image_upload
)
async def process_edit_category_image_upload_input(
    message: Message, state: FSMContext, bot: Bot
):  # Renamed
    """Process the uploaded image for editing category."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    file_id = None
    file_name_orig = "uploaded_image"
    if message.photo:
        file_id = message.photo[-1].file_id
        file_name_orig = f"photo_{file_id}.jpg"
    elif message.document:
        if message.document.mime_type and message.document.mime_type.startswith(
            "image/"
        ):
            file_id = message.document.file_id
            file_name_orig = message.document.file_name or f"doc_image_{file_id}"
        else:
            await message.reply(
                "⚠️ <b>Invalid File Type.</b> Please send a JPG, PNG, or WEBP file.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back", callback_data="edit_category_image"
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
            return
    else:
        await message.reply(
            "⚠️ <b>Invalid Submission.</b> Please send an image as a photo or document.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="edit_category_image"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return
    if not file_id:
        await message.reply(
            "Could not process the uploaded file. Please try again.", parse_mode="HTML"
        )
        return

    state_data = await state.get_data()
    category_id_str = state_data.get("category_id")
    category_name = state_data.get("category_name", "N/A")

    if not category_id_str:
        await message.reply(
            "⚠️ <b>Error:</b> Category ID not found. Please restart editing.",
            parse_mode="HTML",
        )
        await clear_state_data(state)
        return

    try:
        category_id_obj = (
            ObjectId(category_id_str)
            if ObjectId.is_valid(category_id_str)
            else category_id_str
        )
    except NameError:
        category_id_obj = category_id_str

    try:
        # Download the actual file from Telegram
        file_data = await download_file_from_telegram(
            message.bot, file_id, CATEGORY_IMAGES_FOLDER, file_name_orig
        )

        if not file_data:
            await message.reply("❌ Failed to download the image. Please try again.")
            return

        # Get the path starting from uploads/
        image_path = file_data.get(
            "url_path"
        )  # This now contains only the path starting from uploads/

        image_data = {"type": "file", "path": image_path}
        update_data = {"image": image_data}
        success = update_category(category_id_obj, update_data)

        if success:
            await log_admin_action(
                user_id,
                "edit_category_image_upload",
                f"Category ID {category_id_str} ('{category_name}') image uploaded."
            )
            await safe_update_data(
                state, current_image_data=image_data
            )  # Update image in state

            success_message = (
                f"✅ <b>\u2022 IMAGE UPLOADED \u2022</b>\n\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
                f"The new image for category '<b>{category_name}</b>' has been successfully uploaded.\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "What would you like to do next for this category?\n\n"
                "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
                "✨ <i>Select an option or go back.</i>"
            )
            await message.reply(
                success_message,
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="✏️ Edit Name", callback_data="edit_category_name"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="📝 Edit Description",
                                callback_data="edit_category_description",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Category Details",
                                callback_data=f"edit_category:{category_id_str}",
                            )
                        ],
                    ]
                ),
                parse_mode="HTML",
            )
            await state.set_state(None)
        else:
            await message.reply(
                "❌ <b>\u2022 UPDATE FAILED \u2022</b>\n\nFailed to update category image. Please try again.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back",
                                callback_data=f"edit_category:{category_id_str}",
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
    except Exception as e:
        logger.error(
            f"Error processing uploaded image for category {category_id_str}: {e}"
        )
        await message.reply(
            f"❌ <b>\u2022 UPLOAD ERROR \u2022</b>\n\nAn error occurred: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="edit_category_image"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )


@router.callback_query(F.data == "remove_category_image_action")  # Changed action name
async def remove_category_image_action(
    callback_query: CallbackQuery, state: FSMContext, bot: Bot
):  # Renamed
    """Remove image from the category and properly unload it."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    state_data = await state.get_data()
    category_id_str = state_data.get("category_id")
    category_name = state_data.get("category_name", "N/A")
    current_image_data = state_data.get("current_image_data")

    if not category_id_str:
        await callback_query.message.edit_text(
            "⚠️ <b>Error:</b> Category ID not found. Please restart editing.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back", callback_data="edit_category"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    try:
        category_id_obj = (
            ObjectId(category_id_str)
            if ObjectId.is_valid(category_id_str)
            else category_id_str
        )
    except NameError:
        category_id_obj = category_id_str

    # Get the current image path for logging purposes
    image_path = None
    if current_image_data:
        if current_image_data.get("type") == "file":
            image_path = current_image_data.get("path")
        elif current_image_data.get("type") == "url":
            image_path = current_image_data.get("url")

    # In MongoDB, to remove a field, you use $unset
    update_data = {"$unset": {"image": ""}}
    try:
        # Directly use the dummy collection's update_one or your actual DB update
        result = categories_collection.update_one({"_id": category_id_obj}, update_data)
        success = (
            result.matched_count > 0 and result.modified_count > 0
        )  # Ensure it was found and modified

        if success:
            # Log the action with image path details for better tracking
            image_details = f"path: {image_path}" if image_path else "no path available"
            await log_admin_action(
                user_id,
                "remove_category_image",
                f"Category ID {category_id_str} ('{category_name}') image removed. Image {image_details}"
            )

            # Clear image from state to ensure it's properly unloaded
            await safe_update_data(state, current_image_data=None)

            # If the current message has an image, make sure to switch to text-only display
            is_photo = callback_query.message.photo is not None

            success_message = (
                f"✅ <b>\u2022 IMAGE REMOVED \u2022</b>\n\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
                f"The image for category '<b>{category_name}</b>' has been successfully removed.\n"
                f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "What would you like to do next for this category?\n\n"
                "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
                "✨ <i>Select an option or go back.</i>"
            )

            # If the current message has an image, delete it and send a new text-only message
            if is_photo:
                try:
                    await callback_query.bot.delete_message(
                        chat_id=callback_query.message.chat.id,
                        message_id=callback_query.message.message_id,
                    )

                    # Send a new text-only message
                    await callback_query.bot.send_message(
                        chat_id=callback_query.message.chat.id,
                        text=success_message,
                        reply_markup=InlineKeyboardMarkup(
                            inline_keyboard=[
                                [
                                    InlineKeyboardButton(
                                        text="✏️ Edit Name",
                                        callback_data="edit_category_name",
                                    )
                                ],
                                [
                                    InlineKeyboardButton(
                                        text="📝 Edit Description",
                                        callback_data="edit_category_description",
                                    )
                                ],
                                [
                                    InlineKeyboardButton(
                                        text="🖼️ Set New Image",
                                        callback_data="edit_category_image",
                                    )
                                ],  # Option to set new
                                [
                                    InlineKeyboardButton(
                                        text="🔙 Back to Category Details",
                                        callback_data=f"edit_category:{category_id_str}",
                                    )
                                ],
                            ]
                        ),
                        parse_mode="HTML",
                    )
                    logger.debug(
                        "Successfully switched to text-only display after image removal"
                    )
                except Exception as e:
                    logger.warning(f"Error switching to text-only mode: {e}")
                    # Fall back to edit_text if delete fails
                    await callback_query.message.edit_text(
                        success_message,
                        reply_markup=InlineKeyboardMarkup(
                            inline_keyboard=[
                                [
                                    InlineKeyboardButton(
                                        text="✏️ Edit Name",
                                        callback_data="edit_category_name",
                                    )
                                ],
                                [
                                    InlineKeyboardButton(
                                        text="📝 Edit Description",
                                        callback_data="edit_category_description",
                                    )
                                ],
                                [
                                    InlineKeyboardButton(
                                        text="🖼️ Set New Image",
                                        callback_data="edit_category_image",
                                    )
                                ],  # Option to set new
                                [
                                    InlineKeyboardButton(
                                        text="🔙 Back to Category Details",
                                        callback_data=f"edit_category:{category_id_str}",
                                    )
                                ],
                            ]
                        ),
                        parse_mode="HTML",
                    )
            else:
                # If already text-only, just edit the message
                await callback_query.message.edit_text(
                    success_message,
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="✏️ Edit Name",
                                    callback_data="edit_category_name",
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="📝 Edit Description",
                                    callback_data="edit_category_description",
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🖼️ Set New Image",
                                    callback_data="edit_category_image",
                                )
                            ],  # Option to set new
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Category Details",
                                    callback_data=f"edit_category:{category_id_str}",
                                )
                            ],
                        ]
                    ),
                    parse_mode="HTML",
                )
        else:
            await callback_query.message.edit_text(
                "❌ <b>\u2022 REMOVAL FAILED \u2022</b>\n\nFailed to remove category image. It might not have an image or an error occurred.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back",
                                callback_data=f"edit_category:{category_id_str}",
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
    except Exception as e:
        logger.error(f"Error removing image for category {category_id_str}: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>\u2022 SYSTEM ERROR \u2022</b>\n\nAn error occurred: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back",
                            callback_data=f"edit_category:{category_id_str}",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )


# Callback query handler for owner panel
@router.callback_query(F.data == "owner_panel")
async def owner_panel(callback_query: CallbackQuery):
    """Display the owner panel with user name."""
    user_id = callback_query.from_user.id
    user_name = callback_query.from_user.full_name

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    welcome_text = format_text("owner", "owner_welcome", owner_name=user_name)

    if (
        not welcome_text or welcome_text == "owner:owner_welcome"
    ):  # Check if default fallback used
        welcome_text = (
            f"👑 <b>\u2022 OWNER DASHBOARD \u2022</b>\n\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"<b>WELCOME, {user_name}!</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"You have full administrative privileges. Use the options below to manage the bot and its users.\n\n"
            f"Available actions:\n"
            f"  \u2022 Manage Administrators\n"
            f"  \u2022 View System Statistics\n"
            f"  \u2022 Configure Bot Settings\n"
            f"  \u2022 Broadcast Announcements\n\n"
            f"▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            f"✨ <i>Select an option below to proceed.</i>"
        )

    await callback_query.message.edit_text(
        welcome_text,
        reply_markup=admin_main_keyboard(is_owner=True),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "view_faqs_admin")
async def view_faqs_admin(callback_query: CallbackQuery):
    """View all FAQs in the admin panel."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    faqs = get_faqs()

    if not faqs:
        empty_message = (
            "❓ <b>\u2022 FAQ LIST \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "⚠️ <b>No FAQs have been added yet.</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "Use the 'Add FAQ' option to create your first FAQ entry.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Click '➕ Add FAQ' to begin.</i>"
        )
        await callback_query.message.edit_text(
            empty_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="➕ Add FAQ", callback_data="add_faq")],
                    [
                        InlineKeyboardButton(
                            text=format_text("buttons", "faq_back"),
                            callback_data="manage_faqs",
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )
        return

    faq_text = (
        "📚 <b>\u2022 FREQUENTLY ASKED QUESTIONS \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>VIEW & MANAGE ENTRIES</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
    )
    for i, faq in enumerate(faqs, 1):
        question = faq.get("question", "N/A")
        answer = faq.get("answer", "N/A")
        if len(answer) > 70:  # Adjusted preview
            answer = answer[:67] + "..."

        faq_text += f"<b>{i}. Q:</b> {question}\n"
        faq_text += f"   <i>A: {answer}</i>\n"
        if i < len(faqs):
            faq_text += "<code>┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈ ┈</code>\n\n"
        else:
            faq_text += "\n"

    faq_text += "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n" "✨ <i>Select an option below to manage FAQs.</i>"
    await callback_query.message.edit_text(
        faq_text[:4096],
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text=format_text("buttons", "faq_add"), callback_data="add_faq"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text=format_text("buttons", "faq_edit"),
                        callback_data="edit_faq",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text=format_text("buttons", "faq_delete"),
                        callback_data="delete_faq",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text=format_text("buttons", "faq_back"),
                        callback_data="manage_faqs",
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "add_faq")
async def add_faq_prompt_question(
    callback_query: CallbackQuery, state: FSMContext
):  # Renamed
    """Start the process of adding a new FAQ."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    prompt_message = (
        "➕ <b>\u2022 ADD NEW FAQ \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "📝 <b>STEP 1: ENTER QUESTION</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Please enter the question for the new FAQ entry.\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Make the question clear and concise.</i>"
    )
    await callback_query.message.edit_text(
        prompt_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [InlineKeyboardButton(text="❌ Cancel", callback_data="manage_faqs")]
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(FAQManagementStates.waiting_for_question)


@router.message(FAQManagementStates.waiting_for_question)
async def process_faq_question_input(message: Message, state: FSMContext):  # Renamed
    """Process the question for a new FAQ or edited FAQ."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    question = message.text.strip()
    if not question:
        await message.reply(
            "⚠️ <b>Question cannot be empty.</b> Please try again.", parse_mode="HTML"
        )
        return

    await safe_update_data(state, question=question)
    data = await state.get_data()  # Get data to check edit_mode
    edit_mode = data.get("edit_mode", False)

    if edit_mode:
        current_answer = data.get("current_answer", "N/A")
        prompt_message = (
            "✏️ <b>\u2022 EDIT FAQ \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "📝 <b>STEP 2: UPDATE ANSWER</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"<b>New Question:</b> {question}\n"
            f"<b>Current Answer:</b> <i>{current_answer}</i>\n\n"
            "Please enter the new answer for this FAQ.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Provide a comprehensive answer.</i>"
        )
        cancel_callback = (
            f"select_faq_edit:{data.get('faq_index')}"  # Go back to specific FAQ edit
        )
    else:
        prompt_message = (
            "➕ <b>\u2022 ADD NEW FAQ \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "📝 <b>STEP 2: ENTER ANSWER</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"<b>Question:</b> {question}\n\n"
            "Now, please enter the answer for this FAQ.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Be clear and informative.</i>"
        )
        cancel_callback = "add_faq"  # Go back to start adding FAQ

    await message.reply(
        prompt_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [InlineKeyboardButton(text="❌ Cancel", callback_data=cancel_callback)]
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(FAQManagementStates.waiting_for_answer)


@router.message(FAQManagementStates.waiting_for_answer)
async def process_faq_answer_input(
    message: Message, state: FSMContext, bot: Bot
):  # Renamed
    """Process the answer for a FAQ and ask for confirmation before saving."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    data = await state.get_data()
    question = data.get("question", "N/A")
    answer = message.text.strip()
    if not answer:
        await message.reply(
            "⚠️ <b>Answer cannot be empty.</b> Please try again.", parse_mode="HTML"
        )
        return

    await safe_update_data(state, answer=answer)  # Update state with new answer
    edit_mode = data.get("edit_mode", False)

    if edit_mode:
        faq_index = data.get("faq_index")
        old_question = data.get(
            "current_question", "N/A"
        )  # This is the question *before* editing started
        old_answer = data.get(
            "current_answer", "N/A"
        )  # This is the answer *before* editing started
        # 'question' in data is the newly entered or confirmed question from previous step

        confirmation_title = "✏️ <b>\u2022 CONFIRM FAQ EDIT \u2022</b>"
        confirmation_details = (
            f"<b>Original Question:</b>\n<i>{old_question}</i>\n\n"
            f"<b>New Question:</b>\n{question}\n\n"
            f"<b>Original Answer:</b>\n<i>{old_answer}</i>\n\n"
            f"<b>New Answer:</b>\n{answer}\n\n"
        )
        confirm_button_text = "✅ Confirm & Save Edit"
        confirm_callback = "confirm_save_faq_edit"
        edit_again_callback = f"select_faq_edit:{faq_index}"
        cancel_callback = "edit_faq"  # Back to FAQ list for editing
        await state.set_state(FAQManagementStates.confirm_edit)
    else:
        confirmation_title = "➕ <b>\u2022 CONFIRM NEW FAQ \u2022</b>"
        confirmation_details = (
            f"<b>Question:</b>\n{question}\n\n" f"<b>Answer:</b>\n{answer}\n\n"
        )
        confirm_button_text = "✅ Confirm & Add FAQ"
        confirm_callback = "confirm_save_faq_add"
        edit_again_callback = "add_faq"  # Restart adding
        cancel_callback = "manage_faqs"
        await state.set_state(FAQManagementStates.confirm_add)

    full_confirmation_message = (
        f"{confirmation_title}\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>PLEASE REVIEW THE DETAILS BELOW</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"{confirmation_details}"
        "Are you sure you want to save these details?\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Verify the information before confirming.</i>"
    )
    await message.reply(
        full_confirmation_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text=confirm_button_text, callback_data=confirm_callback
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="✏️ Re-enter Details", callback_data=edit_again_callback
                    )
                ],
                [InlineKeyboardButton(text="❌ Cancel", callback_data=cancel_callback)],
            ]
        ),
        parse_mode="HTML",
    )


@router.callback_query(
    F.data == "confirm_save_faq_add", FAQManagementStates.confirm_add
)
async def confirm_save_faq_add_action(
    callback_query: CallbackQuery, state: FSMContext, bot: Bot
):  # Renamed
    """Handle confirmation to save a new FAQ."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    data = await state.get_data()
    question = data.get("question", "N/A")
    answer = data.get("answer", "N/A")

    template_manager = TemplateManager()
    faq_template_data = template_manager.get_template("faq.json")  # Renamed for clarity
    faqs_list = faq_template_data.get("faqs", [])  # Renamed for clarity

    faqs_list.append({"question": question, "answer": answer})
    faq_template_data["faqs"] = faqs_list
    template_manager.save_template("faq.json", faq_template_data)

    await log_admin_action(
        user_id, "add_faq", f"Added FAQ: {question[:50]}..."
    )
    await clear_state_data(state)

    success_message = (
        "✅ <b>\u2022 FAQ ADDED SUCCESSFULLY \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"<b>Question:</b> {question}\n"
        f"<b>Answer:</b> {answer}\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "What would you like to do next?\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>The new FAQ is now live.</i>"
    )
    await callback_query.message.edit_text(
        success_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add Another FAQ", callback_data="add_faq"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="📋 View All FAQs", callback_data="view_faqs_admin"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text=format_text("buttons", "faq_back"),
                        callback_data="manage_faqs",
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )


@router.callback_query(
    F.data == "confirm_save_faq_edit", FAQManagementStates.confirm_edit
)
async def confirm_save_faq_edit_action(
    callback_query: CallbackQuery, state: FSMContext, bot: Bot
):  # Renamed
    """Handle confirmation to save an edited FAQ."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    data = await state.get_data()
    question = data.get("question", "N/A")
    answer = data.get("answer", "N/A")
    faq_index = data.get("faq_index")

    template_manager = TemplateManager()
    faq_template_data = template_manager.get_template("faq.json")
    faqs_list = faq_template_data.get("faqs", [])

    if faq_index is not None and 0 <= faq_index < len(faqs_list):
        faqs_list[faq_index] = {"question": question, "answer": answer}
        faq_template_data["faqs"] = faqs_list
        template_manager.save_template("faq.json", faq_template_data)

        await log_admin_action(
            user_id,
            "edit_faq",
            f"Edited FAQ (Index {faq_index}): {question[:50]}..."
        )
        await clear_state_data(state)

        success_message = (
            "✅ <b>\u2022 FAQ UPDATED SUCCESSFULLY \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"<b>Question:</b> {question}\n"
            f"<b>Answer:</b> {answer}\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "What would you like to do next?\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>The FAQ has been updated.</i>"
        )
        await callback_query.message.edit_text(
            success_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="📋 View All FAQs", callback_data="view_faqs_admin"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="✏️ Edit Another FAQ", callback_data="edit_faq"
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text=format_text("buttons", "faq_back"),
                            callback_data="manage_faqs",
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )
    else:
        error_message = (
            "❌ <b>\u2022 UPDATE FAILED \u2022</b>\n\n"
            "Error: FAQ not found or index out of bounds. The FAQ may have been deleted."
        )
        await callback_query.message.edit_text(
            error_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to FAQ Management",
                            callback_data="manage_faqs",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        await clear_state_data(state)


@router.callback_query(F.data == "edit_faq")
async def edit_faq_select_prompt(
    callback_query: CallbackQuery, state: FSMContext
):  # Renamed
    """Start the process of editing an FAQ by selecting one."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    faqs = get_faqs()

    if not faqs:
        empty_message = (
            "✏️ <b>\u2022 EDIT FAQ \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "⚠️ <b>No FAQs found to edit.</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "Please add some FAQs first.\n\n"
            "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
            "✨ <i>Use '➕ Add FAQ' to create entries.</i>"
        )
        await callback_query.message.edit_text(
            empty_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(text="➕ Add FAQ", callback_data="add_faq")],
                    [
                        InlineKeyboardButton(
                            text=format_text("buttons", "faq_back"),
                            callback_data="manage_faqs",
                        )
                    ],
                ]
            ),
            parse_mode="HTML",
        )
        return

    keyboard = []
    for i, faq_item in enumerate(faqs):  # Renamed to avoid conflict
        question = faq_item.get("question", "N/A")
        if len(question) > 30:
            question = question[:27] + "..."
        keyboard.append(
            [
                InlineKeyboardButton(
                    text=f"{i+1}. {question}", callback_data=f"select_faq_edit:{i}"
                )
            ]
        )
    keyboard.append(
        [
            InlineKeyboardButton(
                text=format_text("buttons", "faq_back"), callback_data="manage_faqs"
            )
        ]
    )

    selection_message = (
        "✏️ <b>\u2022 SELECT FAQ TO EDIT \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "Choose the FAQ you wish to modify from the list below:\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Click on an FAQ to start editing.</i>"
    )
    await callback_query.message.edit_text(
        selection_message,
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
        parse_mode="HTML",
    )


@router.callback_query(lambda c: c.data.startswith("select_faq_edit:"))
async def select_faq_to_edit_action(
    callback_query: CallbackQuery, state: FSMContext
):  # Renamed
    """Handle selection of an FAQ to edit."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    faq_index = int(callback_query.data.split(":")[1])
    faqs = get_faqs()

    if not (0 <= faq_index < len(faqs)):
        await callback_query.message.edit_text(
            "❌ <b>Error: Invalid FAQ selection.</b>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to FAQ List", callback_data="edit_faq"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    selected_faq = faqs[faq_index]
    await safe_update_data(
        state,
        edit_mode=True,  # Set edit mode
        faq_index=faq_index,
        current_question=selected_faq.get("question", "N/A"),
        current_answer=selected_faq.get("answer", "N/A"),
    )

    edit_prompt_message = (
        "✏️ <b>\u2022 EDIT FAQ \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "📝 <b>STEP 1: UPDATE QUESTION</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"<b>Current Question:</b>\n<i>{selected_faq.get('question', 'N/A')}</i>\n\n"
        f"<b>Current Answer:</b>\n<i>{selected_faq.get('answer', 'N/A')}</i>\n\n"
        "Please enter the new question for this FAQ.\n(Send the same text to keep the current question)\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>You will be prompted for the answer next.</i>"
    )
    await callback_query.message.edit_text(
        edit_prompt_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [InlineKeyboardButton(text="❌ Cancel Edit", callback_data="edit_faq")]
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(FAQManagementStates.waiting_for_question)


# This handler (process_edited_faq_question) is removed as its logic is merged into process_faq_question_input


@router.callback_query(F.data == "delete_faq")
async def delete_faq_select_prompt(
    callback_query: CallbackQuery, state: FSMContext
):  # Renamed
    """Start the process of deleting an FAQ by selecting one."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    faqs = get_faqs()

    if not faqs:
        empty_message = (
            "🗑️ <b>\u2022 DELETE FAQ \u2022</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            "⚠️ <b>No FAQs found to delete.</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>"
        )
        await callback_query.message.edit_text(
            empty_message,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text=format_text("buttons", "faq_back"),
                            callback_data="manage_faqs",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    keyboard = []
    for i, faq_item in enumerate(faqs):
        question = faq_item.get("question", "N/A")
        if len(question) > 30:
            question = question[:27] + "..."
        keyboard.append(
            [
                InlineKeyboardButton(
                    text=f"{i+1}. {question}", callback_data=f"select_faq_delete:{i}"
                )
            ]
        )
    keyboard.append(
        [
            InlineKeyboardButton(
                text=format_text("buttons", "faq_back"), callback_data="manage_faqs"
            )
        ]
    )

    selection_message = (
        "🗑️ <b>\u2022 SELECT FAQ TO DELETE \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "Choose the FAQ you wish to remove from the list below:\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>This action is irreversible.</i>"
    )
    await callback_query.message.edit_text(
        selection_message,
        reply_markup=InlineKeyboardMarkup(inline_keyboard=keyboard),
        parse_mode="HTML",
    )


@router.callback_query(lambda c: c.data.startswith("select_faq_delete:"))
async def select_faq_to_delete_action(
    callback_query: CallbackQuery, state: FSMContext
):  # Renamed
    """Handle selection of an FAQ to delete and confirm."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    faq_index = int(callback_query.data.split(":")[1])
    faqs = get_faqs()

    if not (0 <= faq_index < len(faqs)):
        await callback_query.message.edit_text(
            "❌ <b>Error: Invalid FAQ selection.</b>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to FAQ List", callback_data="delete_faq"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    selected_faq = faqs[faq_index]
    await safe_update_data(state, faq_index=faq_index)  # Store index for confirmation

    confirmation_message = (
        "🗑️ <b>\u2022 CONFIRM FAQ DELETION \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "<b>You are about to delete the following FAQ:</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"<b>Question:</b>\n<i>{selected_faq.get('question', 'N/A')}</i>\n\n"
        f"<b>Answer:</b>\n<i>{selected_faq.get('answer', 'N/A')}</i>\n\n"
        "Are you absolutely sure you want to delete this FAQ?\n"
        "<b>This action cannot be undone.</b>\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Please confirm your choice.</i>"
    )
    await callback_query.message.edit_text(
        confirmation_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="✅ Yes, Delete It",
                        callback_data="confirm_delete_faq_action",
                    )
                ],  # Changed action name
                [
                    InlineKeyboardButton(
                        text="❌ No, Keep It", callback_data="delete_faq"
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )
    await state.set_state(FAQManagementStates.confirm_delete)


@router.callback_query(
    F.data == "confirm_delete_faq_action", FAQManagementStates.confirm_delete
)  # Changed action name
async def confirm_delete_faq_action(
    callback_query: CallbackQuery, state: FSMContext, bot: Bot
):  # Renamed
    """Handle confirmation of FAQ deletion."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()
    data = await state.get_data()
    faq_index = data.get("faq_index")

    template_manager = TemplateManager()
    faq_template_data = template_manager.get_template("faq.json")
    faqs_list = faq_template_data.get("faqs", [])

    if faq_index is None or not (0 <= faq_index < len(faqs_list)):
        await callback_query.message.edit_text(
            "❌ <b>Error: FAQ not found or invalid index.</b> It might have been deleted already.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to FAQ List", callback_data="delete_faq"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        await clear_state_data(state)
        return

    deleted_faq = faqs_list.pop(faq_index)  # Remove and get the item
    faq_template_data["faqs"] = faqs_list
    template_manager.save_template("faq.json", faq_template_data)

    await log_admin_action(
        user_id,
        "delete_faq",
        f"Deleted FAQ: {deleted_faq.get('question', 'N/A')[:50]}..."
    )
    await clear_state_data(state)

    success_message = (
        "✅ <b>\u2022 FAQ DELETED SUCCESSFULLY \u2022</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "The selected FAQ has been removed from the system.\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "What would you like to do next?\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Manage other FAQs or return to the menu.</i>"
    )
    await callback_query.message.edit_text(
        success_message,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="📋 View All FAQs", callback_data="view_faqs_admin"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🗑️ Delete Another FAQ", callback_data="delete_faq"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text=format_text("buttons", "faq_back"),
                        callback_data="manage_faqs",
                    )
                ],
            ]
        ),
        parse_mode="HTML",
    )


# Maintenance Mode Handlers
@router.callback_query(F.data == "toggle_maintenance_mode")
async def toggle_maintenance_mode_handler(callback_query: CallbackQuery):
    """Show maintenance mode status and toggle options."""
    user_id = callback_query.from_user.id

    # Only allow owners or privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()

    # Get current maintenance mode status
    db_maintenance_mode = get_maintenance_mode()
    env_maintenance_mode = os.getenv("MAINTENANCE_MODE", "false").lower() == "true"
    is_maintenance_active = db_maintenance_mode or env_maintenance_mode

    # Format status text
    status_text = "🟢 ENABLED" if is_maintenance_active else "🔴 DISABLED"

    # Get maintenance message from template
    maintenance_message = format_text(
        "owner",
        "maintenance_mode",
        status=status_text,
        maintenance_message="<i>Default maintenance message will be shown to users.</i>",
        default=(
            "🛠️ <b>\u2022 MAINTENANCE MODE \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>SYSTEM MAINTENANCE</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"<i>Maintenance mode status:</i> <b>{status_text}</b>\n\n"
            "When enabled, only owners and admins can access the bot while regular users will see a maintenance message.\n\n"
            "<b>Custom Message:</b>\n"
            "<i>Default maintenance message will be shown to users.</i>\n\n"
            "<i>Use the controls below to manage maintenance mode.</i>"
        ),
    )

    await callback_query.message.edit_text(
        maintenance_message,
        reply_markup=maintenance_mode_keyboard(is_maintenance_active),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "maintenance_on")
async def enable_maintenance_mode(callback_query: CallbackQuery):
    """Enable maintenance mode."""
    user_id = callback_query.from_user.id

    # Only allow owners or privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    # Save maintenance mode setting to database
    success = save_maintenance_mode(enabled=True)

    if success:
        await callback_query.answer("✅ Maintenance mode enabled", show_alert=True)
        # Log the action
        log_admin_action(user_id, "Enabled maintenance mode")
    else:
        await callback_query.answer(
            "❌ Failed to enable maintenance mode", show_alert=True
        )

    # Refresh the maintenance mode page
    await toggle_maintenance_mode_handler(callback_query)


@router.callback_query(F.data == "maintenance_off")
async def disable_maintenance_mode(callback_query: CallbackQuery):
    """Disable maintenance mode."""
    user_id = callback_query.from_user.id

    # Only allow owners or privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    # Save maintenance mode setting to database
    success = save_maintenance_mode(enabled=False)

    if success:
        await callback_query.answer("✅ Maintenance mode disabled", show_alert=True)
        # Log the action
        log_admin_action(user_id, "Disabled maintenance mode")
    else:
        await callback_query.answer(
            "❌ Failed to disable maintenance mode", show_alert=True
        )

    # Refresh the maintenance mode page
    await toggle_maintenance_mode_handler(callback_query)


@router.callback_query(F.data == "edit_maintenance_message")
async def edit_maintenance_message_handler(
    callback_query: CallbackQuery, state: FSMContext
):
    """Handler for editing the maintenance message."""
    user_id = callback_query.from_user.id

    # Only allow owners or privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 <b>Access Denied</b>", show_alert=True, parse_mode="HTML"
        )
        return

    await callback_query.answer()

    # Set state to wait for maintenance message
    await state.set_state(AdditionalFeaturesStates.waiting_for_template_value)
    await safe_update_data(state, template_type="maintenance_message")

    # Get current message from template
    current_message = format_text(
        "user",
        "maintenance_active",
        default="Our system is currently undergoing scheduled maintenance.\nThe bot will be back online soon. Thank you for your patience.",
    )

    await callback_query.message.edit_text(
        "✏️ <b>\u2022 EDIT MAINTENANCE MESSAGE \u2022</b>\n\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        "<b>CUSTOMIZE MESSAGE</b>\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        "<i>Please enter the new maintenance message that will be shown to users:</i>\n\n"
        "<b>Current Message:</b>\n"
        f"{current_message}\n\n"
        "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰\n"
        "✨ <i>Type your new message below or use the Cancel button to return.</i>",
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="❌ Cancel", callback_data="toggle_maintenance_mode"
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )


@router.message(AdditionalFeaturesStates.waiting_for_template_value)
async def process_maintenance_message(message: Message, state: FSMContext):
    """Process the maintenance message input."""
    user_id = message.from_user.id

    # Only allow owners or privileged users with owner role
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await clear_state_data(state)
        return

    # Get template type from state
    data = await state.get_data()
    template_type = data.get("template_type")

    if template_type != "maintenance_message":
        await message.reply(
            "❌ <b>Error: Invalid template type.</b>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Maintenance Mode",
                            callback_data="toggle_maintenance_mode",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        await clear_state_data(state)
        return

    # Get the new message
    new_message = message.text.strip()

    if not new_message:
        await message.reply(
            "⚠️ <b>Message cannot be empty.</b>\n\nPlease enter a valid maintenance message.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="❌ Cancel",
                            callback_data="toggle_maintenance_mode",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        return

    # Save the maintenance message
    success = save_maintenance_mode(
        enabled=get_maintenance_mode(),  # Keep current enabled status
        message=new_message,
    )

    if success:
        # Log the action
        log_admin_action(user_id, "Updated maintenance message")

        await message.reply(
            "✅ <b>Maintenance message updated successfully!</b>",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Maintenance Mode",
                            callback_data="toggle_maintenance_mode",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
    else:
        await message.reply(
            "❌ <b>Failed to update maintenance message.</b>\n\nPlease try again later.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Maintenance Mode",
                            callback_data="toggle_maintenance_mode",
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )

    # Clear state
    await clear_state_data(state)


def register_owner_handlers(dp: Dispatcher):
    """Register the owner handlers with the dispatcher"""
    try:
        dp.include_router(router)
        logger.info("Owner and FAQ handlers registered successfully")
    except RuntimeError:
        logger.info("Owner and FAQ router already attached, skipping registration")


def register_category_handlers(_):  # This function seems to be a leftover/placeholder
    pass
