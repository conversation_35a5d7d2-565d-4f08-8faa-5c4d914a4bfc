"""
Exclusive Product Admin Interface - LEGACY REDIRECTS
Provides redirects to the unified product management system.

NOTE: This module now serves as a compatibility layer that redirects
all exclusive product management requests to the unified product management
system. All functionality has been consolidated into the unified interface.
"""

import logging
from datetime import datetime

from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton

from database.operations import is_owner
from handlers.sys_db import is_privileged
from utils.exclusive_product_db_operations import ExclusiveProductDBOperations
from utils.exclusive_product_manager import ExclusiveProductTheme


async def _create_mock_callback_query_exclusive(callback_data: str, original_callback: CallbackQuery):
    """Create a mock CallbackQuery for exclusive product operations."""
    async def mock_answer(*args, **kwargs):
        return None

    return type('CallbackQuery', (), {
        'data': callback_data,
        'from_user': original_callback.from_user,
        'message': original_callback.message,
        'answer': mock_answer
    })()
from utils.telegram_helpers import safe_edit_message

logger = logging.getLogger(__name__)
router = Router()

@router.callback_query(F.data == "admin_exclusive_products")
async def admin_exclusive_products_main(callback_query: CallbackQuery):
    """Main admin panel for exclusive products."""
    user_id = callback_query.from_user.id
    
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return
    
    await callback_query.answer("🔄 Redirecting to Unified Product Management...", show_alert=False)

    # Redirect to unified exclusive management
    from handlers.unified_product_admin import unified_manage_exclusive_only

    # Create a new callback query object for the unified handler
    unified_callback = await _create_mock_callback_query_exclusive('unified_manage_exclusive', callback_query)
    await unified_manage_exclusive_only(unified_callback)

@router.callback_query(F.data == "admin_exclusive_available")
async def admin_exclusive_available_products(callback_query: CallbackQuery):
    """Redirect to unified available exclusive products view."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer("🔄 Redirecting to Unified Management...", show_alert=False)

    # Redirect to unified available exclusive products
    from handlers.unified_product_admin import unified_exclusive_available_only

    unified_callback = type('CallbackQuery', (), {
        'data': 'unified_exclusive_available',
        'from_user': callback_query.from_user,
        'message': callback_query.message,
        'answer': lambda *args, **kwargs: None
    })()

    await unified_exclusive_available_only(unified_callback)
    
    # Get available exclusive products
    available_products = ExclusiveProductDBOperations.get_available_exclusive_products()
    
    header = ExclusiveProductTheme.create_header("AVAILABLE EXCLUSIVE PRODUCTS", "READY FOR PURCHASE")
    
    if not available_products:
        message_text = (
            f"{header}\n"
            f"📭 <b>No available exclusive products found.</b>\n\n"
            f"All exclusive products have either been sold or expired.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create new exclusive products to expand your inventory.</i>"
        )
        
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Exclusive Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Exclusive Management",
                        callback_data="admin_exclusive_products"
                    )
                ]
            ]
        )
    else:
        products_text = ""
        for i, product in enumerate(available_products[:10], 1):  # Limit to 10 for display
            name = product.get("name", "Unnamed Product")
            price = product.get("price", 0)
            file_type = product.get("exclusive_file_type", "Unknown")
            created_date = product.get("created_at", datetime.now())
            
            if isinstance(created_date, str):
                try:
                    created_date = datetime.fromisoformat(created_date)
                except:
                    created_date = datetime.now()
            
            # Safe date formatting
            if isinstance(created_date, str):
                date_str = created_date
            elif hasattr(created_date, 'strftime'):
                date_str = created_date.strftime('%Y-%m-%d')
            else:
                date_str = str(created_date)

            products_text += (
                f"📄 <b>{i}. {name}</b>\n"
                f"💰 <b>Price:</b> <code>${price:.2f}</code>\n"
                f"📁 <b>Type:</b> <code>{file_type}</code>\n"
                f"📅 <b>Created:</b> <code>{date_str}</code>\n\n"
            )
        
        if len(available_products) > 10:
            products_text += f"... and {len(available_products) - 10} more products\n\n"
        
        message_text = (
            f"{header}\n"
            f"🟢 <b>Found {len(available_products)} available exclusive products:</b>\n\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"{products_text}"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>These products are ready for purchase by customers.</i>"
        )
        
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="📋 View All Products",
                        callback_data="admin_exclusive_list"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Exclusive Management",
                        callback_data="admin_exclusive_products"
                    )
                ]
            ]
        )
    
    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "admin_exclusive_list")
async def admin_exclusive_list_all(callback_query: CallbackQuery):
    """Redirect to unified exclusive products management."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer("🔄 Redirecting to Unified Management...", show_alert=False)

    # Redirect to unified exclusive management
    from handlers.unified_product_admin import unified_manage_exclusive_only

    unified_callback = type('CallbackQuery', (), {
        'data': 'unified_manage_exclusive',
        'from_user': callback_query.from_user,
        'message': callback_query.message,
        'answer': lambda *args, **kwargs: None
    })()

    await unified_manage_exclusive_only(unified_callback)

    # Get all exclusive products for admin
    all_products = ExclusiveProductDBOperations.get_all_exclusive_products_for_admin(include_removed=True)

    header = ExclusiveProductTheme.create_header("ALL EXCLUSIVE PRODUCTS", "COMPLETE ADMIN VIEW")

    if not all_products:
        message_text = (
            f"{header}\n"
            f"📭 <b>No exclusive products found.</b>\n\n"
            f"No exclusive products have been created yet.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create your first exclusive product to get started.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Exclusive Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Exclusive Management",
                        callback_data="admin_exclusive_products"
                    )
                ]
            ]
        )
    else:
        # Show individual product management buttons
        message_text = (
            f"{header}\n"
            f"📊 <b>Found {len(all_products)} exclusive products</b>\n\n"
            f"Click on any product below to view details and manage:\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"⚙️ <i>Select a product to view details and management options.</i>"
        )

        # Build keyboard with individual product buttons
        keyboard_rows = []

        # Sort products by status and name for better organization
        available = [p for p in all_products if not p.get("is_purchased", False) and not p.get("removed_from_listings", False)]
        purchased = [p for p in all_products if p.get("is_purchased", False) and not p.get("removed_from_listings", False)]
        removed = [p for p in all_products if p.get("removed_from_listings", False)]

        # Combine in order: available, purchased, removed
        sorted_products = available + purchased + removed

        for product in sorted_products:
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name", "Unknown Product")
            price = product.get("price", 0)

            # Status indicators
            is_purchased = product.get("is_purchased", False)
            removed_from_listings = product.get("removed_from_listings", False)

            if is_purchased and removed_from_listings:
                status_emoji = "🔴"  # Red for sold & delivered
                status_text = "DELIVERED"
            elif is_purchased:
                status_emoji = "🟡"  # Yellow for sold but not delivered
                status_text = "SOLD"
            elif removed_from_listings:
                status_emoji = "⚫"  # Black for removed
                status_text = "REMOVED"
            else:
                status_emoji = "🟢"  # Green for available
                status_text = "AVAILABLE"

            # Truncate long names
            display_name = product_name[:25] + "..." if len(product_name) > 25 else product_name
            button_text = f"{status_emoji} {display_name} - ${price:.2f} ({status_text})"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"exclusive_manage:{product_id}"
                )
            ])

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📊 View Summary",
                callback_data="admin_exclusive_summary"
            ),
            InlineKeyboardButton(
                text="📈 Detailed Stats",
                callback_data="admin_exclusive_stats"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Exclusive Management",
                callback_data="admin_exclusive_products"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "admin_exclusive_summary")
async def admin_exclusive_summary_view(callback_query: CallbackQuery):
    """Show summary view of exclusive products (the old list view)."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all exclusive products for admin
    all_products = ExclusiveProductDBOperations.get_all_exclusive_products_for_admin(include_removed=True)

    header = ExclusiveProductTheme.create_header("EXCLUSIVE PRODUCTS SUMMARY", "OVERVIEW BY STATUS")

    if not all_products:
        message_text = (
            f"{header}\n"
            f"📭 <b>No exclusive products found.</b>\n\n"
            f"No exclusive products have been created yet.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create your first exclusive product to get started.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Exclusive Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Product List",
                        callback_data="admin_exclusive_list"
                    )
                ]
            ]
        )
    else:
        # Categorize products
        available = [p for p in all_products if not p.get("is_purchased", False) and not p.get("removed_from_listings", False)]
        purchased = [p for p in all_products if p.get("is_purchased", False) and not p.get("removed_from_listings", False)]
        removed = [p for p in all_products if p.get("removed_from_listings", False)]

        products_text = ""

        # Available products
        if available:
            products_text += f"🟢 <b>AVAILABLE ({len(available)})</b>\n"
            products_text += f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            for i, product in enumerate(available[:5], 1):  # Show first 5
                name = product.get("name", "Unnamed Product")
                price = product.get("price", 0)
                products_text += f"📄 <b>{i}. {name}</b> - <code>${price:.2f}</code>\n"
            if len(available) > 5:
                products_text += f"... and {len(available) - 5} more available\n"
            products_text += "\n"

        # Purchased products
        if purchased:
            products_text += f"🔴 <b>PURCHASED ({len(purchased)})</b>\n"
            products_text += f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            for i, product in enumerate(purchased[:3], 1):  # Show first 3
                name = product.get("name", "Unnamed Product")
                price = product.get("price", 0)
                buyer_id = product.get("purchased_by_user_id", "Unknown")
                products_text += f"📄 <b>{i}. {name}</b> - <code>${price:.2f}</code> (User: {buyer_id})\n"
            if len(purchased) > 3:
                products_text += f"... and {len(purchased) - 3} more purchased\n"
            products_text += "\n"

        # Removed products
        if removed:
            products_text += f"📤 <b>DELIVERED & REMOVED ({len(removed)})</b>\n"
            products_text += f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            for i, product in enumerate(removed[:3], 1):  # Show first 3
                name = product.get("name", "Unnamed Product")
                price = product.get("price", 0)
                products_text += f"📄 <b>{i}. {name}</b> - <code>${price:.2f}</code> (Delivered)\n"
            if len(removed) > 3:
                products_text += f"... and {len(removed) - 3} more delivered\n"

        message_text = (
            f"{header}\n"
            f"📊 <b>Found {len(all_products)} total exclusive products:</b>\n\n"
            f"{products_text}"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Summary overview of all exclusive products by status.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="📋 Manage Individual Products",
                        callback_data="admin_exclusive_list"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🟢 Available Only",
                        callback_data="admin_exclusive_available"
                    ),
                    InlineKeyboardButton(
                        text="📊 Detailed Stats",
                        callback_data="admin_exclusive_stats"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Exclusive Management",
                        callback_data="admin_exclusive_products"
                    )
                ]
            ]
        )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "admin_exclusive_expired")
async def admin_exclusive_expired_products(callback_query: CallbackQuery):
    """Show expired exclusive products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all exclusive products and filter expired ones
    all_products = ExclusiveProductDBOperations.get_all_exclusive_products_for_admin(include_removed=True)

    # Filter for expired products (products with expiration_date in the past)
    expired_products = []
    current_time = datetime.now()

    for product in all_products:
        expiration_date = product.get("expiration_date")
        if expiration_date:
            # Handle both string and datetime objects
            if isinstance(expiration_date, str):
                try:
                    expiration_date = datetime.fromisoformat(expiration_date)
                except:
                    continue

            if expiration_date < current_time and not product.get("is_purchased", False):
                expired_products.append(product)

    header = ExclusiveProductTheme.create_header("EXPIRED EXCLUSIVE PRODUCTS", "PAST EXPIRATION DATE")

    if not expired_products:
        message_text = (
            f"{header}\n"
            f"✅ <b>No expired exclusive products found.</b>\n\n"
            f"All exclusive products are either still available or have been purchased.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Expired products are automatically hidden from customers.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🟢 View Available Products",
                        callback_data="admin_exclusive_available"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Exclusive Management",
                        callback_data="admin_exclusive_products"
                    )
                ]
            ]
        )
    else:
        products_text = ""
        for i, product in enumerate(expired_products[:10], 1):  # Limit to 10 for display
            name = product.get("name", "Unnamed Product")
            price = product.get("price", 0)
            expiration_date = product.get("expiration_date")

            if isinstance(expiration_date, str):
                try:
                    expiration_date = datetime.fromisoformat(expiration_date)
                except:
                    expiration_date = datetime.now()

            days_expired = (current_time - expiration_date).days

            # Safe date formatting
            if isinstance(expiration_date, str):
                date_str = expiration_date
            elif hasattr(expiration_date, 'strftime'):
                date_str = expiration_date.strftime('%Y-%m-%d')
            else:
                date_str = str(expiration_date)

            products_text += (
                f"⏰ <b>{i}. {name}</b>\n"
                f"💰 <b>Price:</b> <code>${price:.2f}</code>\n"
                f"📅 <b>Expired:</b> <code>{date_str}</code> ({days_expired} days ago)\n\n"
            )

        if len(expired_products) > 10:
            products_text += f"... and {len(expired_products) - 10} more expired products\n\n"

        message_text = (
            f"{header}\n"
            f"⏰ <b>Found {len(expired_products)} expired exclusive products:</b>\n\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"{products_text}"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>These products are no longer available for purchase.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔧 Bulk Operations",
                        callback_data="admin_exclusive_bulk"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Exclusive Management",
                        callback_data="admin_exclusive_products"
                    )
                ]
            ]
        )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "admin_exclusive_stats")
async def admin_exclusive_detailed_stats(callback_query: CallbackQuery):
    """Redirect to unified exclusive statistics."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer("🔄 Redirecting to Unified Statistics...", show_alert=False)

    # Redirect to unified exclusive statistics
    from handlers.unified_product_admin import unified_exclusive_statistics

    unified_callback = await _create_mock_callback_query_exclusive('unified_exclusive_stats', callback_query)

    await unified_exclusive_statistics(unified_callback)

    # Get comprehensive statistics
    stats = ExclusiveProductDBOperations.get_exclusive_product_statistics()
    all_products = ExclusiveProductDBOperations.get_all_exclusive_products_for_admin(include_removed=True)

    header = ExclusiveProductTheme.create_header("DETAILED STATISTICS", "COMPREHENSIVE ANALYTICS")

    if stats.get("error"):
        message_text = (
            f"{header}\n"
            f"❌ <b>Error loading detailed statistics:</b>\n"
            f"<code>{stats['error']}</code>\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Please try again or contact support if the issue persists.</i>"
        )
    else:
        # Calculate additional metrics
        total_products = len(all_products)
        available_count = len([p for p in all_products if not p.get("is_purchased", False) and not p.get("removed_from_listings", False)])
        purchased_count = len([p for p in all_products if p.get("is_purchased", False)])
        removed_count = len([p for p in all_products if p.get("removed_from_listings", False)])

        # Calculate revenue metrics
        total_revenue = sum(p.get("price") or 0.0 for p in all_products if p.get("is_purchased", False))
        avg_price = sum(p.get("price") or 0.0 for p in all_products) / max(total_products, 1)

        # Calculate conversion rate
        conversion_rate = (purchased_count / max(total_products, 1)) * 100

        # Recent activity
        current_time = datetime.now()
        recent_purchases = [p for p in all_products if p.get("purchase_date") and
                          isinstance(p.get("purchase_date"), datetime) and
                          (current_time - p.get("purchase_date")).days <= 7]

        # File type distribution
        file_types = {}
        for product in all_products:
            file_type = product.get("exclusive_file_type", "Unknown")
            file_types[file_type] = file_types.get(file_type, 0) + 1

        file_types_text = ""
        for file_type, count in sorted(file_types.items(), key=lambda x: x[1], reverse=True):
            file_types_text += f"📁 <b>{file_type}:</b> <code>{count}</code>\n"

        message_text = (
            f"{header}\n"
            f"📊 <b>COMPREHENSIVE ANALYTICS REPORT</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"📈 <b>PRODUCT METRICS</b>\n"
            f"📄 <b>Total Products:</b> <code>{total_products}</code>\n"
            f"🟢 <b>Available:</b> <code>{available_count}</code>\n"
            f"🔴 <b>Purchased:</b> <code>{purchased_count}</code>\n"
            f"📤 <b>Delivered & Removed:</b> <code>{removed_count}</code>\n\n"
            f"💰 <b>REVENUE METRICS</b>\n"
            f"💵 <b>Total Revenue:</b> <code>${total_revenue:.2f}</code>\n"
            f"📊 <b>Average Price:</b> <code>${avg_price:.2f}</code>\n"
            f"📈 <b>Conversion Rate:</b> <code>{conversion_rate:.1f}%</code>\n\n"
            f"⏰ <b>RECENT ACTIVITY (7 days)</b>\n"
            f"🛒 <b>Recent Purchases:</b> <code>{len(recent_purchases)}</code>\n\n"
            f"📁 <b>FILE TYPE DISTRIBUTION</b>\n"
            f"{file_types_text}\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"📅 <b>Report Generated:</b> <code>{current_time.strftime('%Y-%m-%d %H:%M:%S')}</code>"
        )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📋 View All Products",
                    callback_data="admin_exclusive_list"
                ),
                InlineKeyboardButton(
                    text="🟢 Available Only",
                    callback_data="admin_exclusive_available"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔄 Refresh Stats",
                    callback_data="admin_exclusive_stats"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Exclusive Management",
                    callback_data="admin_exclusive_products"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "admin_exclusive_bulk")
async def admin_exclusive_bulk_operations(callback_query: CallbackQuery):
    """Show bulk operations menu for exclusive products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get product counts for context
    all_products = ExclusiveProductDBOperations.get_all_exclusive_products_for_admin(include_removed=True)
    available_count = len([p for p in all_products if not p.get("is_purchased", False) and not p.get("removed_from_listings", False)])
    expired_count = len([p for p in all_products if p.get("expiration_date") and
                        isinstance(p.get("expiration_date"), datetime) and
                        p.get("expiration_date") < datetime.now() and
                        not p.get("is_purchased", False)])

    header = ExclusiveProductTheme.create_header("BULK OPERATIONS", "MASS MANAGEMENT TOOLS")

    message_text = (
        f"{header}\n"
        f"🔧 <b>BULK MANAGEMENT OPERATIONS</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"📊 <b>CURRENT STATUS</b>\n"
        f"📄 <b>Total Products:</b> <code>{len(all_products)}</code>\n"
        f"🟢 <b>Available Products:</b> <code>{available_count}</code>\n"
        f"⏰ <b>Expired Products:</b> <code>{expired_count}</code>\n\n"
        f"⚠️ <b>AVAILABLE OPERATIONS</b>\n"
        f"• Clean up expired products\n"
        f"• Export product data\n"
        f"• Bulk status updates\n"
        f"• System maintenance\n\n"
        f"{ExclusiveProductTheme.create_section_break()}\n"
        f"⚠️ <i>Bulk operations affect multiple products. Use with caution!</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🧹 Clean Expired Products",
                    callback_data="bulk_clean_expired"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📊 Export Product Data",
                    callback_data="bulk_export_data"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔄 Refresh Cache",
                    callback_data="bulk_refresh_cache"
                )
            ],
            [
                InlineKeyboardButton(
                    text="⏰ View Expired Products",
                    callback_data="admin_exclusive_expired"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Exclusive Management",
                    callback_data="admin_exclusive_products"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "bulk_clean_expired")
async def bulk_clean_expired_products(callback_query: CallbackQuery):
    """Clean up expired exclusive products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer("🧹 Cleaning expired products...", show_alert=True)

    # This is a placeholder for the actual cleanup operation
    # In a real implementation, you would implement the cleanup logic

    header = ExclusiveProductTheme.create_header("CLEANUP COMPLETE", "EXPIRED PRODUCTS PROCESSED")

    message_text = (
        f"{header}\n"
        f"✅ <b>Expired Product Cleanup Completed</b>\n\n"
        f"🧹 <b>Operation Status:</b> <code>Success</code>\n"
        f"⏰ <b>Processed:</b> <code>0 expired products</code>\n"
        f"🗑️ <b>Cleaned:</b> <code>0 products</code>\n\n"
        f"{ExclusiveProductTheme.create_section_break()}\n"
        f"💡 <i>Cleanup operation completed successfully.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔧 Back to Bulk Operations",
                    callback_data="admin_exclusive_bulk"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Exclusive Management",
                    callback_data="admin_exclusive_products"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "bulk_export_data")
async def bulk_export_product_data(callback_query: CallbackQuery):
    """Export exclusive product data."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer("📊 Preparing export...", show_alert=True)

    # Get all products for export summary
    all_products = ExclusiveProductDBOperations.get_all_exclusive_products_for_admin(include_removed=True)

    header = ExclusiveProductTheme.create_header("DATA EXPORT", "PRODUCT DATA SUMMARY")

    message_text = (
        f"{header}\n"
        f"📊 <b>Export Data Summary</b>\n\n"
        f"📄 <b>Total Products:</b> <code>{len(all_products)}</code>\n"
        f"📅 <b>Export Date:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>\n\n"
        f"📋 <b>Export includes:</b>\n"
        f"• Product names and descriptions\n"
        f"• Pricing information\n"
        f"• Purchase status and dates\n"
        f"• File type information\n"
        f"• Creation and modification dates\n\n"
        f"{ExclusiveProductTheme.create_section_break()}\n"
        f"💡 <i>Export functionality is ready for implementation.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔧 Back to Bulk Operations",
                    callback_data="admin_exclusive_bulk"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Exclusive Management",
                    callback_data="admin_exclusive_products"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "bulk_refresh_cache")
async def bulk_refresh_cache(callback_query: CallbackQuery):
    """Refresh exclusive product cache."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer("🔄 Refreshing cache...", show_alert=True)

    try:
        # Clear exclusive product cache
        from utils.exclusive_product_performance import exclusive_product_cache
        exclusive_product_cache.clear_all()

        header = ExclusiveProductTheme.create_header("CACHE REFRESH", "SYSTEM OPTIMIZATION")

        message_text = (
            f"{header}\n"
            f"✅ <b>Cache Refresh Completed</b>\n\n"
            f"🔄 <b>Operation Status:</b> <code>Success</code>\n"
            f"🗄️ <b>Cache Cleared:</b> <code>All exclusive product cache</code>\n"
            f"⚡ <b>Performance:</b> <code>Optimized</code>\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Cache has been refreshed for optimal performance.</i>"
        )

    except Exception as e:
        header = ExclusiveProductTheme.create_header("CACHE REFRESH", "OPERATION ERROR")

        message_text = (
            f"{header}\n"
            f"❌ <b>Cache Refresh Failed</b>\n\n"
            f"🔄 <b>Operation Status:</b> <code>Error</code>\n"
            f"⚠️ <b>Error Details:</b> <code>{str(e)}</code>\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Please try again or contact support.</i>"
        )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔧 Back to Bulk Operations",
                    callback_data="admin_exclusive_bulk"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Exclusive Management",
                    callback_data="admin_exclusive_products"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ============================================================================
# INDIVIDUAL EXCLUSIVE PRODUCT MANAGEMENT HANDLERS
# ============================================================================

@router.callback_query(F.data.startswith("exclusive_manage:"))
async def admin_exclusive_product_detail(callback_query: CallbackQuery):
    """Redirect to unified product detail view."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer("🔄 Redirecting to Unified Product Management...", show_alert=False)

    # Extract product ID and redirect to unified product detail
    product_id = callback_query.data.split(":")[1]

    from handlers.unified_product_admin import unified_product_detail

    unified_callback = await _create_mock_callback_query_exclusive(f'unified_product_detail:{product_id}', callback_query)
    await unified_product_detail(unified_callback)

    # Extract product ID from callback data
    product_id = callback_query.data.split(":")[1]

    try:
        # Get product details
        product = ExclusiveProductDBOperations.get_exclusive_product_by_id(product_id)

        if not product:
            await callback_query.message.edit_text(
                "❌ <b>Product Not Found</b>\n\n"
                "The requested exclusive product could not be found.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Exclusive Products",
                            callback_data="admin_exclusive_list"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
            return

        # Build detailed product information
        name = product.get("name", "Unknown Product")
        description = product.get("description", "No description")
        price = product.get("price", 0)
        is_purchased = product.get("is_purchased", False)
        removed_from_listings = product.get("removed_from_listings", False)
        purchased_by = product.get("purchased_by_user_id")
        purchase_date = product.get("purchase_date")
        delivery_date = product.get("delivery_date")
        file_path = product.get("file_path", "No file")
        created_date = product.get("created_date")

        # Status determination
        if is_purchased and removed_from_listings:
            status = "🔴 SOLD & DELIVERED"
            status_color = "🔴"
        elif is_purchased:
            status = "🟡 SOLD (Pending Delivery)"
            status_color = "🟡"
        elif removed_from_listings:
            status = "⚫ REMOVED FROM LISTINGS"
            status_color = "⚫"
        else:
            status = "🟢 AVAILABLE FOR PURCHASE"
            status_color = "🟢"

        # Format dates with proper type checking
        def format_date(date_obj):
            if not date_obj:
                return "Unknown"
            if isinstance(date_obj, str):
                return date_obj  # Already formatted
            if hasattr(date_obj, 'strftime'):
                return date_obj.strftime("%Y-%m-%d %H:%M")
            return str(date_obj)

        created_str = format_date(created_date)
        purchase_str = format_date(purchase_date) if purchase_date else "N/A"
        delivery_str = format_date(delivery_date) if delivery_date else "N/A"

        # Build detailed message
        header = ExclusiveProductTheme.create_header("EXCLUSIVE PRODUCT DETAILS")

        message_text = (
            f"{header}\n"
            f"📄 <b>PRODUCT INFORMATION</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"🏷️ <b>Name:</b> {name}\n"
            f"💰 <b>Price:</b> <code>${price:.2f}</code>\n"
            f"📝 <b>Description:</b> {description[:100]}{'...' if len(description) > 100 else ''}\n"
            f"📁 <b>File:</b> {file_path.split('/')[-1] if '/' in file_path else file_path}\n"
            f"📅 <b>Created:</b> {created_str}\n\n"

            f"{status_color} <b>STATUS INFORMATION</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"🔄 <b>Current Status:</b> {status}\n"
        )

        if purchased_by:
            message_text += f"👤 <b>Purchased By:</b> User ID {purchased_by}\n"
        if purchase_date:
            message_text += f"🛒 <b>Purchase Date:</b> {purchase_str}\n"
        if delivery_date:
            message_text += f"📦 <b>Delivery Date:</b> {delivery_str}\n"

        message_text += (
            f"\n{ExclusiveProductTheme.create_section_break()}\n"
            f"⚙️ <i>Use the buttons below to manage this product.</i>"
        )

        # Build management keyboard based on current state
        keyboard_rows = []

        # State management buttons
        if not is_purchased and not removed_from_listings:
            # Available product - can mark as purchased or remove
            keyboard_rows.append([
                InlineKeyboardButton(
                    text="🔴 Mark as Purchased",
                    callback_data=f"exclusive_mark_purchased:{product_id}"
                ),
                InlineKeyboardButton(
                    text="⚫ Remove from Listings",
                    callback_data=f"exclusive_remove:{product_id}"
                )
            ])
        elif is_purchased and not removed_from_listings:
            # Purchased but not delivered - can deliver or restore
            keyboard_rows.append([
                InlineKeyboardButton(
                    text="📦 Mark as Delivered",
                    callback_data=f"exclusive_mark_delivered:{product_id}"
                ),
                InlineKeyboardButton(
                    text="🔄 Restore to Available",
                    callback_data=f"exclusive_restore:{product_id}"
                )
            ])
        elif removed_from_listings:
            # Removed - can restore
            keyboard_rows.append([
                InlineKeyboardButton(
                    text="🔄 Restore to Available",
                    callback_data=f"exclusive_restore:{product_id}"
                )
            ])

        # File management button
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📁 View File Details",
                callback_data=f"exclusive_file_details:{product_id}"
            ),
            InlineKeyboardButton(
                text="🗑️ Delete Product",
                callback_data=f"exclusive_delete_confirm:{product_id}"
            )
        ])

        # Navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to List",
                callback_data="admin_exclusive_list"
            ),
            InlineKeyboardButton(
                text="🏠 Exclusive Management",
                callback_data="admin_exclusive_products"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error showing exclusive product details: {e}")
        await callback_query.message.edit_text(
            "❌ <b>Error</b>\n\n"
            f"Failed to load product details: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to List",
                        callback_data="admin_exclusive_list"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("exclusive_mark_purchased:"))
async def admin_exclusive_mark_purchased(callback_query: CallbackQuery):
    """Mark an exclusive product as purchased (with confirmation)."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    # Show confirmation dialog
    message_text = (
        "⚠️ <b>CONFIRM ACTION</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "🔴 <b>MARK AS PURCHASED</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Are you sure you want to mark this exclusive product as <b>purchased</b>?\n\n"
        "This action will:\n"
        "• Change the product status to 'SOLD'\n"
        "• Record the current timestamp as purchase date\n"
        "• Make the product unavailable for other customers\n\n"
        "⚠️ <i>This action can be reversed if needed.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm Purchase",
                    callback_data=f"exclusive_confirm_purchased:{product_id}"
                ),
                InlineKeyboardButton(
                    text="❌ Cancel",
                    callback_data=f"exclusive_manage:{product_id}"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("exclusive_confirm_purchased:"))
async def admin_exclusive_confirm_purchased(callback_query: CallbackQuery):
    """Confirm marking product as purchased."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    try:
        # Mark as purchased using admin user ID
        result = ExclusiveProductDBOperations.mark_as_purchased(
            product_id,
            user_id,  # Use admin user ID as purchaser
            admin_action=True
        )

        if result["success"]:
            await callback_query.answer("✅ Product marked as purchased!", show_alert=True)
            # Return to product detail view
            mock_callback = await _create_mock_callback_query_exclusive(f'exclusive_manage:{product_id}', callback_query)
            await admin_exclusive_product_detail(mock_callback)
        else:
            await callback_query.message.edit_text(
                f"❌ <b>Error</b>\n\n"
                f"Failed to mark product as purchased: {result.get('error', 'Unknown error')}",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Product",
                            callback_data=f"exclusive_manage:{product_id}"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
    except Exception as e:
        logger.error(f"Error marking product as purchased: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to mark product as purchased: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"exclusive_manage:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("exclusive_mark_delivered:"))
async def admin_exclusive_mark_delivered(callback_query: CallbackQuery):
    """Mark an exclusive product as delivered (with confirmation)."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    # Show confirmation dialog
    message_text = (
        "⚠️ <b>CONFIRM ACTION</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "📦 <b>MARK AS DELIVERED</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Are you sure you want to mark this exclusive product as <b>delivered</b>?\n\n"
        "This action will:\n"
        "• Change the product status to 'DELIVERED'\n"
        "• Remove the product from customer listings\n"
        "• Record the current timestamp as delivery date\n"
        "• Complete the product lifecycle\n\n"
        "⚠️ <i>This action can be reversed if needed.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm Delivery",
                    callback_data=f"exclusive_confirm_delivered:{product_id}"
                ),
                InlineKeyboardButton(
                    text="❌ Cancel",
                    callback_data=f"exclusive_manage:{product_id}"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("exclusive_confirm_delivered:"))
async def admin_exclusive_confirm_delivered(callback_query: CallbackQuery):
    """Confirm marking product as delivered."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    try:
        # Mark as delivered
        result = ExclusiveProductDBOperations.mark_as_delivered(product_id, admin_action=True)

        if result["success"]:
            await callback_query.answer("✅ Product marked as delivered!", show_alert=True)
            # Return to product detail view
            mock_callback = await _create_mock_callback_query_exclusive(f'exclusive_manage:{product_id}', callback_query)
            await admin_exclusive_product_detail(mock_callback)
        else:
            await callback_query.message.edit_text(
                f"❌ <b>Error</b>\n\n"
                f"Failed to mark product as delivered: {result.get('error', 'Unknown error')}",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Product",
                            callback_data=f"exclusive_manage:{product_id}"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
    except Exception as e:
        logger.error(f"Error marking product as delivered: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to mark product as delivered: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"exclusive_manage:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("exclusive_remove:"))
async def admin_exclusive_remove_from_listings(callback_query: CallbackQuery):
    """Remove an exclusive product from listings (with confirmation)."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    # Show confirmation dialog
    message_text = (
        "⚠️ <b>CONFIRM ACTION</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "⚫ <b>REMOVE FROM LISTINGS</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Are you sure you want to remove this exclusive product from listings?\n\n"
        "This action will:\n"
        "• Hide the product from customer view\n"
        "• Prevent new purchases\n"
        "• Keep the product in admin view\n"
        "• Allow restoration later if needed\n\n"
        "⚠️ <i>This action can be reversed.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm Removal",
                    callback_data=f"exclusive_confirm_remove:{product_id}"
                ),
                InlineKeyboardButton(
                    text="❌ Cancel",
                    callback_data=f"exclusive_manage:{product_id}"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("exclusive_confirm_remove:"))
async def admin_exclusive_confirm_remove(callback_query: CallbackQuery):
    """Confirm removing product from listings."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    try:
        # Remove from listings
        result = ExclusiveProductDBOperations.remove_from_listings(product_id, admin_action=True)

        if result["success"]:
            await callback_query.answer("✅ Product removed from listings!", show_alert=True)
            # Return to product detail view
            mock_callback = await _create_mock_callback_query_exclusive(f'exclusive_manage:{product_id}', callback_query)
            await admin_exclusive_product_detail(mock_callback)
        else:
            await callback_query.message.edit_text(
                f"❌ <b>Error</b>\n\n"
                f"Failed to remove product from listings: {result.get('error', 'Unknown error')}",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Product",
                            callback_data=f"exclusive_manage:{product_id}"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
    except Exception as e:
        logger.error(f"Error removing product from listings: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to remove product from listings: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"exclusive_manage:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("exclusive_restore:"))
async def admin_exclusive_restore_product(callback_query: CallbackQuery):
    """Restore an exclusive product to available status (with confirmation)."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    # Show confirmation dialog
    message_text = (
        "⚠️ <b>CONFIRM ACTION</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "🔄 <b>RESTORE TO AVAILABLE</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        "Are you sure you want to restore this exclusive product to available status?\n\n"
        "This action will:\n"
        "• Reset the product to 'AVAILABLE' status\n"
        "• Clear purchase and delivery information\n"
        "• Make the product visible to customers again\n"
        "• Allow new purchases\n\n"
        "⚠️ <i>Use this carefully - it will reset the product's purchase history.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm Restore",
                    callback_data=f"exclusive_confirm_restore:{product_id}"
                ),
                InlineKeyboardButton(
                    text="❌ Cancel",
                    callback_data=f"exclusive_manage:{product_id}"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("exclusive_confirm_restore:"))
async def admin_exclusive_confirm_restore(callback_query: CallbackQuery):
    """Confirm restoring product to available status."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    try:
        # Restore to available status
        result = ExclusiveProductDBOperations.restore_to_available(product_id, admin_action=True)

        if result["success"]:
            await callback_query.answer("✅ Product restored to available!", show_alert=True)
            # Return to product detail view
            await admin_exclusive_product_detail(
                type('CallbackQuery', (), {
                    'data': f'exclusive_manage:{product_id}',
                    'from_user': callback_query.from_user,
                    'message': callback_query.message,
                    'answer': lambda *args, **kwargs: None
                })()
            )
        else:
            await callback_query.message.edit_text(
                f"❌ <b>Error</b>\n\n"
                f"Failed to restore product: {result.get('error', 'Unknown error')}",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Product",
                            callback_data=f"exclusive_manage:{product_id}"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
    except Exception as e:
        logger.error(f"Error restoring product: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to restore product: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"exclusive_manage:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("exclusive_delete_confirm:"))
async def admin_exclusive_delete_confirm(callback_query: CallbackQuery):
    """Show delete confirmation dialog for exclusive product."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    # Get product name for confirmation
    try:
        product = ExclusiveProductDBOperations.get_exclusive_product_by_id(product_id)
        product_name = product.get("name", "Unknown Product") if product else "Unknown Product"
    except:
        product_name = "Unknown Product"

    # Show confirmation dialog
    message_text = (
        "🚨 <b>DANGER - PERMANENT DELETION</b>\n\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        "🗑️ <b>DELETE EXCLUSIVE PRODUCT</b>\n"
        "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"Are you sure you want to <b>permanently delete</b> the product:\n"
        f"<b>'{product_name}'</b>?\n\n"
        "⚠️ <b>THIS ACTION CANNOT BE UNDONE!</b>\n\n"
        "This will:\n"
        "• Permanently remove the product from the database\n"
        "• Delete associated files from storage\n"
        "• Remove all purchase history\n"
        "• Cannot be recovered\n\n"
        "🚨 <i>Only proceed if you are absolutely certain!</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🗑️ DELETE PERMANENTLY",
                    callback_data=f"exclusive_delete_final:{product_id}"
                )
            ],
            [
                InlineKeyboardButton(
                    text="❌ Cancel (Safe)",
                    callback_data=f"exclusive_manage:{product_id}"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("exclusive_delete_final:"))
async def admin_exclusive_delete_final(callback_query: CallbackQuery):
    """Perform final deletion of exclusive product."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    try:
        # Delete the product permanently
        result = ExclusiveProductDBOperations.delete_exclusive_product(product_id, admin_action=True)

        if result["success"]:
            await callback_query.answer("✅ Product deleted permanently!", show_alert=True)

            # Show success message and return to list
            message_text = (
                "✅ <b>PRODUCT DELETED</b>\n\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
                "🗑️ <b>DELETION SUCCESSFUL</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "The exclusive product has been permanently deleted from the system.\n\n"
                "• Product removed from database\n"
                "• Associated files deleted\n"
                "• Purchase history cleared\n\n"
                "✨ <i>Returning to exclusive products list...</i>"
            )

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="📋 View All Products",
                        callback_data="admin_exclusive_list"
                    )],
                    [InlineKeyboardButton(
                        text="🏠 Exclusive Management",
                        callback_data="admin_exclusive_products"
                    )]
                ]
            )

            await safe_edit_message(
                callback_query.message,
                message_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
        else:
            await callback_query.message.edit_text(
                f"❌ <b>Error</b>\n\n"
                f"Failed to delete product: {result.get('error', 'Unknown error')}",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Product",
                            callback_data=f"exclusive_manage:{product_id}"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
    except Exception as e:
        logger.error(f"Error deleting product: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to delete product: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"exclusive_manage:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("exclusive_file_details:"))
async def admin_exclusive_file_details(callback_query: CallbackQuery):
    """Show file details for an exclusive product."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    try:
        # Get product details
        product = ExclusiveProductDBOperations.get_exclusive_product_by_id(product_id)

        if not product:
            await callback_query.message.edit_text(
                "❌ <b>Product Not Found</b>\n\n"
                "The requested exclusive product could not be found.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to List",
                            callback_data="admin_exclusive_list"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
            return

        # Get file information
        file_path = product.get("file_path", "No file")
        file_size = product.get("file_size", 0)
        file_type = product.get("file_type", "Unknown")
        original_filename = product.get("original_filename", "Unknown")

        # Check if file exists
        import os
        file_exists = os.path.exists(file_path) if file_path != "No file" else False

        # Format file size
        if file_size > 0:
            if file_size < 1024:
                size_str = f"{file_size} bytes"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
        else:
            size_str = "Unknown"

        # Build file details message
        header = ExclusiveProductTheme.create_header("FILE DETAILS")

        message_text = (
            f"{header}\n"
            f"📁 <b>FILE INFORMATION</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"📄 <b>Original Name:</b> {original_filename}\n"
            f"📂 <b>File Type:</b> {file_type}\n"
            f"📏 <b>File Size:</b> {size_str}\n"
            f"📍 <b>Storage Path:</b> <code>{file_path}</code>\n"
            f"✅ <b>File Exists:</b> {'Yes' if file_exists else 'No'}\n\n"
        )

        if not file_exists and file_path != "No file":
            message_text += (
                "⚠️ <b>WARNING:</b> File not found on disk!\n"
                "The file may have been moved or deleted.\n\n"
            )

        message_text += (
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"📁 <i>File management options available below.</i>"
        )

        # Build keyboard
        keyboard_rows = []

        if file_exists:
            keyboard_rows.append([
                InlineKeyboardButton(
                    text="📥 Download File",
                    callback_data=f"exclusive_download_file:{product_id}"
                )
            ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Product",
                callback_data=f"exclusive_manage:{product_id}"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error showing file details: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to load file details: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"exclusive_manage:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


def register_exclusive_admin_handlers(dp):
    """Register exclusive product admin handlers."""
    dp.include_router(router)
    logger.info("Exclusive product admin handlers registered successfully")
