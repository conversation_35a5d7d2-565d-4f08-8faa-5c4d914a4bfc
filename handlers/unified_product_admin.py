"""
Unified Product Management Admin Interface
Provides comprehensive management for all product types (exclusive, regular, line-based)
with consistent UI/UX and type-specific functionality.
"""

from aiogram import Router, F, types
from aiogram.types import InlineKeyboardButton, InlineKeyboardMarkup, CallbackQuery
from aiogram.fsm.context import FSMContext
from aiogram.types import Message
from aiogram.filters import StateFilter
import logging
from typing import Dict, Any, Optional, List
from pathlib import Path

# Import existing utilities and operations
from database.operations import (
    get_product_by_id,
    get_all_products_for_admin,
    update_product,
    delete_product,
    get_category_by_id
)
from utils.unified_product_operations import UnifiedProductOperations


async def _create_mock_callback_query(product_id: str, original_callback: CallbackQuery):
    """Create a mock CallbackQuery for refreshing product details."""
    async def mock_answer(*args, **kwargs):
        return None

    return type('CallbackQuery', (), {
        'data': f'unified_product_detail:{product_id}',
        'from_user': original_callback.from_user,
        'message': original_callback.message,
        'answer': mock_answer
    })()
from utils.exclusive_product_db_operations import ExclusiveProductDBOperations
from database.operations import is_owner
from handlers.sys_db import is_privileged
from utils.telegram_helpers import safe_edit_message
from utils.exclusive_product_manager import ExclusiveProductTheme
from utils.file_based_product_manager import FileBasedProductManager
from states.states import UnifiedUploadStates, UnifiedProductStates
from utils.local_file_handling import generate_file_path
from utils.image_handler import download_telegram_image
from utils.file_metadata import store_file_metadata, normalize_file_path
from utils.unified_validation import unified_validation

# Setup router and logging
router = Router()
logger = logging.getLogger(__name__)

# ============================================================================
# UNIFIED PRODUCT MANAGEMENT CORE HANDLERS
# ============================================================================

@router.callback_query(F.data == "unified_product_management")
async def unified_product_management_main(callback_query: CallbackQuery):
    """Main unified product management interface."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all products for overview
    all_products = get_all_products_for_admin()
    
    # Categorize products by type
    exclusive_products = [p for p in all_products if p.get("is_exclusive_single_use", False)]
    line_based_products = [p for p in all_products if p.get("is_line_based", False)]
    regular_products = [p for p in all_products if not p.get("is_exclusive_single_use", False) and not p.get("is_line_based", False)]

    # Build overview message
    header = ExclusiveProductTheme.create_header("UNIFIED PRODUCT MANAGEMENT", "ALL PRODUCT TYPES")
    
    message_text = (
        f"{header}\n"
        f"📊 <b>PRODUCT OVERVIEW</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"🔐 <b>Exclusive Products:</b> {len(exclusive_products)}\n"
        f"📦 <b>Line-Based Products:</b> {len(line_based_products)}\n"
        f"📄 <b>Regular Products:</b> {len(regular_products)}\n"
        f"📋 <b>Total Products:</b> {len(all_products)}\n\n"
        f"{ExclusiveProductTheme.create_section_break()}\n"
        f"⚙️ <i>Select a management option below to get started.</i>"
    )

    # Build management keyboard
    keyboard_rows = [
        [
            InlineKeyboardButton(
                text="📋 Manage All Products",
                callback_data="unified_manage_all_products"
            )
        ],
        [
            InlineKeyboardButton(
                text=f"🔐 Exclusive Products ({len(exclusive_products)})",
                callback_data="unified_manage_exclusive"
            ),
            InlineKeyboardButton(
                text=f"📦 Line-Based ({len(line_based_products)})",
                callback_data="unified_manage_line_based"
            )
        ],
        [
            InlineKeyboardButton(
                text=f"📄 Regular Products ({len(regular_products)})",
                callback_data="unified_manage_regular"
            )
        ],
        [
            InlineKeyboardButton(
                text="📊 Product Analytics",
                callback_data="unified_product_analytics"
            ),
            InlineKeyboardButton(
                text="🔍 Search Products",
                callback_data="unified_search_products"
            )
        ],
        [
            InlineKeyboardButton(
                text="📁 Upload Files",
                callback_data="unified_upload_files"
            )
        ],
        [
            InlineKeyboardButton(
                text="🔙 Back to Admin",
                callback_data="admin_panel"
            )
        ]
    ]

    keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_manage_all_products")
async def unified_manage_all_products(callback_query: CallbackQuery):
    """Show all products with unified management interface."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all products
    all_products = get_all_products_for_admin()

    if not all_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('UNIFIED PRODUCT MANAGEMENT', 'NO PRODUCTS FOUND')}\n"
            f"📭 <b>No products found.</b>\n\n"
            f"No products have been created yet.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create your first product to get started.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Management",
                        callback_data="unified_product_management"
                    )
                ]
            ]
        )
    else:
        # Build product list with type indicators
        message_text = (
            f"{ExclusiveProductTheme.create_header('UNIFIED PRODUCT MANAGEMENT', 'ALL PRODUCTS')}\n"
            f"📊 <b>Found {len(all_products)} products</b>\n\n"
            f"Click on any product below to view details and manage:\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"⚙️ <i>Select a product to view details and management options.</i>"
        )

        # Build keyboard with individual product buttons
        keyboard_rows = []
        
        # Sort products by type and name for better organization
        exclusive_products = [p for p in all_products if p.get("is_exclusive_single_use", False)]
        line_based_products = [p for p in all_products if p.get("is_line_based", False) and not p.get("is_exclusive_single_use", False)]
        regular_products = [p for p in all_products if not p.get("is_exclusive_single_use", False) and not p.get("is_line_based", False)]
        
        # Combine in order: exclusive, line-based, regular
        sorted_products = exclusive_products + line_based_products + regular_products
        
        for product in sorted_products:
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0
            
            # Determine product type and status
            if product.get("is_exclusive_single_use", False):
                is_purchased = product.get("is_purchased", False)
                removed_from_listings = product.get("removed_from_listings", False)
                
                if is_purchased and removed_from_listings:
                    type_emoji = "🔴"  # Red for sold & delivered
                    type_text = "EXCLUSIVE-DELIVERED"
                elif is_purchased:
                    type_emoji = "🟡"  # Yellow for sold but not delivered
                    type_text = "EXCLUSIVE-SOLD"
                elif removed_from_listings:
                    type_emoji = "⚫"  # Black for removed
                    type_text = "EXCLUSIVE-REMOVED"
                else:
                    type_emoji = "🔐"  # Lock for available exclusive
                    type_text = "EXCLUSIVE-AVAILABLE"
            elif product.get("is_line_based", False):
                available_lines = product.get("available_lines", 0)
                total_lines = product.get("total_lines", 0)
                type_emoji = "📦"
                type_text = f"LINE-BASED ({available_lines}/{total_lines})"
            else:
                type_emoji = "📄"
                type_text = "REGULAR"
            
            # Truncate long names
            display_name = product_name[:20] + "..." if len(product_name) > 20 else product_name
            button_text = f"{type_emoji} {display_name} - ${price:.2f}"
            
            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])
        
        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📊 View by Type",
                callback_data="unified_product_management"
            ),
            InlineKeyboardButton(
                text="🔍 Search",
                callback_data="unified_search_products"
            )
        ])
        
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Management",
                callback_data="unified_product_management"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("unified_product_detail:"))
async def unified_product_detail(callback_query: CallbackQuery):
    """Show detailed view and management options for any product type."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID from callback data
    product_id = callback_query.data.split(":")[1]

    try:
        # Get product details using unified operations with validation
        # (validation is now handled internally by UnifiedProductOperations)
        product_result = UnifiedProductOperations.get_product_with_validation(product_id)

        if not product_result["success"]:
            error_code = product_result.get('error_code', 'UNKNOWN')
            error_msg = product_result.get('error', 'Unknown error')

            # Enhanced error message with more debugging info
            debug_info = f"Product ID: {product_id}\nError Code: {error_code}"

            await callback_query.message.edit_text(
                "❌ <b>Product Not Found</b>\n\n"
                f"Error: {error_msg}\n\n"
                f"<code>{debug_info}</code>\n\n"
                "The requested product could not be found or validated. "
                "Please try again or contact support if the problem persists.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Products",
                            callback_data="unified_manage_all_products"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
            return

        product = product_result["product"]

        # Determine product type and build appropriate interface
        is_exclusive = product.get("is_exclusive_single_use", False)
        is_line_based = product.get("is_line_based", False)

        # Use the original product_id for callback data consistency, but sanitized_product_id for database operations
        if is_exclusive:
            await _show_exclusive_product_detail(callback_query, product, product_id)
        elif is_line_based:
            await _show_line_based_product_detail(callback_query, product, product_id)
        else:
            await _show_regular_product_detail(callback_query, product, product_id)

    except Exception as e:
        error_response = unified_validation.create_error_response(e, "loading product details")
        await callback_query.message.edit_text(
            f"<b>{error_response['user_message']}</b>\n\n"
            f"Please try again or contact support if the problem persists.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Products",
                        callback_data="unified_manage_all_products"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


async def _show_exclusive_product_detail(callback_query: CallbackQuery, product: Dict[str, Any], product_id: str):
    """Show exclusive product detail view with exclusive-specific management options."""

    # Build detailed product information
    name = product.get("name", "Unknown Product")
    description = product.get("description", "No description")
    price = product.get("price") or 0
    is_purchased = product.get("is_purchased", False)
    removed_from_listings = product.get("removed_from_listings", False)
    purchased_by = product.get("purchased_by_user_id")
    purchase_date = product.get("purchase_date")
    delivery_date = product.get("delivery_date")
    file_path = product.get("exclusive_file_path", "No file")
    created_date = product.get("created_at")

    # Status determination
    if is_purchased and removed_from_listings:
        status = "🔴 SOLD & DELIVERED"
        status_color = "🔴"
    elif is_purchased:
        status = "🟡 SOLD (Pending Delivery)"
        status_color = "🟡"
    elif removed_from_listings:
        status = "⚫ REMOVED FROM LISTINGS"
        status_color = "⚫"
    else:
        status = "🟢 AVAILABLE FOR PURCHASE"
        status_color = "🟢"

    # Format dates with proper type checking
    def format_date(date_obj):
        if not date_obj:
            return "Unknown"
        if isinstance(date_obj, str):
            return date_obj  # Already formatted
        if hasattr(date_obj, 'strftime'):
            return date_obj.strftime("%Y-%m-%d %H:%M")
        return str(date_obj)

    created_str = format_date(created_date)
    purchase_str = format_date(purchase_date) if purchase_date else "N/A"
    delivery_str = format_date(delivery_date) if delivery_date else "N/A"

    # Build detailed message
    header = ExclusiveProductTheme.create_header("EXCLUSIVE PRODUCT DETAILS")

    message_text = (
        f"{header}\n"
        f"🔐 <b>EXCLUSIVE PRODUCT INFORMATION</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"🏷️ <b>Name:</b> {name}\n"
        f"💰 <b>Price:</b> <code>${price:.2f}</code>\n"
        f"📝 <b>Description:</b> {description[:100]}{'...' if len(description) > 100 else ''}\n"
        f"📁 <b>File:</b> {file_path.split('/')[-1] if '/' in file_path else file_path}\n"
        f"📅 <b>Created:</b> {created_str}\n\n"

        f"{status_color} <b>STATUS INFORMATION</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"🔄 <b>Current Status:</b> {status}\n"
    )

    if purchased_by:
        message_text += f"👤 <b>Purchased By:</b> User ID {purchased_by}\n"
    if purchase_date:
        message_text += f"🛒 <b>Purchase Date:</b> {purchase_str}\n"
    if delivery_date:
        message_text += f"📦 <b>Delivery Date:</b> {delivery_str}\n"

    message_text += (
        f"\n{ExclusiveProductTheme.create_section_break()}\n"
        f"⚙️ <i>Use the buttons below to manage this exclusive product.</i>"
    )

    # Build management keyboard based on current state
    keyboard_rows = []

    # State management buttons for exclusive products
    if not is_purchased and not removed_from_listings:
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔴 Mark as Purchased",
                callback_data=f"unified_exclusive_mark_purchased:{product_id}"
            ),
            InlineKeyboardButton(
                text="⚫ Remove from Listings",
                callback_data=f"unified_exclusive_remove:{product_id}"
            )
        ])
    elif is_purchased and not removed_from_listings:
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📦 Mark as Delivered",
                callback_data=f"unified_exclusive_mark_delivered:{product_id}"
            ),
            InlineKeyboardButton(
                text="🔄 Restore to Available",
                callback_data=f"unified_exclusive_restore:{product_id}"
            )
        ])
    elif removed_from_listings:
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔄 Restore to Available",
                callback_data=f"unified_exclusive_restore:{product_id}"
            )
        ])

    # Common management buttons
    keyboard_rows.append([
        InlineKeyboardButton(
            text="✏️ Edit Product",
            callback_data=f"edit_product:{product_id}"
        ),
        InlineKeyboardButton(
            text="📁 View File Details",
            callback_data=f"unified_file_details:{product_id}"
        )
    ])

    keyboard_rows.append([
        InlineKeyboardButton(
            text="🗑️ Delete Product",
            callback_data=f"unified_delete_confirm:{product_id}"
        )
    ])

    # Navigation buttons
    keyboard_rows.append([
        InlineKeyboardButton(
            text="🔙 Back to Products",
            callback_data="unified_manage_all_products"
        ),
        InlineKeyboardButton(
            text="🏠 Main Management",
            callback_data="unified_product_management"
        )
    ])

    keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def _show_line_based_product_detail(callback_query: CallbackQuery, product: Dict[str, Any], product_id: str):
    """Show line-based product detail view with inventory management options."""

    # Build detailed product information
    name = product.get("name", "Unknown Product")
    description = product.get("description", "No description")
    price = product.get("price") or 0
    line_price = product.get("line_price") or price
    total_lines = product.get("total_lines", 0)
    available_lines = product.get("available_lines", 0)
    reserved_lines = product.get("reserved_lines", 0)
    max_quantity = product.get("max_quantity_per_order", 1)
    inventory_file = product.get("inventory_file_path", "No file")
    preview_format = product.get("preview_format", "No preview")
    created_date = product.get("created_at")
    allow_shared_inventory = product.get("allow_shared_inventory", False)

    # Status determination
    if available_lines <= 0:
        status = "🔴 OUT OF STOCK"
        status_color = "🔴"
    elif available_lines <= 10:
        status = "🟡 LOW STOCK"
        status_color = "🟡"
    else:
        status = "🟢 IN STOCK"
        status_color = "🟢"

    # Calculate utilization
    sold_lines = total_lines - available_lines - reserved_lines
    utilization = (sold_lines / total_lines * 100) if total_lines > 0 else 0

    # Format dates with proper type checking
    def format_date(date_obj):
        if not date_obj:
            return "Unknown"
        if isinstance(date_obj, str):
            return date_obj  # Already formatted
        if hasattr(date_obj, 'strftime'):
            return date_obj.strftime("%Y-%m-%d %H:%M")
        return str(date_obj)

    created_str = format_date(created_date)

    # Build detailed message
    header = ExclusiveProductTheme.create_header("LINE-BASED PRODUCT DETAILS")

    message_text = (
        f"{header}\n"
        f"📦 <b>LINE-BASED PRODUCT INFORMATION</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"🏷️ <b>Name:</b> {name}\n"
        f"💰 <b>Base Price:</b> <code>${price:.2f}</code>\n"
        f"💵 <b>Line Price:</b> <code>${line_price:.2f}</code>\n"
        f"📝 <b>Description:</b> {description[:100]}{'...' if len(description) > 100 else ''}\n"
        f"📁 <b>Inventory File:</b> {inventory_file.split('/')[-1] if '/' in inventory_file else inventory_file}\n"
        f"📅 <b>Created:</b> {created_str}\n\n"

        f"📊 <b>INVENTORY STATUS</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"{status_color} <b>Status:</b> {status}\n"
        f"🔄 <b>Inventory Mode:</b> {'🔄 Shared' if allow_shared_inventory else '📦 Exclusive'}\n"
        f"📋 <b>Total Lines:</b> {total_lines:,}\n"
        f"✅ <b>Available:</b> {available_lines:,}\n"
        f"⏳ <b>Reserved:</b> {reserved_lines:,}\n"
        f"📈 <b>Sold:</b> {sold_lines:,} ({utilization:.1f}%)\n"
        f"🛒 <b>Max per Order:</b> {max_quantity}\n\n"

        f"🔍 <b>PREVIEW FORMAT</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"<code>{preview_format}</code>\n\n"

        f"ℹ️ <b>INVENTORY MODE INFO</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"{'🔄 <b>Shared Inventory:</b> Multiple users can purchase the same lines. Individual purchase history prevents duplicate deliveries.' if allow_shared_inventory else '📦 <b>Exclusive Inventory:</b> Lines become unavailable after purchase. Traditional inventory consumption model.'}\n\n"

        f"{ExclusiveProductTheme.create_section_break()}\n"
        f"⚙️ <i>Use the buttons below to manage this line-based product.</i>"
    )

    # Build management keyboard
    keyboard_rows = []

    # Inventory management buttons
    keyboard_rows.append([
        InlineKeyboardButton(
            text="📊 Inventory Details",
            callback_data=f"unified_inventory_details:{product_id}"
        ),
        InlineKeyboardButton(
            text="🔄 Refresh Inventory",
            callback_data=f"unified_refresh_inventory:{product_id}"
        )
    ])

    keyboard_rows.append([
        InlineKeyboardButton(
            text="➕ Add Lines",
            callback_data=f"unified_add_lines:{product_id}"
        ),
        InlineKeyboardButton(
            text="📝 Update Preview",
            callback_data=f"unified_update_preview:{product_id}"
        )
    ])

    # Common management buttons
    keyboard_rows.append([
        InlineKeyboardButton(
            text="✏️ Edit Product",
            callback_data=f"edit_product:{product_id}"
        ),
        InlineKeyboardButton(
            text="📁 Manage Files",
            callback_data=f"unified_file_details:{product_id}"
        )
    ])

    keyboard_rows.append([
        InlineKeyboardButton(
            text="🗑️ Delete Product",
            callback_data=f"unified_delete_confirm:{product_id}"
        )
    ])

    # Navigation buttons
    keyboard_rows.append([
        InlineKeyboardButton(
            text="🔙 Back to Products",
            callback_data="unified_manage_all_products"
        ),
        InlineKeyboardButton(
            text="🏠 Main Management",
            callback_data="unified_product_management"
        )
    ])

    keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


async def _show_regular_product_detail(callback_query: CallbackQuery, product: Dict[str, Any], product_id: str):
    """Show regular product detail view with standard management options."""

    # Build detailed product information
    name = product.get("name", "Unknown Product")
    description = product.get("description", "No description")
    price = product.get("price") or 0
    file_link = product.get("file_link", "No file")
    image_url = product.get("image_url", "No image")
    category_id = product.get("category_id")
    created_date = product.get("created_at")
    tags = product.get("tags", [])
    quantity = product.get("quantity", 0)
    is_available = product.get("is_available", True)

    # Get category name
    category_name = "No Category"
    if category_id:
        try:
            category = get_category_by_id(category_id)
            if category:
                category_name = category.get("name", "Unknown Category")
        except Exception:
            category_name = "Unknown Category"

    # Status determination
    if not is_available:
        status = "⚫ UNAVAILABLE"
        status_color = "⚫"
    elif quantity <= 0:
        status = "🔴 OUT OF STOCK"
        status_color = "🔴"
    elif quantity <= 5:
        status = "🟡 LOW STOCK"
        status_color = "🟡"
    else:
        status = "🟢 IN STOCK"
        status_color = "🟢"

    # Format dates with proper type checking
    def format_date(date_obj):
        if not date_obj:
            return "Unknown"
        if isinstance(date_obj, str):
            return date_obj  # Already formatted
        if hasattr(date_obj, 'strftime'):
            return date_obj.strftime("%Y-%m-%d %H:%M")
        return str(date_obj)

    created_str = format_date(created_date)

    # Format tags
    tags_str = ", ".join(tags) if tags else "No tags"

    # Build detailed message
    header = ExclusiveProductTheme.create_header("REGULAR PRODUCT DETAILS")

    message_text = (
        f"{header}\n"
        f"📄 <b>REGULAR PRODUCT INFORMATION</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"🏷️ <b>Name:</b> {name}\n"
        f"💰 <b>Price:</b> <code>${price:.2f}</code>\n"
        f"📝 <b>Description:</b> {description[:100]}{'...' if len(description) > 100 else ''}\n"
        f"📁 <b>File:</b> {file_link.split('/')[-1] if '/' in file_link else file_link}\n"
        f"🖼️ <b>Image:</b> {'Available' if image_url and image_url != 'No image' else 'No image'}\n"
        f"📂 <b>Category:</b> {category_name}\n"
        f"🏷️ <b>Tags:</b> {tags_str}\n"
        f"📅 <b>Created:</b> {created_str}\n\n"

        f"📊 <b>AVAILABILITY & STOCK</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"{status_color} <b>Status:</b> {status}\n"
        f"📦 <b>Quantity:</b> {quantity:,}\n"
        f"✅ <b>Available:</b> {'Yes' if is_available else 'No'}\n\n"

        f"{ExclusiveProductTheme.create_section_break()}\n"
        f"⚙️ <i>Use the buttons below to manage this regular product.</i>"
    )

    # Build management keyboard
    keyboard_rows = []

    # Stock management buttons
    keyboard_rows.append([
        InlineKeyboardButton(
            text="📦 Update Stock",
            callback_data=f"unified_update_stock:{product_id}"
        ),
        InlineKeyboardButton(
            text="🔄 Toggle Availability",
            callback_data=f"unified_toggle_availability:{product_id}"
        )
    ])

    # Common management buttons
    keyboard_rows.append([
        InlineKeyboardButton(
            text="✏️ Edit Product",
            callback_data=f"edit_product:{product_id}"
        ),
        InlineKeyboardButton(
            text="📁 Manage Files",
            callback_data=f"unified_file_details:{product_id}"
        )
    ])

    keyboard_rows.append([
        InlineKeyboardButton(
            text="🗑️ Delete Product",
            callback_data=f"unified_delete_confirm:{product_id}"
        )
    ])

    # Navigation buttons
    keyboard_rows.append([
        InlineKeyboardButton(
            text="🔙 Back to Products",
            callback_data="unified_manage_all_products"
        ),
        InlineKeyboardButton(
            text="🏠 Main Management",
            callback_data="unified_product_management"
        )
    ])

    keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ============================================================================
# TYPE-SPECIFIC MANAGEMENT HANDLERS
# ============================================================================

@router.callback_query(F.data.startswith("unified_exclusive_mark_purchased:"))
async def unified_exclusive_mark_purchased(callback_query: CallbackQuery):
    """Mark exclusive product as purchased through unified interface."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()
    product_id = callback_query.data.split(":")[1]

    # Show confirmation dialog
    message_text = (
        f"{ExclusiveProductTheme.create_header('CONFIRM PURCHASE MARKING')}\n"
        f"⚠️ <b>CONFIRM ACTION</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"Are you sure you want to mark this exclusive product as <b>purchased</b>?\n\n"
        f"This action will:\n"
        f"• Mark the product as sold\n"
        f"• Remove it from customer listings\n"
        f"• Record the purchase timestamp\n\n"
        f"⚠️ <i>This action can be reversed later if needed.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm Purchase",
                    callback_data=f"unified_confirm_exclusive_purchased:{product_id}"
                ),
                InlineKeyboardButton(
                    text="❌ Cancel",
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("unified_confirm_exclusive_purchased:"))
async def unified_confirm_exclusive_purchased(callback_query: CallbackQuery):
    """Confirm marking exclusive product as purchased."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    product_id = callback_query.data.split(":")[1]

    try:
        # Use admin user ID for the purchase
        result = ExclusiveProductDBOperations.mark_as_purchased(product_id, user_id, admin_action=True)

        if result.get("success"):
            await callback_query.answer("✅ Product marked as purchased!", show_alert=True)
            # Return to product detail view
            mock_callback = await _create_mock_callback_query(product_id, callback_query)
            await unified_product_detail(mock_callback)
        else:
            await callback_query.message.edit_text(
                f"❌ <b>Error</b>\n\n"
                f"Failed to mark product as purchased: {result.get('error', 'Unknown error')}",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Product",
                            callback_data=f"unified_product_detail:{product_id}"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
    except Exception as e:
        logger.error(f"Error marking product as purchased: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to mark product as purchased: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"unified_product_detail:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("unified_exclusive_mark_delivered:"))
async def unified_exclusive_mark_delivered(callback_query: CallbackQuery):
    """Mark exclusive product as delivered through unified interface."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()
    product_id = callback_query.data.split(":")[1]

    # Show confirmation dialog
    message_text = (
        f"{ExclusiveProductTheme.create_header('CONFIRM DELIVERY MARKING')}\n"
        f"⚠️ <b>CONFIRM ACTION</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"Are you sure you want to mark this exclusive product as <b>delivered</b>?\n\n"
        f"This action will:\n"
        f"• Mark the product as delivered\n"
        f"• Remove it from customer listings\n"
        f"• Complete the product lifecycle\n"
        f"• Record the delivery timestamp\n\n"
        f"⚠️ <i>This action can be reversed later if needed.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm Delivery",
                    callback_data=f"unified_confirm_exclusive_delivered:{product_id}"
                ),
                InlineKeyboardButton(
                    text="❌ Cancel",
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("unified_confirm_exclusive_delivered:"))
async def unified_confirm_exclusive_delivered(callback_query: CallbackQuery):
    """Confirm marking exclusive product as delivered."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    product_id = callback_query.data.split(":")[1]

    try:
        result = ExclusiveProductDBOperations.mark_as_delivered(product_id, admin_action=True)

        if result.get("success"):
            await callback_query.answer("✅ Product marked as delivered!", show_alert=True)
            # Return to product detail view
            mock_callback = await _create_mock_callback_query(product_id, callback_query)
            await unified_product_detail(mock_callback)
        else:
            await callback_query.message.edit_text(
                f"❌ <b>Error</b>\n\n"
                f"Failed to mark product as delivered: {result.get('error', 'Unknown error')}",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Product",
                            callback_data=f"unified_product_detail:{product_id}"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
    except Exception as e:
        logger.error(f"Error marking product as delivered: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to mark product as delivered: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"unified_product_detail:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("unified_exclusive_restore:"))
async def unified_exclusive_restore(callback_query: CallbackQuery):
    """Restore exclusive product to available status through unified interface."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()
    product_id = callback_query.data.split(":")[1]

    # Show confirmation dialog
    message_text = (
        f"{ExclusiveProductTheme.create_header('CONFIRM PRODUCT RESTORATION')}\n"
        f"⚠️ <b>CONFIRM ACTION</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"Are you sure you want to restore this exclusive product to <b>available</b> status?\n\n"
        f"This action will:\n"
        f"• Reset product to available status\n"
        f"• Clear purchase and delivery information\n"
        f"• Make it available for purchase again\n"
        f"• Remove from customer listings if delivered\n\n"
        f"⚠️ <i>This will reset the product's entire lifecycle.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm Restoration",
                    callback_data=f"unified_confirm_exclusive_restore:{product_id}"
                ),
                InlineKeyboardButton(
                    text="❌ Cancel",
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("unified_confirm_exclusive_restore:"))
async def unified_confirm_exclusive_restore(callback_query: CallbackQuery):
    """Confirm restoring exclusive product to available status."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    product_id = callback_query.data.split(":")[1]

    try:
        result = ExclusiveProductDBOperations.restore_to_available(product_id, admin_action=True)

        if result.get("success"):
            await callback_query.answer("✅ Product restored to available!", show_alert=True)
            # Return to product detail view
            mock_callback = await _create_mock_callback_query(product_id, callback_query)
            await unified_product_detail(mock_callback)
        else:
            await callback_query.message.edit_text(
                f"❌ <b>Error</b>\n\n"
                f"Failed to restore product: {result.get('error', 'Unknown error')}",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Product",
                            callback_data=f"unified_product_detail:{product_id}"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
    except Exception as e:
        logger.error(f"Error restoring product: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to restore product: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"unified_product_detail:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("unified_exclusive_remove:"))
async def unified_exclusive_remove(callback_query: CallbackQuery):
    """Remove exclusive product from listings through unified interface."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()
    product_id = callback_query.data.split(":")[1]

    # Show confirmation dialog
    message_text = (
        f"{ExclusiveProductTheme.create_header('CONFIRM REMOVAL FROM LISTINGS')}\n"
        f"⚠️ <b>CONFIRM ACTION</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
        f"Are you sure you want to remove this exclusive product from <b>listings</b>?\n\n"
        f"This action will:\n"
        f"• Hide the product from customer view\n"
        f"• Keep the product in admin management\n"
        f"• Preserve all product data\n"
        f"• Allow restoration later if needed\n\n"
        f"⚠️ <i>This action can be reversed later.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Confirm Removal",
                    callback_data=f"unified_confirm_exclusive_remove:{product_id}"
                ),
                InlineKeyboardButton(
                    text="❌ Cancel",
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("unified_confirm_exclusive_remove:"))
async def unified_confirm_exclusive_remove(callback_query: CallbackQuery):
    """Confirm removing exclusive product from listings."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    product_id = callback_query.data.split(":")[1]

    try:
        result = ExclusiveProductDBOperations.remove_from_listings(product_id, admin_action=True)

        if result.get("success"):
            await callback_query.answer("✅ Product removed from listings!", show_alert=True)
            # Return to product detail view
            mock_callback = await _create_mock_callback_query(product_id, callback_query)
            await unified_product_detail(mock_callback)
        else:
            await callback_query.message.edit_text(
                f"❌ <b>Error</b>\n\n"
                f"Failed to remove product from listings: {result.get('error', 'Unknown error')}",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Product",
                            callback_data=f"unified_product_detail:{product_id}"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
    except Exception as e:
        logger.error(f"Error removing product from listings: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to remove product from listings: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"unified_product_detail:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("unified_file_details:"))
async def unified_file_details(callback_query: CallbackQuery):
    """Show file details for any product type through unified interface."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()
    product_id = callback_query.data.split(":")[1]

    try:
        # Get product details using unified operations with validation
        # (validation is now handled internally by UnifiedProductOperations)
        product_result = UnifiedProductOperations.get_product_with_validation(product_id)

        if not product_result["success"]:
            await callback_query.message.edit_text(
                "❌ <b>Product Not Found</b>\n\n"
                f"Error: {product_result.get('error', 'Unknown error')}\n\n"
                "The requested product could not be found or validated.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Products",
                            callback_data="unified_manage_all_products"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
            return

        product = product_result["product"]

        # Determine product type and get appropriate file information
        is_exclusive = product.get("is_exclusive_single_use", False)
        is_line_based = product.get("is_line_based", False)

        product_name = product.get("name") or "Unknown Product"

        if is_exclusive:
            file_path = product.get("exclusive_file_path", "No file")
            file_type = product.get("exclusive_file_type", "Unknown")
            file_size = product.get("exclusive_file_size", 0)
            mime_type = product.get("exclusive_file_mime_type", "Unknown")
            product_type = "🔐 Exclusive Product"
        elif is_line_based:
            file_path = product.get("inventory_file_path", "No file")
            file_type = "Inventory File"
            file_size = product.get("inventory_file_size", 0)
            mime_type = "text/plain"
            product_type = "📦 Line-Based Product"
        else:
            file_path = product.get("file_link", "No file")
            file_type = "Product File"
            file_size = 0
            mime_type = "Unknown"
            product_type = "📄 Regular Product"

        # Format file size
        if file_size > 0:
            if file_size < 1024:
                size_str = f"{file_size} bytes"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
        else:
            size_str = "Unknown size"

        # Build file details message
        message_text = (
            f"{ExclusiveProductTheme.create_header('FILE DETAILS')}\n"
            f"📁 <b>FILE INFORMATION</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"📄 <b>Product:</b> {product_name}\n"
            f"🏷️ <b>Type:</b> {product_type}\n\n"
            f"📁 <b>FILE DETAILS:</b>\n"
            f"📂 <b>Path:</b> <code>{file_path.split('/')[-1] if '/' in file_path else file_path}</code>\n"
            f"📋 <b>Type:</b> {file_type}\n"
            f"📏 <b>Size:</b> {size_str}\n"
            f"🔧 <b>MIME Type:</b> {mime_type}\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"📋 <i>File information for administrative purposes.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"unified_product_detail:{product_id}"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="📋 All Products",
                        callback_data="unified_manage_all_products"
                    ),
                    InlineKeyboardButton(
                        text="🏠 Main Management",
                        callback_data="unified_product_management"
                    )
                ]
            ]
        )

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error showing file details: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to load file details: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"unified_product_detail:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("unified_delete_confirm:"))
async def unified_delete_confirm(callback_query: CallbackQuery):
    """Show delete confirmation dialog for any product type."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    try:
        # Get product details using unified operations with validation
        product_result = UnifiedProductOperations.get_product_with_validation(product_id)

        if not product_result["success"]:
            await callback_query.message.edit_text(
                "❌ <b>Product Not Found</b>\n\n"
                f"Error: {product_result.get('error', 'Unknown error')}\n\n"
                "The requested product could not be found or validated.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Products",
                            callback_data="unified_manage_all_products"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
            return

        product = product_result["product"]
        product_type = product_result["product_type"]
        product_name = product.get("name", "Unknown Product")

        # Show confirmation dialog with product type-specific information
        message_text = (
            "🚨 <b>DANGER - PERMANENT DELETION</b>\n\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"🗑️ <b>DELETE {product_type.upper()} PRODUCT</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"Are you sure you want to <b>permanently delete</b> the product:\n"
            f"<b>'{product_name}'</b>?\n\n"
            "⚠️ <b>THIS ACTION CANNOT BE UNDONE!</b>\n\n"
            "This will:\n"
            "• Permanently remove the product from the database\n"
            "• Delete associated files from storage\n"
            "• Remove all purchase history\n"
            "• Cannot be recovered\n\n"
            "🚨 <i>Only proceed if you are absolutely certain!</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🗑️ DELETE PERMANENTLY",
                        callback_data=f"unified_delete_execute:{product_id}"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="❌ Cancel (Safe)",
                        callback_data=f"unified_product_detail:{product_id}"
                    )
                ]
            ]
        )

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error showing delete confirmation: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to load product for deletion: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Products",
                        callback_data="unified_manage_all_products"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("unified_delete_execute:"))
async def unified_delete_execute(callback_query: CallbackQuery):
    """Perform final deletion of any product type."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract product ID
    product_id = callback_query.data.split(":")[1]

    try:
        # Delete the product using unified operations
        result = UnifiedProductOperations.delete_product(product_id, admin_action=True)

        if result["success"]:
            await callback_query.answer("✅ Product deleted permanently!", show_alert=True)

            # Show success message and return to list
            product_name = result.get("product_name", "Unknown Product")
            product_type = result.get("product_type", "Unknown")

            message_text = (
                "✅ <b>PRODUCT DELETED</b>\n\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
                "🗑️ <b>DELETION SUCCESSFUL</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                f"The {product_type.lower()} product <b>'{product_name}'</b> has been permanently deleted from the system.\n\n"
                "• Product removed from database\n"
                "• Associated files deleted\n"
                "• Purchase history cleared\n\n"
                "✨ <i>Returning to product management...</i>"
            )

            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="📋 View All Products",
                        callback_data="unified_manage_all_products"
                    )],
                    [InlineKeyboardButton(
                        text="🏠 Product Management",
                        callback_data="unified_product_management"
                    )]
                ]
            )

            await safe_edit_message(
                callback_query.message,
                message_text,
                reply_markup=keyboard,
                parse_mode="HTML"
            )
        else:
            await callback_query.message.edit_text(
                f"❌ <b>Error</b>\n\n"
                f"Failed to delete product: {result.get('error', 'Unknown error')}",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [InlineKeyboardButton(
                            text="🔙 Back to Product",
                            callback_data=f"unified_product_detail:{product_id}"
                        )]
                    ]
                ),
                parse_mode="HTML"
            )
    except Exception as e:
        logger.error(f"Error deleting product: {e}")
        await callback_query.message.edit_text(
            f"❌ <b>Error</b>\n\n"
            f"Failed to delete product: {str(e)}",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [InlineKeyboardButton(
                        text="🔙 Back to Product",
                        callback_data=f"unified_product_detail:{product_id}"
                    )]
                ]
            ),
            parse_mode="HTML"
        )


@router.callback_query(F.data.startswith("unified_update_stock:"))
async def unified_update_stock(callback_query: CallbackQuery, state: FSMContext):
    """Update stock for regular products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()
    product_id = callback_query.data.split(":")[1]

    # Get current product details
    try:
        # Get product details using unified operations with validation
        # (validation is now handled internally by UnifiedProductOperations)
        product_result = UnifiedProductOperations.get_product_with_validation(product_id)

        if not product_result["success"]:
            await callback_query.answer(f"❌ Product not found: {product_result.get('error', 'Unknown error')}", show_alert=True)
            return

        product = product_result["product"]

        current_stock = product.get("quantity", 0)

        message_text = (
            f"{ExclusiveProductTheme.create_header('UPDATE PRODUCT STOCK')}\n"
            f"📦 <b>STOCK MANAGEMENT</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n"
            f"🏷️ <b>Product:</b> {product.get('name', 'Unknown')}\n"
            f"📊 <b>Current Stock:</b> {current_stock:,}\n\n"
            f"Please enter the new stock quantity:\n\n"
            f"💡 <i>Enter a number (e.g., 100, 0, 500)</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="❌ Cancel",
                        callback_data=f"unified_product_detail:{product_id}"
                    )
                ]
            ]
        )

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

        # Store product ID in state for the next message handler
        await state.update_data(unified_stock_update_product_id=product_id)
        await state.set_state(UnifiedProductStates.waiting_for_stock_update)

    except Exception as e:
        error_response = unified_validation.create_error_response(e, "initiating stock update")
        await callback_query.answer(error_response['user_message'], show_alert=True)


@router.callback_query(F.data.startswith("unified_toggle_availability:"))
async def unified_toggle_availability(callback_query: CallbackQuery):
    """Toggle availability for regular products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()
    product_id = callback_query.data.split(":")[1]

    try:
        # Get product details using unified operations with validation
        # (validation is now handled internally by UnifiedProductOperations)
        product_result = UnifiedProductOperations.get_product_with_validation(product_id)

        if not product_result["success"]:
            await callback_query.answer(f"❌ Product not found: {product_result.get('error', 'Unknown error')}", show_alert=True)
            return

        product = product_result["product"]

        current_availability = product.get("is_available", True)
        new_availability = not current_availability

        # Update availability
        result = update_product(product_id, {"is_available": new_availability})

        if result:
            status_text = "available" if new_availability else "unavailable"
            await callback_query.answer(f"✅ Product marked as {status_text}!", show_alert=True)

            # Return to product detail view
            mock_callback = await _create_mock_callback_query(product_id, callback_query)
            await unified_product_detail(mock_callback)
        else:
            await callback_query.answer("❌ Failed to update availability", show_alert=True)

    except Exception as e:
        error_response = unified_validation.create_error_response(e, "updating product availability")
        await callback_query.answer(error_response['user_message'], show_alert=True)


# ============================================================================
# UNIFIED EXCLUSIVE PRODUCT MANAGEMENT HANDLERS
# ============================================================================

@router.callback_query(F.data == "unified_manage_exclusive")
async def unified_manage_exclusive_only(callback_query: CallbackQuery):
    """Show only exclusive products in unified interface."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all exclusive products
    all_products = get_all_products_for_admin()
    exclusive_products = [p for p in all_products if p.get("is_exclusive_single_use", False)]

    if not exclusive_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('EXCLUSIVE PRODUCTS', 'NO PRODUCTS FOUND')}\n"
            f"📭 <b>No exclusive products found.</b>\n\n"
            f"No exclusive products have been created yet.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create your first exclusive product to get started.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Unified Management",
                        callback_data="unified_product_management"
                    )
                ]
            ]
        )
    else:
        # Categorize exclusive products by status
        available = [p for p in exclusive_products if not p.get("is_purchased", False) and not p.get("removed_from_listings", False)]
        purchased = [p for p in exclusive_products if p.get("is_purchased", False) and not p.get("removed_from_listings", False)]
        delivered = [p for p in exclusive_products if p.get("is_purchased", False) and p.get("removed_from_listings", False)]
        removed = [p for p in exclusive_products if not p.get("is_purchased", False) and p.get("removed_from_listings", False)]

        message_text = (
            f"{ExclusiveProductTheme.create_header('EXCLUSIVE PRODUCTS MANAGEMENT')}\n"
            f"🔐 <b>Found {len(exclusive_products)} exclusive products</b>\n\n"
            f"📊 <b>STATUS BREAKDOWN:</b>\n"
            f"🟢 Available: {len(available)}\n"
            f"🟡 Sold (Pending Delivery): {len(purchased)}\n"
            f"🔴 Delivered: {len(delivered)}\n"
            f"⚫ Removed: {len(removed)}\n\n"
            f"Click on any product below to manage:\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"⚙️ <i>Select a product for detailed management options.</i>"
        )

        # Build keyboard with individual exclusive product buttons
        keyboard_rows = []

        # Sort products by status and name
        sorted_products = available + purchased + delivered + removed

        for product in sorted_products:
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0

            # Status indicators
            is_purchased = product.get("is_purchased", False)
            removed_from_listings = product.get("removed_from_listings", False)

            if is_purchased and removed_from_listings:
                status_emoji = "🔴"  # Red for sold & delivered
                status_text = "DELIVERED"
            elif is_purchased:
                status_emoji = "🟡"  # Yellow for sold but not delivered
                status_text = "SOLD"
            elif removed_from_listings:
                status_emoji = "⚫"  # Black for removed
                status_text = "REMOVED"
            else:
                status_emoji = "🟢"  # Green for available
                status_text = "AVAILABLE"

            # Truncate long names
            display_name = product_name[:20] + "..." if len(product_name) > 20 else product_name
            button_text = f"{status_emoji} {display_name} - ${price:.2f} ({status_text})"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])

        # Add management buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📊 Exclusive Statistics",
                callback_data="unified_exclusive_stats"
            ),
            InlineKeyboardButton(
                text="🟢 Available Only",
                callback_data="unified_exclusive_available"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="📋 All Product Types",
                callback_data="unified_manage_all_products"
            ),
            InlineKeyboardButton(
                text="🏠 Main Management",
                callback_data="unified_product_management"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ============================================================================
# MESSAGE HANDLERS FOR INPUT PROCESSING
# ============================================================================

@router.message(F.text, UnifiedProductStates.waiting_for_stock_update)
async def process_stock_update(message: types.Message, state: FSMContext):
    """Process stock update input for regular products."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied</b>", parse_mode="HTML")
        await state.clear()
        return

    try:
        # Get product ID from state
        data = await state.get_data()
        product_id = data.get("unified_stock_update_product_id")

        if not product_id:
            await message.reply("❌ <b>Error</b>\n\nProduct ID not found. Please try again.", parse_mode="HTML")
            await state.clear()
            return

        # Parse the new stock quantity
        try:
            new_stock = int(message.text.strip())
            if new_stock < 0:
                await message.reply(
                    "❌ <b>Invalid Quantity</b>\n\n"
                    "Stock quantity cannot be negative. Please enter a valid number.",
                    parse_mode="HTML"
                )
                return
        except ValueError:
            await message.reply(
                "❌ <b>Invalid Input</b>\n\n"
                "Please enter a valid number for the stock quantity.",
                parse_mode="HTML"
            )
            return

        # Update the product stock
        result = update_product(product_id, {"quantity": new_stock})

        if result:
            await message.reply(
                f"✅ <b>Stock Updated</b>\n\n"
                f"Product stock has been updated to <b>{new_stock:,}</b> units.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="📄 View Product",
                                callback_data=f"unified_product_detail:{product_id}"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="📋 All Products",
                                callback_data="unified_manage_all_products"
                            )
                        ]
                    ]
                ),
                parse_mode="HTML"
            )
        else:
            await message.reply(
                "❌ <b>Update Failed</b>\n\n"
                "Failed to update product stock. Please try again.",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔄 Try Again",
                                callback_data=f"unified_update_stock:{product_id}"
                            )
                        ]
                    ]
                ),
                parse_mode="HTML"
            )

        # Clear state
        await state.clear()

    except Exception as e:
        error_response = unified_validation.create_error_response(e, "updating stock")
        await message.reply(
            f"<b>{error_response['user_message']}</b>\n\n"
            f"Please try again or contact support if the problem persists.",
            parse_mode="HTML"
        )
        await state.clear()


@router.callback_query(F.data == "unified_exclusive_available")
async def unified_exclusive_available_only(callback_query: CallbackQuery):
    """Show only available exclusive products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get available exclusive products
    all_products = get_all_products_for_admin()
    available_products = [
        p for p in all_products
        if p.get("is_exclusive_single_use", False)
        and not p.get("is_purchased", False)
        and not p.get("removed_from_listings", False)
    ]

    message_text = (
        f"{ExclusiveProductTheme.create_header('AVAILABLE EXCLUSIVE PRODUCTS')}\n"
        f"🟢 <b>Found {len(available_products)} available exclusive products</b>\n\n"
    )

    if not available_products:
        message_text += (
            f"📭 No available exclusive products found.\n\n"
            f"All exclusive products are either sold or removed from listings.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create new exclusive products or restore existing ones.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔐 All Exclusive Products",
                        callback_data="unified_manage_exclusive"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Management",
                        callback_data="unified_product_management"
                    )
                ]
            ]
        )
    else:
        message_text += (
            f"Click on any product below to manage:\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"⚙️ <i>These products are ready for purchase by customers.</i>"
        )

        # Build keyboard with available products
        keyboard_rows = []

        for product in available_products:
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0

            display_name = product_name[:25] + "..." if len(product_name) > 25 else product_name
            button_text = f"🟢 {display_name} - ${price:.2f}"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔐 All Exclusive Products",
                callback_data="unified_manage_exclusive"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Management",
                callback_data="unified_product_management"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_exclusive_stats")
async def unified_exclusive_statistics(callback_query: CallbackQuery):
    """Show detailed statistics for exclusive products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all exclusive products
    all_products = get_all_products_for_admin()
    exclusive_products = [p for p in all_products if p.get("is_exclusive_single_use", False)]

    if not exclusive_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('EXCLUSIVE PRODUCTS STATISTICS', 'NO DATA AVAILABLE')}\n"
            f"📊 <b>No exclusive products found for analysis.</b>\n\n"
            f"Create exclusive products to see detailed statistics.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Statistics will appear here once you have exclusive products.</i>"
        )
    else:
        # Calculate statistics
        total_products = len(exclusive_products)
        available = len([p for p in exclusive_products if not p.get("is_purchased", False) and not p.get("removed_from_listings", False)])
        sold = len([p for p in exclusive_products if p.get("is_purchased", False)])
        delivered = len([p for p in exclusive_products if p.get("is_purchased", False) and p.get("removed_from_listings", False)])
        removed = len([p for p in exclusive_products if not p.get("is_purchased", False) and p.get("removed_from_listings", False)])

        # Calculate revenue
        total_revenue = sum(p.get("price") or 0 for p in exclusive_products if p.get("is_purchased", False))
        avg_price = sum(p.get("price") or 0 for p in exclusive_products) / total_products if total_products > 0 else 0

        # Calculate percentages
        available_pct = (available / total_products * 100) if total_products > 0 else 0
        sold_pct = (sold / total_products * 100) if total_products > 0 else 0
        delivered_pct = (delivered / total_products * 100) if total_products > 0 else 0

        message_text = (
            f"{ExclusiveProductTheme.create_header('EXCLUSIVE PRODUCTS STATISTICS')}\n"
            f"📊 <b>COMPREHENSIVE ANALYTICS</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"

            f"📈 <b>PRODUCT COUNTS:</b>\n"
            f"📋 Total Products: <b>{total_products}</b>\n"
            f"🟢 Available: <b>{available}</b> ({available_pct:.1f}%)\n"
            f"🟡 Sold: <b>{sold}</b> ({sold_pct:.1f}%)\n"
            f"🔴 Delivered: <b>{delivered}</b> ({delivered_pct:.1f}%)\n"
            f"⚫ Removed: <b>{removed}</b>\n\n"

            f"💰 <b>REVENUE ANALYTICS:</b>\n"
            f"💵 Total Revenue: <b>${total_revenue:.2f}</b>\n"
            f"📊 Average Price: <b>${avg_price:.2f}</b>\n"
            f"🎯 Conversion Rate: <b>{sold_pct:.1f}%</b>\n\n"

            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"📈 <i>Statistics updated in real-time based on current product status.</i>"
        )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔐 Manage Exclusive Products",
                    callback_data="unified_manage_exclusive"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📋 All Product Types",
                    callback_data="unified_manage_all_products"
                ),
                InlineKeyboardButton(
                    text="🏠 Main Management",
                    callback_data="unified_product_management"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_manage_line_based")
async def unified_manage_line_based_only(callback_query: CallbackQuery):
    """Show only line-based products in unified interface."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all line-based products
    all_products = get_all_products_for_admin()
    line_based_products = [p for p in all_products if p.get("is_line_based", False)]

    if not line_based_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('LINE-BASED PRODUCTS', 'NO PRODUCTS FOUND')}\n"
            f"📦 <b>No line-based products found.</b>\n\n"
            f"No line-based products have been created yet.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create your first line-based product to get started.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Unified Management",
                        callback_data="unified_product_management"
                    )
                ]
            ]
        )
    else:
        # Categorize line-based products by stock status and inventory mode
        in_stock = [p for p in line_based_products if p.get("stock", 0) > 0]
        low_stock = [p for p in line_based_products if 0 < p.get("stock", 0) <= 10]
        out_of_stock = [p for p in line_based_products if p.get("stock", 0) == 0]

        # Categorize by inventory mode
        shared_inventory = [p for p in line_based_products if p.get("allow_shared_inventory", False)]
        exclusive_inventory = [p for p in line_based_products if not p.get("allow_shared_inventory", False)]

        message_text = (
            f"{ExclusiveProductTheme.create_header('LINE-BASED PRODUCTS MANAGEMENT')}\n"
            f"📦 <b>Found {len(line_based_products)} line-based products</b>\n\n"
            f"📊 <b>INVENTORY STATUS:</b>\n"
            f"🟢 In Stock: {len(in_stock)}\n"
            f"🟡 Low Stock (≤10): {len(low_stock)}\n"
            f"🔴 Out of Stock: {len(out_of_stock)}\n\n"
            f"🔄 <b>INVENTORY MODES:</b>\n"
            f"🔄 Shared Inventory: {len(shared_inventory)}\n"
            f"📦 Exclusive Inventory: {len(exclusive_inventory)}\n\n"
            f"Click on any product below to manage:\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"⚙️ <i>Select a product for inventory management options.</i>"
        )

        # Build keyboard with individual line-based product buttons
        keyboard_rows = []

        # Sort products by stock status and name
        sorted_products = in_stock + low_stock + out_of_stock

        for product in sorted_products:
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0
            stock = product.get("stock", 0)

            # Status indicators
            if stock == 0:
                status_emoji = "🔴"
                status_text = "OUT OF STOCK"
            elif stock <= 10:
                status_emoji = "🟡"
                status_text = f"LOW ({stock})"
            else:
                status_emoji = "🟢"
                status_text = f"STOCK: {stock}"

            # Inventory mode indicator
            inventory_mode_emoji = "🔄" if product.get("allow_shared_inventory", False) else "📦"
            inventory_mode_text = "SHARED" if product.get("allow_shared_inventory", False) else "EXCL"

            # Truncate long names
            display_name = product_name[:18] + "..." if len(product_name) > 18 else product_name
            button_text = f"{status_emoji}{inventory_mode_emoji} {display_name} - ${price:.2f} ({status_text}|{inventory_mode_text})"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])

        # Add filtering and management buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔄 Shared Only",
                callback_data="unified_line_based_shared_only"
            ),
            InlineKeyboardButton(
                text="📦 Exclusive Only",
                callback_data="unified_line_based_exclusive_only"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="📊 Inventory Analytics",
                callback_data="unified_line_based_analytics"
            ),
            InlineKeyboardButton(
                text="🔴 Out of Stock Only",
                callback_data="unified_line_based_out_of_stock"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="📋 All Product Types",
                callback_data="unified_manage_all_products"
            ),
            InlineKeyboardButton(
                text="🏠 Main Management",
                callback_data="unified_product_management"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_line_based_shared_only")
async def unified_line_based_shared_only(callback_query: CallbackQuery):
    """Show only shared inventory line-based products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all line-based products with shared inventory
    all_products = get_all_products_for_admin()
    shared_products = [p for p in all_products if p.get("is_line_based", False) and p.get("allow_shared_inventory", False)]

    if not shared_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('SHARED INVENTORY PRODUCTS', 'NO PRODUCTS FOUND')}\n"
            f"🔄 <b>No shared inventory products found.</b>\n\n"
            f"No line-based products with shared inventory have been created yet.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create a line-based product and select 'Shared Inventory' mode.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Line-Based",
                        callback_data="unified_manage_line_based"
                    )
                ]
            ]
        )
    else:
        message_text = (
            f"{ExclusiveProductTheme.create_header('SHARED INVENTORY PRODUCTS')}\n"
            f"🔄 <b>Found {len(shared_products)} shared inventory products</b>\n\n"
            f"📊 <b>SHARED INVENTORY BENEFITS:</b>\n"
            f"• Multiple users can purchase same content\n"
            f"• Individual purchase history prevents duplicates\n"
            f"• Lines remain available after purchase\n"
            f"• Ideal for data products and digital content\n\n"
            f"Click on any product below to manage:\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"⚙️ <i>Select a product for detailed management.</i>"
        )

        keyboard_rows = []

        for product in shared_products:
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0
            stock = product.get("stock", 0)

            # Status indicators for shared inventory
            if stock == 0:
                status_emoji = "🔴"
                status_text = "NO CONTENT"
            else:
                status_emoji = "🔄"
                status_text = f"SHARED ({stock} lines)"

            # Truncate long names
            display_name = product_name[:25] + "..." if len(product_name) > 25 else product_name
            button_text = f"{status_emoji} {display_name} - ${price:.2f} ({status_text})"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])

        # Add management buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📊 Shared Analytics",
                callback_data="unified_shared_inventory_analytics"
            ),
            InlineKeyboardButton(
                text="👥 User History",
                callback_data="unified_shared_user_history"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Line-Based",
                callback_data="unified_manage_line_based"
            ),
            InlineKeyboardButton(
                text="🏠 Main Management",
                callback_data="unified_product_management"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_line_based_exclusive_only")
async def unified_line_based_exclusive_only(callback_query: CallbackQuery):
    """Show only exclusive inventory line-based products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all line-based products with exclusive inventory
    all_products = get_all_products_for_admin()
    exclusive_products = [p for p in all_products if p.get("is_line_based", False) and not p.get("allow_shared_inventory", False)]

    if not exclusive_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('EXCLUSIVE INVENTORY PRODUCTS', 'NO PRODUCTS FOUND')}\n"
            f"📦 <b>No exclusive inventory products found.</b>\n\n"
            f"All line-based products are using shared inventory mode.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create a line-based product and select 'Exclusive Inventory' mode.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Line-Based",
                        callback_data="unified_manage_line_based"
                    )
                ]
            ]
        )
    else:
        message_text = (
            f"{ExclusiveProductTheme.create_header('EXCLUSIVE INVENTORY PRODUCTS')}\n"
            f"📦 <b>Found {len(exclusive_products)} exclusive inventory products</b>\n\n"
            f"📊 <b>EXCLUSIVE INVENTORY FEATURES:</b>\n"
            f"• Lines become unavailable after purchase\n"
            f"• Traditional inventory consumption model\n"
            f"• Each line can only be sold once\n"
            f"• Inventory decreases with each sale\n\n"
            f"Click on any product below to manage:\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"⚙️ <i>Select a product for detailed management.</i>"
        )

        keyboard_rows = []

        for product in exclusive_products:
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0
            stock = product.get("stock", 0)

            # Status indicators for exclusive inventory
            if stock == 0:
                status_emoji = "🔴"
                status_text = "OUT OF STOCK"
            elif stock <= 10:
                status_emoji = "🟡"
                status_text = f"LOW ({stock})"
            else:
                status_emoji = "📦"
                status_text = f"STOCK: {stock}"

            # Truncate long names
            display_name = product_name[:25] + "..." if len(product_name) > 25 else product_name
            button_text = f"{status_emoji} {display_name} - ${price:.2f} ({status_text})"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])

        # Add management buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📊 Stock Analytics",
                callback_data="unified_exclusive_inventory_analytics"
            ),
            InlineKeyboardButton(
                text="🔄 Convert to Shared",
                callback_data="unified_convert_to_shared"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Line-Based",
                callback_data="unified_manage_line_based"
            ),
            InlineKeyboardButton(
                text="🏠 Main Management",
                callback_data="unified_product_management"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_shared_inventory_analytics")
async def unified_shared_inventory_analytics(callback_query: CallbackQuery):
    """Show analytics for shared inventory products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get shared inventory analytics
    from database.operations import get_all_products_for_admin
    all_products = get_all_products_for_admin()
    shared_products = [p for p in all_products if p.get("is_line_based", False) and p.get("allow_shared_inventory", False)]

    if not shared_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('SHARED INVENTORY ANALYTICS', 'NO DATA')}\n"
            f"📊 <b>No shared inventory products to analyze.</b>\n\n"
            f"Create some shared inventory products first to see analytics.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Analytics will show purchase patterns and user engagement.</i>"
        )
    else:
        # Calculate analytics
        total_products = len(shared_products)
        total_lines = sum(p.get("total_lines", 0) for p in shared_products)
        total_revenue_potential = sum((p.get("price") or 0) * p.get("total_lines", 0) for p in shared_products)

        # Get purchase history statistics
        from database.operations import line_purchase_history_collection
        try:
            total_purchases = line_purchase_history_collection.count_documents({})
            unique_users = len(line_purchase_history_collection.distinct("user_id"))

            # Get top products by purchase count
            pipeline = [
                {"$group": {"_id": "$product_id", "purchase_count": {"$sum": 1}, "total_lines": {"$sum": "$quantity"}}},
                {"$sort": {"purchase_count": -1}},
                {"$limit": 5}
            ]
            top_products = list(line_purchase_history_collection.aggregate(pipeline))
        except Exception as e:
            total_purchases = 0
            unique_users = 0
            top_products = []

        message_text = (
            f"{ExclusiveProductTheme.create_header('SHARED INVENTORY ANALYTICS')}\n"
            f"📊 <b>Shared Inventory Performance Overview</b>\n\n"
            f"🔄 <b>PRODUCT STATISTICS:</b>\n"
            f"• Total Shared Products: {total_products}\n"
            f"• Total Available Lines: {total_lines:,}\n"
            f"• Revenue Potential: ${total_revenue_potential:,.2f}\n\n"
            f"👥 <b>USER ENGAGEMENT:</b>\n"
            f"• Total Purchases: {total_purchases:,}\n"
            f"• Unique Customers: {unique_users:,}\n"
            f"• Avg Purchases per User: {total_purchases/max(unique_users, 1):.1f}\n\n"
            f"🏆 <b>TOP PERFORMING PRODUCTS:</b>\n"
        )

        if top_products:
            for i, product_data in enumerate(top_products[:3], 1):
                product_id = product_data["_id"]
                purchase_count = product_data["purchase_count"]
                total_lines_sold = product_data["total_lines"]

                # Find product name
                product = next((p for p in shared_products if p.get("id") == product_id or str(p.get("_id")) == str(product_id)), None)
                product_name = (product.get("name") or f"Product {product_id}") if product else f"Product {product_id}"

                message_text += f"{i}. {product_name[:20]}... - {purchase_count} purchases ({total_lines_sold} lines)\n"
        else:
            message_text += "No purchase data available yet.\n"

        message_text += (
            f"\n{ExclusiveProductTheme.create_section_break()}\n"
            f"📈 <i>Shared inventory allows unlimited sales potential per line.</i>"
        )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="👥 User Purchase History",
                    callback_data="unified_shared_user_history"
                ),
                InlineKeyboardButton(
                    text="📊 Detailed Reports",
                    callback_data="unified_shared_detailed_reports"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Shared Products",
                    callback_data="unified_line_based_shared_only"
                ),
                InlineKeyboardButton(
                    text="🏠 Main Management",
                    callback_data="unified_product_management"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_manage_regular")
async def unified_manage_regular_only(callback_query: CallbackQuery):
    """Show only regular products in unified interface."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all regular products
    all_products = get_all_products_for_admin()
    regular_products = [p for p in all_products if not p.get("is_exclusive_single_use", False) and not p.get("is_line_based", False)]

    if not regular_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('REGULAR PRODUCTS', 'NO PRODUCTS FOUND')}\n"
            f"📄 <b>No regular products found.</b>\n\n"
            f"No regular products have been created yet.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create your first regular product to get started.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Unified Management",
                        callback_data="unified_product_management"
                    )
                ]
            ]
        )
    else:
        # Categorize regular products by availability
        available = [p for p in regular_products if p.get("is_available", True)]
        unavailable = [p for p in regular_products if not p.get("is_available", True)]

        message_text = (
            f"{ExclusiveProductTheme.create_header('REGULAR PRODUCTS MANAGEMENT')}\n"
            f"📄 <b>Found {len(regular_products)} regular products</b>\n\n"
            f"📊 <b>AVAILABILITY STATUS:</b>\n"
            f"🟢 Available: {len(available)}\n"
            f"🔴 Unavailable: {len(unavailable)}\n\n"
            f"Click on any product below to manage:\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"⚙️ <i>Select a product for management options.</i>"
        )

        # Build keyboard with individual regular product buttons
        keyboard_rows = []

        # Sort products by availability and name
        sorted_products = available + unavailable

        for product in sorted_products:
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0
            is_available = product.get("is_available", True)

            # Status indicators
            if is_available:
                status_emoji = "🟢"
                status_text = "AVAILABLE"
            else:
                status_emoji = "🔴"
                status_text = "UNAVAILABLE"

            # Truncate long names
            display_name = product_name[:20] + "..." if len(product_name) > 20 else product_name
            button_text = f"{status_emoji} {display_name} - ${price:.2f} ({status_text})"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])

        # Add management buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📊 Regular Product Analytics",
                callback_data="unified_regular_analytics"
            ),
            InlineKeyboardButton(
                text="🟢 Available Only",
                callback_data="unified_regular_available"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="📋 All Product Types",
                callback_data="unified_manage_all_products"
            ),
            InlineKeyboardButton(
                text="🏠 Main Management",
                callback_data="unified_product_management"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_product_analytics")
async def unified_product_analytics(callback_query: CallbackQuery):
    """Show comprehensive product analytics across all types."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all products for analysis
    all_products = get_all_products_for_admin()

    # Categorize products
    exclusive_products = [p for p in all_products if p.get("is_exclusive_single_use", False)]
    line_based_products = [p for p in all_products if p.get("is_line_based", False)]
    regular_products = [p for p in all_products if not p.get("is_exclusive_single_use", False) and not p.get("is_line_based", False)]

    # Calculate analytics
    total_products = len(all_products)
    total_revenue = sum(p.get("price") or 0 for p in all_products)
    avg_price = total_revenue / total_products if total_products > 0 else 0

    # Exclusive analytics
    exclusive_sold = len([p for p in exclusive_products if p.get("is_purchased", False)])
    exclusive_revenue = sum(p.get("price") or 0 for p in exclusive_products if p.get("is_purchased", False))

    # Line-based analytics
    total_stock = sum(p.get("stock", 0) for p in line_based_products)
    out_of_stock = len([p for p in line_based_products if p.get("stock", 0) == 0])

    # Regular products analytics
    available_regular = len([p for p in regular_products if p.get("is_available", True)])

    message_text = (
        f"{ExclusiveProductTheme.create_header('PRODUCT ANALYTICS DASHBOARD')}\n"
        f"📊 <b>COMPREHENSIVE PRODUCT ANALYTICS</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"

        f"📈 <b>OVERALL STATISTICS:</b>\n"
        f"📋 Total Products: <b>{total_products}</b>\n"
        f"💰 Total Value: <b>${total_revenue:.2f}</b>\n"
        f"📊 Average Price: <b>${avg_price:.2f}</b>\n\n"

        f"🔐 <b>EXCLUSIVE PRODUCTS:</b>\n"
        f"📄 Total: <b>{len(exclusive_products)}</b>\n"
        f"💰 Sold: <b>{exclusive_sold}</b>\n"
        f"💵 Revenue: <b>${exclusive_revenue:.2f}</b>\n\n"

        f"📦 <b>LINE-BASED PRODUCTS:</b>\n"
        f"📄 Total: <b>{len(line_based_products)}</b>\n"
        f"📦 Total Stock: <b>{total_stock}</b>\n"
        f"🔴 Out of Stock: <b>{out_of_stock}</b>\n\n"

        f"📄 <b>REGULAR PRODUCTS:</b>\n"
        f"📄 Total: <b>{len(regular_products)}</b>\n"
        f"🟢 Available: <b>{available_regular}</b>\n\n"

        f"{ExclusiveProductTheme.create_section_break()}\n"
        f"📈 <i>Analytics updated in real-time based on current product data.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔐 Exclusive Analytics",
                    callback_data="unified_exclusive_stats"
                ),
                InlineKeyboardButton(
                    text="📦 Inventory Analytics",
                    callback_data="unified_line_based_analytics"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📄 Regular Analytics",
                    callback_data="unified_regular_analytics"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📋 All Products",
                    callback_data="unified_manage_all_products"
                ),
                InlineKeyboardButton(
                    text="🏠 Main Management",
                    callback_data="unified_product_management"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_search_products")
async def unified_search_products(callback_query: CallbackQuery):
    """Show product search interface."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all products for search
    all_products = get_all_products_for_admin()

    message_text = (
        f"{ExclusiveProductTheme.create_header('PRODUCT SEARCH & FILTERS')}\n"
        f"🔍 <b>SEARCH AND FILTER PRODUCTS</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"📊 <b>AVAILABLE FILTERS:</b>\n"
        f"• Search by product type\n"
        f"• Filter by availability status\n"
        f"• Sort by price or stock\n"
        f"• Find products by category\n\n"
        f"📋 <b>Total Products:</b> {len(all_products)}\n\n"
        f"{ExclusiveProductTheme.create_section_break()}\n"
        f"🔍 <i>Select a filter option below to search products.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔐 Exclusive Products",
                    callback_data="unified_manage_exclusive"
                ),
                InlineKeyboardButton(
                    text="📦 Line-Based Products",
                    callback_data="unified_manage_line_based"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📄 Regular Products",
                    callback_data="unified_manage_regular"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🟢 Available Only",
                    callback_data="unified_filter_available"
                ),
                InlineKeyboardButton(
                    text="🔴 Unavailable Only",
                    callback_data="unified_filter_unavailable"
                )
            ],
            [
                InlineKeyboardButton(
                    text="💰 Sort by Price",
                    callback_data="unified_sort_price"
                ),
                InlineKeyboardButton(
                    text="📊 Sort by Stock",
                    callback_data="unified_sort_stock"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📋 All Products",
                    callback_data="unified_manage_all_products"
                ),
                InlineKeyboardButton(
                    text="🏠 Main Management",
                    callback_data="unified_product_management"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ============================================================================
# MISSING ANALYTICS HANDLERS
# ============================================================================

@router.callback_query(F.data == "unified_line_based_analytics")
async def unified_line_based_analytics(callback_query: CallbackQuery):
    """Show detailed analytics for line-based products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all line-based products
    all_products = get_all_products_for_admin()
    line_based_products = [p for p in all_products if p.get("is_line_based", False)]

    if not line_based_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('LINE-BASED ANALYTICS', 'NO DATA AVAILABLE')}\n"
            f"📊 <b>No line-based products found.</b>\n\n"
            f"No line-based products have been created yet.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create line-based products to view analytics.</i>"
        )
    else:
        # Calculate analytics
        total_products = len(line_based_products)
        shared_inventory = len([p for p in line_based_products if p.get("allow_shared_inventory", False)])
        exclusive_inventory = total_products - shared_inventory

        # Calculate stock levels
        total_stock = 0
        out_of_stock = 0
        low_stock = 0

        for product in line_based_products:
            stock = product.get("stock_quantity", 0)
            total_stock += stock
            if stock == 0:
                out_of_stock += 1
            elif stock <= 5:  # Consider low stock as 5 or fewer items
                low_stock += 1

        # Calculate revenue potential
        total_revenue_potential = sum((p.get("price") or 0) * p.get("stock_quantity", 0) for p in line_based_products)
        avg_price = sum(p.get("price") or 0 for p in line_based_products) / total_products if total_products > 0 else 0

        message_text = (
            f"{ExclusiveProductTheme.create_header('LINE-BASED PRODUCT ANALYTICS')}\n"
            f"📊 <b>INVENTORY ANALYTICS DASHBOARD</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"📦 <b>PRODUCT OVERVIEW:</b>\n"
            f"• Total Products: <code>{total_products}</code>\n"
            f"• Shared Inventory: <code>{shared_inventory}</code>\n"
            f"• Exclusive Inventory: <code>{exclusive_inventory}</code>\n\n"
            f"📈 <b>STOCK STATUS:</b>\n"
            f"• Total Stock Items: <code>{total_stock}</code>\n"
            f"• Out of Stock: <code>{out_of_stock}</code> products\n"
            f"• Low Stock (≤5): <code>{low_stock}</code> products\n\n"
            f"💰 <b>REVENUE METRICS:</b>\n"
            f"• Revenue Potential: <code>${total_revenue_potential:.2f}</code>\n"
            f"• Average Price: <code>${avg_price:.2f}</code>\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"📊 <i>Detailed inventory analytics for line-based products.</i>"
        )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔄 Shared Inventory",
                    callback_data="unified_line_based_shared_only"
                ),
                InlineKeyboardButton(
                    text="📦 Exclusive Inventory",
                    callback_data="unified_line_based_exclusive_only"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔴 Out of Stock",
                    callback_data="unified_line_based_out_of_stock"
                ),
                InlineKeyboardButton(
                    text="📋 All Line-Based",
                    callback_data="unified_manage_line_based"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🏠 Main Management",
                    callback_data="unified_product_management"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_regular_analytics")
async def unified_regular_analytics(callback_query: CallbackQuery):
    """Show detailed analytics for regular products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all regular products
    all_products = get_all_products_for_admin()
    regular_products = [p for p in all_products if not p.get("is_exclusive_single_use", False) and not p.get("is_line_based", False)]

    if not regular_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('REGULAR PRODUCT ANALYTICS', 'NO DATA AVAILABLE')}\n"
            f"📊 <b>No regular products found.</b>\n\n"
            f"No regular products have been created yet.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create regular products to view analytics.</i>"
        )
    else:
        # Calculate analytics
        total_products = len(regular_products)
        available_products = len([p for p in regular_products if p.get("is_available", True)])
        unavailable_products = total_products - available_products

        # Calculate stock levels for products that have stock tracking
        total_stock = 0
        products_with_stock = 0
        out_of_stock = 0

        for product in regular_products:
            if "stock_quantity" in product:
                products_with_stock += 1
                stock = product.get("stock_quantity", 0)
                total_stock += stock
                if stock == 0:
                    out_of_stock += 1

        # Calculate pricing metrics
        total_revenue_potential = sum((p.get("price") or 0) * p.get("stock_quantity", 1) for p in regular_products)
        avg_price = sum(p.get("price") or 0 for p in regular_products) / total_products if total_products > 0 else 0
        min_price = min(p.get("price") or 0 for p in regular_products) if regular_products else 0
        max_price = max(p.get("price") or 0 for p in regular_products) if regular_products else 0

        message_text = (
            f"{ExclusiveProductTheme.create_header('REGULAR PRODUCT ANALYTICS')}\n"
            f"📊 <b>PRODUCT ANALYTICS DASHBOARD</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"📦 <b>PRODUCT OVERVIEW:</b>\n"
            f"• Total Products: <code>{total_products}</code>\n"
            f"• Available: <code>{available_products}</code>\n"
            f"• Unavailable: <code>{unavailable_products}</code>\n\n"
            f"📈 <b>STOCK STATUS:</b>\n"
            f"• Products with Stock Tracking: <code>{products_with_stock}</code>\n"
            f"• Total Stock Items: <code>{total_stock}</code>\n"
            f"• Out of Stock: <code>{out_of_stock}</code> products\n\n"
            f"💰 <b>PRICING METRICS:</b>\n"
            f"• Revenue Potential: <code>${total_revenue_potential:.2f}</code>\n"
            f"• Average Price: <code>${avg_price:.2f}</code>\n"
            f"• Price Range: <code>${min_price:.2f} - ${max_price:.2f}</code>\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"📊 <i>Comprehensive analytics for regular products.</i>"
        )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🟢 Available Only",
                    callback_data="unified_regular_available"
                ),
                InlineKeyboardButton(
                    text="📋 All Regular",
                    callback_data="unified_manage_regular"
                )
            ],
            [
                InlineKeyboardButton(
                    text="📊 All Analytics",
                    callback_data="unified_product_analytics"
                ),
                InlineKeyboardButton(
                    text="🏠 Main Management",
                    callback_data="unified_product_management"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_regular_available")
async def unified_regular_available_only(callback_query: CallbackQuery):
    """Show only available regular products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get available regular products
    all_products = get_all_products_for_admin()
    available_products = [
        p for p in all_products
        if not p.get("is_exclusive_single_use", False)
        and not p.get("is_line_based", False)
        and p.get("is_available", True)
    ]

    if not available_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('AVAILABLE REGULAR PRODUCTS', 'NO PRODUCTS FOUND')}\n"
            f"🟢 <b>No available regular products found.</b>\n\n"
            f"All regular products are currently unavailable or none exist.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create regular products or make existing ones available.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Regular",
                        callback_data="unified_manage_regular"
                    )
                ]
            ]
        )
    else:
        # Sort products by price
        sorted_products = sorted(available_products, key=lambda x: x.get("price", 0))

        message_text = (
            f"{ExclusiveProductTheme.create_header('AVAILABLE REGULAR PRODUCTS')}\n"
            f"🟢 <b>AVAILABLE PRODUCTS LISTING</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"📊 <b>Found {len(available_products)} available regular products</b>\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"🟢 <i>Click on any product below to manage it.</i>"
        )

        # Build keyboard with available products
        keyboard_rows = []

        for product in sorted_products:
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0

            display_name = product_name[:25] + "..." if len(product_name) > 25 else product_name
            button_text = f"🟢 {display_name} - ${price:.2f}"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📄 All Regular Products",
                callback_data="unified_manage_regular"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Management",
                callback_data="unified_product_management"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ============================================================================
# MISSING FILTER AND SORTING HANDLERS
# ============================================================================

@router.callback_query(F.data == "unified_filter_available")
async def unified_filter_available_products(callback_query: CallbackQuery):
    """Show only available products across all types."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all products and filter available ones
    all_products = get_all_products_for_admin()
    available_products = []

    for product in all_products:
        # Check availability based on product type
        if product.get("is_exclusive_single_use", False):
            # Exclusive products are available if not purchased and not removed
            if not product.get("is_purchased", False) and not product.get("removed_from_listings", False):
                available_products.append(product)
        elif product.get("is_line_based", False):
            # Line-based products are available if they have stock
            if product.get("stock_quantity", 0) > 0:
                available_products.append(product)
        else:
            # Regular products use is_available flag
            if product.get("is_available", True):
                available_products.append(product)

    if not available_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('AVAILABLE PRODUCTS', 'NO PRODUCTS FOUND')}\n"
            f"🟢 <b>No available products found.</b>\n\n"
            f"All products are currently unavailable or out of stock.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Check individual product types or add new products.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Search",
                        callback_data="unified_search_products"
                    )
                ]
            ]
        )
    else:
        # Sort products by type and price
        sorted_products = sorted(available_products, key=lambda x: (
            0 if x.get("is_exclusive_single_use", False) else
            1 if x.get("is_line_based", False) else 2,
            x.get("price", 0)
        ))

        message_text = (
            f"{ExclusiveProductTheme.create_header('AVAILABLE PRODUCTS')}\n"
            f"🟢 <b>ALL AVAILABLE PRODUCTS</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"📊 <b>Found {len(available_products)} available products</b>\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"🟢 <i>Click on any product below to manage it.</i>"
        )

        # Build keyboard with available products (limit to prevent "reply markup is too long")
        keyboard_rows = []
        max_products_per_page = 15  # Limit to prevent keyboard size issues

        for i, product in enumerate(sorted_products[:max_products_per_page]):
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0

            # Determine product type emoji
            if product.get("is_exclusive_single_use", False):
                type_emoji = "🔐"
            elif product.get("is_line_based", False):
                type_emoji = "📦"
            else:
                type_emoji = "📄"

            display_name = product_name[:20] + "..." if len(product_name) > 20 else product_name
            button_text = f"🟢 {type_emoji} {display_name} - ${price:.2f}"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])

        # Add "Show More" button if there are more products
        if len(sorted_products) > max_products_per_page:
            keyboard_rows.append([
                InlineKeyboardButton(
                    text=f"📋 Show More ({len(sorted_products) - max_products_per_page} more)",
                    callback_data="unified_show_more_available"
                )
            ])

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔴 Unavailable Only",
                callback_data="unified_filter_unavailable"
            ),
            InlineKeyboardButton(
                text="📋 All Products",
                callback_data="unified_manage_all_products"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Search",
                callback_data="unified_search_products"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_filter_unavailable")
async def unified_filter_unavailable_products(callback_query: CallbackQuery):
    """Show only unavailable products across all types."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all products and filter unavailable ones
    all_products = get_all_products_for_admin()
    unavailable_products = []

    for product in all_products:
        # Check availability based on product type
        if product.get("is_exclusive_single_use", False):
            # Exclusive products are unavailable if purchased or removed
            if product.get("is_purchased", False) or product.get("removed_from_listings", False):
                unavailable_products.append(product)
        elif product.get("is_line_based", False):
            # Line-based products are unavailable if they have no stock
            if product.get("stock_quantity", 0) == 0:
                unavailable_products.append(product)
        else:
            # Regular products use is_available flag
            if not product.get("is_available", True):
                unavailable_products.append(product)

    if not unavailable_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('UNAVAILABLE PRODUCTS', 'NO PRODUCTS FOUND')}\n"
            f"🔴 <b>No unavailable products found.</b>\n\n"
            f"All products are currently available.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"✅ <i>Great! All your products are available for purchase.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🟢 Available Products",
                        callback_data="unified_filter_available"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Search",
                        callback_data="unified_search_products"
                    )
                ]
            ]
        )
    else:
        # Sort products by type and price
        sorted_products = sorted(unavailable_products, key=lambda x: (
            0 if x.get("is_exclusive_single_use", False) else
            1 if x.get("is_line_based", False) else 2,
            x.get("price", 0)
        ))

        message_text = (
            f"{ExclusiveProductTheme.create_header('UNAVAILABLE PRODUCTS')}\n"
            f"🔴 <b>ALL UNAVAILABLE PRODUCTS</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"📊 <b>Found {len(unavailable_products)} unavailable products</b>\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"🔴 <i>Click on any product below to manage it.</i>"
        )

        # Build keyboard with unavailable products
        keyboard_rows = []

        for product in sorted_products:
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0

            # Determine product type emoji and status
            if product.get("is_exclusive_single_use", False):
                type_emoji = "🔐"
                if product.get("is_purchased", False):
                    status_text = "SOLD"
                else:
                    status_text = "REMOVED"
            elif product.get("is_line_based", False):
                type_emoji = "📦"
                status_text = "OUT OF STOCK"
            else:
                type_emoji = "📄"
                status_text = "UNAVAILABLE"

            display_name = product_name[:15] + "..." if len(product_name) > 15 else product_name
            button_text = f"🔴 {type_emoji} {display_name} - ${price:.2f} ({status_text})"

            # Limit products to prevent keyboard size issues
            if len(keyboard_rows) < 15:  # Max 15 products per page
                keyboard_rows.append([
                    InlineKeyboardButton(
                        text=button_text,
                        callback_data=f"unified_product_detail:{product_id}"
                    )
                ])
            else:
                # Add "Show More" button and break
                keyboard_rows.append([
                    InlineKeyboardButton(
                        text=f"📋 Show More ({len(sorted_products) - 15} more)",
                        callback_data="unified_show_more_unavailable"
                    )
                ])
                break

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🟢 Available Only",
                callback_data="unified_filter_available"
            ),
            InlineKeyboardButton(
                text="📋 All Products",
                callback_data="unified_manage_all_products"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Search",
                callback_data="unified_search_products"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_sort_price")
async def unified_sort_by_price(callback_query: CallbackQuery):
    """Show all products sorted by price (low to high)."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all products and sort by price
    all_products = get_all_products_for_admin()

    if not all_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('PRODUCTS BY PRICE', 'NO PRODUCTS FOUND')}\n"
            f"💰 <b>No products found.</b>\n\n"
            f"No products have been created yet.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create your first product to get started.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Search",
                        callback_data="unified_search_products"
                    )
                ]
            ]
        )
    else:
        # Sort products by price (low to high)
        sorted_products = sorted(all_products, key=lambda x: x.get("price", 0))

        # Calculate price statistics
        prices = [p.get("price") or 0 for p in all_products]
        min_price = min(prices) if prices else 0
        max_price = max(prices) if prices else 0
        avg_price = sum(prices) / len(prices) if prices else 0

        message_text = (
            f"{ExclusiveProductTheme.create_header('PRODUCTS SORTED BY PRICE')}\n"
            f"💰 <b>PRICE-SORTED PRODUCT LISTING</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"📊 <b>PRICE STATISTICS:</b>\n"
            f"• Total Products: <code>{len(all_products)}</code>\n"
            f"• Price Range: <code>${min_price:.2f} - ${max_price:.2f}</code>\n"
            f"• Average Price: <code>${avg_price:.2f}</code>\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💰 <i>Products sorted from lowest to highest price.</i>"
        )

        # Build keyboard with price-sorted products (limit to prevent "reply markup is too long")
        keyboard_rows = []
        max_products_per_page = 15  # Limit to prevent keyboard size issues

        for i, product in enumerate(sorted_products[:max_products_per_page]):
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0

            # Determine product type emoji
            if product.get("is_exclusive_single_use", False):
                type_emoji = "🔐"
            elif product.get("is_line_based", False):
                type_emoji = "📦"
            else:
                type_emoji = "📄"

            display_name = product_name[:20] + "..." if len(product_name) > 20 else product_name
            button_text = f"💰 {type_emoji} {display_name} - ${price:.2f}"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])

        # Add "Show More" button if there are more products
        if len(sorted_products) > max_products_per_page:
            keyboard_rows.append([
                InlineKeyboardButton(
                    text=f"📋 Show More ({len(sorted_products) - max_products_per_page} more)",
                    callback_data="unified_show_more_price_sorted"
                )
            ])

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📊 Sort by Stock",
                callback_data="unified_sort_stock"
            ),
            InlineKeyboardButton(
                text="📋 All Products",
                callback_data="unified_manage_all_products"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Search",
                callback_data="unified_search_products"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_sort_stock")
async def unified_sort_by_stock(callback_query: CallbackQuery):
    """Show all products sorted by stock levels (high to low)."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all products and sort by stock
    all_products = get_all_products_for_admin()

    if not all_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('PRODUCTS BY STOCK', 'NO PRODUCTS FOUND')}\n"
            f"📊 <b>No products found.</b>\n\n"
            f"No products have been created yet.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create your first product to get started.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="➕ Add New Product",
                        callback_data="add_new_product"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Search",
                        callback_data="unified_search_products"
                    )
                ]
            ]
        )
    else:
        # Sort products by stock levels (high to low)
        # For exclusive products, treat as 1 if available, 0 if sold
        # For line-based products, use stock_quantity
        # For regular products, use stock_quantity if available, otherwise treat as unlimited (999999)

        def get_stock_level(product):
            if product.get("is_exclusive_single_use", False):
                return 0 if product.get("is_purchased", False) else 1
            elif product.get("is_line_based", False):
                return product.get("stock_quantity", 0)
            else:
                return product.get("stock_quantity", 999999)  # Treat as unlimited if no stock tracking

        sorted_products = sorted(all_products, key=get_stock_level, reverse=True)

        # Calculate stock statistics
        total_stock = 0
        products_with_stock = 0
        out_of_stock = 0

        for product in all_products:
            stock = get_stock_level(product)
            if stock < 999999:  # Don't count unlimited stock in totals
                total_stock += stock
                products_with_stock += 1
                if stock == 0:
                    out_of_stock += 1

        message_text = (
            f"{ExclusiveProductTheme.create_header('PRODUCTS SORTED BY STOCK')}\n"
            f"📊 <b>STOCK-SORTED PRODUCT LISTING</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"📈 <b>STOCK STATISTICS:</b>\n"
            f"• Total Products: <code>{len(all_products)}</code>\n"
            f"• Products with Stock Tracking: <code>{products_with_stock}</code>\n"
            f"• Total Stock Items: <code>{total_stock}</code>\n"
            f"• Out of Stock: <code>{out_of_stock}</code> products\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"📊 <i>Products sorted from highest to lowest stock.</i>"
        )

        # Build keyboard with stock-sorted products
        keyboard_rows = []

        for product in sorted_products:
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0
            stock = get_stock_level(product)

            # Determine product type emoji and stock display
            if product.get("is_exclusive_single_use", False):
                type_emoji = "🔐"
                stock_text = "AVAILABLE" if stock > 0 else "SOLD"
            elif product.get("is_line_based", False):
                type_emoji = "📦"
                stock_text = f"{stock} items"
            else:
                type_emoji = "📄"
                stock_text = "UNLIMITED" if stock >= 999999 else f"{stock} items"

            display_name = product_name[:15] + "..." if len(product_name) > 15 else product_name
            button_text = f"📊 {type_emoji} {display_name} - ${price:.2f} ({stock_text})"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="💰 Sort by Price",
                callback_data="unified_sort_price"
            ),
            InlineKeyboardButton(
                text="📋 All Products",
                callback_data="unified_manage_all_products"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Search",
                callback_data="unified_search_products"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ============================================================================
# MISSING LINE-BASED HANDLERS
# ============================================================================

@router.callback_query(F.data == "unified_line_based_out_of_stock")
async def unified_line_based_out_of_stock(callback_query: CallbackQuery):
    """Show only out of stock line-based products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get all line-based products that are out of stock
    all_products = get_all_products_for_admin()
    out_of_stock_products = [
        p for p in all_products
        if p.get("is_line_based", False) and p.get("stock_quantity", 0) == 0
    ]

    if not out_of_stock_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('OUT OF STOCK PRODUCTS', 'NO PRODUCTS FOUND')}\n"
            f"🔴 <b>No out of stock line-based products found.</b>\n\n"
            f"All line-based products currently have stock available.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"✅ <i>Great! All your line-based products are in stock.</i>"
        )

        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="📦 All Line-Based",
                        callback_data="unified_manage_line_based"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="📊 Inventory Analytics",
                        callback_data="unified_line_based_analytics"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Management",
                        callback_data="unified_product_management"
                    )
                ]
            ]
        )
    else:
        # Sort products by price
        sorted_products = sorted(out_of_stock_products, key=lambda x: x.get("price", 0))

        message_text = (
            f"{ExclusiveProductTheme.create_header('OUT OF STOCK LINE-BASED PRODUCTS')}\n"
            f"🔴 <b>INVENTORY ALERT - OUT OF STOCK</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"⚠️ <b>Found {len(out_of_stock_products)} products out of stock</b>\n\n"
            f"📦 <b>INVENTORY STATUS:</b>\n"
            f"• These products need restocking\n"
            f"• Customers cannot purchase these items\n"
            f"• Consider uploading new inventory files\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"🔴 <i>Click on any product below to restock it.</i>"
        )

        # Build keyboard with out of stock products (limit to prevent "reply markup is too long")
        keyboard_rows = []
        max_products_per_page = 15  # Limit to prevent keyboard size issues

        for i, product in enumerate(sorted_products[:max_products_per_page]):
            product_id = product.get("_id") or product.get("id")
            product_name = product.get("name") or "Unknown Product"
            price = product.get("price") or 0

            # Determine inventory type
            inventory_type = "EXCLUSIVE" if not product.get("allow_shared_inventory", False) else "SHARED"

            display_name = product_name[:20] + "..." if len(product_name) > 20 else product_name
            button_text = f"🔴 📦 {display_name} - ${price:.2f} ({inventory_type})"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_product_detail:{product_id}"
                )
            ])

        # Add "Show More" button if there are more products
        if len(sorted_products) > max_products_per_page:
            keyboard_rows.append([
                InlineKeyboardButton(
                    text=f"📋 Show More ({len(sorted_products) - max_products_per_page} more)",
                    callback_data="unified_show_more_out_of_stock"
                )
            ])

        # Add management buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📦 All Line-Based",
                callback_data="unified_manage_line_based"
            ),
            InlineKeyboardButton(
                text="📊 Inventory Analytics",
                callback_data="unified_line_based_analytics"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔄 Shared Inventory",
                callback_data="unified_line_based_shared_only"
            ),
            InlineKeyboardButton(
                text="📦 Exclusive Inventory",
                callback_data="unified_line_based_exclusive_only"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Management",
                callback_data="unified_product_management"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


# ============================================================================
# REMAINING MISSING HANDLERS
# ============================================================================

@router.callback_query(F.data == "unified_shared_user_history")
async def unified_shared_user_history(callback_query: CallbackQuery):
    """Show user purchase history for shared inventory products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    message_text = (
        f"{ExclusiveProductTheme.create_header('SHARED INVENTORY USER HISTORY')}\n"
        f"👥 <b>USER PURCHASE HISTORY</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"📊 <b>This feature shows detailed user purchase history for shared inventory products.</b>\n\n"
        f"🔍 <b>AVAILABLE DATA:</b>\n"
        f"• User purchase patterns\n"
        f"• Product consumption rates\n"
        f"• Popular items analysis\n"
        f"• Revenue by user segments\n\n"
        f"{ExclusiveProductTheme.create_section_break()}\n"
        f"👥 <i>Feature coming soon - detailed user analytics.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📊 Shared Analytics",
                    callback_data="unified_shared_inventory_analytics"
                ),
                InlineKeyboardButton(
                    text="📋 Detailed Reports",
                    callback_data="unified_shared_detailed_reports"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Shared Products",
                    callback_data="unified_line_based_shared_only"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_shared_detailed_reports")
async def unified_shared_detailed_reports(callback_query: CallbackQuery):
    """Show detailed reports for shared inventory products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    message_text = (
        f"{ExclusiveProductTheme.create_header('SHARED INVENTORY DETAILED REPORTS')}\n"
        f"📊 <b>COMPREHENSIVE REPORTING</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"📈 <b>This feature provides detailed reports for shared inventory analysis.</b>\n\n"
        f"📋 <b>REPORT TYPES:</b>\n"
        f"• Sales performance reports\n"
        f"• Inventory turnover analysis\n"
        f"• User behavior patterns\n"
        f"• Revenue optimization insights\n\n"
        f"{ExclusiveProductTheme.create_section_break()}\n"
        f"📊 <i>Advanced reporting features coming soon.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="👥 User History",
                    callback_data="unified_shared_user_history"
                ),
                InlineKeyboardButton(
                    text="📊 Shared Analytics",
                    callback_data="unified_shared_inventory_analytics"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Shared Products",
                    callback_data="unified_line_based_shared_only"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_exclusive_inventory_analytics")
async def unified_exclusive_inventory_analytics(callback_query: CallbackQuery):
    """Show analytics for exclusive inventory line-based products."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    # Get exclusive inventory products
    all_products = get_all_products_for_admin()
    exclusive_products = [p for p in all_products if p.get("is_line_based", False) and not p.get("allow_shared_inventory", False)]

    if not exclusive_products:
        message_text = (
            f"{ExclusiveProductTheme.create_header('EXCLUSIVE INVENTORY ANALYTICS', 'NO DATA AVAILABLE')}\n"
            f"📊 <b>No exclusive inventory products found.</b>\n\n"
            f"All line-based products are using shared inventory mode.\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"💡 <i>Create line-based products with exclusive inventory to view analytics.</i>"
        )
    else:
        # Calculate analytics
        total_products = len(exclusive_products)
        total_stock = sum(p.get("stock_quantity", 0) for p in exclusive_products)
        out_of_stock = len([p for p in exclusive_products if p.get("stock_quantity", 0) == 0])
        low_stock = len([p for p in exclusive_products if 0 < p.get("stock_quantity", 0) <= 5])

        # Calculate revenue potential
        total_revenue_potential = sum((p.get("price") or 0) * p.get("stock_quantity", 0) for p in exclusive_products)
        avg_price = sum(p.get("price") or 0 for p in exclusive_products) / total_products if total_products > 0 else 0

        message_text = (
            f"{ExclusiveProductTheme.create_header('EXCLUSIVE INVENTORY ANALYTICS')}\n"
            f"📦 <b>EXCLUSIVE STOCK ANALYTICS</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"📊 <b>INVENTORY OVERVIEW:</b>\n"
            f"• Total Products: <code>{total_products}</code>\n"
            f"• Total Stock Items: <code>{total_stock}</code>\n"
            f"• Out of Stock: <code>{out_of_stock}</code> products\n"
            f"• Low Stock (≤5): <code>{low_stock}</code> products\n\n"
            f"💰 <b>REVENUE METRICS:</b>\n"
            f"• Revenue Potential: <code>${total_revenue_potential:.2f}</code>\n"
            f"• Average Price: <code>${avg_price:.2f}</code>\n\n"
            f"{ExclusiveProductTheme.create_section_break()}\n"
            f"📦 <i>Detailed analytics for exclusive inventory products.</i>"
        )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🔄 Convert to Shared",
                    callback_data="unified_convert_to_shared"
                ),
                InlineKeyboardButton(
                    text="📦 All Exclusive",
                    callback_data="unified_line_based_exclusive_only"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Line-Based",
                    callback_data="unified_manage_line_based"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_convert_to_shared")
async def unified_convert_to_shared(callback_query: CallbackQuery):
    """Show interface for converting exclusive inventory to shared inventory."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return

    await callback_query.answer()

    message_text = (
        f"{ExclusiveProductTheme.create_header('CONVERT TO SHARED INVENTORY')}\n"
        f"🔄 <b>INVENTORY CONVERSION TOOL</b>\n"
        f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
        f"⚠️ <b>This feature allows converting exclusive inventory products to shared inventory mode.</b>\n\n"
        f"📋 <b>CONVERSION PROCESS:</b>\n"
        f"• Select products to convert\n"
        f"• Merge inventory pools\n"
        f"• Update product settings\n"
        f"• Preserve purchase history\n\n"
        f"🔒 <b>IMPORTANT:</b> This action cannot be easily undone.\n\n"
        f"{ExclusiveProductTheme.create_section_break()}\n"
        f"🔄 <i>Conversion tool coming soon - contact support for manual conversion.</i>"
    )

    keyboard = InlineKeyboardMarkup(
        inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="📊 Stock Analytics",
                    callback_data="unified_exclusive_inventory_analytics"
                ),
                InlineKeyboardButton(
                    text="📦 Exclusive Products",
                    callback_data="unified_line_based_exclusive_only"
                )
            ],
            [
                InlineKeyboardButton(
                    text="🔙 Back to Line-Based",
                    callback_data="unified_manage_line_based"
                )
            ]
        ]
    )

    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "unified_upload_files")
async def handle_unified_upload_files(callback_query: CallbackQuery, state: FSMContext):
    """Handle the Upload Files button - show all file-based products and their status."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access denied.", show_alert=True)
        return

    try:
        await callback_query.answer()

        # Ensure uploads directory exists
        FileBasedProductManager.ensure_uploads_directory()

        # Get upload statistics
        stats = FileBasedProductManager.get_upload_statistics()

        # Get file-based products
        file_based_products = FileBasedProductManager.get_file_based_products()

        # Build message
        message_text = "📁 <b>File Upload Management</b>\n\n"

        # Add statistics
        message_text += f"📊 <b>Upload Statistics:</b>\n"
        message_text += f"• Total Products: {stats['total_products']}\n"
        message_text += f"• Complete: {stats['products_with_all_files']} ({stats['completion_rate']}%)\n"
        message_text += f"• Missing Files: {stats['products_with_missing_files']}\n"
        message_text += f"• Total Files: {stats['total_existing_files']}/{stats['total_files']} ({stats['file_completion_rate']}%)\n\n"

        # Add directory status
        if stats['uploads_directory_exists']:
            message_text += "✅ Uploads directory: <b>Available</b>\n\n"
        else:
            message_text += "⚠️ Uploads directory: <b>Missing (Created)</b>\n\n"

        # Add product type breakdown
        message_text += "📦 <b>By Product Type:</b>\n"
        for product_type, type_stats in stats['product_type_stats'].items():
            if type_stats['total'] > 0:
                type_emoji = {"regular": "📄", "line-based": "📦", "exclusive": "🔐"}[product_type]
                completion = round((type_stats['complete'] / type_stats['total']) * 100, 1) if type_stats['total'] > 0 else 0
                message_text += f"{type_emoji} {product_type.title()}: {type_stats['complete']}/{type_stats['total']} ({completion}%)\n"

        if not file_based_products:
            message_text += "\n📝 <b>No file-based products found.</b>\n"
            message_text += "Products with file uploads will appear here."

        # Build keyboard
        keyboard_rows = []

        if file_based_products:
            # Add filter buttons
            keyboard_rows.append([
                InlineKeyboardButton(
                    text="🟢 Complete Only",
                    callback_data="unified_upload_files_complete"
                ),
                InlineKeyboardButton(
                    text="🔴 Missing Files Only",
                    callback_data="unified_upload_files_missing"
                )
            ])

            keyboard_rows.append([
                InlineKeyboardButton(
                    text="📄 Regular Products",
                    callback_data="unified_upload_files_regular"
                ),
                InlineKeyboardButton(
                    text="📦 Line-Based",
                    callback_data="unified_upload_files_line_based"
                )
            ])

            keyboard_rows.append([
                InlineKeyboardButton(
                    text="🔐 Exclusive Products",
                    callback_data="unified_upload_files_exclusive"
                )
            ])

            keyboard_rows.append([
                InlineKeyboardButton(
                    text="📋 All File Products",
                    callback_data="unified_upload_files_all"
                ),
                InlineKeyboardButton(
                    text="📊 Missing Files Report",
                    callback_data="unified_upload_files_report"
                )
            ])

        # Add management buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔄 Refresh Status",
                callback_data="unified_upload_files"
            ),
            InlineKeyboardButton(
                text="📁 Ensure Directories",
                callback_data="unified_upload_ensure_dirs"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Management",
                callback_data="unified_product_management"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error in handle_unified_upload_files: {e}")
        await callback_query.answer("❌ Error loading upload files interface.", show_alert=True)


@router.callback_query(F.data == "unified_upload_files_all")
async def handle_unified_upload_files_all(callback_query: CallbackQuery, state: FSMContext):
    """Show all file-based products with their upload status."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access denied.", show_alert=True)
        return

    try:
        await callback_query.answer()

        # Get file-based products
        file_based_products = FileBasedProductManager.get_file_based_products()

        if not file_based_products:
            await callback_query.answer("📝 No file-based products found.", show_alert=True)
            return

        # Build message
        message_text = "📁 <b>All File-Based Products</b>\n\n"

        # Build keyboard with products
        keyboard_rows = []

        for product in file_based_products[:20]:  # Limit to 20 products to avoid message length issues
            product_id = product["product_id"]
            product_name = product["name"]
            product_type = product["type"]
            file_status = product["file_status"]

            # Determine status emoji and text
            if file_status["all_files_present"]:
                status_emoji = "✅"
                status_text = "Complete"
            else:
                status_emoji = "❌"
                status_text = f"Missing {file_status['missing_files']}"

            # Determine type emoji
            type_emoji = {"regular": "📄", "line-based": "📦", "exclusive": "🔐"}[product_type]

            # Truncate long names
            display_name = product_name[:15] + "..." if len(product_name) > 15 else product_name
            button_text = f"{status_emoji} {type_emoji} {display_name} ({status_text})"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_upload_product_detail:{product_id}"
                )
            ])

        if len(file_based_products) > 20:
            message_text += f"<i>Showing first 20 of {len(file_based_products)} products</i>\n\n"

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔴 Missing Files Only",
                callback_data="unified_upload_files_missing"
            ),
            InlineKeyboardButton(
                text="🟢 Complete Only",
                callback_data="unified_upload_files_complete"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Upload Files",
                callback_data="unified_upload_files"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error in handle_unified_upload_files_all: {e}")
        await callback_query.answer("❌ Error loading products.", show_alert=True)


@router.callback_query(F.data == "unified_upload_files_missing")
async def handle_unified_upload_files_missing(callback_query: CallbackQuery, state: FSMContext):
    """Show only products with missing files."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access denied.", show_alert=True)
        return

    try:
        await callback_query.answer()

        # Get file-based products and filter for missing files
        file_based_products = FileBasedProductManager.get_file_based_products()
        missing_products = [p for p in file_based_products if not p["file_status"]["all_files_present"]]

        if not missing_products:
            await callback_query.answer("🎉 All file-based products have complete uploads!", show_alert=True)
            return

        # Build message
        message_text = f"🔴 <b>Products with Missing Files ({len(missing_products)})</b>\n\n"

        # Build keyboard with products
        keyboard_rows = []

        for product in missing_products[:20]:  # Limit to 20 products
            product_id = product["product_id"]
            product_name = product["name"]
            product_type = product["type"]
            file_status = product["file_status"]

            # Determine type emoji
            type_emoji = {"regular": "📄", "line-based": "📦", "exclusive": "🔐"}[product_type]

            # Truncate long names
            display_name = product_name[:15] + "..." if len(product_name) > 15 else product_name
            button_text = f"❌ {type_emoji} {display_name} (Missing {file_status['missing_files']})"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_upload_product_detail:{product_id}"
                )
            ])

        if len(missing_products) > 20:
            message_text += f"<i>Showing first 20 of {len(missing_products)} products</i>\n\n"

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📋 All Products",
                callback_data="unified_upload_files_all"
            ),
            InlineKeyboardButton(
                text="📊 Missing Files Report",
                callback_data="unified_upload_files_report"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Upload Files",
                callback_data="unified_upload_files"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error in handle_unified_upload_files_missing: {e}")
        await callback_query.answer("❌ Error loading missing files.", show_alert=True)


@router.callback_query(F.data == "unified_upload_files_complete")
async def handle_unified_upload_files_complete(callback_query: CallbackQuery, state: FSMContext):
    """Show only products with complete file uploads."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access denied.", show_alert=True)
        return

    try:
        await callback_query.answer()

        # Get file-based products and filter for complete files
        file_based_products = FileBasedProductManager.get_file_based_products()
        complete_products = [p for p in file_based_products if p["file_status"]["all_files_present"]]

        if not complete_products:
            await callback_query.answer("📝 No products with complete uploads found.", show_alert=True)
            return

        # Build message
        message_text = f"🟢 <b>Products with Complete Uploads ({len(complete_products)})</b>\n\n"

        # Build keyboard with products
        keyboard_rows = []

        for product in complete_products[:20]:  # Limit to 20 products
            product_id = product["product_id"]
            product_name = product["name"]
            product_type = product["type"]
            file_status = product["file_status"]

            # Determine type emoji
            type_emoji = {"regular": "📄", "line-based": "📦", "exclusive": "🔐"}[product_type]

            # Truncate long names
            display_name = product_name[:15] + "..." if len(product_name) > 15 else product_name
            button_text = f"✅ {type_emoji} {display_name} ({file_status['total_files']} files)"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_upload_product_detail:{product_id}"
                )
            ])

        if len(complete_products) > 20:
            message_text += f"<i>Showing first 20 of {len(complete_products)} products</i>\n\n"

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📋 All Products",
                callback_data="unified_upload_files_all"
            ),
            InlineKeyboardButton(
                text="🔴 Missing Files Only",
                callback_data="unified_upload_files_missing"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Upload Files",
                callback_data="unified_upload_files"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error in handle_unified_upload_files_complete: {e}")
        await callback_query.answer("❌ Error loading complete files.", show_alert=True)


@router.callback_query(F.data == "unified_upload_files_report")
async def handle_unified_upload_files_report(callback_query: CallbackQuery, state: FSMContext):
    """Show detailed missing files report."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access denied.", show_alert=True)
        return

    try:
        await callback_query.answer()

        # Get missing files report
        report = FileBasedProductManager.get_missing_files_report()

        # Build message
        message_text = "📊 <b>Missing Files Report</b>\n\n"

        if report["total_missing_files"] == 0:
            message_text += "🎉 <b>All files are present!</b>\n"
            message_text += "No missing files found across all products."
        else:
            message_text += f"❌ <b>Total Missing Files:</b> {report['total_missing_files']}\n"
            message_text += f"📦 <b>Products Affected:</b> {report['products_with_missing_files']}\n\n"

            # Show missing files by type
            message_text += "📁 <b>Missing Files by Type:</b>\n"
            for file_type, files in report["missing_files_by_type"].items():
                if files:
                    type_names = {
                        "product_file": "Product Files",
                        "inventory_file": "Inventory Files",
                        "exclusive_file": "Exclusive Files",
                        "image_file": "Image Files"
                    }
                    message_text += f"• {type_names[file_type]}: {len(files)}\n"

            message_text += "\n"

            # Show first few products with missing files
            if report["missing_files_by_product"]:
                message_text += "🔍 <b>Products with Missing Files:</b>\n"
                for i, product in enumerate(report["missing_files_by_product"][:5]):
                    message_text += f"{i+1}. {product['product_name']} ({len(product['missing_files'])} missing)\n"

                if len(report["missing_files_by_product"]) > 5:
                    remaining = len(report["missing_files_by_product"]) - 5
                    message_text += f"... and {remaining} more products\n"

        # Add directory status
        message_text += f"\n📁 Uploads Directory: {'✅ Available' if report['uploads_directory_exists'] else '❌ Missing'}"

        # Build keyboard
        keyboard_rows = []

        if report["total_missing_files"] > 0:
            keyboard_rows.append([
                InlineKeyboardButton(
                    text="🔴 View Missing Files",
                    callback_data="unified_upload_files_missing"
                ),
                InlineKeyboardButton(
                    text="📁 Ensure Directories",
                    callback_data="unified_upload_ensure_dirs"
                )
            ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Upload Files",
                callback_data="unified_upload_files"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error in handle_unified_upload_files_report: {e}")
        await callback_query.answer("❌ Error generating report.", show_alert=True)


@router.callback_query(F.data.startswith("unified_upload_product_detail:"))
async def handle_unified_upload_product_detail(callback_query: CallbackQuery, state: FSMContext):
    """Show detailed file upload status for a specific product."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access denied.", show_alert=True)
        return

    try:
        await callback_query.answer()

        # Extract product ID from callback data
        product_id = callback_query.data.split(":", 1)[1]

        # Get product file integrity check
        integrity_check = FileBasedProductManager.check_product_file_integrity(product_id)

        if not integrity_check["success"]:
            await callback_query.answer(f"❌ {integrity_check.get('error', 'Product not found')}", show_alert=True)
            return

        if not integrity_check["has_files"]:
            await callback_query.answer("📝 This product does not require file uploads.", show_alert=True)
            return

        product_name = integrity_check["product_name"]
        file_info = integrity_check["file_info"]
        file_status = integrity_check["file_status"]

        # Build message
        message_text = f"📁 <b>File Upload Details</b>\n\n"
        message_text += f"📦 <b>Product:</b> {product_name}\n"
        message_text += f"🆔 <b>ID:</b> {product_id}\n\n"

        # Overall status
        if file_status["all_files_present"]:
            message_text += "✅ <b>Status:</b> All files present\n"
        else:
            message_text += f"❌ <b>Status:</b> {file_status['missing_files']} files missing\n"

        message_text += f"📊 <b>Files:</b> {file_status['existing_files']}/{file_status['total_files']}\n\n"

        # File details
        message_text += "📋 <b>File Details:</b>\n"
        for i, file_detail in enumerate(file_status["file_details"], 1):
            status_emoji = "✅" if file_detail["exists"] else "❌"
            size_text = f" ({file_detail['size_mb']} MB)" if file_detail["exists"] else ""
            message_text += f"{i}. {status_emoji} {file_detail['description']}{size_text}\n"
            if not file_detail["exists"]:
                message_text += f"   📍 Expected: {file_detail['normalized_path']}\n"

        # Build keyboard
        keyboard_rows = []

        # Add upload buttons for missing files
        missing_files = [f for f in file_status["file_details"] if not f["exists"]]
        if missing_files:
            message_text += f"\n🔧 <b>Upload Missing Files:</b>\n"

            for file_detail in missing_files[:3]:  # Limit to 3 buttons to avoid keyboard size issues
                button_text = f"📤 Upload {file_detail['description']}"
                callback_data = f"unified_upload_file:{product_id}:{file_detail['type']}"

                keyboard_rows.append([
                    InlineKeyboardButton(
                        text=button_text,
                        callback_data=callback_data
                    )
                ])

            if len(missing_files) > 3:
                keyboard_rows.append([
                    InlineKeyboardButton(
                        text=f"📤 Upload All Missing ({len(missing_files)})",
                        callback_data=f"unified_upload_all_missing:{product_id}"
                    )
                ])

        # Add re-upload options for existing files
        existing_files = [f for f in file_status["file_details"] if f["exists"]]
        if existing_files:
            keyboard_rows.append([
                InlineKeyboardButton(
                    text="🔄 Re-upload Files",
                    callback_data=f"unified_reupload_files:{product_id}"
                )
            ])

        # Add management buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔍 Check Integrity",
                callback_data=f"unified_upload_product_detail:{product_id}"
            ),
            InlineKeyboardButton(
                text="📊 Product Details",
                callback_data=f"unified_product_detail:{product_id}"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Upload Files",
                callback_data="unified_upload_files"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error in handle_unified_upload_product_detail: {e}")
        await callback_query.answer("❌ Error loading product details.", show_alert=True)


@router.callback_query(F.data == "unified_upload_ensure_dirs")
async def handle_unified_upload_ensure_dirs(callback_query: CallbackQuery, state: FSMContext):
    """Ensure uploads directory structure exists."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access denied.", show_alert=True)
        return

    try:
        await callback_query.answer()

        # Ensure directories
        success = FileBasedProductManager.ensure_uploads_directory()

        if success:
            await callback_query.answer("✅ Uploads directory structure ensured!", show_alert=True)
        else:
            await callback_query.answer("❌ Error ensuring directory structure.", show_alert=True)

        # Refresh the upload files interface
        await handle_unified_upload_files(callback_query, state)

    except Exception as e:
        logger.error(f"Error in handle_unified_upload_ensure_dirs: {e}")
        await callback_query.answer("❌ Error ensuring directories.", show_alert=True)


@router.callback_query(F.data.startswith("unified_reupload_files:"))
async def handle_unified_reupload_files(callback_query: CallbackQuery, state: FSMContext):
    """Handle re-upload files for a specific product - show existing files for re-upload selection."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access denied.", show_alert=True)
        return

    try:
        await callback_query.answer()

        # Parse callback data: unified_reupload_files:product_id
        parts = callback_query.data.split(":", 1)
        if len(parts) != 2:
            await callback_query.answer("❌ Invalid re-upload request.", show_alert=True)
            return

        product_id = parts[1]

        # Validate product_id format
        if not product_id or len(product_id) < 10:
            await callback_query.answer("❌ Invalid product ID.", show_alert=True)
            return

        # Get product to verify it exists
        from database.operations import get_product
        product = get_product(product_id)
        if not product:
            await callback_query.answer("❌ Product not found.", show_alert=True)
            return

        product_name = product.get("name") or "Unknown Product"

        # Get file status for this product
        file_status = FileBasedProductManager.check_product_file_integrity(product_id)

        if not file_status.get("success"):
            logger.error(f"File integrity check failed for product {product_id}: {file_status.get('error', 'Unknown error')}")
            await callback_query.answer("❌ Error checking product files.", show_alert=True)
            return

        # Check if product has files
        if not file_status.get("has_files", False):
            logger.info(f"Product {product_id} does not support file uploads")
            await callback_query.answer("❌ This product does not support file uploads.", show_alert=True)
            return

        # Get file details from the correct location in the response
        if "file_status" in file_status and "file_details" in file_status["file_status"]:
            file_details = file_status["file_status"]["file_details"]
        elif "file_details" in file_status:
            file_details = file_status["file_details"]
        else:
            logger.error(f"Could not find file_details in response for product {product_id}. Response keys: {list(file_status.keys())}")
            await callback_query.answer("❌ Could not retrieve file information.", show_alert=True)
            return

        # Validate file_details structure
        if not isinstance(file_details, list):
            logger.error(f"file_details is not a list for product {product_id}: {type(file_details)}")
            await callback_query.answer("❌ Invalid file information structure.", show_alert=True)
            return

        # Filter for existing files only
        existing_files = []
        try:
            existing_files = [f for f in file_details if f.get("exists", False)]
        except Exception as e:
            logger.error(f"Error filtering existing files for product {product_id}: {e}")
            await callback_query.answer("❌ Error processing file information.", show_alert=True)
            return

        if not existing_files:
            logger.info(f"No existing files found for re-upload for product {product_id}")
            await callback_query.answer("❌ No existing files found to re-upload.", show_alert=True)
            return

        # Build message
        message_text = f"🔄 <b>Re-upload Files</b>\n\n"
        message_text += f"📦 <b>Product:</b> {product_name}\n\n"
        message_text += f"📁 <b>Existing Files ({len(existing_files)}):</b>\n"

        # Build keyboard with re-upload options for each existing file
        keyboard_rows = []

        for file_detail in existing_files:
            file_type = file_detail["type"]
            description = file_detail["description"]
            size_mb = file_detail["size_mb"]

            message_text += f"• {description}: {size_mb} MB\n"

            # Add individual re-upload button
            button_text = f"🔄 Re-upload {description}"
            callback_data = f"unified_upload_file:{product_id}:{file_type}"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=callback_data
                )
            ])

        # Add bulk re-upload option if multiple files
        if len(existing_files) > 1:
            keyboard_rows.append([
                InlineKeyboardButton(
                    text=f"🔄 Re-upload All Files ({len(existing_files)})",
                    callback_data=f"unified_reupload_all:{product_id}"
                )
            ])

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Product Details",
                callback_data=f"unified_upload_product_detail:{product_id}"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error in handle_unified_reupload_files: {e}")
        await callback_query.answer("❌ Error loading re-upload interface.", show_alert=True)


@router.callback_query(F.data.startswith("unified_reupload_all:"))
async def handle_unified_reupload_all(callback_query: CallbackQuery, state: FSMContext):
    """Handle bulk re-upload of all files for a specific product."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access denied.", show_alert=True)
        return

    try:
        await callback_query.answer()

        # Parse callback data: unified_reupload_all:product_id
        parts = callback_query.data.split(":", 1)
        if len(parts) != 2:
            await callback_query.answer("❌ Invalid bulk re-upload request.", show_alert=True)
            return

        product_id = parts[1]

        # Validate product_id format
        if not product_id or len(product_id) < 10:
            await callback_query.answer("❌ Invalid product ID.", show_alert=True)
            return

        # Get product to verify it exists
        from database.operations import get_product
        product = get_product(product_id)
        if not product:
            await callback_query.answer("❌ Product not found.", show_alert=True)
            return

        product_name = product.get("name") or "Unknown Product"

        # Verify product has file-based content
        file_info = FileBasedProductManager._get_product_file_info(product)
        if not file_info.get("has_files"):
            await callback_query.answer("❌ This product does not support file uploads.", show_alert=True)
            return

        # Store upload context in state for bulk re-upload
        await state.update_data(
            product_id=product_id,
            product_name=product_name,
            upload_context="bulk_reupload"
        )
        await state.set_state(UnifiedUploadStates.waiting_for_file_upload)

        # Build message
        message_text = f"🔄 <b>Bulk Re-upload Files</b>\n\n"
        message_text += f"📦 <b>Product:</b> {product_name}\n\n"
        message_text += "📎 Please send the files you want to re-upload.\n"
        message_text += "📋 You can send multiple files one by one.\n"
        message_text += "📄 Supported formats: Documents, Images, Archives\n"
        message_text += "📏 Maximum size: 50MB per file\n\n"
        message_text += "⚠️ <i>Files will be automatically categorized based on their type.</i>\n"
        message_text += "💡 <i>Send files one at a time for best results.</i>"

        # Build keyboard
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="✅ Finish Upload",
                    callback_data=f"unified_upload_product_detail:{product_id}"
                )
            ],
            [
                InlineKeyboardButton(
                    text="❌ Cancel Upload",
                    callback_data=f"unified_reupload_files:{product_id}"
                )
            ]
        ])

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error in handle_unified_reupload_all: {e}")
        await callback_query.answer("❌ Error initiating bulk re-upload.", show_alert=True)


@router.callback_query(F.data.startswith("unified_upload_file:"))
async def handle_unified_upload_file(callback_query: CallbackQuery, state: FSMContext):
    """Initiate file upload for a specific product file type."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access denied.", show_alert=True)
        return

    try:
        await callback_query.answer()

        # Parse callback data: unified_upload_file:product_id:file_type
        parts = callback_query.data.split(":", 2)
        if len(parts) != 3:
            await callback_query.answer("❌ Invalid upload request.", show_alert=True)
            return

        product_id = parts[1]
        file_type = parts[2]

        # Get product to verify it exists
        from database.operations import get_product
        product = get_product(product_id)
        if not product:
            await callback_query.answer("❌ Product not found.", show_alert=True)
            return

        product_name = product.get("name") or "Unknown Product"

        # Determine file type description
        file_type_descriptions = {
            "product_file": "Product File",
            "inventory_file": "Inventory File",
            "exclusive_file": "Exclusive File",
            "image_file": "Product Image"
        }

        file_description = file_type_descriptions.get(file_type, "File")

        # Store upload context in state
        await state.update_data(
            product_id=product_id,
            file_type=file_type,
            product_name=product_name,
            file_description=file_description,
            upload_context="single_file"
        )
        await state.set_state(UnifiedUploadStates.waiting_for_file_upload)

        # Build message
        message_text = f"📤 <b>Upload {file_description}</b>\n\n"
        message_text += f"📦 <b>Product:</b> {product_name}\n"
        message_text += f"📁 <b>File Type:</b> {file_description}\n\n"

        if file_type == "image_file":
            message_text += "📷 Please send an image file (JPG, PNG, GIF, WebP)\n"
            message_text += "Maximum size: 50MB"
        elif file_type == "inventory_file":
            message_text += "📄 Please send a text file (.txt) containing inventory data\n"
            message_text += "Each line should contain one inventory item\n"
            message_text += "Maximum size: 50MB"
        else:
            message_text += "📎 Please send the file for this product\n"
            message_text += "Supported formats: PDF, TXT, DOC, DOCX, ZIP, RAR, 7Z, images\n"
            message_text += "Maximum size: 50MB"

        # Build keyboard
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="❌ Cancel Upload",
                    callback_data=f"unified_upload_product_detail:{product_id}"
                )
            ]
        ])

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error in handle_unified_upload_file: {e}")
        await callback_query.answer("❌ Error initiating upload.", show_alert=True)


@router.message(F.document | F.photo, UnifiedUploadStates.waiting_for_file_upload)
async def process_unified_file_upload(message: Message, state: FSMContext):
    """Process uploaded file for unified file management."""
    user_id = message.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply("🚫 <b>Access Denied.</b>", parse_mode="HTML")
        await state.clear()
        return

    try:
        # Get upload context from state
        data = await state.get_data()
        product_id = data.get("product_id")
        file_type = data.get("file_type")
        product_name = data.get("product_name", "Unknown Product")
        file_description = data.get("file_description", "File")
        upload_context = data.get("upload_context", "single_file")

        if not product_id:
            await message.reply("❌ Upload context lost. Please try again.")
            await state.clear()
            return

        # Handle bulk re-upload context - auto-detect file type
        if upload_context == "bulk_reupload" and not file_type:
            file_type = _auto_detect_file_type(message)
            if not file_type:
                await message.reply("❌ Could not determine file type. Please use individual file upload instead.")
                return

            # Validate file size for bulk upload
            file_size = 0
            if message.document:
                file_size = message.document.file_size or 0
            elif message.photo:
                # Get the largest photo size
                file_size = max([photo.file_size for photo in message.photo if photo.file_size], default=0)

            # Check file size limit (50MB)
            max_size = 50 * 1024 * 1024  # 50MB in bytes
            if file_size > max_size:
                await message.reply(f"❌ File too large ({file_size / (1024*1024):.1f}MB). Maximum size is 50MB.")
                return

            # Update file description for bulk upload
            file_type_descriptions = {
                "product_file": "Product File",
                "inventory_file": "Inventory File",
                "exclusive_file": "Exclusive File",
                "image_file": "Product Image"
            }
            file_description = file_type_descriptions.get(file_type, "File")

        elif not file_type:
            await message.reply("❌ Upload context lost. Please try again.")
            await state.clear()
            return

        # Process the uploaded file
        upload_result = await _process_uploaded_file(message, product_id, file_type, product_name)

        if upload_result["success"]:
            # Update product in database with new file path and metadata
            update_success = await _update_product_file_path(
                product_id,
                file_type,
                upload_result["relative_path"],
                file_metadata=upload_result
            )

            if update_success:
                success_message = f"✅ <b>{file_description} uploaded successfully!</b>\n\n"
                success_message += f"📦 <b>Product:</b> {product_name}\n"
                success_message += f"📁 <b>File:</b> {upload_result['original_filename']}\n"
                success_message += f"📊 <b>Size:</b> {upload_result['size_mb']} MB\n"
                success_message += f"📍 <b>Path:</b> {upload_result['relative_path']}"

                # Handle different upload contexts
                if upload_context == "bulk_reupload":
                    # For bulk re-upload, don't clear state and provide continue options
                    success_message += "\n\n💡 <i>You can upload more files or finish the upload.</i>"

                    keyboard = InlineKeyboardMarkup(inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="✅ Finish Upload",
                                callback_data=f"unified_upload_product_detail:{product_id}"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🔄 Re-upload More Files",
                                callback_data=f"unified_reupload_files:{product_id}"
                            )
                        ]
                    ])

                    await message.reply(success_message, reply_markup=keyboard, parse_mode="HTML")
                    # Don't clear state for bulk upload
                else:
                    # For single file upload, provide standard options
                    keyboard = InlineKeyboardMarkup(inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="📋 View Product Details",
                                callback_data=f"unified_upload_product_detail:{product_id}"
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="📁 Upload Files Menu",
                                callback_data="unified_upload_files"
                            )
                        ]
                    ])

                    await message.reply(success_message, reply_markup=keyboard, parse_mode="HTML")
                    await state.clear()
            else:
                await message.reply(f"⚠️ File uploaded but database update failed. Please check product settings.")
                if upload_context != "bulk_reupload":
                    await state.clear()
        else:
            await message.reply(f"❌ Upload failed: {upload_result.get('error', 'Unknown error')}")
            if upload_context != "bulk_reupload":
                await state.clear()

    except Exception as e:
        logger.error(f"Error in process_unified_file_upload: {e}")
        await message.reply("❌ Error processing file upload.")
        await state.clear()


async def _process_uploaded_file(message: Message, product_id: str, file_type: str, product_name: str) -> Dict[str, Any]:
    """
    Process an uploaded file and store it in the appropriate location.
    Uses the unified file processing system for consistent behavior.

    Returns:
        Dict with success status and file information
    """
    try:
        # Import the unified file processing function
        from utils.local_file_handling import process_file_upload

        # Determine target folder based on file type
        folder_mapping = {
            "product_file": "files",
            "inventory_file": "inventory",
            "exclusive_file": "exclusive_files",
            "image_file": "product_images"
        }

        target_folder = folder_mapping.get(file_type, "files")

        if message.document:
            # Handle document upload
            document = message.document
            original_filename = document.file_name or f"file_{product_id}"
            file_size = document.file_size
            mime_type = document.mime_type or "application/octet-stream"

            # Download file to temporary location first
            file_info = await message.bot.get_file(document.file_id)

            # Create temporary file path
            import tempfile
            temp_dir = Path("uploads/temp")
            temp_dir.mkdir(parents=True, exist_ok=True)
            temp_file_path = temp_dir / f"temp_{document.file_id}"

            # Download file to temporary location
            await message.bot.download_file(file_info.file_path, temp_file_path)

            # Process file using unified system
            result = await process_file_upload(
                source_path=str(temp_file_path),
                original_filename=original_filename,
                folder=target_folder,
                product_id=product_id,
                file_size=file_size,
                mime_type=mime_type
            )

            # Clean up temporary file
            if temp_file_path.exists():
                temp_file_path.unlink()

            if not result["success"]:
                return result

        elif message.photo:
            # Handle photo upload
            photo = message.photo[-1]  # Get largest photo
            file_size = photo.file_size
            original_filename = f"product_{product_id}_image.jpg"
            mime_type = "image/jpeg"

            # Download file to temporary location first
            file_info = await message.bot.get_file(photo.file_id)

            # Create temporary file path
            import tempfile
            temp_dir = Path("uploads/temp")
            temp_dir.mkdir(parents=True, exist_ok=True)
            temp_file_path = temp_dir / f"temp_{photo.file_id}.jpg"

            # Download file to temporary location
            await message.bot.download_file(file_info.file_path, temp_file_path)

            # Process file using unified system
            result = await process_file_upload(
                source_path=str(temp_file_path),
                original_filename=original_filename,
                folder=target_folder,
                product_id=product_id,
                file_size=file_size,
                mime_type=mime_type
            )

            # Clean up temporary file
            if temp_file_path.exists():
                temp_file_path.unlink()

            if not result["success"]:
                return result

        else:
            return {
                "success": False,
                "error": "Unsupported file type"
            }

        # Validate the generated path for security
        path_validation = unified_validation.validate_file_path(result["relative_path"], "uploads")
        if not path_validation["valid"]:
            logger.error(f"Generated path failed validation: {path_validation['error']}")
            return {
                "success": False,
                "error": "Generated file path is invalid",
                "security_issue": path_validation.get("security_issue")
            }

        return {
            "success": True,
            "absolute_path": result["absolute_path"],
            "relative_path": result["relative_path"],
            "original_filename": result["original_filename"],
            "sanitized_filename": result["sanitized_filename"],
            "file_size": result["file_size"],
            "mime_type": result["mime_type"],
            "size_mb": round(result["file_size"] / (1024 * 1024), 2) if result["file_size"] else 0
        }

    except Exception as e:
        logger.error(f"Error processing uploaded file: {e}")
        return {
            "success": False,
            "error": str(e)
        }


async def _update_product_file_path(product_id: str, file_type: str, file_path: str, file_metadata: Dict[str, Any] = None) -> bool:
    """
    Update the product database with the new file path and consistent metadata.

    Args:
        product_id: Product ID to update
        file_type: Type of file being updated
        file_path: Relative file path
        file_metadata: Additional file metadata from unified processing

    Returns:
        True if update was successful, False otherwise
    """
    try:
        from database.operations import update_product

        # Determine which field to update based on file type
        field_mapping = {
            "product_file": "file_path",
            "inventory_file": "inventory_file_path",
            "exclusive_file": "exclusive_file_path",
            "image_file": "image_url"
        }

        field_name = field_mapping.get(file_type)
        if not field_name:
            logger.error(f"Unknown file type: {file_type}")
            return False

        # Start with the basic file path update
        update_data = {field_name: file_path}

        # Add consistent metadata if provided
        if file_metadata:
            # Store original filename in a consistent field
            if "original_filename" in file_metadata:
                original_filename_field = f"{file_type}_original_filename"
                update_data[original_filename_field] = file_metadata["original_filename"]

            # Store file size in a consistent field
            if "file_size" in file_metadata:
                file_size_field = f"{file_type}_size"
                update_data[file_size_field] = file_metadata["file_size"]

            # Store MIME type in a consistent field
            if "mime_type" in file_metadata:
                mime_type_field = f"{file_type}_mime_type"
                update_data[mime_type_field] = file_metadata["mime_type"]

        # For inventory files, we might also need to update line counts
        if file_type == "inventory_file":
            # Count lines in the file to update inventory stats
            try:
                from pathlib import Path
                full_path = Path("uploads") / file_path
                if full_path.exists():
                    with open(full_path, 'r', encoding='utf-8') as f:
                        line_count = sum(1 for line in f if line.strip())

                    update_data.update({
                        "total_lines": line_count,
                        "available_lines": line_count,
                        "reserved_lines": 0
                    })
            except Exception as e:
                logger.warning(f"Could not count lines in inventory file: {e}")

        result = update_product(product_id, update_data)

        if result:
            logger.info(f"Successfully updated product {product_id} with file metadata: {update_data}")

        return result is not None

    except Exception as e:
        logger.error(f"Error updating product file path: {e}")
        return False


@router.callback_query(F.data == "unified_upload_files_regular")
async def handle_unified_upload_files_regular(callback_query: CallbackQuery, state: FSMContext):
    """Show only regular products with file uploads."""
    await _handle_upload_files_by_type(callback_query, "regular", "📄 Regular Products with Files")


@router.callback_query(F.data == "unified_upload_files_line_based")
async def handle_unified_upload_files_line_based(callback_query: CallbackQuery, state: FSMContext):
    """Show only line-based products with file uploads."""
    await _handle_upload_files_by_type(callback_query, "line-based", "📦 Line-Based Products with Files")


@router.callback_query(F.data == "unified_upload_files_exclusive")
async def handle_unified_upload_files_exclusive(callback_query: CallbackQuery, state: FSMContext):
    """Show only exclusive products with file uploads."""
    await _handle_upload_files_by_type(callback_query, "exclusive", "🔐 Exclusive Products with Files")


async def _handle_upload_files_by_type(callback_query: CallbackQuery, product_type: str, title: str):
    """Helper function to handle product type filtering for upload files."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access denied.", show_alert=True)
        return

    try:
        await callback_query.answer()

        # Get file-based products and filter by type
        file_based_products = FileBasedProductManager.get_file_based_products()
        filtered_products = [p for p in file_based_products if p["type"] == product_type]

        if not filtered_products:
            await callback_query.answer(f"📝 No {product_type} products with files found.", show_alert=True)
            return

        # Build message
        message_text = f"{title} ({len(filtered_products)})\n\n"

        # Build keyboard with products
        keyboard_rows = []

        for product in filtered_products[:20]:  # Limit to 20 products
            product_id = product["product_id"]
            product_name = product["name"]
            file_status = product["file_status"]

            # Determine status emoji and text
            if file_status["all_files_present"]:
                status_emoji = "✅"
                status_text = "Complete"
            else:
                status_emoji = "❌"
                status_text = f"Missing {file_status['missing_files']}"

            # Truncate long names
            display_name = product_name[:20] + "..." if len(product_name) > 20 else product_name
            button_text = f"{status_emoji} {display_name} ({status_text})"

            keyboard_rows.append([
                InlineKeyboardButton(
                    text=button_text,
                    callback_data=f"unified_upload_product_detail:{product_id}"
                )
            ])

        if len(filtered_products) > 20:
            message_text += f"<i>Showing first 20 of {len(filtered_products)} products</i>\n\n"

        # Add navigation buttons
        keyboard_rows.append([
            InlineKeyboardButton(
                text="📋 All Products",
                callback_data="unified_upload_files_all"
            ),
            InlineKeyboardButton(
                text="🔴 Missing Files Only",
                callback_data="unified_upload_files_missing"
            )
        ])

        keyboard_rows.append([
            InlineKeyboardButton(
                text="🔙 Back to Upload Files",
                callback_data="unified_upload_files"
            )
        ])

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_rows)

        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML"
        )

    except Exception as e:
        logger.error(f"Error in _handle_upload_files_by_type ({product_type}): {e}")
        await callback_query.answer("❌ Error loading products.", show_alert=True)


def _auto_detect_file_type(message: Message) -> Optional[str]:
    """
    Auto-detect file type based on the uploaded file's characteristics.

    Args:
        message: Telegram message containing the file

    Returns:
        Detected file type or None if cannot determine
    """
    try:
        # Validate message has file content
        if not message.photo and not message.document:
            logger.warning("Auto-detect called on message without file content")
            return None

        if message.photo:
            return "image_file"

        if message.document:
            file_name = message.document.file_name or ""
            mime_type = message.document.mime_type or ""
            file_size = message.document.file_size or 0

            # Validate file size (50MB limit)
            max_size = 50 * 1024 * 1024  # 50MB
            if file_size > max_size:
                logger.warning(f"File too large for auto-detection: {file_size} bytes")
                return None

            # Check for image files
            if (mime_type.startswith("image/") or
                file_name.lower().endswith(('.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'))):
                return "image_file"

            # Check for inventory files (text files)
            if (mime_type.startswith("text/") or
                file_name.lower().endswith(('.txt', '.csv'))):
                return "inventory_file"

            # Check for exclusive files (archives and documents)
            if (mime_type in ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'] or
                file_name.lower().endswith(('.zip', '.rar', '.7z', '.tar', '.gz'))):
                return "exclusive_file"

            # Default to product file for other document types
            if (mime_type.startswith("application/") or
                file_name.lower().endswith(('.pdf', '.doc', '.docx', '.xls', '.xlsx'))):
                return "product_file"

            # Log unrecognized file types for debugging
            logger.info(f"Unrecognized file type - Name: {file_name}, MIME: {mime_type}")

        # Default fallback
        return "product_file"

    except Exception as e:
        logger.error(f"Error auto-detecting file type: {e}")
        return None


def register_unified_product_admin_handlers(dp):
    """Register all unified product admin handlers."""
    dp.include_router(router)
