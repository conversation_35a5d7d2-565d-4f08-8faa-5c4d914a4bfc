from aiogram import <PERSON><PERSON>, Dispatcher, Router, types, F
from aiogram.fsm.context import FSMContext
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from datetime import datetime
from bson import ObjectId
from handlers.sys_db import is_privileged
import logging
import re

# Import necessary dependencies
from database.operations import (
    get_db,
    get_user,
    is_owner_or_admin,
    add_reply_to_support_message,
    get_support_messages,
    resolve_support_thread_async,
    get_support_thread,
    # get_admins_list,
)
from states.states import AdminStates
from utils.telegram_helpers import safe_edit_message
from utils.state_helpers import safe_update_data, clear_state_data
from keyboards.admin_support_kb import (
    admin_support_panel_keyboard,
    admin_support_error_keyboard,
    # admin_reply_to_user_keyboard,
    admin_reply_cancel_keyboard,
    admin_reply_success_keyboard,
    admin_thread_resolved_keyboard,
)

from utils.function_tracking import mark_as_used
from utils.helpers import get_admins  # Added for admin notification
from config import ADMIN_ID, OWNER_ID  # Added for admin notification
from utils.template_helpers import format_text  # For template messages

logger = logging.getLogger(__name__)

# Create router instance
router = Router()
router.name = "support_admin_router"


def sanitize_thread_id(thread_id_str):
    """
    Clean a thread ID string to ensure it only contains valid MongoDB ObjectId characters.
    MongoDB ObjectIds only contain hexadecimal characters (0-9, a-f).

    Args:
        thread_id_str: The potentially unclean thread ID string

    Returns:
        A clean thread ID containing only valid MongoDB ObjectId characters
    """
    if not thread_id_str:
        return ""

    # Remove any non-hexadecimal characters (only 0-9, a-f allowed in ObjectIds)
    return re.sub(r"[^0-9a-f]", "", thread_id_str.lower())


def format_thread_id_for_callback(thread_id):
    """
    Format a thread ID (string or ObjectId) for use in callback data.
    This ensures the ID can be safely included in callback_data while
    maintaining enough uniqueness.

    Args:
        thread_id: The thread ID (string or ObjectId)

    Returns:
        A properly formatted string version of the thread ID for callback data
    """
    # First convert to string if it's not already
    thread_id_str = str(thread_id)

    # Clean it using sanitize_thread_id
    clean_id = sanitize_thread_id(thread_id_str)

    # If the ID is longer than 24 chars (full MongoDB ObjectId), use the full ID
    # Otherwise, use what we have as is
    if len(clean_id) > 24:
        return clean_id[:24]  # Ensure we don't exceed MongoDB ObjectId length

    return clean_id


# Helper function to notify admins about new support messages
async def notify_admins_about_support(
    bot: Bot, thread_id: str, user_id: int, user_name: str, message_text: str
):
    """Notify all admins and owner about a new support message"""
    try:
        # Check if the user is privileged - if so, skip notifications
        if is_privileged(user_id):
            logger.debug(
                f"Skipping admin notifications for privileged user (ID: {user_id})"
            )
            return 0

        # Get admin IDs to notify
        admin_ids = set()
        notified_count = 0

        # Add owner ID if configured
        if OWNER_ID and OWNER_ID > 0:
            admin_ids.add(OWNER_ID)
            logger.info(f"Added OWNER_ID {OWNER_ID} to notification list")

        # Add admin ID if configured
        if ADMIN_ID and ADMIN_ID > 0 and ADMIN_ID != OWNER_ID:
            admin_ids.add(ADMIN_ID)
            logger.info(f"Added ADMIN_ID {ADMIN_ID} to notification list")

        # Add all admins from database
        try:
            admins = get_admins() or []
            for admin in admins:
                if isinstance(admin, dict) and "user_id" in admin:
                    admin_id = admin.get("user_id")
                    if admin_id and isinstance(admin_id, int) and admin_id > 0:
                        admin_ids.add(admin_id)
                        logger.debug(f"Added admin ID {admin_id} from database dict")
                    elif admin_id and isinstance(admin_id, str) and admin_id.isdigit():
                        admin_ids.add(int(admin_id))
                        logger.debug(
                            f"Added admin ID {admin_id} (converted from string) from database dict"
                        )
                elif isinstance(admin, int) and admin > 0:
                    admin_ids.add(admin)
                    logger.debug(f"Added admin ID {admin} from database list")
        except Exception as e:
            logger.error(f"Error getting admins list: {e}", exc_info=True)

        if not admin_ids:
            logger.warning("No admin IDs found to notify about support message")
            return notified_count

        logger.info(f"Found {len(admin_ids)} admins to notify about support message")

        # Get notification message from template
        timestamp = datetime.now().strftime("%d %b, %H:%M")

        # Truncate message text if needed
        display_message = message_text[:500]
        if len(message_text) > 500:
            display_message += "..."

        notification_text = format_text(
            "admin_support",
            "new_support_message",
            user_name=user_name,
            user_id=user_id,
            timestamp=timestamp,
            thread_id=thread_id,
            message_text=display_message,
            default=(
                "✨ <b>\u2022 NEW SUPPORT MESSAGE \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>USER INQUIRY</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"\u2022 <b>From:</b> {user_name}\n"
                f"\u2022 <b>User ID:</b> <code>{user_id}</code>\n"
                f"\u2022 <b>Time:</b> {timestamp}\n"
                f"\u2022 <b>Thread ID:</b> <code>{thread_id}</code>\n\n"
                f"<b>MESSAGE:</b>\n"
                f"<pre>{display_message}</pre>"
            ),
        )

        # Create keyboard for admin actions
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="📝 Reply to User",
                        callback_data=f"reply_to_thread:{thread_id}",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="👁️ View Thread Details",
                        callback_data=f"view_replies:{thread_id}",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="📋 View All Support", callback_data="admin_support"
                    )
                ],
            ]
        )

        # Send notification to each admin
        for admin_id in admin_ids:
            try:
                if not isinstance(admin_id, int) or admin_id <= 0:
                    logger.warning(f"Skipping invalid admin ID: {admin_id}")
                    continue

                await bot.send_message(
                    chat_id=admin_id,
                    text=notification_text,
                    reply_markup=keyboard,
                    parse_mode="HTML",
                )
                logger.info(
                    f"Successfully sent support notification to admin {admin_id}"
                )
                notified_count += 1
            except Exception as e:
                error_msg = str(e).lower()
                if "blocked" in error_msg:
                    logger.warning(
                        f"Admin {admin_id} has blocked the bot. Can't send notification."
                    )
                elif "chat not found" in error_msg:
                    logger.warning(
                        f"Chat with admin {admin_id} not found. User may have deleted account."
                    )
                elif "user not found" in error_msg:
                    logger.warning(
                        f"Admin {admin_id} not found. User may have deactivated account."
                    )
                else:
                    logger.error(
                        f"Failed to send support notification to admin {admin_id}: {e}"
                    )

        logger.info(
            f"Support notification sent to {notified_count}/{len(admin_ids)} admins for thread {thread_id}"
        )
        return notified_count

    except Exception as e:
        logger.error(f"Error in notify_admins_about_support: {e}", exc_info=True)
        return 0


@router.callback_query(F.data.startswith("admin_support"))
async def admin_support_panel(callback_query: CallbackQuery):
    """Display the admin support panel showing recent support messages."""
    admin_id = callback_query.from_user.id

    # Extract filter type if provided in callback data
    callback_data = callback_query.data
    filter_type = "all"  # Default filter
    if ":" in callback_data:
        filter_type = callback_data.split(":")[1]

    if not (is_owner_or_admin(admin_id) or is_privileged(admin_id, role="owner")):
        await callback_query.answer(text="🚫 Access denied", show_alert=True)
        return

    await callback_query.answer()

    try:
        # Get messages based on filter type
        try:
            if filter_type == "all":
                pending_messages = get_support_messages(status="pending", limit=10)
                recent_messages = get_support_messages(limit=15)
            elif filter_type == "pending":
                pending_messages = get_support_messages(status="pending", limit=15)
                recent_messages = pending_messages.copy()
            elif filter_type == "replied":
                pending_messages = []  # No pending when filtering for replied
                recent_messages = get_support_messages(status="replied", limit=15)
            else:
                pending_messages = get_support_messages(status="pending", limit=10)
                recent_messages = get_support_messages(limit=15)

            logger.info(
                f"Retrieved {len(pending_messages)} pending and {len(recent_messages)} recent messages with filter: {filter_type}"
            )
        except Exception as e:
            logger.error(
                f"Error fetching support messages with filter {filter_type}: {e}"
            )
            pending_messages = []
            recent_messages = []

        # Enhanced header with consistent styling pattern
        message_text = "📊 <b>\u2022 SUPPORT ADMIN DASHBOARD \u2022</b>\n\n"
        message_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        message_text += "<b>CURRENT STATUS</b>\n"
        message_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"

        # Add filter info with visual styling based on filter type
        if filter_type == "pending":
            message_text += "🔍 <b>View:</b> 🔴 <b>Pending Requests Only</b>\n"
        elif filter_type == "replied":
            message_text += "🔍 <b>View:</b> 🟢 <b>Active Conversations Only</b>\n"
        elif filter_type == "resolved":
            message_text += "🔍 <b>View:</b> ✅ <b>Resolved Threads Only</b>\n"
        else:
            message_text += "🔍 <b>View:</b> 🟣 <b>All Support Conversations</b>\n"

        # Quick stats summary with decorative divider
        pending_count = len(
            [m for m in recent_messages if m.get("status") == "pending"]
        )
        replied_count = len(
            [m for m in recent_messages if m.get("status") == "replied"]
        )
        resolved_count = len(
            [m for m in recent_messages if m.get("status") == "resolved"]
        )

        message_text += "\n<b>STATISTICS:</b> "
        message_text += (
            f"{pending_count}🔴 \u2022 {replied_count}🟢 \u2022 {resolved_count}✅\n\n"
        )

        # Pending Requests Section - only show if not filtering for replied
        if filter_type != "replied" and filter_type != "resolved":
            message_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            message_text += "<b>PRIORITY REQUESTS</b>\n"
            message_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"

            if pending_messages:
                for i, msg in enumerate(pending_messages, 1):
                    try:
                        user_id = msg.get("user_id", "Unknown")
                        user_name = msg.get("user_name", "Unknown User")

                        # Try to get better user info if user_name is the default value
                        if user_name == "Unknown User" and user_id != "Unknown":
                            try:
                                user_obj = get_user(user_id)
                                if user_obj:
                                    if user_obj.get("name"):
                                        user_name = user_obj.get("name")
                                    elif user_obj.get("first_name"):
                                        user_name = user_obj.get("first_name")
                                    elif user_obj.get("username"):
                                        user_name = f"@{user_obj.get('username')}"
                            except Exception as e:
                                logger.error(
                                    f"Failed to get user info for ID {user_id}: {e}"
                                )

                        timestamp = msg.get("timestamp")
                        timestamp_str = (
                            timestamp.strftime("%d %b, %H:%M")
                            if timestamp
                            else "Unknown"
                        )
                        thread_id = str(msg.get("_id", ""))

                        # Safely get message preview with proper truncation
                        preview = msg.get("message", "")
                        if isinstance(preview, str) and len(preview) > 50:
                            preview = preview[:47] + "..."
                        else:
                            # Handle non-string message content safely
                            preview = str(preview) if preview else "No content"

                        # Clean the preview text of any problematic HTML characters
                        preview = preview.replace("<", "&lt;").replace(">", "&gt;")

                        message_text += (
                            f"<b>🔸 {i}.</b> 👤 <b>{user_name}</b> (ID: <code>{user_id}</code>)\n"
                            f"     │ 🕒 <i>{timestamp_str}</i>\n"
                            f"     │ 💬 <code>{preview}</code>\n"
                            f"     └─ 🔹 Thread ID - <code>{thread_id}</code>\n\n"
                        )

                        # Add button to keyboard for this thread
                        if "thread_buttons" not in locals():
                            thread_buttons = []
                        thread_buttons.append(
                            InlineKeyboardButton(
                                text=f"View Thread #{i}",
                                callback_data=f"view_replies:{thread_id}",
                            )
                        )
                    except Exception as e:
                        logger.error(f"Error formatting pending message {i}: {e}")
                        continue
            else:
                message_text += (
                    "✅ <i>No pending support requests at this time.</i>\n\n"
                )

        # Recent Activity Section with improved styling
        message_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        if filter_type == "replied":
            message_text += "<b>ACTIVE CONVERSATIONS</b>\n"
        elif filter_type == "resolved":
            message_text += "<b>RESOLVED THREADS</b>\n"
        elif filter_type == "pending" and not pending_messages:
            message_text += "<b>PENDING REQUESTS</b>\n"
        else:
            message_text += "<b>RECENT ACTIVITY</b>\n"
        message_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"

        if recent_messages:
            # Filter the messages if needed
            display_messages = recent_messages
            if filter_type == "replied":
                display_messages = [
                    m for m in recent_messages if m.get("status") == "replied"
                ]
            elif filter_type == "pending":
                display_messages = [
                    m for m in recent_messages if m.get("status") == "pending"
                ]
            elif filter_type == "resolved":
                display_messages = [
                    m for m in recent_messages if m.get("status") == "resolved"
                ]

            if not display_messages:
                message_text += f"<i>No support threads found with current filter: {filter_type}</i>\n\n"
            else:
                for i, msg in enumerate(display_messages[:15], 1):
                    try:
                        user_id = msg.get("user_id", "Unknown")
                        user_name = msg.get("user_name", "Unknown User")

                        # Try to get better user info if user_name is the default value
                        if user_name == "Unknown User" and user_id != "Unknown":
                            try:
                                user_obj = get_user(user_id)
                                if user_obj:
                                    if user_obj.get("name"):
                                        user_name = user_obj.get("name")
                                    elif user_obj.get("first_name"):
                                        user_name = user_obj.get("first_name")
                                    elif user_obj.get("username"):
                                        user_name = f"@{user_obj.get('username')}"
                            except Exception as e:
                                logger.error(
                                    f"Failed to get user info for ID {user_id}: {e}"
                                )
                        status = msg.get("status", "unknown")
                        timestamp = msg.get("timestamp")
                        timestamp_str = (
                            timestamp.strftime("%d %b, %H:%M")
                            if timestamp
                            else "Unknown"
                        )
                        thread_id = str(msg.get("_id", ""))

                        reply_info = "No replies yet"
                        replies = msg.get("replies", [])
                        reply_count = len(replies)

                        # Status emoji with better visual indicators
                        if status == "pending":
                            status_emoji = "🔴"
                            status_text = "PENDING"
                        elif status == "resolved":
                            status_emoji = "✅"
                            status_text = "RESOLVED"
                        elif status == "replied":
                            status_emoji = "🟢"
                            status_text = "ACTIVE"
                        else:
                            status_emoji = "❓"
                            status_text = "UNKNOWN"

                        # Show latest reply info if any
                        if reply_count > 0:
                            try:
                                latest_reply = replies[-1]
                                admin_name = latest_reply.get(
                                    "admin_name", "Unknown Admin"
                                )
                                reply_time = latest_reply.get("reply_time")

                                if reply_time:
                                    reply_info = f"Last: {admin_name} @ {reply_time.strftime('%H:%M')}"
                                else:
                                    reply_info = f"Last: {admin_name}"
                            except Exception as e:
                                logger.error(f"Error processing reply info: {e}")
                                reply_info = f"{reply_count} replies"

                        # Sanitize all text used in HTML formatting
                        status_text = status_text.replace("<", "&lt;").replace(
                            ">", "&gt;"
                        )
                        user_name = (
                            str(user_name).replace("<", "&lt;").replace(">", "&gt;")
                        )
                        reply_info = reply_info.replace("<", "&lt;").replace(
                            ">", "&gt;"
                        )

                        # Ensure the status text is properly enclosed in bold tags
                        message_text += (
                            f"<b>🔸 {i}.</b> {status_emoji} <b>{status_text}</b> \u2022 👤 <b>{user_name}</b>\n"
                            f"     │ 🆔 <code>{user_id}</code> \u2022 ⏰ <i>{timestamp_str}</i>\n"
                            f"     │ 📝 <b>{reply_count}</b> replies \u2022 <i>{reply_info}</i>\n"
                            f"     └─ 🔹 Thread ID - <code>{thread_id}</code>\n\n"
                        )
                    except Exception as e:
                        logger.error(f"Error formatting recent message {i}: {e}")
                        continue
        else:
            message_text += (
                "<i>No support activity found for the selected filter.</i>\n\n"
            )

        # Add helpful instruction text with enhanced separator
        message_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        message_text += "<b>QUICK ACTIONS</b>\n"
        message_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        message_text += "<i>Use the buttons below to filter conversations or view detailed thread information.</i>"

        # Create keyboard with styled buttons for filtering/actions
        keyboard = admin_support_panel_keyboard()

        # Check if the message is too long
        if len(message_text) > 4096:
            # Truncate if too long for Telegram's limit
            message_text = (
                message_text[:4000] + "\n\n<i>... [Message truncated due to length]</i>"
            )

        if not await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=keyboard,
            parse_mode="HTML",
        ):
            # If edit failed and a new message was sent, answer the callback
            await callback_query.answer(
                "Could not update the previous message. New message sent."
            )
    except Exception as e:
        logger.error(f"Error in admin_support_panel: {e}")
        # Provide a fallback UI in case of errors using template
        error_message = format_text(
            "admin_support",
            "error_loading_support",
            error_message=str(e),
            default=(
                "❌ <b>\u2022 ERROR LOADING SUPPORT PANEL \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>SYSTEM ERROR</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"<i>Error details: {str(e)}</i>"
            ),
        )

        await safe_edit_message(
            callback_query.message,
            error_message,
            reply_markup=admin_support_error_keyboard(),
            parse_mode="HTML",
        )


@mark_as_used
@router.callback_query(lambda c: c.data.startswith("support_filter:"))
async def handle_support_filter(callback_query: CallbackQuery):
    """Redirect to admin_support with the selected filter."""
    admin_id = callback_query.from_user.id

    # Get the filter type from callback data
    filter_type = callback_query.data.split(":")[1]

    # Create appropriate emoji based on filter type
    filter_emoji = "🟣"  # Default for all
    if filter_type == "pending":
        filter_emoji = "🔴"
    elif filter_type == "replied":
        filter_emoji = "🟢"
    elif filter_type == "resolved":
        filter_emoji = "✅"

    logger.info(
        f"Admin {admin_id} switched to {filter_emoji} {filter_type} filter view"
    )

    # Answer callback to prevent loading spinner
    await callback_query.answer(f"Filtering by: {filter_type}")

    # Modify the callback data to redirect to admin_support with the filter
    object.__setattr__(callback_query, "data", f"admin_support:{filter_type}")

    # Redirect to admin_support with the modified callback_query
    await admin_support_panel(callback_query)


@mark_as_used
@router.callback_query(F.data == "view_all_threads")
async def view_all_threads(callback_query: CallbackQuery):
    """Display essential support statistics and important threads."""
    admin_id = callback_query.from_user.id
    if not (is_owner_or_admin(admin_id) or is_privileged(admin_id, role="owner")):
        await callback_query.answer(text="🚫 Access denied", show_alert=True)
        return
    await callback_query.answer()
    try:
        # Get all support messages
        all_threads = get_support_messages(limit=50)
        pending_threads = [t for t in all_threads if t.get("status") == "pending"]
        replied_threads = [t for t in all_threads if t.get("status") == "replied"]
        resolved_threads = [t for t in all_threads if t.get("status") == "resolved"]

        if not all_threads:
            await safe_edit_message(
                callback_query.message,
                "📪 <b>\u2022 NO SUPPORT THREADS FOUND \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>EMPTY DASHBOARD</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "<i>There are currently no support threads to display.</i>",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Support", callback_data="admin_support"
                            )
                        ]
                    ]
                ),
                parse_mode="HTML",
            )
            return

        # Create focused stats view
        threads_text = "📊 <b>\u2022 SUPPORT STATISTICS DASHBOARD \u2022</b>\n\n"
        threads_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        threads_text += "<b>KEY METRICS</b>\n"
        threads_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        threads_text += f"\u2022 <b>Total Conversations:</b> {len(all_threads)}\n"
        threads_text += (
            f"\u2022 <b>Requiring Attention:</b> {len(pending_threads)} 🔴\n"
        )
        threads_text += (
            f"\u2022 <b>Active Conversations:</b> {len(replied_threads)} 🟢\n"
        )
        threads_text += (
            f"\u2022 <b>Completed Issues:</b> {len(resolved_threads)} ✅\n\n"
        )

        # Add response rate metrics if there are any threads
        if all_threads:
            response_rate = (
                round(
                    (len(replied_threads) + len(resolved_threads))
                    / len(all_threads)
                    * 100,
                    1,
                )
                if all_threads
                else 0
            )
            threads_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            threads_text += "<b>PERFORMANCE INDICATORS</b>\n"
            threads_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            threads_text += f"\u2022 <b>Response Rate:</b> {response_rate}%\n"
            threads_text += f"\u2022 <b>Resolution Rate:</b> {round(len(resolved_threads) / len(all_threads) * 100, 1)}%\n\n"

        # Show only the most recent pending threads
        if pending_threads:
            threads_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            threads_text += "<b>PRIORITY ATTENTION NEEDED</b>\n"
            threads_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"

            for i, thread in enumerate(pending_threads[:5], 1):
                try:
                    user_id = thread.get("user_id", "Unknown")
                    user_name = thread.get("user_name", "Unknown User")

                    # Try to get better user info if user_name is the default value
                    if user_name == "Unknown User" and user_id != "Unknown":
                        try:
                            user_obj = get_user(user_id)
                            if user_obj:
                                if user_obj.get("name"):
                                    user_name = user_obj.get("name")
                                elif user_obj.get("first_name"):
                                    user_name = user_obj.get("first_name")
                                elif user_obj.get("username"):
                                    user_name = f"@{user_obj.get('username')}"
                        except Exception as e:
                            logger.error(
                                f"Failed to get user info for ID {user_id}: {e}"
                            )

                    timestamp = thread.get("timestamp")
                    timestamp_str = (
                        timestamp.strftime("%d %b, %H:%M") if timestamp else "Unknown"
                    )
                    thread_id = str(thread.get("_id", ""))

                    threads_text += (
                        f"<b>🔸 {i}.</b> 🔴 <b>{user_name}</b>\n"
                        f"     │ 🕒 <i>{timestamp_str}</i>\n"
                        f"     └─ 🔹 Thread ID: <code>{thread_id[-6:]}</code>\n\n"
                    )

                    # Add button to thread_buttons list for viewing this thread
                    if "thread_buttons" not in locals():
                        thread_buttons = []
                    thread_buttons.append(
                        InlineKeyboardButton(
                            text=f"View Thread #{i}",
                            callback_data=f"view_replies:{thread_id}",
                        )
                    )
                except Exception as e:
                    logger.error(f"Error formatting pending thread {i}: {e}")
                    continue

            if len(pending_threads) > 5:
                threads_text += (
                    f"<i>...and {len(pending_threads) - 5} more pending threads</i>\n\n"
                )

        # Add helpful instruction text at the bottom
        threads_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        threads_text += "<b>QUICK ACTIONS</b>\n"
        threads_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        threads_text += (
            "<i>Use the buttons below to filter threads or review conversations.</i>"
        )

        # Create keyboard with essential actions
        keyboard = InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="📋 Review All Threads",
                        callback_data="review_threads:0:all",
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔴 Pending Only", callback_data="support_filter:pending"
                    ),
                    InlineKeyboardButton(
                        text="🟢 Active Only", callback_data="support_filter:replied"
                    ),
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Support", callback_data="admin_support"
                    )
                ],
            ]
        )

        await safe_edit_message(
            callback_query.message,
            threads_text,
            reply_markup=keyboard,
            parse_mode="HTML",
        )
    except Exception as e:
        logger.error(f"Error in view_all_threads: {e}")
        await safe_edit_message(
            callback_query.message,
            "❌ <b>\u2022 ERROR LOADING SUPPORT STATISTICS \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>SYSTEM ERROR</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"<i>Error details: {str(e)}</i>",
            reply_markup=admin_support_error_keyboard(),
            parse_mode="HTML",
        )


@mark_as_used
@router.callback_query(lambda c: c.data.startswith("reopen_support:"))
async def reopen_support_thread(callback_query: CallbackQuery):
    """Reopen a resolved support thread."""
    admin_id = callback_query.from_user.id

    if not (is_owner_or_admin(admin_id) or is_privileged(admin_id, role="owner")):
        await callback_query.answer(text="🚫 Access denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract thread ID and sanitize it
    thread_id = callback_query.data.split(":")[1]
    thread_id = sanitize_thread_id(thread_id)

    try:
        # Get the DB collection and update status
        db = get_db()
        result = db.support_messages.update_one(
            {"_id": ObjectId(thread_id)},
            {"$set": {"status": "replied", "last_updated": datetime.now()}},
        )

        if result.modified_count > 0:
            # Add a system note about reopening using the correct parameter structure
            add_reply_to_support_message(
                thread_id,
                {
                    "admin_id": admin_id,
                    "admin_name": callback_query.from_user.full_name,
                    "message": "Thread reopened by admin",
                    "is_admin": True,
                    "is_system": True,
                },
            )

            # Create success message with proper formatting using template
            success_message = format_text(
                "admin_support",
                "thread_reopened",
                default=(
                    "🔄 <b>\u2022 THREAD REOPENED \u2022</b>\n\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    "<b>STATUS UPDATED</b>\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    "✅ Support thread has been reopened and marked as replied.\n\n"
                    "<i>You can now view the thread or send additional replies.</i>"
                ),
            )

            # Create keyboard matching other admin panels
            keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="👁️ View Thread",
                            callback_data=f"view_replies:{thread_id}",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="📝 Reply to User",
                            callback_data=f"reply_to_thread:{thread_id}",
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Support", callback_data="admin_support"
                        )
                    ],
                ]
            )

            await safe_edit_message(
                callback_query.message,
                success_message,
                reply_markup=keyboard,
                parse_mode="HTML",
            )
        else:
            await safe_edit_message(
                callback_query.message,
                "⚠️ <b>OPERATION FAILED</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "No changes were made to the thread. It may not be in resolved status.",
                reply_markup=admin_support_error_keyboard(),
                parse_mode="HTML",
            )
    except Exception as e:
        logger.error(f"Error reopening support thread: {e}")
        await safe_edit_message(
            callback_query.message,
            "❌ <b>ERROR REOPENING THREAD</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"<i>Error details: {str(e)}</i>",
            reply_markup=admin_support_error_keyboard(),
            parse_mode="HTML",
        )


@mark_as_used
@router.callback_query(lambda c: c.data.startswith("reply_to_user:"))
async def reply_to_user(callback_query: CallbackQuery, state: FSMContext):
    """Handle admin clicking on reply button from notification message."""
    admin_id = callback_query.from_user.id

    if not (is_owner_or_admin(admin_id) or is_privileged(admin_id, role="owner")):
        await callback_query.answer(text="🚫 Access denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract user ID from callback data
    user_id = callback_query.data.split(":")[1]

    # Find the most recent thread for this user
    try:
        # Get recent threads for this user, sorted by timestamp
        user_threads = get_support_messages(limit=5)
        target_thread = None

        # Find the most recent thread from this user that isn't resolved
        for thread in user_threads:
            if (
                str(thread.get("user_id")) == str(user_id)
                and thread.get("status") != "resolved"
            ):
                target_thread = thread
                break

        if not target_thread:
            # If no active thread found, check for any thread from this user
            for thread in user_threads:
                if str(thread.get("user_id")) == str(user_id):
                    target_thread = thread
                    break

        if target_thread:
            thread_id = str(target_thread.get("_id"))
            # Store thread ID in state using safe update
            await safe_update_data(state, thread_id=thread_id)
            # Get user info
            user = get_user(user_id) or {}
            user_name = target_thread.get("user_name", "Unknown User")
            user_display = user.get("username", user_name)
            if user.get("name"):
                user_display = user.get("name")

            # Get reply to user message from template
            reply_message = format_text(
                "admin_support",
                "reply_to_user",
                user_display=user_display,
                user_id=user_id,
                default=(
                    f"✍️ <b>\u2022 REPLY TO USER \u2022</b>\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>MESSAGE COMPOSITION</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"<b>User:</b> <i>{user_display}</i> (ID: <code>{user_id}</code>)\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>INSTRUCTIONS</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"Please type your reply message. Your response will be sent directly to the user.\n\n"
                    f"<i>Note: Type a clear and helpful message. Media files are not supported in replies.</i>"
                ),
            )

            await safe_edit_message(
                callback_query.message,
                reply_message,
                reply_markup=admin_reply_cancel_keyboard(),
                parse_mode="HTML",
            )

            # Set state to wait for admin's reply message
            await state.set_state(AdminStates.waiting_for_reply_message)
        else:
            # Get thread not found message from template
            thread_not_found = format_text(
                "admin_support",
                "thread_not_found",
                default=(
                    "❌ <b>\u2022 ERROR: THREAD NOT FOUND \u2022</b>\n\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    "<b>MISSING DATA</b>\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    "<i>No support threads found for this user.</i>"
                ),
            )

            await safe_edit_message(
                callback_query.message,
                thread_not_found,
                reply_markup=admin_support_error_keyboard(),
                parse_mode="HTML",
            )
    except Exception as e:
        logger.error(f"Error finding thread for user {user_id}: {e}")
        await safe_edit_message(
            callback_query.message,
            f"❌ <b>ERROR OCCURRED</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"<i>Error details: {str(e)}</i>",
            reply_markup=admin_support_error_keyboard(),
            parse_mode="HTML",
        )


@mark_as_used
@router.callback_query(lambda c: c.data.startswith("reply_to_thread:"))
async def reply_to_thread(callback_query: CallbackQuery, state: FSMContext):
    """Handle admin clicking on reply button from thread view."""
    # from database.operations import update_support_thread

    admin_id = callback_query.from_user.id

    if not (is_owner_or_admin(admin_id) or is_privileged(admin_id, role="owner")):
        await callback_query.answer(text="🚫 Access denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract thread ID and clean it
    thread_id = callback_query.data.split(":")[1]
    original_thread_id = thread_id
    thread_id = sanitize_thread_id(thread_id)

    # If thread ID is longer than 24 chars, truncate to standard MongoDB ObjectID length
    if len(thread_id) > 24:
        logger.info(f"Thread ID {thread_id} exceeds 24 characters, truncating to 24")
        thread_id = thread_id[-24:]  # Keep the last 24 characters

    # Store sanitized thread ID in state using safe update
    await safe_update_data(state, thread_id=thread_id)

    # Get thread data to show context
    thread = None

    try:
        # First try direct lookup
        thread = get_support_thread(thread_id)

        # If not found, try finding by suffix (whether ID was originally short or long)
        if not thread:
            logger.info(
                f"Direct lookup failed for thread ID: {thread_id}, trying suffix match"
            )
            all_threads = get_support_messages(limit=100)

            # Try with the original thread ID first (in case it's significant)
            for msg in all_threads:
                msg_id_str = str(msg.get("_id", ""))
                if msg_id_str.endswith(original_thread_id) or msg_id_str.endswith(
                    thread_id
                ):
                    thread = msg
                    full_thread_id = msg_id_str
                    await safe_update_data(state, thread_id=full_thread_id)
                    logger.info(
                        f"Found thread with matching ID suffix: {full_thread_id}"
                    )
                    break

            # If still not found and thread_id is longer than 6 chars, try with just last 6 chars
            if not thread and len(thread_id) > 6:
                short_suffix = thread_id[-6:]
                logger.info(f"Trying shorter suffix match with: {short_suffix}")
                for msg in all_threads:
                    msg_id_str = str(msg.get("_id", ""))
                    if msg_id_str.endswith(short_suffix):
                        thread = msg
                        full_thread_id = msg_id_str
                        await safe_update_data(state, thread_id=full_thread_id)
                        logger.info(
                            f"Found thread with short suffix match: {full_thread_id}"
                        )
                        break
    except Exception as e:
        logger.error(f"Error finding thread with ID {thread_id}: {str(e)}")

    if not thread:
        # Get thread not found message from template
        thread_not_found = format_text(
            "admin_support",
            "thread_not_found",
            default=(
                "❌ <b>\u2022 ERROR: THREAD NOT FOUND \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>MISSING DATA</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "<i>Support thread not found or has been deleted.</i>"
            ),
        )

        await safe_edit_message(
            callback_query.message,
            thread_not_found,
            reply_markup=admin_support_error_keyboard(),
            parse_mode="HTML",
        )
        return

    # Get user info
    user_id = thread.get("user_id", "Unknown")
    user_name = thread.get("user_name", "Unknown User")
    user = get_user(user_id) or {}
    user_display = user.get("username", user_name)
    if user.get("name"):
        user_display = user.get("name")

    # Get reply to thread message from template
    reply_message = format_text(
        "admin_support",
        "reply_to_user",
        user_display=user_display,
        user_id=user_id,
        default=(
            f"✍️ <b>\u2022 REPLY TO SUPPORT THREAD \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>MESSAGE COMPOSITION</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"<b>User:</b> <i>{user_display}</i> (ID: <code>{user_id}</code>)\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>INSTRUCTIONS</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"Please type your reply message. Your response will be sent directly to the user.\n\n"
            f"<i>Note: Type a clear and helpful message. Media files are not supported in replies.</i>"
        ),
    )

    await safe_edit_message(
        callback_query.message,
        reply_message,
        reply_markup=admin_reply_cancel_keyboard(),
        parse_mode="HTML",
    )

    # Set state to wait for admin's reply message
    await state.set_state(AdminStates.waiting_for_reply_message)


@mark_as_used
@router.message(AdminStates.waiting_for_reply_message)
async def process_admin_reply(message: types.Message, state: FSMContext, bot: Bot):
    """Process the admin's reply to a support message."""
    admin_id = message.from_user.id
    admin_name = message.from_user.full_name

    if not (is_owner_or_admin(admin_id) or is_privileged(admin_id, role="owner")):
        await message.reply("🚫 Access denied")
        await clear_state_data(state)  # Use our safer clear function
        return

    # Get reply content
    reply_text = message.text.strip()
    if not reply_text:
        await message.reply(
            "⚠️ <b>ERROR: EMPTY MESSAGE</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "Reply cannot be empty. Please type a message.",
            parse_mode="HTML",
        )
        return

    try:
        # Get thread ID from state
        data = await state.get_data()
        thread_id = data.get("thread_id")
        if not thread_id:
            await message.reply(
                "❌ <b>ERROR: THREAD NOT FOUND</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "Thread information missing. Please try again.",
                reply_markup=admin_support_error_keyboard(),
                parse_mode="HTML",
            )
            await clear_state_data(state)  # Use our safer clear function
            return

        # Get thread to find user ID for notification
        thread = get_support_thread(thread_id)
        if not thread:
            await message.reply(
                "❌ <b>ERROR: THREAD NOT FOUND</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "Support thread not found. It may have been deleted.",
                reply_markup=admin_support_error_keyboard(),
                parse_mode="HTML",
            )
            await clear_state_data(state)  # Use our safer clear function
            return

        user_id = thread.get("user_id")

        # Add reply to the thread
        reply_added = add_reply_to_support_message(
            thread_id,
            {
                "admin_id": admin_id,
                "admin_name": admin_name,
                "message": reply_text,
                "is_admin": True,
            },
        )
        if not reply_added:
            await message.reply(
                "❌ <b>ERROR: REPLY FAILED</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "Failed to add reply to the thread. Please try again.",
                reply_markup=admin_support_error_keyboard(),
                parse_mode="HTML",
            )
            return

        # Import and update the thread status
        # from database.operations import update_support_thread

        # # Create the success message with a keyboard
        # update_support_thread(
        #     thread_id,
        #     {
        #         "status": "replied",
        #         # Add any other fields you need to update
        #     },
        # )

        # Get reply sent message from template
        reply_sent = format_text(
            "admin_support",
            "reply_sent",
            user_id=user_id,
            default=(
                "✅ <b>\u2022 REPLY SENT SUCCESSFULLY \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>MESSAGE DELIVERED</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"<i>Your reply has been sent to the user (ID: <code>{user_id}</code>).</i>\n\n"
                f"<b>Thread Status:</b> <code>Active</code>\n\n"
                f"<i>The support thread has been updated and marked as replied.</i>"
            ),
        )

        await message.reply(
            reply_sent,
            reply_markup=admin_reply_success_keyboard(thread_id),
            parse_mode="HTML",
        )

        # Try to notify the user about the admin's reply
        try:
            # Prepare user notification keyboard
            user_keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="📝 Reply",
                            callback_data=f"reply_to_support:{thread_id}",
                        )
                    ]
                ]
            )

            # Safely escape reply text to prevent HTML parsing issues
            safe_reply_text = reply_text.replace("<", "&lt;").replace(">", "&gt;")

            # Get user notification message from template

            notification_message = format_text(
                "user",
                "support_team_reply",
                reply_text=safe_reply_text,
                default=(
                    f"📬 <b>\u2022 NEW REPLY FROM SUPPORT TEAM \u2022</b>\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>MESSAGE RECEIVED</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"<b>From:</b> Support Team\n\n"
                    f"<b>Message:</b>\n<pre>{safe_reply_text}</pre>\n\n"
                    f"<i>You can continue the conversation by clicking the Reply button below.</i>"
                ),
            )

            # Send notification to user with properly formatted HTML
            await bot.send_message(
                chat_id=user_id,
                text=notification_message,
                reply_markup=user_keyboard,
                parse_mode="HTML",
            )
            logger.info(
                f"Admin {admin_id} replied to thread {thread_id} and notified user {user_id}"
            )
        except Exception as e:
            logger.error(f"Failed to notify user {user_id} about admin reply: {e}")
            await message.reply(
                "⚠️ <b>NOTIFICATION WARNING</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "Your reply was saved but we couldn't notify the user. They'll see it when they check support.",
                parse_mode="HTML",
            )

        # Clear the state
        await clear_state_data(state)  # Use our safer clear function
    except Exception as e:
        logger.error(f"Error processing admin reply: {e}")
        await message.reply(
            f"❌ <b>ERROR OCCURRED</b>\n"
            f"<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"<i>Error details: {str(e)}</i>",
            reply_markup=admin_support_error_keyboard(),
            parse_mode="HTML",
        )
        await clear_state_data(state)  # Use our safer clear function


@mark_as_used
@router.callback_query(F.data == "cancel_admin_reply")
async def cancel_admin_reply(callback_query: types.CallbackQuery, state: FSMContext):
    """Cancel admin's reply to user."""
    await callback_query.answer("Reply cancelled")
    # Get reply cancelled message from template
    reply_cancelled = format_text(
        "admin_support",
        "reply_cancelled",
        default=(
            "❌ <b>\u2022 REPLY CANCELLED \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>ACTION ABORTED</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            "<i>You have cancelled your reply to this support thread.</i>"
        ),
    )

    await safe_edit_message(
        callback_query.message,
        reply_cancelled,
        reply_markup=InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Support", callback_data="admin_support"
                    )
                ]
            ]
        ),
        parse_mode="HTML",
    )
    await clear_state_data(state)  # Use our safer clear function


@mark_as_used
@router.callback_query(lambda c: c.data.startswith("review_threads:"))
async def review_threads_sequentially(callback_query: CallbackQuery):
    """Review support threads one by one with options to reply to each."""
    admin_id = callback_query.from_user.id

    if not (is_owner_or_admin(admin_id) or is_privileged(admin_id, role="owner")):
        await callback_query.answer(text="🚫 Access denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract current index and filter type from callback data
    try:
        parts = callback_query.data.split(":")
        current_index = int(parts[1])
        # If filter is specified, use it; otherwise default to "all"
        filter_type = parts[2] if len(parts) > 2 else "all"
    except (ValueError, IndexError):
        current_index = 0
        filter_type = "all"

    try:
        # Get threads based on filter
        if filter_type == "all":
            # Get threads, prioritizing pending ones first
            pending_threads = get_support_messages(status="pending", limit=50)
            replied_threads = get_support_messages(status="replied", limit=20)
            resolved_threads = get_support_messages(status="resolved", limit=10)

            # Combine them with pending first, then replied, then resolved
            all_threads = (
                pending_threads
                + [t for t in replied_threads if t not in pending_threads]
                + [
                    t
                    for t in resolved_threads
                    if t not in pending_threads and t not in replied_threads
                ]
            )
        else:
            all_threads = get_support_messages(status=filter_type, limit=50)

        if not all_threads:
            await safe_edit_message(
                callback_query.message,
                "📪 <b>NO THREADS FOUND</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                f"<i>No {filter_type} support threads found to review.</i>",
                reply_markup=admin_support_error_keyboard(),
                parse_mode="HTML",
            )
            return

        # Ensure index is within bounds
        if current_index >= len(all_threads):
            current_index = 0
        elif current_index < 0:
            current_index = len(all_threads) - 1

        # Get the current thread
        thread = all_threads[current_index]
        thread_id = str(thread.get("_id", ""))
        short_thread_id = thread_id[-6:] if thread_id else "??????"
        user_id = thread.get("user_id", "Unknown")
        user_name = thread.get("user_name", "Unknown User")
        status = thread.get("status", "unknown")
        timestamp = thread.get("timestamp")
        timestamp_str = timestamp.strftime("%d %b, %H:%M") if timestamp else "Unknown"

        # Get message and replies
        message = thread.get("message", "No content")
        # Escape HTML in message content
        message = message.replace("<", "&lt;").replace(">", "&gt;")
        replies = thread.get("replies", [])

        # Count threads by status for display
        if filter_type == "all":
            pending_count = len(pending_threads)
            replied_count = len(
                [t for t in all_threads if t.get("status") == "replied"]
            )
            resolved_count = len(
                [t for t in all_threads if t.get("status") == "resolved"]
            )

            status_counts = f"🔴 {pending_count} pending \u2022 🟢 {replied_count} replied \u2022 ✅ {resolved_count} resolved"
        else:
            status_counts = f"Showing {filter_type.capitalize()} threads only"

        # Status emoji and text
        if status == "pending":
            status_display = "🔴 <i>Pending</i>"
        elif status == "replied":
            status_display = "🟢 <i>Replied</i>"
        elif status == "resolved":
            status_display = "✅ <i>Resolved</i>"
        else:
            status_display = "❓ <i>Unknown</i>"

        # Try to get additional user info if needed
        if user_name == "Unknown User" and user_id != "Unknown":
            try:
                user_obj = get_user(user_id)
                if user_obj:
                    if user_obj.get("name"):
                        user_name = user_obj.get("name")
                    elif user_obj.get("first_name"):
                        user_name = user_obj.get("first_name")
            except Exception as e:
                logger.error(f"Failed to get user info for ID {user_id}: {e}")

        # Get username if available
        username = thread.get("username", "")
        if not username and user_id != "Unknown":
            try:
                user_obj = get_user(user_id)
                if user_obj and user_obj.get("username"):
                    username = user_obj.get("username")
            except Exception:
                pass

        user_display = f"{user_name}" + (f" | @{username}" if username else "")

        # Create thread preview with enhanced styling
        thread_preview = (
            f"🧵 <b>\u2022 THREAD REVIEW #{short_thread_id} ({current_index+1}/{len(all_threads)}) \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>THREAD INFORMATION</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"🔹 <b>Status:</b> {status_display}\n"
            f"🔹 <b>From:</b> <i>{user_display}</i> (ID: <code>{user_id}</code>)\n"
            f"🔹 <b>Time:</b> <i>{timestamp_str}</i>\n"
            f"🔹 <b>Summary:</b> {status_counts}\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>MESSAGE CONTENT</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"<pre>\n<b>💬 {message[:500]}</b></pre>"
            f"{' [...]' if len(message) > 500 else ''}\n\n"
        )

        # Add the most recent reply if any
        if replies:
            thread_preview += (
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>CONVERSATION HISTORY</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            )

            latest_reply = replies[-1]
            is_admin = latest_reply.get("is_admin", False)
            is_system = latest_reply.get("is_system", False)
            reply_msg = latest_reply.get("message", "")[:200]
            reply_time = latest_reply.get("reply_time")
            reply_time_str = (
                reply_time.strftime("%d %b, %H:%M") if reply_time else "Unknown"
            )

            # Escape HTML in reply message
            safe_reply_msg = reply_msg.replace("<", "&lt;").replace(">", "&gt;")

            # Determine message type indicator and sender
            if is_system:
                msg_type = "🤖"
                sender = "🤖 <b>System</b>"
            elif is_admin:
                msg_type = "🛡️"
                admin_name = latest_reply.get("admin_name", "Admin")
                sender = f"👨‍💼 <b>{admin_name}</b>"
            else:
                msg_type = "💬"
                sender = f"<i>{user_display}</i>"

            thread_preview += (
                f"<b>Latest Reply \u2022 {msg_type}</b>\n"
                f"<b>From:</b> {sender}\n"
                f"<b>Time:</b> <i>{reply_time_str}</i>\n"
                f"<b>Content:</b>\n<pre>\n<b>💬 {safe_reply_msg}</b></pre>"
                f"{' [...]' if len(reply_msg) > 200 else ''}\n\n"
                f"<i>({len(replies)} total replies in this thread)</i>\n\n"
            )

        # Add actions footer
        thread_preview += (
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>ACTIONS AVAILABLE</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"<i>Use the buttons below to navigate through {filter_type} threads or take action on this support request.</i>"
        )

        # Create action buttons
        keyboard_buttons = []

        # Reply button
        keyboard_buttons.append(
            [
                InlineKeyboardButton(
                    text="📝 Reply", callback_data=f"reply_to_thread:{thread_id}"
                )
            ]
        )

        # Resolve/Reopen button based on status
        if status == "pending" or status == "replied":
            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="✅ Mark as Resolved",
                        callback_data=f"resolve_support:{thread_id}",
                    )
                ]
            )
        elif status == "resolved":
            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="🔄 Reopen Thread",
                        callback_data=f"reopen_support:{thread_id}",
                    )
                ]
            )

        # Navigation buttons
        keyboard_buttons.extend(
            [
                [
                    InlineKeyboardButton(
                        text="📋 View All Threads", callback_data="view_all_threads"
                    )
                ],
                [
                    InlineKeyboardButton(
                        text="🔙 Back to Support", callback_data="admin_support"
                    )
                ],
            ]
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        # Check if the message is too long for Telegram limits
        if len(thread_preview) > 4096:
            # More careful truncation to ensure HTML tags are properly balanced
            thread_preview = thread_preview[:4000]
            # Make sure we don't cut in the middle of an HTML tag
            if (
                "<" in thread_preview[-100:]
                and ">" not in thread_preview[thread_preview.rfind("<") :]
            ):
                thread_preview = thread_preview[: thread_preview.rfind("<")]
            thread_preview += "\n\n<i>... [Thread truncated due to length]</i>"

        await safe_edit_message(
            callback_query.message,
            thread_preview,
            reply_markup=keyboard,
            parse_mode="HTML",
        )

    except Exception as e:
        logger.error(f"Error in review_threads_sequentially: {e}")
        await safe_edit_message(
            callback_query.message,
            "❌ <b>ERROR OCCURRED</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"<i>Error details: {str(e)}</i>",
            reply_markup=admin_support_error_keyboard(),
            parse_mode="HTML",
        )


@mark_as_used
@router.callback_query(lambda c: c.data.startswith("view_replies:"))
async def view_support_thread_replies(callback_query: CallbackQuery):
    """View all messages and replies in a support thread."""
    admin_id = callback_query.from_user.id

    if not (is_owner_or_admin(admin_id) or is_privileged(admin_id, role="owner")):
        await callback_query.answer(text="🚫 Access denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract thread ID from callback data and sanitize it
    thread_id = callback_query.data.split(":")[1]
    thread_id = sanitize_thread_id(thread_id)

    try:
        thread = None
        if len(thread_id) < 24 and len(thread_id) > 0:
            try:
                # Get support messages and find the one with an ID that ends with our truncated ID
                messages = get_support_messages(limit=100)
                for msg in messages:
                    msg_id = str(msg.get("_id", ""))
                    if msg_id.endswith(thread_id):
                        thread = msg
                        # Use the full thread ID for further operations
                        thread_id = msg_id
                        break
            except Exception as e:
                logger.error(f"Error finding thread with truncated ID {thread_id}: {e}")
        else:
            # Normal case - try to get thread directly
            thread = get_support_thread(thread_id)

        if not thread:
            await safe_edit_message(
                callback_query.message,
                "❌ Thread not found. It may have been deleted.",
                reply_markup=admin_support_error_keyboard(),
                parse_mode="HTML",
            )
            return

        # Extract essential thread information
        user_id = thread.get("user_id", "Unknown")
        user_name = thread.get("user_name", "Unknown User")
        user_name = str(user_name).replace("<", "&lt;").replace(">", "&gt;")
        status = thread.get("status", "unknown")
        timestamp = thread.get("timestamp")
        timestamp_str = timestamp.strftime("%d %b, %H:%M") if timestamp else "Unknown"
        short_thread_id = str(thread.get("_id", ""))[-6:]

        # Try to get better user info
        if user_name == "Unknown User" and user_id != "Unknown":
            try:
                user_obj = get_user(user_id)
                if user_obj:
                    if user_obj.get("name"):
                        user_name = user_obj.get("name")
                    elif user_obj.get("first_name"):
                        user_name = user_obj.get("first_name")
            except Exception as e:
                logger.error(f"Failed to get user info for ID {user_id}: {e}")

        # Get username if available
        username = thread.get("username", "")
        if not username and user_id != "Unknown":
            try:
                user_obj = get_user(user_id)
                if user_obj and user_obj.get("username"):
                    username = user_obj.get("username")
            except Exception:
                pass

        # Status emoji
        status_emoji = "❓"
        if status == "pending":
            status_emoji = "🔴"
        elif status == "replied":
            status_emoji = "🟢"
        elif status == "resolved":
            status_emoji = "✅"

        # Create more concise thread view
        thread_view = (
            f"🧵 <b>THREAD #{short_thread_id}</b> \u2022 {status_emoji} {status.upper()}\n"
            f"👤 <b>{user_name}</b>"
            + (f" (@{username})" if username else "")
            + f" \u2022 ID: <code>{user_id}</code>\n"
            f"⏰ {timestamp_str}\n"
            f"<code>──────────────</code>\n\n"
        )

        # Add original message with truncation if needed
        original_message = str(thread.get("message", "No content"))
        original_message = original_message.replace("<", "&lt;").replace(">", "&gt;")
        if len(original_message) > 250:
            original_message = original_message[:247] + "..."

        thread_view += f"<b>Initial message:</b>\n<pre>{original_message}</pre>\n\n"

        # Add summary of replies
        replies = thread.get("replies", [])
        if replies:
            reply_count = len(replies)
            admin_replies = sum(1 for r in replies if r.get("is_admin", False))
            user_replies = reply_count - admin_replies

            thread_view += f"<b>Replies:</b> {reply_count} ({admin_replies} admin, {user_replies} user)\n"

            # Add just the most recent reply
            if reply_count > 0:
                latest_reply = replies[-1]
                is_admin = latest_reply.get("is_admin", False)
                is_system = latest_reply.get("is_system", False)
                reply_msg = str(latest_reply.get("message", ""))[:200]
                reply_time = latest_reply.get("reply_time")

                sender = "System" if is_system else ("Admin" if is_admin else user_name)
                sender_emoji = "🤖" if is_system else ("👨‍💼" if is_admin else "💬")

                thread_view += (
                    f"\n<b>Latest: {sender_emoji} {sender}</b> \u2022 {reply_time.strftime('%H:%M') if reply_time else ''}\n"
                    f"<pre>{reply_msg.replace('<', '&lt;').replace('>', '&gt;')}</pre>\n"
                )

            thread_view += "<code>──────────────</code>\n"
        else:
            thread_view += "<b>No replies yet</b>\n<code>──────────────</code>\n"

        # Create keyboard with essential actions
        keyboard_buttons = [
            [
                InlineKeyboardButton(
                    text="📝 Reply", callback_data=f"reply_to_thread:{thread_id}"
                )
            ],
        ]

        # Resolve/Reopen button based on status
        if status in ["pending", "replied"]:
            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="✅ Resolve", callback_data=f"resolve_support:{thread_id}"
                    )
                ]
            )
        elif status == "resolved":
            keyboard_buttons.append(
                [
                    InlineKeyboardButton(
                        text="🔄 Reopen", callback_data=f"reopen_support:{thread_id}"
                    )
                ]
            )

        # Navigation buttons
        keyboard_buttons.append(
            [
                InlineKeyboardButton(
                    text="📋 All Threads", callback_data="view_all_threads"
                ),
                InlineKeyboardButton(text="🔙 Back", callback_data="admin_support"),
            ]
        )

        keyboard = InlineKeyboardMarkup(inline_keyboard=keyboard_buttons)

        await safe_edit_message(
            callback_query.message,
            thread_view,
            reply_markup=keyboard,
            parse_mode="HTML",
        )
    except Exception as e:
        logger.error(f"Error viewing support thread replies: {e}")
        await safe_edit_message(
            callback_query.message,
            f"❌ Error: {str(e)}",
            reply_markup=admin_support_error_keyboard(),
            parse_mode="HTML",
        )


@mark_as_used
@router.callback_query(F.data.startswith("resolve_support:"))
async def resolve_support_thread(callback_query: CallbackQuery):
    """Mark a support thread as resolved."""
    admin_id = callback_query.from_user.id

    if not (is_owner_or_admin(admin_id) or is_privileged(admin_id, role="owner")):
        await callback_query.answer(text="🚫 Access denied", show_alert=True)
        return

    await callback_query.answer()

    # Extract thread ID
    try:
        # Get the raw callback data and safely extract thread ID
        callback_data = callback_query.data
        if ":" not in callback_data:
            logger.error(f"Invalid callback data format: {callback_data}")
            raise ValueError(f"Invalid callback data format: {callback_data}")

        # Extract the base thread ID from callback data
        thread_id = callback_data.split(":", 1)[1]

        # Enhanced cleaning: remove ALL non-alphanumeric characters except for valid ObjectId characters (0-9, a-f)
        import re

        thread_id = re.sub(r"[^0-9a-f]", "", thread_id.lower())

        if not thread_id or len(thread_id) != 24:
            logger.error(f"Invalid ObjectId format after cleaning: {thread_id}")
            await safe_edit_message(
                callback_query.message,
                "❌ <b>INVALID THREAD ID</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "<i>The thread ID format is invalid. Please try again or contact development.</i>",
                reply_markup=admin_support_error_keyboard(),
                parse_mode="HTML",
            )
            return

        # Verify the thread exists before attempting to resolve it
        thread = get_support_thread(thread_id)
        if not thread:
            logger.error(f"Support thread not found with ID: {thread_id}")
            await safe_edit_message(
                callback_query.message,
                "❌ <b>THREAD NOT FOUND</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "<i>The requested support thread could not be found. It may have been deleted.</i>",
                reply_markup=admin_support_error_keyboard(),
                parse_mode="HTML",
            )
            return

        # Call the async function to resolve the thread
        result = await resolve_support_thread_async(thread_id, admin_id)

        if result:
            # Add a system note about resolving with the is_system parameter
            add_reply_to_support_message(
                thread_id,
                {
                    "admin_id": admin_id,
                    "admin_name": callback_query.from_user.full_name,
                    "message": "Thread marked as resolved by admin",
                    "is_admin": True,
                    "is_system": True,
                },
            )
            await safe_edit_message(
                callback_query.message,
                "✅ <b>THREAD RESOLVED</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "<i>Support thread has been marked as resolved.</i>",
                reply_markup=admin_thread_resolved_keyboard(thread_id),
                parse_mode="HTML",
            )
        else:
            await safe_edit_message(
                callback_query.message,
                "⚠️ <b>OPERATION FAILED</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "No changes were made to the thread. It may not be in resolved status.",
                reply_markup=admin_support_error_keyboard(),
                parse_mode="HTML",
            )
    except Exception as e:
        logger.error(f"Error resolving support thread: {e}")
        await safe_edit_message(
            callback_query.message,
            "❌ <b>ERROR OCCURRED</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"<i>Error details: {str(e)}</i>",
            reply_markup=admin_support_error_keyboard(),
            parse_mode="HTML",
        )


@mark_as_used
@router.callback_query(lambda c: c.data.startswith("check_thread_status:"))
async def check_thread_status(callback_query: CallbackQuery):
    """Check for new replies in a support thread and inform the user."""
    try:
        await callback_query.answer("Checking thread status...")

        # Extract thread ID from callback data
        thread_id = callback_query.data.split(":")[1]
        thread_id = sanitize_thread_id(thread_id)

        # Get user ID to check if this is admin or regular user
        user_id = callback_query.from_user.id
        is_admin = is_owner_or_admin(user_id) or is_privileged(user_id, role="owner")

        # Get thread data
        thread = get_support_thread(thread_id)
        if not thread:
            # Try finding by suffix if ID is short
            if len(thread_id) < 24:
                all_threads = get_support_messages(limit=100)
                for msg in all_threads:
                    msg_id_str = str(msg.get("_id", ""))
                    if msg_id_str.endswith(thread_id):
                        thread = msg
                        thread_id = msg_id_str
                        break

        if not thread:
            await safe_edit_message(
                callback_query.message,
                "❌ <b>THREAD NOT FOUND</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "The support conversation you're looking for could not be found.\n"
                "Please create a new support request.",
                parse_mode="HTML",
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="✉️ New Support Request",
                                callback_data="send_support_message",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Support",
                                callback_data="contact_support",
                            )
                        ],
                    ]
                ),
            )
            return

        # Get thread information
        thread_user_id = thread.get("user_id", "Unknown")
        status = thread.get("status", "unknown")
        replies = thread.get("replies", [])
        reply_count = len(replies)

        # Check if user is authorized to view this thread
        if not is_admin and str(thread_user_id) != str(user_id):
            await callback_query.answer(
                "You don't have permission to access this thread", show_alert=True
            )
            return

        # Format status message based on thread status
        if status == "pending":
            status_emoji = "🔴"
            status_text = "PENDING"
            status_message = (
                f"{status_emoji} <b>{status_text}</b>\n\n"
                "Your message is pending review. Our support team will respond soon.\n"
                "Please check back later for a reply."
            )
        elif status == "replied":
            status_emoji = "🟢"
            status_text = "REPLIED"

            # Get information about the latest reply
            latest_reply = replies[-1] if replies else None
            if latest_reply:
                is_admin_reply = latest_reply.get("is_admin", False)
                reply_time = latest_reply.get("reply_time")
                reply_time_str = (
                    reply_time.strftime("%d %b, %H:%M")
                    if reply_time
                    else "Unknown time"
                )

                if is_admin_reply:
                    status_message = (
                        f"{status_emoji} <b>{status_text}</b>\n\n"
                        f"<b>Great news!</b> Support has replied to your message at {reply_time_str}.\n"
                        f'Click "View Full Thread" to see the complete conversation.'
                    )
                else:
                    status_message = (
                        f"{status_emoji} <b>{status_text}</b>\n\n"
                        f"Our team has seen your request and we have an active conversation.\n"
                        f"Last message was from you at {reply_time_str}.\n"
                        f"Please wait for our team to respond."
                    )
            else:
                status_message = (
                    f"{status_emoji} <b>{status_text}</b>\n\n"
                    "We have an active conversation. Our team will respond soon."
                )
        elif status == "resolved":
            status_emoji = "✅"
            status_text = "RESOLVED"
            status_message = (
                f"{status_emoji} <b>{status_text}</b>\n\n"
                "This support thread has been resolved.\n"
                "If you need further assistance, please create a new support request."
            )
        else:
            status_emoji = "❓"
            status_text = "UNKNOWN"
            status_message = (
                f"{status_emoji} <b>{status_text}</b>\n\n"
                "The status of this thread is unknown.\n"
                "Please try again or create a new support request."
            )

        # Create a message with thread status and action buttons
        short_thread_id = str(thread.get("_id", ""))[-6:]
        message_text = (
            f"🧵 <b>\u2022 SUPPORT THREAD #{short_thread_id} \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>STATUS CHECK RESULTS</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"{status_message}\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>CONVERSATION SUMMARY</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"\u2022 Total messages: {reply_count + 1}\n"
            f"\u2022 Thread created: {thread.get('timestamp').strftime('%d %b, %H:%M') if thread.get('timestamp') else 'Unknown'}\n"
            f"\u2022 Last activity: {replies[-1].get('reply_time').strftime('%d %b, %H:%M') if replies and replies[-1].get('reply_time') else 'No replies yet'}\n\n"
            f"<i>Use the buttons below to view the complete conversation or check again later.</i>"
        )

        # Use the check_for_replies_keyboard from admin_support_kb
        from keyboards.admin_support_kb import check_for_replies_keyboard

        # Update the message with new status info
        await safe_edit_message(
            callback_query.message,
            message_text,
            reply_markup=check_for_replies_keyboard(thread_id),
            parse_mode="HTML",
        )

    except Exception as e:
        logger.error(f"Error in check_thread_status: {e}", exc_info=True)
        await callback_query.answer("Error checking thread status. Please try again.")
        await safe_edit_message(
            callback_query.message,
            "❌ <b>ERROR OCCURRED</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            f"<i>Error details: {str(e)}</i>",
            reply_markup=admin_support_error_keyboard(),
            parse_mode="HTML",
        )


def register_support_admin_handlers(dp: Dispatcher):
    """Register the support admin handlers with the dispatcher"""
    try:
        dp.include_router(router)
    except RuntimeError:
        # Router is already attached, skip it
        print("Support admin router already attached, skipping registration")
