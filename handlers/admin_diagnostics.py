"""
Admin diagnostic functionality integrated into the owner panel for monitoring
system performance and callback response times.
"""

import asyncio
import logging
from aiogram import Router, F
from aiogram.types import CallbackQuery, InlineKeyboardMarkup, InlineKeyboardButton
from utils.performance_monitor import (
    get_performance_summary,
    test_api_connectivity,
    format_performance_report,
    get_slow_operations_report,
    PerformanceTimer
)
from database.operations import is_owner
from handlers.sys_db import is_privileged
from payments.oxa_verify import check_oxapay_payment
import time

logger = logging.getLogger(__name__)
router = Router()


@router.callback_query(F.data == "system_diagnostics")
async def show_diagnostics_menu(callback_query: CallbackQuery):
    """Show system diagnostics menu integrated into owner panel."""
    user_id = callback_query.from_user.id

    # Check if user has owner privileges
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("❌ Access denied. Owner privileges required.", show_alert=True)
        return

    await callback_query.answer()

    # Create diagnostic keyboard
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [
            InlineKeyboardButton(text="📊 Performance Report", callback_data="diag_performance"),
            InlineKeyboardButton(text="🌐 API Connectivity", callback_data="diag_connectivity")
        ],
        [
            InlineKeyboardButton(text="🐌 Slow Operations", callback_data="diag_slow_ops"),
            InlineKeyboardButton(text="🧪 Test Payment API", callback_data="diag_test_payment")
        ],
        [
            InlineKeyboardButton(text="🔄 Refresh", callback_data="diag_refresh"),
            InlineKeyboardButton(text="🔙 Back to Advanced", callback_data="additional_features")
        ]
    ])

    await callback_query.message.edit_text(
        "🔧 <b>System Diagnostics</b>\n\n"
        "Monitor system performance and troubleshoot issues:\n\n"
        "• <b>Performance Report</b> - View detailed timing metrics\n"
        "• <b>API Connectivity</b> - Test external service connections\n"
        "• <b>Slow Operations</b> - Identify performance bottlenecks\n"
        "• <b>Test Payment API</b> - Verify payment gateway response\n\n"
        "<i>Select an option to begin diagnostics.</i>",
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data == "diag_performance")
async def show_performance_report(callback_query: CallbackQuery):
    """Show detailed performance report."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("❌ Access denied. Owner privileges required.", show_alert=True)
        return

    await callback_query.answer()

    with PerformanceTimer("generate_performance_report", "admin_operation"):
        report = format_performance_report()

    await callback_query.message.edit_text(
        report,
        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🔙 Back to Diagnostics", callback_data="system_diagnostics")]
        ]),
        parse_mode="HTML"
    )


@router.callback_query(F.data == "diag_connectivity")
async def test_connectivity(callback_query: CallbackQuery):
    """Test connectivity to external APIs."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("❌ Access denied. Owner privileges required.", show_alert=True)
        return

    await callback_query.answer("Testing API connectivity...")

    # Show loading message
    await callback_query.message.edit_text(
        "🔄 <b>Testing API Connectivity...</b>\n\n"
        "This may take a few seconds...",
        parse_mode="HTML"
    )

    with PerformanceTimer("api_connectivity_test", "admin_operation"):
        results = await test_api_connectivity()

    # Format results
    lines = ["🌐 <b>API Connectivity Test Results</b>\n"]

    for api_name, result in results.items():
        status_icon = "✅" if result["success"] else "❌"
        duration = result["duration"]
        status = result["status"]

        lines.append(f"{status_icon} <b>{api_name}</b>")
        lines.append(f"   • Response Time: {duration:.2f}s")
        lines.append(f"   • Status: {status}")

        if duration > 3.0:
            lines.append("   ⚠️ <i>Slow response detected</i>")

        lines.append("")

    await callback_query.message.edit_text(
        "\n".join(lines),
        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🔄 Test Again", callback_data="diag_connectivity")],
            [InlineKeyboardButton(text="🔙 Back to Diagnostics", callback_data="system_diagnostics")]
        ]),
        parse_mode="HTML"
    )


@router.callback_query(F.data == "diag_slow_ops")
async def show_slow_operations(callback_query: CallbackQuery):
    """Show recent slow operations."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("❌ Access denied. Owner privileges required.", show_alert=True)
        return

    await callback_query.answer()

    with PerformanceTimer("get_slow_operations", "admin_operation"):
        slow_ops = get_slow_operations_report(hours=2)

    if not slow_ops:
        text = "✅ <b>No Slow Operations Detected</b>\n\nAll operations are performing within normal thresholds."
    else:
        lines = ["🐌 <b>Recent Slow Operations (Last 2 Hours)</b>\n"]

        for i, op in enumerate(slow_ops[:10]):  # Show top 10
            timestamp = op["timestamp"].strftime("%H:%M:%S")
            duration = op["duration"]
            operation = op["operation_name"]
            success = "✅" if op["success"] else "❌"

            lines.append(f"{i+1}. {success} <b>{operation}</b>")
            lines.append(f"   • Duration: {duration:.2f}s")
            lines.append(f"   • Time: {timestamp}")
            lines.append("")

        text = "\n".join(lines)

    await callback_query.message.edit_text(
        text,
        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🔄 Refresh", callback_data="diag_slow_ops")],
            [InlineKeyboardButton(text="🔙 Back to Diagnostics", callback_data="system_diagnostics")]
        ]),
        parse_mode="HTML"
    )


@router.callback_query(F.data == "diag_test_payment")
async def test_payment_api(callback_query: CallbackQuery):
    """Test the payment verification API with a dummy request."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("❌ Access denied. Owner privileges required.", show_alert=True)
        return

    await callback_query.answer("Testing payment API...")

    # Show loading message
    await callback_query.message.edit_text(
        "🧪 <b>Testing Payment API...</b>\n\n"
        "Performing test API call...",
        parse_mode="HTML"
    )

    # Test with a dummy track ID (this will fail but we can measure response time)
    test_track_id = "test_track_id_12345"

    start_time = time.time()
    try:
        with PerformanceTimer("payment_api_test", "api_call"):
            result = await check_oxapay_payment(test_track_id)

        duration = time.time() - start_time

        # Analyze the result
        if result.get("status") == "error":
            status_text = "✅ API Responding (Expected error for test ID)"
        elif result.get("status") == "timeout":
            status_text = "⚠️ API Timeout"
        else:
            status_text = "✅ API Responding"

        lines = [
            "🧪 <b>Payment API Test Results</b>\n",
            f"📊 <b>Response Time:</b> {duration:.2f}s",
            f"🔗 <b>Status:</b> {status_text}",
            f"📝 <b>Response:</b> {result.get('status', 'unknown')}",
        ]

        if duration > 5.0:
            lines.append("\n⚠️ <b>Warning:</b> Response time is slow")
            lines.append("This may cause callback delays for users.")
        elif duration > 8.0:
            lines.append("\n❌ <b>Critical:</b> Response time is very slow")
            lines.append("Users will experience significant delays.")
        else:
            lines.append("\n✅ Response time is acceptable")

    except Exception as e:
        duration = time.time() - start_time
        lines = [
            "🧪 <b>Payment API Test Results</b>\n",
            f"📊 <b>Response Time:</b> {duration:.2f}s",
            f"❌ <b>Error:</b> {str(e)}",
            "\n⚠️ Payment API is not responding properly"
        ]

    await callback_query.message.edit_text(
        "\n".join(lines),
        reply_markup=InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(text="🔄 Test Again", callback_data="diag_test_payment")],
            [InlineKeyboardButton(text="🔙 Back to Diagnostics", callback_data="system_diagnostics")]
        ]),
        parse_mode="HTML"
    )


@router.callback_query(F.data == "diag_refresh")
async def refresh_diagnostics(callback_query: CallbackQuery):
    """Refresh the main diagnostics menu."""
    user_id = callback_query.from_user.id

    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("❌ Access denied. Owner privileges required.", show_alert=True)
        return

    await callback_query.answer("Refreshed")

    # Redirect to the main diagnostics menu
    await show_diagnostics_menu(callback_query)
