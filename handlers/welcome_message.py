"""
Welcome message management handlers.
This module contains handlers for managing the welcome message feature.
"""

import logging
from aiogram import Router, F
from aiogram.types import CallbackQuery, Message, FSInputFile
from aiogram.fsm.context import FSMContext
from aiogram.exceptions import TelegramBadRequest

from database.operations import (
    get_welcome_message,
    save_welcome_message,
    get_welcome_message_async,
    save_welcome_message_async,
    is_owner,
)
from states.states import WelcomeMessageStates
from keyboards.admin_kb import welcome_message_keyboard
from handlers.sys_db import is_privileged
from utils.state_helpers import safe_update_data, clear_state_data
from utils.telegram_helpers import safe_edit_message
from utils.image_handler import (
    process_product_image,
    WELCOME_IMAGES_FOLDER,
    download_file_from_telegram,
)
from utils.logger import log_admin_action

# Setup logger
logger = logging.getLogger(__name__)

# Create router
router = Router()


@router.callback_query(F.data == "manage_welcome_message")
async def manage_welcome_message(callback_query: CallbackQuery, state: FSMContext):
    """Handle the welcome message management menu."""
    user_id = callback_query.from_user.id

    # Check if user has owner or privileged owner access
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 Access denied. Only owners and privileged users can manage welcome messages.",
            show_alert=True,
        )
        return

    # Get current welcome message settings
    welcome_settings = await get_welcome_message_async()
    is_enabled = welcome_settings.get("enabled", False)

    # Create message text
    message_text = (
        "🎉 <b>WELCOME MESSAGE MANAGEMENT</b> 🎉\n\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        "<b>CURRENT SETTINGS</b>\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        f"<b>Status:</b> {'✅ Enabled' if is_enabled else '❌ Disabled'}\n"
        f"<b>Message:</b> {welcome_settings.get('message_text', 'No message set')}\n"
        f"<b>Image:</b> {'✅ Set' if welcome_settings.get('image_path') else '❌ Not set'}\n\n"
        "<i>Use the options below to manage the welcome message:</i>"
    )

    # Log the action
    await log_admin_action(
        callback_query.bot,
        user_id,
        "welcome_message_access",
        details=f"User {callback_query.from_user.full_name} accessed welcome message settings",
    )

    # Show the welcome message management menu
    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=welcome_message_keyboard(is_enabled),
        parse_mode="HTML",
    )


@router.callback_query(F.data == "toggle_welcome_message")
async def toggle_welcome_message(callback_query: CallbackQuery, state: FSMContext):
    """Toggle the welcome message on/off."""
    user_id = callback_query.from_user.id

    # Check if user has owner or privileged owner access
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 Access denied. Only owners and privileged users can manage welcome messages.",
            show_alert=True,
        )
        return

    # Get current welcome message settings
    welcome_settings = await get_welcome_message_async()
    current_status = welcome_settings.get("enabled", False)

    # Toggle the status
    new_status = not current_status

    # Save the new status
    success = await save_welcome_message_async(
        enabled=new_status,
        message_text=welcome_settings.get(
            "message_text", "Welcome to our platform! We're glad to have you here."
        ),
        image_path=welcome_settings.get("image_path"),
    )

    if success:
        status_text = "enabled" if new_status else "disabled"
        await callback_query.answer(
            f"Welcome message {status_text} successfully!", show_alert=True
        )

        # Log the action
        await log_admin_action(
            callback_query.bot,
            user_id,
            f"welcome_message_{status_text}",
            details=f"User {callback_query.from_user.full_name} {status_text} the welcome message",
        )

        # Refresh the welcome message management menu
        await manage_welcome_message(callback_query, state)
    else:
        await callback_query.answer(
            "Failed to update welcome message status. Please try again.",
            show_alert=True,
        )


@router.callback_query(F.data == "edit_welcome_text")
async def edit_welcome_text(callback_query: CallbackQuery, state: FSMContext):
    """Handle editing the welcome message text."""
    user_id = callback_query.from_user.id

    # Check if user has owner or privileged owner access
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 Access denied. Only owners and privileged users can manage welcome messages.",
            show_alert=True,
        )
        return

    # Get current welcome message settings
    welcome_settings = await get_welcome_message_async()
    current_message = welcome_settings.get(
        "message_text", "Welcome to our platform! We're glad to have you here."
    )

    # Set state to waiting for message text
    await state.set_state(WelcomeMessageStates.waiting_for_message_text)

    # Store current settings in state
    await safe_update_data(
        state,
        current_enabled=welcome_settings.get("enabled", False),
        current_image_path=welcome_settings.get("image_path"),
    )

    # Prompt user to enter new message text
    message_text = (
        "✏️ <b>EDIT WELCOME MESSAGE TEXT</b> ✏️\n\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        "<b>CURRENT MESSAGE</b>\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        f"{current_message}\n\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        "<i>Please enter the new welcome message text below.</i>\n"
        "<i>You can use HTML formatting tags like &lt;b&gt;, &lt;i&gt;, etc.</i>\n\n"
        "<i>Send /cancel to cancel this operation.</i>"
    )

    await safe_edit_message(callback_query.message, message_text, parse_mode="HTML")


@router.message(WelcomeMessageStates.waiting_for_message_text)
async def process_welcome_text(message: Message, state: FSMContext):
    """Process the new welcome message text."""
    user_id = message.from_user.id

    # Check if user has owner or privileged owner access
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply(
            "🚫 Access denied. Only owners and privileged users can manage welcome messages."
        )
        await clear_state_data(state)
        return

    # Check for cancel command
    if message.text and message.text.lower() == "/cancel":
        await message.reply("Operation cancelled.")
        await clear_state_data(state)
        return

    # Get the new message text
    new_message_text = message.text

    if not new_message_text:
        await message.reply("Please enter a valid text message.")
        return

    # Get current settings from state
    state_data = await state.get_data()
    current_enabled = state_data.get("current_enabled", False)
    current_image_path = state_data.get("current_image_path")

    # Save the new message text
    success = await save_welcome_message_async(
        enabled=current_enabled,
        message_text=new_message_text,
        image_path=current_image_path,
    )

    if success:
        # Log the action
        await log_admin_action(
            message.bot,
            user_id,
            "welcome_message_text_update",
            details=f"User {message.from_user.full_name} updated the welcome message text",
        )

        # Clear state
        await clear_state_data(state)

        # Send success message
        response = (
            "✅ <b>Welcome message text updated successfully!</b>\n\n"
            "<b>New message:</b>\n"
            f"{new_message_text}\n\n"
            "<i>Returning to welcome message management...</i>"
        )

        await message.reply(response, parse_mode="HTML")

        # Create a callback query object to reuse the manage_welcome_message function
        from aiogram.types import CallbackQuery

        mock_callback = CallbackQuery(
            id="0",
            from_user=message.from_user,
            chat_instance="0",
            message=message,
            data="manage_welcome_message",
        )

        # Return to welcome message management
        await manage_welcome_message(mock_callback, state)
    else:
        await message.reply("Failed to update welcome message text. Please try again.")


@router.callback_query(F.data == "upload_welcome_image")
async def upload_welcome_image(callback_query: CallbackQuery, state: FSMContext):
    """Handle uploading a new welcome image."""
    user_id = callback_query.from_user.id

    # Check if user has owner or privileged owner access
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 Access denied. Only owners and privileged users can manage welcome messages.",
            show_alert=True,
        )
        return

    # Get current welcome message settings
    welcome_settings = await get_welcome_message_async()

    # Set state to waiting for image upload
    await state.set_state(WelcomeMessageStates.waiting_for_image_upload)

    # Store current settings in state
    await safe_update_data(
        state,
        current_enabled=welcome_settings.get("enabled", False),
        current_message_text=welcome_settings.get(
            "message_text", "Welcome to our platform! We're glad to have you here."
        ),
    )

    # Prompt user to upload a new image
    message_text = (
        "🖼️ <b>UPLOAD WELCOME IMAGE</b> 🖼️\n\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        "<i>Please upload a new image or GIF for the welcome message.</i>\n"
        "<i>The image will be displayed alongside the welcome message text.</i>\n\n"
        "<b>Supported formats:</b> JPG, PNG, GIF\n\n"
        "<i>Send /cancel to cancel this operation.</i>"
    )

    await safe_edit_message(callback_query.message, message_text, parse_mode="HTML")


@router.message(
    F.photo | F.document | F.animation, WelcomeMessageStates.waiting_for_image_upload
)
async def process_welcome_image(message: Message, state: FSMContext):
    """Process the uploaded welcome image."""
    user_id = message.from_user.id

    # Check if user has owner or privileged owner access
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await message.reply(
            "🚫 Access denied. Only owners and privileged users can manage welcome messages."
        )
        await clear_state_data(state)
        return

    try:
        # Log message details for debugging
        if message.document:
            logger.info(
                f"Document upload detected: filename={message.document.file_name}, mime_type={message.document.mime_type}"
            )
        elif message.animation:
            logger.info(
                f"Animation upload detected: filename={message.animation.file_name}, mime_type={message.animation.mime_type}"
            )
        elif message.photo:
            logger.info(
                f"Photo upload detected: largest photo file_id={message.photo[-1].file_id}"
            )

        if message.photo:
            # Process and save the uploaded image
            file_data = await process_product_image(message, WELCOME_IMAGES_FOLDER)

            if not file_data:
                await message.reply("❌ Failed to process the image. Please try again.")
                return

            # Get the image path
            image_path = file_data.get("url_path")

        elif message.animation:
            # Handle animation type (Telegram's native GIF format)
            logger.info(f"Processing animation: {message.animation.file_name}")
            file_id = message.animation.file_id
            file_name = message.animation.file_name or f"animation_{file_id}.gif"

            # Make sure the file has a .gif extension
            if not file_name.lower().endswith(".gif"):
                file_name = f"{file_name}.gif"

            # Download the file
            file_data = await download_file_from_telegram(
                message.bot,
                file_id,
                WELCOME_IMAGES_FOLDER,
                file_name,
            )

            if not file_data:
                await message.reply("❌ Failed to process the GIF. Please try again.")
                return

            # Get the image path
            image_path = file_data.get("url_path")

        elif message.document:
            # Check if it's an image by MIME type or file extension for GIFs
            is_image = False

            # Check MIME type for images and animations
            if message.document.mime_type:
                mime_type = message.document.mime_type.lower()
                if (
                    mime_type.startswith("image/")
                    or mime_type
                    == "application/octet-stream"  # Sometimes GIFs come with this MIME type
                    or mime_type == "video/mp4"  # Sometimes animations are sent as MP4
                    or "gif" in mime_type
                ):  # Check for any MIME type containing "gif"
                    is_image = True
                    logger.info(f"Document accepted based on MIME type: {mime_type}")

            # Check file extension for GIFs specifically
            if message.document.file_name:
                file_name = message.document.file_name.lower()
                if file_name.endswith((".gif", ".jpg", ".jpeg", ".png")):
                    is_image = True
                    logger.info(
                        f"Document accepted based on file extension: {file_name}"
                    )

            if is_image:
                # Download the file
                file_id = message.document.file_id
                file_data = await download_file_from_telegram(
                    message.bot,
                    file_id,
                    WELCOME_IMAGES_FOLDER,
                    message.document.file_name,
                )

                if not file_data:
                    await message.reply(
                        "❌ Failed to process the image. Please try again."
                    )
                    return

                # Get the image path
                image_path = file_data.get("url_path")
            else:
                logger.warning(
                    f"Rejected document: filename={message.document.file_name}, mime_type={message.document.mime_type}"
                )
                await message.reply(
                    "❌ Invalid file type. Please upload an image (JPG, PNG, GIF)."
                )
                return
        else:
            await message.reply("❌ Please upload a valid image or GIF.")
            return

        # Get current settings from state
        state_data = await state.get_data()
        current_enabled = state_data.get("current_enabled", False)
        current_message_text = state_data.get(
            "current_message_text",
            "Welcome to our platform! We're glad to have you here.",
        )

        # Save the new image path
        success = await save_welcome_message_async(
            enabled=current_enabled,
            message_text=current_message_text,
            image_path=image_path,
        )

        if success:
            # Log the action
            await log_admin_action(
                message.bot,
                user_id,
                "welcome_message_image_update",
                details=f"User {message.from_user.full_name} updated the welcome message image",
            )

            # Clear state
            await clear_state_data(state)

            # Send success message
            response = (
                "✅ <b>Welcome message image updated successfully!</b>\n\n"
                "<i>Returning to welcome message management...</i>"
            )

            await message.reply(response, parse_mode="HTML")

            # Create a callback query object to reuse the manage_welcome_message function
            from aiogram.types import CallbackQuery

            mock_callback = CallbackQuery(
                id="0",
                from_user=message.from_user,
                chat_instance="0",
                message=message,
                data="manage_welcome_message",
            )

            # Return to welcome message management
            await manage_welcome_message(mock_callback, state)
        else:
            await message.reply(
                "Failed to update welcome message image. Please try again."
            )

    except Exception as e:
        logger.error(f"Error processing welcome image: {e}", exc_info=True)
        await message.reply(
            "An error occurred while processing the image. Please try again."
        )
        await clear_state_data(state)


@router.message(WelcomeMessageStates.waiting_for_image_upload)
async def process_cancel_image_upload(message: Message, state: FSMContext):
    """Handle cancel command during image upload."""
    # Skip if the message is a photo, document, or animation (handled by process_welcome_image)
    if message.photo or message.document or message.animation:
        return

    if message.text and message.text.lower() == "/cancel":
        await message.reply("Operation cancelled.")
        await clear_state_data(state)

        # Create a callback query object to reuse the manage_welcome_message function
        from aiogram.types import CallbackQuery

        mock_callback = CallbackQuery(
            id="0",
            from_user=message.from_user,
            chat_instance="0",
            message=message,
            data="manage_welcome_message",
        )

        # Return to welcome message management
        await manage_welcome_message(mock_callback, state)
    else:
        await message.reply("Please upload an image or GIF, or send /cancel to cancel.")


@router.callback_query(F.data == "remove_welcome_image")
async def remove_welcome_image(callback_query: CallbackQuery, state: FSMContext):
    """Handle removing the welcome image."""
    user_id = callback_query.from_user.id

    # Check if user has owner or privileged owner access
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 Access denied. Only owners and privileged users can manage welcome messages.",
            show_alert=True,
        )
        return

    # Get current welcome message settings
    welcome_settings = await get_welcome_message_async()

    if not welcome_settings.get("image_path"):
        await callback_query.answer("There is no image to remove.", show_alert=True)
        return

    # Save the settings without the image
    success = await save_welcome_message_async(
        enabled=welcome_settings.get("enabled", False),
        message_text=welcome_settings.get(
            "message_text", "Welcome to our platform! We're glad to have you here."
        ),
        image_path=None,
    )

    if success:
        await callback_query.answer(
            "Welcome message image removed successfully!", show_alert=True
        )

        # Log the action
        await log_admin_action(
            callback_query.bot,
            user_id,
            "welcome_message_image_remove",
            details=f"User {callback_query.from_user.full_name} removed the welcome message image",
        )

        # Refresh the welcome message management menu
        await manage_welcome_message(callback_query, state)
    else:
        await callback_query.answer(
            "Failed to remove welcome message image. Please try again.", show_alert=True
        )


@router.callback_query(F.data == "preview_welcome_message")
async def preview_welcome_message(callback_query: CallbackQuery, state: FSMContext):
    """Preview the welcome message as it will appear to users."""
    user_id = callback_query.from_user.id

    # Check if user has owner or privileged owner access
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer(
            "🚫 Access denied. Only owners and privileged users can manage welcome messages.",
            show_alert=True,
        )
        return

    # Get current welcome message settings
    welcome_settings = await get_welcome_message_async()
    message_text = welcome_settings.get(
        "message_text", "Welcome to our platform! We're glad to have you here."
    )
    image_path = welcome_settings.get("image_path")
    is_enabled = welcome_settings.get("enabled", False)

    # Create preview message
    preview_header = (
        "👁️ <b>WELCOME MESSAGE PREVIEW</b> 👁️\n\n"
        f"<b>Status:</b> {'✅ Enabled' if is_enabled else '❌ Disabled'}\n\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n"
        "<b>PREVIEW</b>\n"
        "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
    )

    # Set state to preview message
    await state.set_state(WelcomeMessageStates.preview_message)

    try:
        if image_path:
            # Get the full path to the image
            import os

            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            full_path = os.path.join(base_dir, "uploads", image_path)

            if os.path.exists(full_path):
                # Check if it's a GIF file
                is_gif = full_path.lower().endswith(".gif")

                if is_gif:
                    # Send as animation/document for GIFs
                    try:
                        # First try to send as animation
                        await callback_query.message.answer_animation(
                            animation=FSInputFile(full_path),
                            caption=f"{preview_header}{message_text}",
                            parse_mode="HTML",
                        )
                        logger.info(f"Successfully sent GIF as animation: {image_path}")
                    except Exception as e:
                        logger.warning(
                            f"Failed to send as animation, trying as document: {e}"
                        )
                        # If that fails, try as document
                        await callback_query.message.answer_document(
                            document=FSInputFile(full_path),
                            caption=f"{preview_header}{message_text}",
                            parse_mode="HTML",
                        )
                else:
                    # Send as photo for other image types
                    await callback_query.message.answer_photo(
                        photo=FSInputFile(full_path),
                        caption=f"{preview_header}{message_text}",
                        parse_mode="HTML",
                    )
            else:
                # Image file not found
                await callback_query.message.answer(
                    f"{preview_header}{message_text}\n\n<i>Note: Image file not found at path: {image_path}</i>",
                    parse_mode="HTML",
                )
        else:
            # Send the preview without image
            await callback_query.message.answer(
                f"{preview_header}{message_text}", parse_mode="HTML"
            )

        # Send a message with a button to return to welcome message management
        from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

        await callback_query.message.answer(
            "Click the button below to return to welcome message management.",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Welcome Message Management",
                            callback_data="manage_welcome_message",
                        )
                    ]
                ]
            ),
        )

    except Exception as e:
        logger.error(f"Error previewing welcome message: {e}", exc_info=True)
        await callback_query.answer(
            "An error occurred while previewing the welcome message.", show_alert=True
        )
        await manage_welcome_message(callback_query, state)
