"""
Additional edit handlers for bonus tier management.
This file contains the remaining edit handlers to keep the main file manageable.
"""

import logging
from datetime import datetime
from typing import Optional

from aiogram import Router, F
from aiogram.types import CallbackQuery, Message, InlineKeyboardMarkup, InlineKeyboardButton
from aiogram.fsm.context import FSMContext

from database.operations import (
    is_owner,
    get_bonus_tier_by_id,
    update_bonus_tier,
)
from handlers.sys_db import is_privileged
from utils.telegram_helpers import safe_edit_message
from states.states import BonusManagementStates

logger = logging.getLogger(__name__)
router = Router()


def validate_edit_session(data: dict) -> Optional[str]:
    """Validate edit session and return tier_id or None if invalid."""
    tier_id = data.get("editing_tier_id")
    if not tier_id:
        return None
    return tier_id


async def handle_session_recovery(message: Message, state: FSMContext, operation: str) -> bool:
    """
    Handle session recovery for interrupted edit operations.

    Args:
        message: Telegram message
        state: FSM context
        operation: Operation being performed

    Returns:
        bool: True if session was recovered, False if expired
    """
    try:
        from utils.bonus_tier_errors import tier_error_handler

        data = await state.get_data()
        tier_id = validate_edit_session(data)

        if not tier_id:
            # Session expired - provide recovery options
            error_info = tier_error_handler.handle_tier_operation_error(
                f"session_recovery_{operation}",
                Exception("Edit session expired"),
                user_id=message.from_user.id
            )

            recovery_message = (
                "⏱️ <b>Session Expired</b>\n\n"
                "Your editing session has expired for security reasons.\n\n"
                "💡 <b>What you can do:</b>\n"
                "• Go back to tier management\n"
                "• Select the tier you want to edit again\n"
                "• Your changes were not saved\n\n"
                "🔄 <i>Please start the edit process again.</i>"
            )

            from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="📊 View All Tiers", callback_data="bonus_view_all")],
                [InlineKeyboardButton(text="🔙 Back to Management", callback_data="bonus_management")]
            ])

            await message.reply(recovery_message, reply_markup=keyboard, parse_mode="HTML")
            await state.clear()
            return False

        return True

    except Exception as e:
        logger.error(f"Error in session recovery: {e}")
        await message.reply(
            "⚙️ <b>System Error</b>\n\n"
            "An error occurred while validating your session.\n"
            "Please start over from the tier management menu.",
            parse_mode="HTML"
        )
        await state.clear()
        return False


async def handle_edit_success(message: Message, state: FSMContext, field_name: str, new_value: str):
    """Handle successful edit operation."""
    await message.reply(
        f"✅ <b>{field_name} updated successfully!</b>\n\n"
        f"New {field_name.lower()}: {new_value}",
        parse_mode="HTML"
    )
    await state.clear()


async def handle_edit_failure(message: Message, state: FSMContext, field_name: str):
    """Handle failed edit operation."""
    await message.reply(
        f"❌ <b>Failed to update {field_name.lower()}!</b>\n\n"
        "There was a database error.",
        parse_mode="HTML"
    )
    await state.clear()


@router.message(BonusManagementStates.editing_threshold)
async def process_edit_threshold(message: Message, state: FSMContext):
    """Process the new threshold value with enhanced validation."""
    from utils.unified_validation import UnifiedValidation

    # Enhanced validation
    validation_result = UnifiedValidation.validate_bonus_tier_threshold(message.text.strip())

    if not validation_result["valid"]:
        await message.reply(
            f"❌ <b>Invalid threshold!</b>\n\n"
            f"{validation_result['error']}\n\n"
            "Please enter a valid threshold amount:\n"
            "• Must be greater than $0\n"
            "• Maximum allowed: $100,000\n"
            "• Example: <code>75.00</code>",
            parse_mode="HTML"
        )
        return

    new_threshold = validation_result["sanitized_threshold"]

    # Use enhanced session recovery
    if not await handle_session_recovery(message, state, "threshold_edit"):
        return

    # Get tier_id after successful session validation
    data = await state.get_data()
    tier_id = data.get("editing_tier_id")

    # Update the tier
    success = update_bonus_tier(tier_id, threshold=new_threshold)

    if success:
        await handle_edit_success(message, state, "Threshold", f"${new_threshold:.2f}")
    else:
        await message.reply(
            "❌ <b>Failed to update threshold!</b>\n\n"
            "This threshold might already exist or there was a database error.\n"
            "Please try a different value.",
            parse_mode="HTML"
        )
        await state.clear()


@router.message(BonusManagementStates.editing_percentage)
async def process_edit_percentage(message: Message, state: FSMContext):
    """Process the new percentage value with enhanced validation."""
    from utils.unified_validation import UnifiedValidation

    # Enhanced validation
    validation_result = UnifiedValidation.validate_bonus_percentage(message.text.strip())

    if not validation_result["valid"]:
        await message.reply(
            f"❌ <b>Invalid percentage!</b>\n\n"
            f"{validation_result['error']}\n\n"
            "Please enter a valid percentage:\n"
            "• Must be between 0 and 100%\n"
            "• Example: <code>15</code> for 15%\n"
            "• Example: <code>75</code> for 75%\n"
            "• Example: <code>100</code> for 100%",
            parse_mode="HTML"
        )
        return

    percentage_display = validation_result["sanitized_percentage"]
    bonus_percentage = validation_result["decimal_value"]

    data = await state.get_data()
    tier_id = data.get("editing_tier_id")

    if not tier_id:
        await message.reply("❌ Session expired. Please start over.", parse_mode="HTML")
        await state.clear()
        return

    # Update the tier
    success = update_bonus_tier(tier_id, bonus_percentage=bonus_percentage, bonus_type="percentage")

    if success:
        await message.reply(
            f"✅ <b>Percentage updated successfully!</b>\n\n"
            f"New percentage: {percentage_display:.1f}%",
            parse_mode="HTML"
        )
    else:
        await message.reply(
            "❌ <b>Failed to update percentage!</b>\n\n"
            "There was a database error. Please try again.",
            parse_mode="HTML"
        )

    await state.clear()


@router.message(BonusManagementStates.editing_fixed_amount)
async def process_edit_fixed_amount(message: Message, state: FSMContext):
    """Process the new fixed amount value with enhanced validation."""
    from utils.unified_validation import UnifiedValidation

    # Enhanced validation
    validation_result = UnifiedValidation.validate_bonus_fixed_amount(message.text.strip())

    if not validation_result["valid"]:
        await message.reply(
            f"❌ <b>Invalid fixed amount!</b>\n\n"
            f"{validation_result['error']}\n\n"
            "Please enter a valid fixed amount:\n"
            "• Must be greater than $0\n"
            "• Maximum allowed: $10,000\n"
            "• Example: <code>25.00</code>",
            parse_mode="HTML"
        )
        return

    new_fixed_amount = validation_result["sanitized_amount"]

    data = await state.get_data()
    tier_id = data.get("editing_tier_id")

    if not tier_id:
        await message.reply("❌ Session expired. Please start over.", parse_mode="HTML")
        await state.clear()
        return

    # Update the tier
    success = update_bonus_tier(tier_id, bonus_fixed_amount=new_fixed_amount, bonus_type="fixed")

    if success:
        await message.reply(
            f"✅ <b>Fixed amount updated successfully!</b>\n\n"
            f"New fixed amount: ${new_fixed_amount:.2f}",
            parse_mode="HTML"
        )
    else:
        await message.reply(
            "❌ <b>Failed to update fixed amount!</b>\n\n"
            "There was a database error. Please try again.",
            parse_mode="HTML"
        )

    await state.clear()


@router.message(BonusManagementStates.editing_description)
async def process_edit_description(message: Message, state: FSMContext):
    """Process the new description value with enhanced validation."""
    from utils.unified_validation import UnifiedValidation

    # Enhanced validation and sanitization
    validation_result = UnifiedValidation.validate_bonus_tier_description(message.text)

    if not validation_result["valid"]:
        await message.reply(
            f"❌ <b>Invalid description!</b>\n\n"
            f"{validation_result['error']}\n\n"
            "Please enter a valid description:\n"
            "• Maximum 200 characters\n"
            "• Type 'skip' to remove description\n"
            "• HTML/script content will be sanitized",
            parse_mode="HTML"
        )
        return

    new_description = validation_result["sanitized_description"]

    data = await state.get_data()
    tier_id = data.get("editing_tier_id")

    if not tier_id:
        await message.reply("❌ Session expired. Please start over.", parse_mode="HTML")
        await state.clear()
        return

    # Update the tier
    success = update_bonus_tier(tier_id, description=new_description)

    if success:
        display_desc = new_description if new_description else "No description"
        await message.reply(
            f"✅ <b>Description updated successfully!</b>\n\n"
            f"New description: <i>{display_desc}</i>",
            parse_mode="HTML"
        )
    else:
        await message.reply(
            "❌ <b>Failed to update description!</b>\n\n"
            "There was a database error. Please try again.",
            parse_mode="HTML"
        )

    await state.clear()


@router.callback_query(F.data.startswith("bonus_change_to_percentage:"))
async def bonus_change_to_percentage(callback_query: CallbackQuery, state: FSMContext):
    """Change bonus type to percentage."""
    user_id = callback_query.from_user.id
    
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return
    
    tier_id = callback_query.data.split(":", 1)[1]
    tier = get_bonus_tier_by_id(tier_id)
    
    if not tier:
        await callback_query.answer("❌ Tier not found", show_alert=True)
        return
    
    await state.update_data(editing_tier_id=tier_id)
    await state.set_state(BonusManagementStates.editing_percentage)
    await callback_query.answer()
    
    threshold = tier.get("threshold", 0)
    
    message_text = (
        "✏️ <b>• CHANGE TO PERCENTAGE •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>NEW PERCENTAGE VALUE</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f}\n"
        f"🎯 <b>New Type:</b> Percentage Bonus\n\n"
        "📈 <b>Enter the bonus percentage for this tier:</b>\n\n"
        "<i>Examples:</i>\n"
        "• <code>10</code> for 10% bonus\n"
        "• <code>15.5</code> for 15.5% bonus\n"
        "• <code>25</code> for 25% bonus\n\n"
        "⚠️ <i>Enter only the percentage number (without % symbol).</i>"
    )
    
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="❌ Cancel", callback_data=f"bonus_edit:{tier_id}")]
    ])
    
    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("bonus_change_to_fixed:"))
async def bonus_change_to_fixed(callback_query: CallbackQuery, state: FSMContext):
    """Change bonus type to fixed amount."""
    user_id = callback_query.from_user.id
    
    if not (is_owner(user_id) or is_privileged(user_id, role="owner")):
        await callback_query.answer("🚫 Access Denied", show_alert=True)
        return
    
    tier_id = callback_query.data.split(":", 1)[1]
    tier = get_bonus_tier_by_id(tier_id)
    
    if not tier:
        await callback_query.answer("❌ Tier not found", show_alert=True)
        return
    
    await state.update_data(editing_tier_id=tier_id)
    await state.set_state(BonusManagementStates.editing_fixed_amount)
    await callback_query.answer()
    
    threshold = tier.get("threshold", 0)
    
    message_text = (
        "✏️ <b>• CHANGE TO FIXED AMOUNT •</b>\n\n"
        "━━━━━━━━━━━━━━━━━━\n"
        "<b>NEW FIXED AMOUNT VALUE</b>\n"
        "━━━━━━━━━━━━━━━━━━\n\n"
        f"💰 <b>Threshold:</b> ${threshold:.2f}\n"
        f"🎯 <b>New Type:</b> Fixed Amount Bonus\n\n"
        "💵 <b>Enter the fixed bonus amount for this tier:</b>\n\n"
        "<i>Examples:</i>\n"
        "• <code>10.00</code> for $10 fixed bonus\n"
        "• <code>25.50</code> for $25.50 fixed bonus\n"
        "• <code>100</code> for $100 fixed bonus\n\n"
        "⚠️ <i>Enter only the dollar amount (without $ symbol).</i>"
    )
    
    keyboard = InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="❌ Cancel", callback_data=f"bonus_edit:{tier_id}")]
    ])
    
    await safe_edit_message(
        callback_query.message,
        message_text,
        reply_markup=keyboard,
        parse_mode="HTML"
    )
