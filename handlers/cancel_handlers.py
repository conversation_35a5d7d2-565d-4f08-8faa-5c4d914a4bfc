from aiogram import Router, F, types
from aiogram.fsm.context import FSMContext
from aiogram.types import Callback<PERSON>uery, InlineKeyboardMarkup, InlineKeyboardButton
from keyboards.deposit_kb import deposit_cancel_keyboard
from keyboards.common_kb import back_to_menu_keyboard
from utils.state_helpers import clear_state_data

# from keyboards.product_kb import (
#     browse_products_keyboard,
#     cancel_add_product_keyboard,
#     cancel_checkout_keyboard,
# )
from utils.function_tracking import mark_as_used, register_usage

router = Router()
router.name = "cancel_handlers"


@router.callback_query(F.data == "cancel_deposit")
@mark_as_used
async def cancel_deposit(callback_query: CallbackQuery, state: FSMContext):
    """Primary handler for canceling deposit."""
    await callback_query.answer()
    await clear_state_data(state)  # Use our safer clear function
    await callback_query.message.edit_text(
        "Deposit process cancelled.", reply_markup=deposit_cancel_keyboard()
    )


@router.callback_query(F.data == "cancel_purchase")
@mark_as_used
async def cancel_purchase(callback_query: CallbackQuery, state: FSMContext):
    """Cancel the purchase process."""
    await callback_query.answer()
    await clear_state_data(state)  # Use our safer clear function
    await callback_query.message.edit_text(
        "✅ Purchase cancelled.", reply_markup=back_to_menu_keyboard()
    )


@router.callback_query(F.data == "cancel_checkout")
@mark_as_used
async def cancel_checkout(callback_query: CallbackQuery, state: FSMContext):
    """Primary handler for canceling checkout."""
    await callback_query.answer()
    await clear_state_data(state)  # Use our safer clear function
    await callback_query.message.edit_text(
        "Checkout cancelled.", reply_markup=back_to_menu_keyboard()
    )


@router.callback_query(F.data == "cancel_admin_reply")
@mark_as_used
async def cancel_admin_reply(callback_query: CallbackQuery, state: FSMContext):
    """Primary handler for canceling admin reply."""
    await callback_query.answer()
    await clear_state_data(state)  # Use our safer clear function
    from keyboards.common_kb import cancel_admin_reply_keyboard

    await callback_query.message.edit_text(
        "Reply cancelled.", reply_markup=cancel_admin_reply_keyboard()
    )


@router.callback_query(F.data == "cancel_edit")
@mark_as_used
async def cancel_edit(callback_query: CallbackQuery, state: FSMContext):
    """Primary handler for canceling edit operations."""
    await callback_query.answer()
    await clear_state_data(state)  # Use our safer clear function
    from keyboards.common_kb import cancel_edit_keyboard

    await callback_query.message.edit_text(
        "Edit operation cancelled.", reply_markup=cancel_edit_keyboard()
    )


@router.callback_query(F.data == "cancel_add_product")
@mark_as_used
async def cancel_add_product(callback_query: CallbackQuery, state: FSMContext):
    """Primary handler for canceling product addition."""
    await callback_query.answer()
    await clear_state_data(state)  # Use our safer clear function
    from keyboards.common_kb import cancel_add_product_keyboard

    await callback_query.message.edit_text(
        "❌ Product addition cancelled.", reply_markup=cancel_add_product_keyboard()
    )


def register_cancel_handlers(dp):
    """Register all cancellation handlers with the dispatcher"""
    # Also register the keyboard functions to prevent them from being flagged as unused
    from keyboards.deposit_kb import deposit_pay_keyboard, custom_amount_cancel_keyboard

    register_usage(deposit_pay_keyboard, custom_amount_cancel_keyboard)

    dp.include_router(router)
