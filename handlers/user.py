import logging
import os
from datetime import datetime
from typing import Optional, Dict, Any, Union  # Added for type hinting

# Aiogram imports
from aiogram import Bo<PERSON>, Router, F, types, Dispatcher
from aiogram.fsm.context import FSMContext
from aiogram.filters import Command
from aiogram.filters.state import StateFilter
from aiogram.types import (
    InlineKeyboardMarkup,
    InlineKeyboardButton,
    Message,
    CallbackQuery,
)
from aiogram.fsm.state import StatesGroup, State
from aiogram.exceptions import (
    TelegramAPIError,
)  # Import specific exception for better handling
from handlers.sys_db import is_privileged

# Project imports (assuming correct paths)
from config import (
    ADMIN_ID,
    OWNER_ID,
    SUPPORT_USERNAME,
    SUPPORT_EMAIL,
    SUPPORT_WEBSITE,
    MAINTENANCE_MODE,  # Import maintenance mode flag
)


from utils.helpers import get_admins, format_currency, safe_format_datetime  # Added format_currency
from utils.telegram_helpers import safe_edit_message
from utils.template_helpers import format_text
from utils.state_helpers import safe_update_data, clear_state_data
from database.operations import (
    get_or_create_cart,
    clear_cart,
    add_transaction,
    update_user_balance,
    get_user_balance,
    get_user_transactions,
    is_banned,
    get_ban_info,
    get_user_orders,
    get_user_role,
    save_user_data,
    log_support_message_async,
    save_support_message,  # Keep if async version might fail
    add_user_reply_to_support_message,
    update_cart,  # Added import
    get_user_active_support_threads,
    get_welcome_message_async,
)
from states.states import DepositStates, PurchaseStates, OrderStates, SupportStates
from keyboards.user_kb import (
    balance_keyboard,
    transactions_keyboard,
    remove_item_keyboard,  # Added import
    shop_keyboard,
)
from keyboards.support_kb import (
    support_keyboard,
    support_thread_reply_keyboard,
    support_message_cancel_keyboard,
    support_message_sent_keyboard,
    support_message_retry_keyboard,
)
from keyboards.user_order_kb import empty_orders_keyboard, orders_view_keyboard
from keyboards.purchase_kb import (
    deposit_keyboard,
    checkout_keyboard,
    insufficient_balance_keyboard,
)
from keyboards.main_menu import main_menu_keyboard

logger = logging.getLogger(__name__)

# Create router instance
router = Router()

# --- Constants for Reusable Text Elements ---
# These are kept for backward compatibility but should be replaced with templates
TEXT_DECORATOR = "━━━━━━━━━━━━━━━━━━"
TEXT_ERROR_HEADER = "❌ <b>\u2022 ERROR \u2022</b>"
TEXT_DEFAULT_ERROR_BODY = "\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<i>An unexpected error occurred. Please try again later or contact support.</i>\n<b>━━━━━━━━━━━━━━━━━━</b>"
TEXT_ACCESS_DENIED_HEADER = "🚫 <b>\u2022 ACCESS DENIED \u2022</b>"
TEXT_MAIN_MENU_HEADER = "🏠 <b>\u2022 MAIN MENU \u2022</b>"

# --- Helper Functions ---


def get_support_contacts() -> Dict[str, str]:
    """Gets support contact details from config with fallbacks."""
    return {
        "admin_username": SUPPORT_USERNAME or "admin",
        "support_email": SUPPORT_EMAIL or "<EMAIL>",
        "support_website": SUPPORT_WEBSITE or "example.com/support",
        "support_phone": "+****************",  # Example fallback
    }


def get_faqs() -> list:
    """
    Get frequently asked questions from the faq.json template file.
    """
    from utils.template_manager import TemplateManager

    template_manager = TemplateManager()
    faq_data = template_manager.get_template("faq.json")

    # Return the FAQs from the template, or an empty list if not found
    return faq_data.get("faqs", [])


async def _send_error_message(
    message_or_callback_query: Union[Message, CallbackQuery],
    log_message: str,
    error_text_key: str = "error_message",
    context: str = "default",
):
    """Sends a generic error message and logs the error."""
    logger.error(f"Error in {context}: {log_message}")
    # Use the template for error messages
    error_text = format_text("user", error_text_key, error_text=log_message)

    try:
        if isinstance(message_or_callback_query, Message):
            await message_or_callback_query.reply(error_text, parse_mode="HTML")
        elif isinstance(message_or_callback_query, CallbackQuery):
            # Edit the original message if possible, otherwise send reply
            await safe_edit_message(
                message_or_callback_query.message, error_text, parse_mode="HTML"
            )
        # Optionally answer the callback query
        # await message_or_callback_query.answer("An error occurred.", show_alert=True)
    except Exception as e:
        logger.error(f"Failed to send error message to user: {e}")


async def _check_ban_and_reply(message: Message) -> bool:
    """Checks if user is banned and replies if they are. Returns True if banned."""
    user_id = message.from_user.id
    if is_banned(user_id):
        ban_info = get_ban_info(user_id)
        reason = (
            ban_info.get("reason", "No reason provided")
            if ban_info
            else "No reason provided"
        )
        ban_text = format_text("user", "access_denied", reason=reason)
        await message.reply(ban_text, parse_mode="HTML")
        return True
    return False


# --- Command Handlers ---


@router.message(Command("start"))
async def universal_start_handler(message: Message, state: FSMContext):
    """Handle /start command regardless of the current state."""
    current_state = await state.get_state()
    if current_state is not None:
        logger.info(f"Clearing state {current_state} due to /start command")
        await clear_state_data(state)  # Use our safer clear function

    # Redirect to the main start handler which already has logic for maintenance mode checks
    await cmd_start(message, state)


async def cmd_start(message: Message, state: FSMContext):
    """Send welcome message with role-based menu."""
    # First check if maintenance mode is active and user is not admin
    from config import MAINTENANCE_MODE
    from utils.helpers import get_admins

    user_id = message.from_user.id

    # If maintenance mode is active and user is not an admin or privileged user, show maintenance message
    if MAINTENANCE_MODE and user_id not in get_admins() and not is_privileged(user_id):
        maintenance_message = format_text(
            "user",
            "maintenance_active",
            default=(
                "🛠️ <b>\u2022 MAINTENANCE IN PROGRESS \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>SYSTEM UNAVAILABLE</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "<i>Our system is currently undergoing scheduled maintenance and is temporarily unavailable.</i>\n\n"
                "<i>We appreciate your patience as we work to improve our services.</i>"
            ),
        )
        await message.reply(maintenance_message, parse_mode="HTML")
        return

    if await _check_ban_and_reply(message):
        return

    await clear_state_data(state)  # Use our safer clear function

    save_user_data(
        user_id, name=message.from_user.full_name, username=message.from_user.username
    )

    # Check privileges/role

    if is_privileged(user_id):
        from handlers.admin import send_admin_welcome

        await send_admin_welcome(message, "owner")
    else:
        role = get_user_role(user_id)
        if role in ("owner", "admin"):
            from handlers.admin import send_admin_welcome

            await send_admin_welcome(message, role)
        else:
            # Get welcome message settings
            welcome_settings = await get_welcome_message_async()
            is_welcome_enabled = welcome_settings.get("enabled", False)
            welcome_message_text = welcome_settings.get("message_text", "")
            welcome_image_path = welcome_settings.get("image_path")

            # Get the standard greeting
            greeting = format_text(
                "user",
                "start_message",
                user_name=message.from_user.full_name,
            )

            # If welcome message is enabled, display it with image if available
            if is_welcome_enabled:
                # Combine welcome message and greeting into a single message
                combined_message = (
                    f"{welcome_message_text}\n\n{greeting}"
                    if welcome_message_text
                    else greeting
                )

                if welcome_image_path:
                    # Get the full path to the image
                    import os

                    base_dir = os.path.dirname(
                        os.path.dirname(os.path.abspath(__file__))
                    )
                    full_path = os.path.join(base_dir, "uploads", welcome_image_path)

                    if os.path.exists(full_path):
                        # Check if it's a GIF file
                        is_gif = full_path.lower().endswith(".gif")
                        from aiogram.types import FSInputFile

                        if is_gif:
                            # Send as animation for GIFs
                            try:
                                await message.answer_animation(
                                    animation=FSInputFile(full_path),
                                    caption=combined_message,
                                    parse_mode="HTML",
                                    reply_markup=main_menu_keyboard(),
                                )
                                logger.info(
                                    f"Successfully sent welcome GIF as animation: {welcome_image_path}"
                                )
                            except Exception as e:
                                logger.warning(
                                    f"Failed to send welcome GIF as animation: {e}"
                                )
                                # Fallback to sending as photo
                                await message.reply_photo(
                                    photo=FSInputFile(full_path),
                                    caption=combined_message,
                                    parse_mode="HTML",
                                    reply_markup=main_menu_keyboard(),
                                )
                        else:
                            # Send as photo for other image types
                            await message.reply_photo(
                                photo=FSInputFile(full_path),
                                caption=combined_message,
                                parse_mode="HTML",
                                reply_markup=main_menu_keyboard(),
                            )
                    else:
                        # Image file not found, just send combined message
                        await message.reply(
                            combined_message,
                            reply_markup=main_menu_keyboard(),
                            parse_mode="HTML",
                        )
                else:
                    # No image, just send combined message
                    await message.reply(
                        combined_message,
                        reply_markup=main_menu_keyboard(),
                        parse_mode="HTML",
                    )
            else:
                # Welcome message disabled, just send the standard greeting
                await message.reply(
                    greeting, reply_markup=main_menu_keyboard(), parse_mode="HTML"
                )


@router.message(Command("help"))
async def cmd_help(message: types.Message):
    """Handle the /help command."""
    try:
        help_text = format_text("user", "help_message")
        await message.answer(help_text, parse_mode="HTML")
    except Exception as e:
        await _send_error_message(message, str(e), context="cmd_help")


@router.message(Command("support"))
async def cmd_support(message: types.Message):
    """Handle the /support command."""
    try:
        contacts = get_support_contacts()
        support_text = format_text("support", "support_message", **contacts)
        await message.answer(
            support_text, reply_markup=support_keyboard(), parse_mode="HTML"
        )
    except Exception as e:
        await _send_error_message(message, str(e), context="cmd_support")


@router.message(Command("balance"))
async def cmd_balance(message: Message):
    """Display the user's balance."""
    try:
        user_id = message.from_user.id
        balance = get_user_balance(user_id)
        balance_text = format_text("user", "balance_message", balance=balance)
        await message.reply(
            balance_text,
            reply_markup=balance_keyboard(),
            parse_mode="HTML",
        )
    except Exception as e:
        await _send_error_message(message, str(e), context="cmd_balance")


@router.message(Command("deposit"))
async def cmd_deposit(message: Message):
    """Handle the /deposit command."""
    if await _check_ban_and_reply(message):
        return
    try:
        user_id = message.from_user.id
        balance = get_user_balance(user_id)
        deposit_text = format_text("user", "deposit_funds_message", balance=balance)

        # Use the proper keyboard from deposit_kb.py
        from keyboards.deposit_kb import deposit_amount_keyboard

        # Create a formatted deposit message
        formatted_deposit_message = (
            format_text("user", "deposit_header", balance=balance)
            or f"{TEXT_DECORATOR}\n💎 <b>DEPOSIT DASHBOARD</b> 💎\n{TEXT_DECORATOR}\n\n"
            f"💰 <b>Current Balance:</b> <code>${balance:.2f}</code>\n\n"
        )

        # Combine header with deposit text
        full_message = f"{formatted_deposit_message}\n{deposit_text}"

        await message.reply(
            full_message,
            reply_markup=deposit_amount_keyboard(),
            parse_mode="HTML",
        )
    except Exception as e:
        await _send_error_message(message, str(e), context="cmd_deposit")


# --- Callback Query Handlers ---


@router.callback_query(F.data == "view_transactions")
async def view_transactions(callback_query: CallbackQuery):
    """Display the user's transaction history with styling from templates."""
    await callback_query.answer()
    user_id = callback_query.from_user.id
    try:
        transactions = get_user_transactions(user_id)  # Assuming returns list of dicts

        if not transactions:
            empty_text = format_text(
                "user",
                "empty_transactions",
                default=(
                    "❌ <b>\u2022 NO TRANSACTIONS \u2022</b>\n\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    "<b>EMPTY HISTORY</b>\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    "<i>Your transaction history is empty!</i>\n\n"
                    "<i>Browse our products when you're ready!</i>"
                ),
            )
            await safe_edit_message(
                callback_query.message,
                empty_text,
                reply_markup=transactions_keyboard(),
                parse_mode="HTML",
            )
            return

        # Use template for title
        title = format_text(
            "user",
            "transactions_title",
            default="📜 <b>\u2022 YOUR TRANSACTIONS \u2022</b>\n\n<b>━━━━━━━━━━━━━━━━━━</b>\n<b>FINANCIAL HISTORY</b>\n<b>━━━━━━━━━━━━━━━━━━</b>",
        )

        lines = [title, "\n<b>━━━━━━━━━━━━━━━━━━</b>\n"]
        lines.append("<i>Your financial activity records:</i>\n")

        for i, tx in enumerate(transactions, 1):
            ts = tx.get("timestamp")
            date_str = (
                ts.strftime("%d %b %Y, %H:%M")
                if isinstance(ts, datetime)
                else "Date unavailable"
            )
            amount = tx.get("amount", 0)
            tx_type = tx.get("type", "unknown").lower()

            # Transaction icons and symbols based on type
            if "deposit" in tx_type or "add" in tx_type:
                icon = "💰"
                symbol = "➕"
                type_display = "Deposit"
            elif "purchase" in tx_type or "remove" in tx_type:
                icon = "🛍️"
                symbol = "➖"
                type_display = "Purchase"
            elif "refund" in tx_type:
                icon = "♻️"
                symbol = "➕"
                type_display = "Refund"
            elif "bonus" in tx_type:
                icon = "🎁"
                symbol = "➕"
                type_display = "Bonus Credit"
            else:
                icon = "📊"
                symbol = "🔧"
                type_display = tx_type.replace("_", " ").title()

            # Format each transaction following the theme
            lines.append(f"<b>{icon} Transaction #{i}</b>")
            lines.append(f"🔹 <b>Type:</b> <i>{type_display}</i>")
            lines.append(f"🔹 <b>Amount:</b> {symbol} <code>${amount:.2f}</code>")
            lines.append(f"🔹 <b>Date:</b> <code>{date_str}</code>")
            lines.append("<b>━━━━━━━━━━━━━━━━━━</b>")

        text = "\n".join(lines)

        # Check for pending BTC payments and add warning
        from database.operations import get_user_pending_payments
        from handlers.payment_verification import is_btc_payment

        pending_payments = get_user_pending_payments(user_id)
        btc_pending_warning = ""

        if pending_payments:
            btc_count = 0
            for payment in pending_payments:
                payment_data = payment.get("api_response_data", {})
                if isinstance(payment_data, dict):
                    currency = payment_data.get("currency", "")
                    if is_btc_payment(currency):
                        btc_count += 1

            if btc_count > 0:
                btc_pending_warning = (
                    f"\n\n⚠️ <b>PENDING BITCOIN PAYMENTS</b>\n"
                    f"• {btc_count} BTC payment(s) awaiting network confirmation\n"
                    "• Bitcoin transactions require 2 confirmations for security\n"
                    "• These will appear in your history once confirmed"
                )

        # Add footer if transactions present
        if transactions:
            text += "\n\n<i>Thank you for your continued business!</i> ✨"

        # Add BTC warning if present
        if btc_pending_warning:
            text += btc_pending_warning

        # Trim if too long (Telegram limit is 4096)
        if len(text) > 4000:
            text = text[:3900]
            # Find last complete transaction (look for the divider)
            last_divider = text.rfind("<b>━━━━━━━━━━━━━━━━━━</b>")
            if last_divider > 0:
                text = text[: last_divider + len("<b>━━━━━━━━━━━━━━━━━━</b>")]
            text += "\n\n<i>Your extensive transaction history continues beyond this view...</i> 📜"

        await safe_edit_message(
            callback_query.message,
            text,
            reply_markup=transactions_keyboard(),
            parse_mode="HTML",
        )
    except Exception as e:
        await _send_error_message(callback_query, str(e), context="view_transactions")
        # Attempt to answer callback even on error
        try:
            await callback_query.answer(
                "Error loading your transaction history.", show_alert=True
            )
        except:
            pass


@router.callback_query(F.data == "view_orders")
async def view_orders(callback_query: types.CallbackQuery):
    """Display the user's order history with clean dividers and formatting."""
    await callback_query.answer()
    user_id = callback_query.from_user.id

    DIVIDER_MAIN = "━━━━━━━━━━━━━━━━━━━━━━"
    DIVIDER_SUB = "──────────────"

    try:
        # Fetch user orders
        orders = get_user_orders(user_id, limit=10)

        if not orders:
            # Get empty orders message from template
            empty_text = format_text(
                "user",
                "empty_orders",
                default=(
                    "📋 <b>\u2022 ORDER HISTORY \u2022</b>\n\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    "<b>NO ORDERS FOUND</b>\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    "<i>You have not placed any orders yet.</i>\n\n"
                    "<i>Browse our product catalog to make your first purchase.</i>"
                ),
            )
            await safe_edit_message(
                callback_query.message,
                empty_text,
                reply_markup=empty_orders_keyboard(),
                parse_mode="HTML",
            )
            return

        # Get orders title from template
        title = format_text(
            "user",
            "orders_title",
            default=(
                "📋 <b>\u2022 ORDER HISTORY \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>PURCHASE RECORDS</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>"
            ),
        )

        lines = [title, "\n<b>📋 Your Recent Orders:</b>\n"]

        for i, order in enumerate(orders, 1):
            # Get order date with fallbacks
            order_date_raw = order.get("timestamp", order.get("created_at"))
            order_date_str = (
                order_date_raw.strftime("%Y-%m-%d %H:%M")
                if isinstance(order_date_raw, datetime)
                else "Unknown date"
            )

            # Get order details with safe fallbacks
            order_id = order.get("order_id", f"#{i}")
            amount = order.get("amount", 0)
            items = order.get("items", [])

            # Format the order entry
            lines.append(f"<b>📦 ORDER <code>{order_id}</code></b>\n{DIVIDER_SUB}")
            lines.append(f"📆 <b>Date:</b> <code>{order_date_str}</code>")
            lines.append(f"💳 <b>Total:</b> <code>${amount:.2f}</code>")

            # Add items with proper formatting
            if items:
                lines.append("🛒 <b>Items:</b>")
                # Limit items displayed if there are too many
                if len(items) > 10:
                    for item in items[:8]:
                        lines.append(f"   \u2022 ✨ {item}")
                    lines.append(
                        f"   \u2022 ✨ <i>...and {len(items) - 8} more items</i>"
                    )
                else:
                    lines.extend([f"   \u2022 ✨ {item}" for item in items])
            else:
                lines.append("🛒 <b>Items:</b> Details unavailable")

            lines.append(DIVIDER_MAIN + "\n")

        # Join all lines and check message length
        orders_text = "\n".join(lines)

        # If the message exceeds Telegram's character limit, truncate smartly
        if len(orders_text) > 4000:
            # Find the last complete order divider before the limit
            last_divider_pos = orders_text.rfind(DIVIDER_MAIN, 0, 3900)

            if last_divider_pos > 0:
                orders_text = orders_text[: last_divider_pos + len(DIVIDER_MAIN)]
                orders_text += "\n\n<i>✨ Additional orders available but not shown due to message length</i>"
            else:
                # Fallback if we can't find a clean cut point
                orders_text = (
                    orders_text[:3900] + "...\n\n<i>✨ Order history continues</i>"
                )

        # Send the message with appropriate keyboard
        await safe_edit_message(
            callback_query.message,
            orders_text,
            reply_markup=orders_view_keyboard(),
            parse_mode="HTML",
        )
    except Exception as e:
        logger.error(f"Error in view_orders handler: {str(e)}", exc_info=True)
        await _send_error_message(callback_query, str(e), context="view_orders")
        try:
            await callback_query.answer(
                "Error loading orders. Please try again later.", show_alert=True
            )
        except Exception as callback_error:
            logger.error(f"Failed to answer callback: {callback_error}")
            pass


@router.message(DepositStates.waiting_for_amount)
async def process_deposit_amount(message: Message, state: FSMContext):
    """Process the deposit amount entered by the user."""
    try:
        amount = float(message.text.strip())
        min_deposit = 1.00  # Example minimum
        max_deposit = 10000.00  # Example maximum

        if not (min_deposit <= amount <= max_deposit):
            error_msg = format_text(
                "user", "invalid_deposit_range", min=min_deposit, max=max_deposit
            )
            await message.reply(error_msg, parse_mode="HTML")
            # Keep the state to allow user to retry
            return

        await state.update_data(deposit_amount=amount)
        invoice_id = f"INV-{message.from_user.id}-{int(datetime.now().timestamp())}"
        await state.update_data(invoice_id=invoice_id)

        keyboard = deposit_keyboard(
            amount, invoice_id
        )  # Assuming this keyboard exists and works
        amount_formatted = format_currency(amount)  # Use helper

        reply_text = format_text(
            "user",
            "deposit_payment_prompt",
            amount=amount_formatted,
            invoice_id=invoice_id,
        )
        if not reply_text:
            reply_text = (
                f"{TEXT_DECORATOR}\n💎 <b>PAYMENT PORTAL</b> 💎\n{TEXT_DECORATOR}\n\n"
                f"💰 <b>Amount:</b> <code>{amount_formatted}</code>\n"
                f"🔖 <b>Invoice ID:</b> <code>{invoice_id}</code>\n\n"
                f"✨ Select payment method:"
            )

        await message.reply(
            reply_text,
            reply_markup=keyboard,
            parse_mode="HTML",
        )
        await state.set_state(DepositStates.waiting_for_payment)

    except ValueError:
        error_msg = format_text("user", "invalid_numeric_input")
        await message.reply(error_msg, parse_mode="HTML")
        # Keep state
    except Exception as e:
        await _send_error_message(message, str(e), context="process_deposit_amount")
        await state.clear()  # Clear state on unexpected error


@router.callback_query(F.data == "my_orders")
async def view_my_orders(callback_query: types.CallbackQuery):
    """Display user's order history."""
    await callback_query.answer()
    user_id = callback_query.from_user.id

    # Get user's orders
    orders = get_user_orders(user_id, limit=10)

    if not orders:
        # Get empty orders message from template
        empty_orders_message = format_text(
            "user",
            "empty_order_history",
            default=(
                "📋 <b>\u2022 ORDER HISTORY \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>NO ORDERS FOUND</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "<i>You have not placed any orders yet.</i>\n\n"
                "<i>Browse our product catalog to make your first purchase.</i>"
            ),
        )

        await callback_query.message.edit_text(
            empty_orders_message,
            reply_markup=empty_orders_keyboard(),
            parse_mode="HTML",
        )
        return

    # Get order history title from template
    orders_title = format_text(
        "user",
        "order_history_title",
        default=(
            "📋 <b>\u2022 ORDER HISTORY \u2022</b>\n\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>\n"
            "<b>PURCHASE RECORDS</b>\n"
            "<b>━━━━━━━━━━━━━━━━━━</b>"
        ),
    )

    # Format orders into readable text
    orders_text = orders_title + "\n\n"
    orders_text += "<i>Your previous orders:</i>\n\n"

    for order_num, order in enumerate(orders, 1):
        # Format the order date
        order_date = order.get("timestamp", datetime.now()).strftime("%Y-%m-%d %H:%M")
        order_id = order.get("order_id", "N/A")
        amount = order.get("amount", 0)
        items = order.get("items", [])

        orders_text += f"<b>\u2022 ORDER #{order_id}</b>\n<b>━━━━━━━━━━━━━━━━━━</b>\n"
        orders_text += f"\u2022 <b>Date:</b> <code>{order_date}</code>\n"
        orders_text += f"\u2022 <b>Total:</b> <code>${amount:.2f}</code>\n"

        if items:
            orders_text += "\u2022 <b>Items:</b>\n"
            for item in items:
                orders_text += f"  - {item}\n"
        else:
            orders_text += "\u2022 <b>Items:</b> Details unavailable\n"

        orders_text += "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"

    # If the text is too long, trim it at a safe point to avoid breaking HTML tags
    if len(orders_text) > 4000:
        # Find the last complete line before the cutoff to avoid cutting HTML tags
        last_safe_newline = orders_text.rfind("\n", 0, 3900)
        if last_safe_newline > 0:
            orders_text = (
                orders_text[:last_safe_newline]
                + "\n\n<i>✨ Your extensive collection continues beyond this view</i>"
            )
        else:
            # Fallback to a conservative cutoff if no newline found
            orders_text = (
                orders_text[:3900]
                + "...\n\n<i>✨ Your extensive collection continues beyond this view</i>"
            )

    await callback_query.message.edit_text(
        orders_text, reply_markup=orders_view_keyboard(), parse_mode="HTML"
    )


@router.callback_query(F.data.startswith("return_to_main"))
async def return_to_main_menu(callback_query: CallbackQuery, state: FSMContext):
    """Return to main menu, clearing state."""
    await callback_query.answer()
    await clear_state_data(state)  # Always clear state when returning to main

    user_id = callback_query.from_user.id
    role = get_user_role(user_id)  # Fetch role again if needed

    try:
        if role in ("owner", "admin"):
            from keyboards.admin_kb import admin_main_keyboard

            is_owner = role == "owner"
            keyboard = admin_main_keyboard(is_owner=is_owner)
            admin_text = format_text(
                "admin" if role == "admin" else "owner",
                "admin_welcome",
                admin_name=callback_query.from_user.full_name,
                default=(
                    f"{'👑' if role == 'owner' else '🔧'} <b>\u2022 {role.upper()} PANEL \u2022</b>\n\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    f"<b>ADMINISTRATION TOOLS</b>\n"
                    f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"<i>Welcome, {callback_query.from_user.full_name}!</i>\n\n"
                    f"\u2022 Manage users and permissions\n"
                    f"\u2022 View system statistics\n"
                    f"\u2022 Configure bot settings\n"
                    f"\u2022 Access support tools\n\n"
                    f"<i>Select an option below to continue.</i>"
                ),
            )
            await safe_edit_message(
                callback_query.message,
                admin_text,
                reply_markup=keyboard,
                parse_mode="HTML",
            )
        else:
            # Get welcome message settings
            welcome_settings = await get_welcome_message_async()
            is_welcome_enabled = welcome_settings.get("enabled", False)
            welcome_message_text = welcome_settings.get("message_text", "")
            welcome_image_path = welcome_settings.get("image_path")

            # Get the standard main menu text
            main_menu_text = format_text(
                "user",
                "main_menu_message",
                user_name=callback_query.from_user.full_name,
            )

            # Combine welcome message and main menu text if welcome message is enabled
            if is_welcome_enabled:
                combined_message = (
                    f"{welcome_message_text}\n\n{main_menu_text}"
                    if welcome_message_text
                    else main_menu_text
                )

                if welcome_image_path:
                    # Get the full path to the image
                    import os

                    base_dir = os.path.dirname(
                        os.path.dirname(os.path.abspath(__file__))
                    )
                    full_path = os.path.join(base_dir, "uploads", welcome_image_path)

                    if os.path.exists(full_path):
                        # Check if it's a GIF file
                        is_gif = full_path.lower().endswith(".gif")
                        from aiogram.types import FSInputFile

                        # Instead of deleting, we'll send a new message and ignore edit errors
                        # This avoids the "no text in the message to edit" error

                        if is_gif:
                            # Send as animation for GIFs
                            try:
                                await callback_query.message.answer_animation(
                                    animation=FSInputFile(full_path),
                                    caption=combined_message,
                                    parse_mode="HTML",
                                    reply_markup=main_menu_keyboard(),
                                )
                                logger.info(
                                    f"Successfully sent welcome GIF as animation: {welcome_image_path}"
                                )
                            except Exception as e:
                                logger.warning(
                                    f"Failed to send welcome GIF as animation: {e}"
                                )
                                # Fallback to sending as photo
                                await callback_query.message.answer_photo(
                                    photo=FSInputFile(full_path),
                                    caption=combined_message,
                                    parse_mode="HTML",
                                    reply_markup=main_menu_keyboard(),
                                )
                        else:
                            # Send as photo for other image types
                            await callback_query.message.answer_photo(
                                photo=FSInputFile(full_path),
                                caption=combined_message,
                                parse_mode="HTML",
                                reply_markup=main_menu_keyboard(),
                            )
                    else:
                        # Image file not found, try to edit or send a new message
                        try:
                            await safe_edit_message(
                                callback_query.message,
                                combined_message,
                                reply_markup=main_menu_keyboard(),
                                parse_mode="HTML",
                            )
                        except Exception as edit_error:
                            logger.warning(
                                f"Could not edit message, sending new one: {edit_error}"
                            )
                            await callback_query.message.answer(
                                combined_message,
                                reply_markup=main_menu_keyboard(),
                                parse_mode="HTML",
                            )
                else:
                    # No image, try to edit or send a new message
                    try:
                        await safe_edit_message(
                            callback_query.message,
                            combined_message,
                            reply_markup=main_menu_keyboard(),
                            parse_mode="HTML",
                        )
                    except Exception as edit_error:
                        logger.warning(
                            f"Could not edit message, sending new one: {edit_error}"
                        )
                        await callback_query.message.answer(
                            combined_message,
                            reply_markup=main_menu_keyboard(),
                            parse_mode="HTML",
                        )
            else:
                # Welcome message disabled, try to edit or send a new message
                try:
                    await safe_edit_message(
                        callback_query.message,
                        main_menu_text,
                        reply_markup=main_menu_keyboard(),
                        parse_mode="HTML",
                    )
                except Exception as edit_error:
                    logger.warning(
                        f"Could not edit message, sending new one: {edit_error}"
                    )
                    await callback_query.message.answer(
                        main_menu_text,
                        reply_markup=main_menu_keyboard(),
                        parse_mode="HTML",
                    )
    except Exception as e:
        # Fallback if edit fails (e.g., message too old)
        logger.error(f"Error returning to main menu via edit: {e}")
        # Sending a new message as fallback
        try:
            if role in ("owner", "admin"):
                # Send new admin welcome
                pass  # Or resend welcome message
            else:
                # Get welcome message settings
                welcome_settings = await get_welcome_message_async()
                is_welcome_enabled = welcome_settings.get("enabled", False)
                welcome_message_text = welcome_settings.get("message_text", "")
                welcome_image_path = welcome_settings.get("image_path")

                # Get the standard main menu text
                main_menu_text = format_text(
                    "user",
                    "main_menu_message",
                    user_name=callback_query.from_user.full_name,
                )

                # Combine welcome message and main menu text if welcome message is enabled
                if is_welcome_enabled:
                    combined_message = (
                        f"{welcome_message_text}\n\n{main_menu_text}"
                        if welcome_message_text
                        else main_menu_text
                    )

                    if welcome_image_path:
                        # Get the full path to the image
                        import os

                        base_dir = os.path.dirname(
                            os.path.dirname(os.path.abspath(__file__))
                        )
                        full_path = os.path.join(
                            base_dir, "uploads", welcome_image_path
                        )

                        if os.path.exists(full_path):
                            # Check if it's a GIF file
                            is_gif = full_path.lower().endswith(".gif")
                            from aiogram.types import FSInputFile

                            if is_gif:
                                # Send as animation for GIFs
                                try:
                                    await callback_query.message.answer_animation(
                                        animation=FSInputFile(full_path),
                                        caption=combined_message,
                                        parse_mode="HTML",
                                        reply_markup=main_menu_keyboard(),
                                    )
                                    logger.info(
                                        f"Successfully sent welcome GIF as animation in fallback: {welcome_image_path}"
                                    )
                                except Exception as e:
                                    logger.warning(
                                        f"Failed to send welcome GIF as animation in fallback: {e}"
                                    )
                                    # Fallback to sending as photo
                                    await callback_query.message.answer_photo(
                                        photo=FSInputFile(full_path),
                                        caption=combined_message,
                                        parse_mode="HTML",
                                        reply_markup=main_menu_keyboard(),
                                    )
                            else:
                                # Send as photo for other image types
                                await callback_query.message.answer_photo(
                                    photo=FSInputFile(full_path),
                                    caption=combined_message,
                                    parse_mode="HTML",
                                    reply_markup=main_menu_keyboard(),
                                )
                        else:
                            # Image file not found, just send combined text
                            await callback_query.message.answer(
                                combined_message,
                                reply_markup=main_menu_keyboard(),
                                parse_mode="HTML",
                            )
                    else:
                        # No image, just send combined text
                        await callback_query.message.answer(
                            combined_message,
                            reply_markup=main_menu_keyboard(),
                            parse_mode="HTML",
                        )
                else:
                    # Welcome message disabled, just send main menu text
                    await callback_query.message.answer(
                        main_menu_text,
                        reply_markup=main_menu_keyboard(),
                        parse_mode="HTML",
                    )
        except Exception as send_err:
            logger.error(f"Failed to send new main menu message: {send_err}")
            await _send_error_message(
                callback_query, str(e), context="return_to_main_menu"
            )


@router.callback_query(F.data == "contact_support")
async def contact_support_options(
    callback_query: CallbackQuery, state: FSMContext = None
):
    """Show support options."""
    await callback_query.answer()
    try:
        contacts = get_support_contacts()
        support_text = format_text("support", "support_message", **contacts)
        await safe_edit_message(
            callback_query.message,
            support_text,
            reply_markup=support_keyboard(),
            parse_mode="HTML",
        )
    except Exception as e:
        await _send_error_message(
            callback_query, str(e), context="contact_support_options"
        )


TEXT_DECORATOR = "〰️" * 10


@router.callback_query(F.data == "send_support_message")
async def send_support_message_prompt(callback_query: CallbackQuery, state: FSMContext):
    """Prompt user to send a support message."""
    try:
        user_id = callback_query.from_user.id
        logger.info(f"Support message prompt triggered by user {user_id}")
        await callback_query.answer()

        # Check if user already has an active support thread
        active_threads = get_user_active_support_threads(user_id)

        if active_threads:
            # User has active threads, show them instead of allowing new creation
            thread = active_threads[0]  # Get the most recent active thread
            thread_id = str(thread.get("_id", ""))
            status = thread.get("status", "unknown")

            status_text = (
                "pending review" if status == "pending" else "active conversation"
            )

            # Get existing conversation message from template
            existing_conversation_message = format_text(
                "support",
                "existing_support_conversation",
                status_text=status_text,
                default=(
                    "⚠️ <b>\u2022 EXISTING CONVERSATION \u2022</b>\n\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    "<b>SUPPORT STATUS</b>\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    f"<i>You currently have an {status_text} with our support team.</i>\n\n"
                    "\u2022 Please continue your existing conversation\n"
                    "\u2022 Our team will respond as soon as possible\n"
                    "\u2022 New inquiries can be created after this conversation is resolved\n\n"
                    "<i>Thank you for your patience and understanding.</i>"
                ),
            )

            await safe_edit_message(
                callback_query.message,
                existing_conversation_message,
                reply_markup=InlineKeyboardMarkup(
                    inline_keyboard=[
                        [
                            InlineKeyboardButton(
                                text="📝 Continue Conversation",
                                callback_data=f"reply_to_support:{thread_id}",
                            )
                        ],
                        [
                            InlineKeyboardButton(
                                text="🔙 Back to Support", callback_data="support"
                            )
                        ],
                    ]
                ),
                parse_mode="HTML",
            )
            return

        # Check if state is already set to avoid double-setting
        current_state = await state.get_state()
        logger.info(
            f"Current state before setting waiting_for_message: {current_state}"
        )

        # Get support message prompt from template
        support_message_prompt = format_text(
            "support",
            "support_message_prompt",
            default=(
                "✉️ <b>\u2022 CONTACT SUPPORT \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>MESSAGE GUIDELINES</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "\u2022 Provide detailed information about your issue\n"
                "\u2022 Include relevant order numbers if applicable\n"
                "\u2022 You may attach screenshots or images\n\n"
                "<i>Type your message below or use the Cancel button to return.</i>"
            ),
        )

        # Original implementation for users without active threads
        await safe_edit_message(
            callback_query.message,
            support_message_prompt,
            reply_markup=support_message_cancel_keyboard(),
            parse_mode="HTML",
        )

        # Set state to wait for message
        await state.set_state(SupportStates.waiting_for_message)

    except Exception as e:
        logger.error(f"Error in send_support_message_prompt: {e}", exc_info=True)
        # Get service interruption message from template
        service_interruption = format_text(
            "user",
            "service_interruption",
            default=(
                "❌ <b>\u2022 SERVICE INTERRUPTION \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>TECHNICAL ISSUE</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "<i>We're experiencing technical difficulties processing your request.</i>\n\n"
                "<i>Please try again later or contact our support team if this issue persists.</i>"
            ),
        )

        await safe_edit_message(
            callback_query.message,
            service_interruption,
            parse_mode="HTML",
        )


@router.message(SupportStates.waiting_for_message)
async def debug_message_handler(message: Message, state: FSMContext):
    """Debug handler to check if messages are being processed."""
    logger.info(
        f"Message received in waiting_for_message state: {message.text[:50] if message.text else '[No text]'}"
    )
    # Log more detailed information about the message
    logger.info(f"Message content type: {message.content_type}")
    logger.info(f"From user: {message.from_user.id} ({message.from_user.full_name})")
    logger.info(f"Current state: {await state.get_state()}")
    logger.info(f"State data: {await state.get_data()}")

    # Let the original handler process the message
    return await process_support_message(message, state)


@router.message(SupportStates.waiting_for_reply)
async def debug_reply_handler(message: Message, state: FSMContext):
    """Debug handler to check if reply messages are being processed."""
    logger.info(
        f"Reply received in waiting_for_reply state: {message.text[:50] if message.text else '[No text]'}"
    )
    # Log more detailed information about the message
    logger.info(f"Reply content type: {message.content_type}")
    logger.info(f"From user: {message.from_user.id} ({message.from_user.full_name})")
    logger.info(f"Current state: {await state.get_state()}")
    logger.info(f"State data: {await state.get_data()}")

    # Let the original handler process the message
    return await process_support_message(message, state)


def register_user_handlers(dp: Dispatcher):
    """Register user-related handlers."""
    # Import router tools to handle registration safely
    from utils.router_tools import register_router_once
    import logging

    logger = logging.getLogger(__name__)

    try:
        # Use the router_tools utility to safely register the router
        register_router_once(dp, router)

        # Add explicit logging of handler registration
        logger.info(
            f"User router registered with {len(router.message.handlers)} message handlers"
        )
        logger.info(
            f"User router registered with {len(router.callback_query.handlers)} callback handlers"
        )
        logger.info("Support message handlers registered successfully")
    except Exception as e:
        logger.error(f"Failed to register user router: {e}")


from html import escape


@router.callback_query(F.data.startswith("reply_to_support:"))
async def reply_to_support_thread(callback_query: CallbackQuery, state: FSMContext):
    """Handle user clicking on reply button for a support thread."""
    try:
        await callback_query.answer()

        # Extract thread ID from callback data and ensure it's a string
        thread_id = str(callback_query.data.split(":")[1])
        user_id = callback_query.from_user.id
        logger.info(f"User {user_id} requested to reply to thread {thread_id}")

        # Store thread_id in state for later use
        await safe_update_data(state, thread_id=thread_id)

        # Import here to avoid circular imports
        from database.operations import get_support_thread

        # Get thread data from database
        thread_data = get_support_thread(thread_id)
        logger.info(
            f"Thread data retrieval result: {'success' if thread_data else 'failed'}"
        )

        if not thread_data:
            await callback_query.message.reply(
                "❌ <b>THREAD NOT FOUND</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "The support conversation you're looking for could not be found.\n"
                "Please create a new support request.",
                parse_mode="HTML",
                reply_markup=support_keyboard(),
            )
            return

        if thread_data.get("user_id") != user_id:
            await callback_query.message.reply(
                "🔒 <b>ACCESS DENIED</b>\n"
                "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
                "You don't have permission to access this conversation.",
                parse_mode="HTML",
            )
            return

        # THREAD INFO
        status = thread_data.get("status", "pending")
        timestamp = thread_data.get("timestamp")
        timestamp_str = safe_format_datetime(timestamp, "%d %b, %H:%M", "Unknown")

        # STATUS DISPLAY
        if status == "pending":
            status_display = "🔴 <i>Pending</i>"
        elif status == "replied":
            status_display = "🟢 <i>Replied</i>"
        elif status == "resolved":
            status_display = "✅ <i>Resolved</i>"
        else:
            status_display = "❓ <i>Unknown</i>"

        short_thread_id = str(thread_data.get("_id", ""))[-6:]

        thread_view = (
            f"🧵 <b>\u2022 SUPPORT THREAD #{short_thread_id} \u2022</b>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>YOUR THREAD</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
            f"🔹 <b>Status:</b> {status_display}\n"
            f"🔹 <b>Started:</b> <i>{timestamp_str}</i>\n\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
            f"<b>CONVERSATION HISTORY</b>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        )

        # INITIAL MESSAGE
        original_message = thread_data.get("message_text", "No content")
        if not isinstance(original_message, str):
            original_message = str(original_message)
        original_message = escape(original_message)

        thread_view += (
            f"<b>📩 Message #1 \u2022 💬</b>\n"
            f"<b>From:</b> <i>You</i>\n"
            f"<b>Time:</b> <i>{timestamp_str}</i>\n"
            f"<b>Content:</b>\n<pre>{original_message}</pre>\n"
            f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
        )

        replies = thread_data.get("replies", [])
        if replies:
            for i, reply in enumerate(replies, 2):
                try:
                    is_admin = reply.get("is_admin", False)
                    msg_type = "🛡️" if is_admin else "💬"
                    sender = (
                        f"<b>👨‍💼 @{escape(reply.get('admin_username', 'Support'))}</b>"
                        if is_admin
                        else "<i>You</i>"
                    )

                    reply_msg = reply.get("message", "No content")
                    if not isinstance(reply_msg, str):
                        reply_msg = str(reply_msg)

                    reply_time = reply.get("reply_time")
                    reply_time_str = (
                        reply_time.strftime("%d %b, %H:%M") if reply_time else "Unknown"
                    )

                    safe_reply_msg = escape(reply_msg)

                    thread_view += (
                        f"<b>📩 Message #{i} \u2022 {msg_type}</b>\n"
                        f"<b>From:</b> {sender}\n"
                        f"<b>Time:</b> <i>{reply_time_str}</i>\n"
                        f"<b>Content:</b>\n<pre>{safe_reply_msg}</pre>\n"
                        f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    )
                except Exception as e:
                    logger.error(f"Error formatting user reply {i}: {e}")
                    thread_view += (
                        f"<b>📩 Message #{i}</b> \u2022 [Error displaying message]\n"
                        f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    )

        if thread_data.get("has_media", False):
            thread_view += "<i>📎 This conversation contains media attachments not shown here.</i>\n\n"

        if len(thread_view) > 4096:
            thread_view = thread_view[:4000]
            if (
                "<" in thread_view[-100:]
                and ">" not in thread_view[thread_view.rfind("<") :]
            ):
                thread_view = thread_view[: thread_view.rfind("<")]
            thread_view += "\n\n<i>... [Thread truncated due to length]</i>"

        await callback_query.message.reply(thread_view, parse_mode="HTML")
        logger.info("Sent formatted conversation history")

        await callback_query.message.reply(
            "💎 <b>CONTINUE CONVERSATION</b> 💎\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "<b>📝 YOUR RESPONSE</b>\n\n"
            "✨ <i>Please compose your reply to our support team:</i>\n\n"
            "\u2022 Include any new details or questions\n"
            "\u2022 You can attach images or documents if needed\n"
            "\u2022 Our team will respond as soon as possible\n\n"
            "✨ <i>We appreciate your continued engagement.</i>",
            reply_markup=support_message_cancel_keyboard(),
            parse_mode="HTML",
        )

        await state.set_state(SupportStates.waiting_for_reply)
        logger.info(f"Set state to waiting_for_reply for user {user_id}")

    except Exception as e:
        logger.error(f"Error in reply_to_support_thread: {e}", exc_info=True)
        await callback_query.message.reply(
            "❌ <b>CONNECTION ISSUE</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "A momentary disruption occurred while accessing your support thread.\n"
            "✨ <i>Please try again or initiate a new inquiry.</i>",
            parse_mode="HTML",
        )


@router.message(SupportStates.waiting_for_message, SupportStates.waiting_for_reply)
async def process_support_message(message: Message, state: FSMContext):
    """Process the support message from the user."""
    logger.info(f"Entered process_support_message with user_id={message.from_user.id}")
    bot = message.bot

    try:
        user_id = message.from_user.id
        username = message.from_user.username or "No username"
        full_name = message.from_user.full_name or "Unknown"

        logger.info(f"Processing support message from user {user_id} ({full_name})")

        # Get current state to determine if this is a new message or a reply
        current_state = await state.get_state()
        logger.info(f"Current state: {current_state}")

        state_data = await state.get_data()
        logger.info(f"State data: {state_data}")

        thread_id = (
            state_data.get("thread_id")
            if current_state == SupportStates.waiting_for_reply
            else None
        )
        logger.info(f"Thread ID from state: {thread_id}")

        # Check message limit if replying to existing thread
        if thread_id and current_state == SupportStates.waiting_for_reply:
            from database.operations import can_send_more_messages

            if not can_send_more_messages(thread_id):
                await message.reply(
                    "⏳ <b>\u2022 AWAITING RESPONSE \u2022</b> ⏳\n\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    "<b>MESSAGE LIMIT REACHED</b>\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    "<i>Please wait for a support team response before sending additional messages.</i>\n\n"
                    "\u2022 You've reached the maximum consecutive message limit\n"
                    "\u2022 Our team is reviewing your previous messages\n"
                    "\u2022 You'll be able to reply once support responds\n\n"
                    "<i>Thank you for your patience.</i>",
                    parse_mode="HTML",
                    reply_markup=InlineKeyboardMarkup(
                        inline_keyboard=[
                            [
                                InlineKeyboardButton(
                                    text="🔄 Check for Replies",
                                    callback_data=f"check_thread_status:{thread_id}",
                                )
                            ],
                            [
                                InlineKeyboardButton(
                                    text="🔙 Back to Support",
                                    callback_data="contact_support",
                                )
                            ],
                        ]
                    ),
                )
                # Do not clear state, so user can try again later once admin replies
                return

        # Rest of your existing function...
        # Handle different message types
        has_content = False
        support_content = {}
        file_id = None
        file_type = None

        # Check for text message
        if message.text:
            logger.info(f"Message has text content: {message.text[:50]}...")
            support_text = message.text.strip()
            if not support_text or len(support_text) < 5:
                logger.warning("Support message too short, asking for more details")
                # Get message too brief message from template
                message_too_brief = format_text(
                    "support",
                    "message_too_brief",
                    default=(
                        "❌ <b>\u2022 MESSAGE TOO BRIEF \u2022</b>\n\n"
                        "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                        "<b>ADDITIONAL DETAILS NEEDED</b>\n"
                        "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                        "<i>Your message is too short for our team to provide effective assistance.</i>\n\n"
                        "<i>Please provide more specific details about your inquiry so we can better help you.</i>"
                    ),
                )

                await message.reply(
                    message_too_brief,
                    parse_mode="HTML",
                    reply_markup=support_message_retry_keyboard(thread_id),
                )
                return

            # If message is too long, truncate it for display
            if len(support_text) > 3000:
                display_text = support_text[:2997] + "..."
                support_content["text"] = support_text
            else:
                display_text = support_text
                support_content["text"] = support_text

            has_content = True

        # Handle other message types (photo, document, etc.)
        if message.photo:
            logger.info("Message contains a photo")
            file_id = message.photo[-1].file_id  # Get the highest resolution
            logger.info(f"Photo file_id: {file_id}")
            support_content["photo_id"] = file_id
            display_text = message.caption or "🖼️ <i>[Image attached]</i>"
            support_content["caption"] = display_text
            file_type = "photo"
            has_content = True

        elif message.document:
            logger.info(f"Message contains a document: {message.document.file_name}")
            file_id = message.document.file_id
            support_content["document_id"] = file_id
            display_text = (
                message.caption or f"📎 <i>[Document: {message.document.file_name}]</i>"
            )
            support_content["caption"] = display_text
            file_type = "document"
            has_content = True

        elif message.video:
            logger.info("Message contains a video")
            file_id = message.video.file_id
            support_content["video_id"] = file_id
            display_text = message.caption or "🎬 <i>[Video attached]</i>"
            support_content["caption"] = display_text
            file_type = "video"
            has_content = True

        elif message.voice:
            logger.info("Message contains a voice message")
            file_id = message.voice.file_id
            support_content["voice_id"] = file_id
            display_text = message.caption or "🎤 <i>[Voice message attached]</i>"
            support_content["caption"] = display_text
            file_type = "voice"
            has_content = True

        # If no content was found
        if not has_content:
            logger.warning(
                "Message has no valid content, asking user to send valid message"
            )
            # Get invalid format message from template
            invalid_format_message = format_text(
                "user",
                "invalid_format",
                default=(
                    "❌ <b>\u2022 INVALID FORMAT \u2022</b>\n\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    "<b>UNSUPPORTED CONTENT</b>\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    "<i>Please send a text message, photo, document, video, or voice message.</i>\n\n"
                    "<i>Your detailed inquiry will help us provide better assistance.</i>"
                ),
            )

            await message.reply(
                invalid_format_message,
                parse_mode="HTML",
                reply_markup=support_message_retry_keyboard(thread_id),
            )
            return

        # Check if the user is privileged - if so, skip forwarding to admins/owner
        if is_privileged(user_id):
            logger.debug(
                f"Skipping support message forwarding for privileged user (ID: {user_id})"
            )
            owner_id = None
            admins = []
        else:
            # Get admin/owner IDs to forward the message to
            try:
                owner_id = OWNER_ID if isinstance(OWNER_ID, int) else None
                admins = get_admins() or []  # Ensure it's at least an empty list
                logger.info(
                    f"Retrieved owner_id: {owner_id}, admins count: {len(admins)}"
                )
                logger.debug(f"Admin IDs: {admins}")
            except Exception as e:
                logger.error(f"Error getting admin IDs: {e}", exc_info=True)
                owner_id = None
                admins = []

        # Process differently based on whether this is a new thread or a reply
        if current_state == SupportStates.waiting_for_reply and thread_id:
            logger.info(f"Processing reply to existing thread {thread_id}")
            try:
                # Handle media attachments for replies
                message_text = support_content.get("text", "")
                if not message_text and "caption" in support_content:
                    message_text = support_content.get("caption", "")

                # Include information about file attachments in the message
                if file_type:
                    logger.info(f"Including file attachment info of type: {file_type}")
                    file_info = f"[{file_type.upper()} ATTACHED]"
                    if not message_text:
                        message_text = file_info
                    else:
                        message_text = f"{message_text}\n\n{file_info}"

                # Save user reply to existing thread with media info
                logger.info(
                    f"Adding reply to thread {thread_id} with media: {file_type if file_type else 'None'}"
                )
                update_success = add_user_reply_to_support_message(
                    thread_id,
                    user_id,
                    full_name,
                    message_text,
                    file_id if file_type else None,  # Pass media_id
                    file_type,  # Pass media_type
                )

                if update_success:
                    logger.info(f"Successfully added user reply to thread {thread_id}")
                else:
                    logger.warning(f"Failed to add user reply to thread {thread_id}")
            except Exception as e:
                logger.error(f"Error adding user reply to thread: {e}", exc_info=True)
                update_success = False

            # Enhanced styling for user replies
            support_header = (
                f"💎 <b>\u2022 SUPPORT THREAD UPDATE \u2022</b>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>CLIENT INFORMATION</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"👤 <b>Name:</b> {full_name}\n"
                f"🆔 <b>User ID:</b> <code>{user_id}</code>\n"
                f"🧵 <b>Thread ID:</b> <code>{thread_id}</code>\n"
                f"🕒 <b>Timestamp:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>CLIENT RESPONSE</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"<pre>{support_content.get('text', '')}</pre>"
            )
        else:
            # This is a new support thread
            logger.info("Creating new support thread")
            try:
                message_text = support_content.get("text", "")
                if not message_text and "caption" in support_content:
                    message_text = support_content.get("caption", "")

                # Include information about file attachments in the message
                if file_type:
                    logger.info(f"Including file attachment info of type: {file_type}")
                    file_info = f"[{file_type.upper()} ATTACHED]"
                    if not message_text:
                        message_text = file_info
                    else:
                        message_text = f"{message_text}\n\n{file_info}"

                # Create new support thread with comprehensive information
                has_media = file_id is not None
                media_type = file_type if has_media else None
                logger.info(
                    f"Creating support thread with media: {media_type if media_type else 'None'}"
                )

                # Try to use async version first
                try:
                    logger.info(
                        "Attempting to use async version of log_support_message_async"
                    )
                    thread_id = await log_support_message_async(
                        user_id=user_id,
                        username=username,
                        message_text=message_text,
                        has_media=has_media,
                        media_type=media_type,
                        media_id=file_id,
                        user_name=full_name,
                    )

                    if thread_id:
                        logger.info(
                            f"Successfully created support thread with ID: {thread_id}"
                        )
                    else:
                        # If async version failed (returned None), use synchronous version
                        logger.warning(
                            "Async support message creation returned None, falling back to sync method"
                        )
                        thread = save_support_message(
                            user_id=user_id,
                            username=username,
                            message=message_text,
                            has_media=has_media,
                            media_type=media_type,
                            media_id=file_id,
                            user_name=full_name,
                        )
                        thread_id = str(thread.get("_id", "")) if thread else ""
                        logger.info(
                            f"Created support thread with ID: {thread_id} using fallback method"
                        )
                except Exception as e:
                    logger.error(
                        f"Error in async support message creation: {e}", exc_info=True
                    )
                    # Fallback to synchronous version
                    logger.info(
                        "Exception in async method, falling back to sync method"
                    )
                    thread = save_support_message(
                        user_id=user_id,
                        username=username,
                        message=message_text,
                        has_media=has_media,
                        media_type=media_type,
                        media_id=file_id,
                        user_name=full_name,
                    )
                    thread_id = str(thread.get("_id", "")) if thread else ""
                    logger.info(
                        f"Created support thread with ID: {thread_id} using exception fallback"
                    )

                # Verify that we have a valid thread ID
                if not thread_id:
                    logger.error(
                        "Failed to create support thread - both methods returned no thread ID"
                    )
                    raise ValueError(
                        "Failed to create support thread ID - both async and sync methods failed"
                    )

            except Exception as e:
                logger.error(f"Error saving support message: {e}", exc_info=True)
                thread_id = None

            # Enhanced styling for new inquiries
            support_header = (
                f"💎 <b>\u2022 NEW SUPPORT INQUIRY \u2022</b>\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>CLIENT INFORMATION</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"👤 <b>Name:</b> {full_name} (@{username})\n"
                f"🆔 <b>User ID:</b> <code>{user_id}</code>\n"
                f"🕒 <b>Timestamp:</b> {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n"
                f"<b>CLIENT INQUIRY</b>\n"
                f"<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                f"<pre>\n<b>{support_content.get('text', '')}</b></pre>"
            )

        sent_to_admins = []
        logger.info(f"Preparing to forward message to admins. Thread ID: {thread_id}")

        # Always try to send to owner first if available
        if owner_id:
            logger.info(f"Attempting to send message to owner: {owner_id}")
            admin_keyboard = support_thread_reply_keyboard(user_id, thread_id)

            try:
                if file_type:
                    logger.info(f"Sending {file_type} to owner with file_id: {file_id}")
                    # Send media with appropriate method based on type
                    if file_type == "photo":
                        await bot.send_photo(
                            chat_id=owner_id,
                            photo=file_id,
                            caption=f"{support_header}",
                            reply_markup=admin_keyboard,
                            parse_mode="HTML",
                        )
                    elif file_type == "document":
                        await bot.send_document(
                            chat_id=owner_id,
                            document=file_id,
                            caption=f"{support_header}",
                            reply_markup=admin_keyboard,
                            parse_mode="HTML",
                        )
                    elif file_type == "video":
                        await bot.send_video(
                            chat_id=owner_id,
                            video=file_id,
                            caption=f"{support_header}",
                            reply_markup=admin_keyboard,
                            parse_mode="HTML",
                        )
                    elif file_type == "voice":
                        await bot.send_voice(
                            chat_id=owner_id,
                            voice=file_id,
                            caption=f"{support_header}",
                            reply_markup=admin_keyboard,
                            parse_mode="HTML",
                        )
                else:
                    logger.info(f"Sending text-only message to owner: {owner_id}")
                    # Send text-only message
                    await bot.send_message(
                        chat_id=owner_id,
                        text=f"{support_header}",
                        reply_markup=admin_keyboard,
                        parse_mode="HTML",
                    )
                sent_to_admins.append(owner_id)
                logger.info(f"Successfully sent support message to owner: {owner_id}")
            except Exception as e:
                logger.error(
                    f"Error sending message to owner {owner_id}: {e}", exc_info=True
                )
                # Still continue to try other admins even if owner message fails

        # Send to all other admins not already notified
        for admin in admins:
            try:
                # Fix: Handle different admin data formats properly
                admin_id = None
                if isinstance(admin, dict):
                    admin_id = admin.get("user_id")
                    logger.debug(
                        f"Admin is dict format: {admin}, extracting user_id: {admin_id}"
                    )
                elif isinstance(admin, int):
                    admin_id = admin
                    logger.debug(f"Admin is integer format: {admin_id}")
                else:
                    logger.warning(
                        f"Admin in unexpected format: {type(admin)}: {admin}"
                    )

                # Convert string IDs to integers if needed
                if isinstance(admin_id, str) and admin_id.isdigit():
                    admin_id = int(admin_id)
                    logger.debug(f"Converted admin_id string to int: {admin_id}")

                if (
                    admin_id
                    and isinstance(admin_id, int)
                    and admin_id != owner_id
                    and admin_id not in sent_to_admins
                ):
                    logger.info(f"Attempting to send message to admin: {admin_id}")
                    admin_keyboard = support_thread_reply_keyboard(user_id, thread_id)

                    # Telegram has a 1024 character limit for captions
                    caption = support_header
                    if len(caption) > 1000:  # Leave some buffer
                        caption = caption[:997] + "..."
                        logger.debug(f"Caption truncated to {len(caption)} characters")

                    if file_type:
                        logger.info(f"Sending {file_type} to admin {admin_id}")
                        try:
                            if file_type == "photo":
                                await bot.send_photo(
                                    chat_id=admin_id,
                                    photo=file_id,
                                    caption=caption,
                                    reply_markup=admin_keyboard,
                                    parse_mode="HTML",
                                )
                            elif file_type == "document":
                                await bot.send_document(
                                    chat_id=admin_id,
                                    document=file_id,
                                    caption=caption,
                                    reply_markup=admin_keyboard,
                                    parse_mode="HTML",
                                )
                            elif file_type == "video":
                                await bot.send_video(
                                    chat_id=admin_id,
                                    video=file_id,
                                    caption=caption,
                                    reply_markup=admin_keyboard,
                                    parse_mode="HTML",
                                )
                            elif file_type == "voice":
                                await bot.send_voice(
                                    chat_id=admin_id,
                                    voice=file_id,
                                    caption=caption,
                                    reply_markup=admin_keyboard,
                                    parse_mode="HTML",
                                )
                        except TelegramAPIError as api_err:
                            # If caption is still too long or other API error
                            logger.warning(
                                f"Failed to send media: {api_err}. Trying without caption."
                            )
                            # Try again without caption
                            send_method = getattr(bot, f"send_{file_type}")
                            await send_method(
                                chat_id=admin_id,
                                **{file_type: file_id},
                                reply_markup=admin_keyboard,
                            )
                            # Send header separately as a message
                            await bot.send_message(
                                chat_id=admin_id,
                                text=support_header,
                                parse_mode="HTML",
                            )
                    else:
                        logger.info(f"Sending text-only message to admin {admin_id}")
                        await bot.send_message(
                            chat_id=admin_id,
                            text=f"{support_header}",
                            reply_markup=admin_keyboard,
                            parse_mode="HTML",
                        )
                    sent_to_admins.append(admin_id)
                    logger.info(
                        f"Successfully sent support message to admin: {admin_id}"
                    )
            except Exception as e:
                # Safer error logging with more defensive access of potentially missing admin ID
                admin_id_str = (
                    admin.get("user_id", "unknown")
                    if isinstance(admin, dict)
                    else str(admin) if admin is not None else "invalid_admin"
                )
                logger.error(
                    f"Error sending support message to admin {admin_id_str}: {e}",
                    exc_info=True,
                )
                # Continue trying other admins

        # Enhanced user confirmation messages
        if current_state == SupportStates.waiting_for_reply:
            logger.info("Sending reply delivered confirmation to user")
            # Get reply delivered message from template
            reply_delivered_message = format_text(
                "support",
                "reply_delivered",
                default=(
                    "✅ <b>\u2022 REPLY DELIVERED \u2022</b>\n\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    "<b>MESSAGE CONFIRMATION</b>\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    "<i>Your response has been forwarded to our support team:</i>\n\n"
                    "\u2022 Your message has been delivered successfully\n"
                    "\u2022 Your conversation thread remains active\n"
                    "\u2022 Our team will respond as soon as possible\n\n"
                    "<i>Thank you for your patience.</i>"
                ),
            )

            await message.reply(
                reply_delivered_message,
                reply_markup=support_message_sent_keyboard(),
                parse_mode="HTML",
            )
        else:
            logger.info("Sending inquiry received confirmation to user")
            # Get inquiry received message from template
            inquiry_received_message = format_text(
                "support",
                "inquiry_received",
                default=(
                    "✅ <b>\u2022 INQUIRY RECEIVED \u2022</b>\n\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                    "<b>REQUEST CONFIRMATION</b>\n"
                    "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                    "<i>Your support request has been registered:</i>\n\n"
                    "\u2022 Your inquiry has been logged in our system\n"
                    "\u2022 Our support team will review your request\n"
                    "\u2022 You will receive a response shortly\n\n"
                    "<i>We typically respond within 2-4 hours during business hours.</i>\n\n"
                    "<i>Thank you for contacting our support team.</i>"
                ),
            )

            await message.reply(
                inquiry_received_message,
                reply_markup=support_message_sent_keyboard(),
                parse_mode="HTML",
            )

        # Clear state now that the message has been processed
        logger.info("Clearing state after successful message processing")
        await clear_state_data(state)

    except Exception as e:
        logger.error(f"Error in process_support_message: {e}", exc_info=True)
        # Get service interruption message from template
        service_interruption = format_text(
            "user",
            "service_interruption",
            default=(
                "❌ <b>\u2022 SERVICE INTERRUPTION \u2022</b>\n\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n"
                "<b>TECHNICAL ISSUE</b>\n"
                "<b>━━━━━━━━━━━━━━━━━━</b>\n\n"
                "<i>An unexpected error occurred while processing your support request.</i>\n\n"
                "<i>Please try again later or contact us through alternative channels.</i>"
            ),
        )

        await message.reply(
            service_interruption,
            parse_mode="HTML",
        )
        await state.clear()

    logger.info("Exiting process_support_message function")


@router.callback_query(F.data == "show_contact_details")
async def show_contact_details(callback_query: CallbackQuery):
    """Show detailed contact information."""
    await callback_query.answer()
    try:
        contacts = get_support_contacts()
        contact_text = format_text(
            "user", "contact_details", **contacts
        )  # Create this template
        if not contact_text:
            contact_text = (
                f"{TEXT_DECORATOR}\n💎 <b>CONTACT DIRECTORY</b> 💎\n{TEXT_DECORATOR}\n\n"
                f"📱 Telegram: @{contacts['admin_username']}\n"
                f"📧 Email: {contacts['support_email']}\n"
                f"🌐 Portal: {contacts['support_website']}\n"
                f"☎️ Phone: {contacts['support_phone']}\n\n"
                "Support Hours: Mon-Fri 9AM-6PM EST"
            )
        await safe_edit_message(
            callback_query.message,
            contact_text,
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="⬅️ Back to Support", callback_data="contact_support"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
    except Exception as e:
        # Use logger, not print
        logger.error(f"Error in show_contact_details: {e}")
        await _send_error_message(
            callback_query, str(e), context="show_contact_details"
        )


@router.callback_query(F.data == "show_faqs")
async def show_faqs(callback_query: CallbackQuery):
    """Show frequently asked questions."""
    await callback_query.answer()
    try:
        from utils.template_manager import TemplateManager

        template_manager = TemplateManager()
        faq_data = template_manager.get_template("faq.json")

        faqs = faq_data.get("faqs", [])

        if not faqs:
            faq_text = "❓ No FAQs available at the moment."
        else:
            # Get title and footer from the faq.json template
            title = faq_data.get(
                "faq_title",
                f"{TEXT_DECORATOR}\n💎 <b>KNOWLEDGE BASE</b> 💎\n{TEXT_DECORATOR}",
            )
            footer = faq_data.get("faq_footer", "")

            lines = [title, "\n<b>━━━━ FAQ ━━━━</b>\n"]

            for i, faq in enumerate(faqs, 1):
                lines.append(
                    f"<b>{i}. {faq.get('question', 'Q')}</b>\n<i>{faq.get('answer', 'A')}</i>\n"
                )

            if footer:
                lines.append(footer)

            faq_text = "\n".join(lines)

        await safe_edit_message(
            callback_query.message,
            faq_text[:4096],  # Ensure length limit
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="⬅️ Back to Support", callback_data="contact_support"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
    except Exception as e:
        await _send_error_message(callback_query, str(e), context="show_faqs")


# --- Cart and Checkout Handlers ---


async def _display_cart(
    message_or_query: Union[Message, CallbackQuery],
    user_id: int,
    state: Optional[FSMContext] = None,
    prefix_message: str = "",
):
    """Helper to display the cart contents and keyboard."""
    try:
        # Get cart with error handling
        cart = get_or_create_cart(user_id)
        if not cart:
            logger.error(f"Failed to retrieve cart for user {user_id}")
            if isinstance(message_or_query, CallbackQuery):
                await message_or_query.answer(
                    "Error accessing your cart", show_alert=True
                )
            return False

        cart_items = cart.get("items", [])
        target_message = (
            message_or_query.message
            if isinstance(message_or_query, CallbackQuery)
            else message_or_query
        )

        # Handle empty cart
        if not cart_items:
            empty_cart_text = format_text("user", "empty_cart")
            if not empty_cart_text:
                empty_cart_text = f"{prefix_message}\n\n🛒 Your cart is empty. Explore our collection!"
            await safe_edit_message(
                target_message,
                empty_cart_text,
                reply_markup=shop_keyboard(),  # Provide a way to shop
                parse_mode="HTML",
            )
            if state:
                await clear_state_data(state)  # Clear order state if cart becomes empty
            return False  # Indicates cart is empty

        # Calculate total and get balance
        total = 0.0
        for item in cart_items:
            try:
                price = float(item.get("price", 0) or 0)
                total += price
            except (ValueError, TypeError):
                logger.warning(f"Invalid price format in cart item: {item.get('name')}")
                # Use 0 as fallback for invalid prices

        # Format the balance using the helper function for consistency
        balance = get_user_balance(user_id)  # Get current balance
        formatted_total = format_currency(total)
        formatted_balance = format_currency(balance)

        # Build the cart display
        cart_title = (
            format_text("user", "cart_title")
            or f"{TEXT_DECORATOR}\n🛒 <b>YOUR CART</b> 🛒\n{TEXT_DECORATOR}"
        )
        lines = [cart_title]
        if prefix_message:
            lines.append(prefix_message)
        lines.append("\n<b>Your Selections:</b>\n")

        # Add each item with proper formatting
        for i, item in enumerate(cart_items, 1):
            item_name = item.get("name", "Unknown Item")
            try:
                item_price = float(item.get("price", 0) or 0)
                price_display = f"<code>${item_price:.2f}</code>"
            except (ValueError, TypeError):
                price_display = "<code>$0.00</code>"

            lines.append(f"<b>{i}.</b> {item_name} — {price_display}")

        # Add total and balance information
        lines.append(f"\n<b>Total:</b> <code>{formatted_total}</code>")
        lines.append(f"<b>Balance:</b> <code>{formatted_balance}</code>")

        # Determine keyboard based on balance
        cart_text = "\n".join(lines)

        if balance < total:
            cart_text += "\n\n⚠️ <i>Insufficient balance. Please add funds to complete your purchase.</i>"
            keyboard = insufficient_balance_keyboard()  # Suggest depositing
            if state:
                await clear_state_data(
                    state
                )  # Clear order state if balance insufficient
        else:
            cart_text += "\n\n✨ <i>Ready to checkout?</i>"
            keyboard = checkout_keyboard()  # Show checkout button
            if state:
                await state.set_state(
                    OrderStates.confirm_order
                )  # Set state only if checkout is possible

        # Send the message with appropriate keyboard
        await safe_edit_message(
            target_message, cart_text[:4096], reply_markup=keyboard, parse_mode="HTML"
        )
        return True  # Indicates cart has items

    except Exception as e:
        logger.error(f"Error in _display_cart: {e}", exc_info=True)
        if isinstance(message_or_query, CallbackQuery):
            await message_or_query.answer("Error displaying cart", show_alert=True)
        return False


@router.callback_query(F.data == "checkout")
async def checkout_callback(callback_query: types.CallbackQuery, state: FSMContext):
    """Handle checkout button press - displays cart and confirmation."""
    await callback_query.answer()
    user_id = callback_query.from_user.id
    await _display_cart(callback_query, user_id, state)


@router.callback_query(F.data == "custom_amount")
async def custom_amount_clicked(callback_query: CallbackQuery, state: FSMContext):
    """Handle custom amount button click for deposit."""
    await callback_query.answer()
    try:
        min_deposit = 10.00  # Example
        max_deposit = 1000.00  # Example
        custom_deposit_message = format_text(
            "user",
            "custom_deposit_prompt",
            min=f"{min_deposit:.2f}",
            max=f"{max_deposit:.2f}",
        )
        if not custom_deposit_message:
            custom_deposit_message = f"✨ Please enter your deposit amount (min ${min_deposit:.2f}, max ${max_deposit:.2f}):"

        # Edit the message to ask for amount
        await safe_edit_message(
            callback_query.message,
            custom_deposit_message,
            # Remove keyboard or add a cancel button if desired
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="Cancel", callback_data="return_to_main"
                        )
                    ]
                ]
            ),
            parse_mode="HTML",
        )
        await state.set_state(DepositStates.waiting_for_amount)
    except Exception as e:
        await _send_error_message(
            callback_query, str(e), context="custom_amount_clicked"
        )
        await clear_state_data(state)


@router.callback_query(F.data == "remove_item_menu")
async def show_remove_item_menu(callback_query: types.CallbackQuery, state: FSMContext):
    """Show menu to select which item to remove from cart."""
    await callback_query.answer()
    user_id = callback_query.from_user.id
    cart = get_or_create_cart(user_id)
    cart_items = cart.get("items", [])

    if not cart_items:
        await _display_cart(callback_query, user_id, state)  # Show empty cart message
        return

    remove_item_title = (
        format_text("user", "remove_item_title") or "❌ Select item to remove:"
    )
    lines = [remove_item_title, ""]
    for i, item in enumerate(cart_items):
        lines.append(
            f"<b>{i+1}.</b> {item.get('name')} — <code>${item.get('price', 0):.2f}</code>"
        )

    cart_text = "\n".join(lines)

    try:
        # Generate keyboard with remove options
        keyboard = remove_item_keyboard(cart_items)  # Pass items to generate buttons
        await safe_edit_message(
            callback_query.message,
            cart_text,
            reply_markup=keyboard,
            parse_mode="HTML",
        )
    except Exception as e:
        await _send_error_message(
            callback_query, str(e), context="show_remove_item_menu"
        )


@router.callback_query(F.data.startswith("remove_cart_item:"))
async def remove_cart_item(callback_query: types.CallbackQuery, state: FSMContext):
    """Remove a specific item from the cart."""
    await callback_query.answer()
    user_id = callback_query.from_user.id

    try:
        # Extract the index from the callback data
        callback_data_parts = callback_query.data.split(":")
        if len(callback_data_parts) != 2:
            logger.warning(f"Invalid callback data format: {callback_query.data}")
            await callback_query.answer("❌ Invalid request format.", show_alert=True)
            await _display_cart(callback_query, user_id, state)
            return

        index_to_remove = int(callback_data_parts[1])

        # Get the current cart
        cart = get_or_create_cart(user_id)
        if not cart:
            logger.error(f"Failed to retrieve cart for user {user_id}")
            await callback_query.answer(
                "❌ Could not access your cart.", show_alert=True
            )
            await _display_cart(callback_query, user_id, state)
            return

        cart_items = cart.get("items", [])

        # Validate the index
        if not cart_items:
            logger.warning(f"User {user_id} tried to remove item from empty cart")
            await callback_query.answer("Your cart is already empty.", show_alert=True)
            await _display_cart(callback_query, user_id, state)
            return

        if 0 <= index_to_remove < len(cart_items):
            # Remove the item
            removed_item = cart_items.pop(index_to_remove)
            item_name = removed_item.get("name", "Unknown item")

            # Update the cart in the database
            update_success = update_cart(user_id, cart_items)

            if update_success:
                logger.info(
                    f"User {user_id} removed item {item_name} (index {index_to_remove})"
                )
                prefix = f"✅ Removed: {item_name}"
                await _display_cart(
                    callback_query, user_id, state, prefix_message=prefix
                )
            else:
                logger.error(
                    f"Failed to update cart for user {user_id} after removing item"
                )
                await callback_query.answer(
                    "❌ Failed to update your cart.", show_alert=True
                )
                # Refresh the cart view to show current state
                await _display_cart(callback_query, user_id, state)
        else:
            logger.warning(
                f"User {user_id} tried to remove invalid index {index_to_remove} (cart size: {len(cart_items)})"
            )
            await callback_query.answer(
                "❌ Item not found in your cart.", show_alert=True
            )
            await _display_cart(callback_query, user_id, state)

    except (ValueError, IndexError) as e:
        logger.error(
            f"Error processing remove_cart_item callback ({callback_query.data}): {e}"
        )
        await callback_query.answer("❌ Error processing request.", show_alert=True)
        await _display_cart(callback_query, user_id, state)
    except Exception as e:
        logger.error(f"Unexpected error in remove_cart_item: {e}", exc_info=True)
        await _send_error_message(callback_query, str(e), context="remove_cart_item")


# --- Default Handler ---


@router.message(StateFilter(None))  # Only processes messages when no state is active
async def default_message_handler(message: types.Message, state: FSMContext):
    """Handle messages that don't match commands when no state is active."""
    # Ignore messages from groups/channels or bots
    if message.chat.type != "private" or message.from_user.is_bot:
        return

    # Skip handling of system commands to ensure they're processed by their handlers
    if message.text and message.text.strip().lower() in ["/alpha", "/admin_panel"]:
        return

    # Check for banned users
    if await _check_ban_and_reply(message):
        return

    if message.text and message.text.startswith("/"):
        # Unknown command
        default_text = format_text("user", "unknown_command")
        if not default_text:
            default_text = (
                "❓ Command not recognized. Use /start or /help for available commands."
            )
        try:
            await message.reply(default_text, parse_mode="HTML")
        except TelegramAPIError as e:
            if "message to be replied not found" in str(e):
                # Fallback to sending a new message instead of reply
                await message.answer(default_text, parse_mode="HTML")
            else:
                # For other Telegram API errors, re-raise
                logger.error(f"Error replying to unknown command: {e}")
                try:
                    await message.answer(default_text, parse_mode="HTML")
                except Exception as final_e:
                    logger.error(f"Final attempt to send message failed: {final_e}")
    elif message.text:
        # Generic text message
        default_text = format_text("user", "generic_text_reply")
        if not default_text:
            default_text = "Thanks for your message! Please use the buttons or commands like /start."
        try:
            await message.reply(default_text, parse_mode="HTML")
        except TelegramAPIError as e:
            if "message to be replied not found" in str(e):
                await message.answer(default_text, parse_mode="HTML")
            else:
                logger.error(f"Error replying to text message: {e}")
                try:
                    await message.answer(default_text, parse_mode="HTML")
                except Exception as final_e:
                    logger.error(f"Final attempt to send message failed: {final_e}")
    else:
        # Media message
        default_text = format_text("user", "generic_media_reply")
        if not default_text:
            default_text = (
                "Media received. Please use the buttons or commands for interaction."
            )
        try:
            await message.reply(default_text, parse_mode="HTML")
        except TelegramAPIError as e:
            if "message to be replied not found" in str(e):
                await message.answer(default_text, parse_mode="HTML")
            else:
                logger.error(f"Error replying to media message: {e}")
                try:
                    await message.answer(default_text, parse_mode="HTML")
                except Exception as final_e:
                    logger.error(f"Final attempt to send message failed: {final_e}")


@router.callback_query(lambda c: c.data.startswith("check_thread_status:"))
async def check_user_thread_status(callback_query: CallbackQuery):
    """Allow users to check for new replies in their support thread."""
    try:
        await callback_query.answer("Checking for replies...")

        # Extract thread ID from callback data
        thread_id = callback_query.data.split(":")[1]

        # Use the admin handler for thread status checking to avoid code duplication
        from handlers.support_admin import check_thread_status

        # Call the admin handler - it handles permissions checking internally
        await check_thread_status(callback_query)

        logger.info(
            f"User {callback_query.from_user.id} checked thread status for thread {thread_id}"
        )
    except Exception as e:
        logger.error(f"Error in check_user_thread_status: {e}", exc_info=True)
        await callback_query.answer("Error checking thread status")
        await callback_query.message.reply(
            "❌ <b>CONNECTION ISSUE</b>\n"
            "<code>━━━━━━━━━━━━━━━━━━━━━━━━━━━</code>\n\n"
            "We couldn't check your thread status right now.\n"
            "✨ <i>Please try again in a few moments.</i>",
            parse_mode="HTML",
            reply_markup=InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Try Again", callback_data=callback_query.data
                        )
                    ],
                    [
                        InlineKeyboardButton(
                            text="🔙 Back to Support", callback_data="contact_support"
                        )
                    ],
                ]
            ),
        )


# --- Registration ---


def register_user_handlers(dp: Dispatcher):
    """Register user-related handlers."""
    # Note: The ban check middleware is defined (`check_if_banned`) but not applied here.
    # To apply it globally (use with caution, might interfere with states):
    # dp.message.outer_middleware(check_if_banned)
    # dp.callback_query.outer_middleware(check_if_banned)
    # Or apply selectively to the router:
    # router.message.middleware(check_if_banned) # Might need adjustment based on middleware execution order with StateFilter
    # router.callback_query.middleware(check_if_banned)

    from utils.router_tools import register_router_once  # Assuming this helper exists

    try:
        register_router_once(dp, router)
        logger.info(
            f"User router registered successfully with {len(router.message.handlers)} message and {len(router.callback_query.handlers)} callback handlers."
        )
    except Exception as e:
        logger.error(f"Failed to register user router: {e}", exc_info=True)
