# Core dependencies
aiogram==3.19.0           # Telegram bot framework
pymongo==4.12.0           # MongoDB driver
motor==3.7.0              # Async MongoDB driver
python-dotenv==1.1.0      # Environment variable management
certifi==2025.1.31        # Security certificates

# Payment processing
aiohttp==3.11.16            # Async HTTP client for payment API integration
requests==2.32.3          # HTTP client for synchronous API calls

# Date/time handling
pytz==2025.2              # Timezone handling
python-dateutil==2.9.0.post0  # Advanced date utilities

# MongoDB connection helpers
dnspython==2.7.0          # DNS resolution for MongoDB (needed for SRV connection strings)

# Data processing
pydantic==2.10.6           # Data validation and settings management
ujson==5.10.0             # Faster JSON processing

# Utilities
tenacity==8.3.0           # Retry logic for API calls
cachetools==5.4.0         # Caching for frequently accessed data
colorama==0.4.6           # Colored terminal text for interactive CLI
rich==13.7.0              # Rich text and beautiful formatting for terminal

# Cryptography for secure handling of payment data
cryptography==44.0.2      # Cryptography library
PyJWT==2.8.0              # JSON Web Tokens for secure payment verification
pyOpenSSL==25.0.0
urllib3==2.4.0
bcrypt==4.2.1             # Secure password hashing

# Web server for payment callbacks
waitress==3.0.2           # Production WSGI server
flask==3.1.0              # Web framework for payment callbacks

# CLI interface
prompt_toolkit==3.0.51    # Interactive CLI prompts