from aiogram.fsm.state import StatesGroup, State


class ProductManagementStates(StatesGroup):
    waiting_for_name = State()
    waiting_for_description = State()
    waiting_for_category = State()
    waiting_for_price = State()
    waiting_for_file_option = State()
    waiting_for_file_link = State()
    waiting_for_file_upload = State()
    waiting_for_text_content = State()
    waiting_for_image_url = State()
    waiting_for_image_option = State()
    waiting_for_image_upload = State()
    confirm_product = State()
    waiting_for_quantity = State()
    waiting_for_tags = State()
    editing_name = State()
    editing_description = State()
    editing_price = State()
    editing_file_link = State()
    editing_image_url = State()
    # Line-based inventory states
    waiting_for_inventory_upload = State()
    waiting_for_line_price = State()
    waiting_for_max_quantity = State()
    waiting_for_preview_format = State()
    waiting_for_inventory_mode = State()
    confirm_line_product = State()
    waiting_for_availability = State()


class CategoryManagementStates(StatesGroup):
    waiting_for_category_name = State()
    waiting_for_image_url = State()
    confirm_category = State()
    editing_category_name = State()
    editing_category_image = State()
    # Additional states from second definition
    waiting_for_category_description = State()
    waiting_for_image_upload = State()
    waiting_for_edit_name = State()
    waiting_for_edit_description = State()
    waiting_for_edit_image_url = State()
    waiting_for_edit_image_upload = State()


class UserManagementStates(StatesGroup):
    waiting_for_user_search = State()
    viewing_user = State()
    editing_balance = State()
    confirm_balance_edit = State()
    waiting_for_user_id = State()
    waiting_for_user_action = State()
    waiting_for_new_balance = State()
    # Additional states from second definition
    waiting_for_balance_amount = State()
    waiting_for_balance_note = State()
    waiting_for_search_term = State()
    message_edit_verification = State()
    # User profile states
    waiting_for_user_profile_id = State()
    viewing_user_profile = State()
    viewing_user_orders = State()
    viewing_user_transactions = State()


class DepositStates(StatesGroup):
    waiting_for_amount = State()
    waiting_for_confirmation = State()
    # Additional states from second definition
    waiting_for_payment = State()
    waiting_for_verification = State()


class PurchaseStates(StatesGroup):
    selecting_product = State()
    confirming_purchase = State()
    # Additional states from second definition
    waiting_for_payment = State()
    waiting_for_confirmation = State()
    confirm_purchase = State()


class OrderStates(StatesGroup):
    viewing_orders = State()
    # Additional states from second definition
    selecting_product = State()
    confirm_order = State()
    processing_payment = State()
    # Line-based product purchase states
    selecting_quantity = State()
    waiting_for_custom_quantity = State()
    confirm_line_purchase = State()


class CartStates(StatesGroup):
    viewing_cart = State()
    # Additional states from second definition
    removing_item = State()
    editing_item = State()


class AdminManagementStates(StatesGroup):
    adding_admin = State()
    removing_admin = State()
    # Additional states from second definition
    waiting_for_admin_id = State()
    waiting_for_confirmation = State()
    message_being_edited = State()


class AdditionalFeaturesStates(StatesGroup):
    main = State()
    # Additional states from second definition
    waiting_for_log_channel = State()
    waiting_for_template_value = State()
    waiting_for_template_key = State()


class BanUserStates(StatesGroup):
    waiting_for_user_id = State()
    waiting_for_reason = State()
    confirm_ban = State()


class UnbanUserStates(StatesGroup):
    waiting_for_user_id = State()
    confirm_unban = State()


class AnnouncementStates(StatesGroup):
    waiting_for_message = State()
    confirm_announcement = State()
    # Additional state from second definition
    confirm_send = State()


class SupportStates(StatesGroup):
    """States for support interactions."""

    waiting_for_message = State()
    waiting_for_reply = State()


class AdminStates(StatesGroup):
    """States for admin operations."""

    waiting_for_reply_message = State()
    waiting_for_resolution_note = State()
    editing_message = State()
    edit_message_context = State()
    waiting_for_category_name = State()  # State for adding a new category


class ProductEditStates(StatesGroup):
    """States for product editing."""

    product_id = State()
    waiting_for_name = State()
    waiting_for_description = State()
    waiting_for_price = State()
    waiting_for_file_option = State()
    waiting_for_file_link = State()
    waiting_for_file_upload = State()
    waiting_for_text_content = State()
    waiting_for_image_url = State()
    waiting_for_tags = State()
    # Additional states from second definition
    waiting_for_url = State()
    edit_message_id = State()
    message_has_text = State()
    selecting_content_type = State()
    waiting_for_link = State()
    waiting_for_file = State()
    waiting_for_text = State()
    editing_product = State()


class TemplateManagementStates(StatesGroup):
    """States for template management."""

    waiting_for_key_selection = State()
    waiting_for_template_edit = State()
    waiting_for_confirmation = State()
    current_template_file = State()
    current_template_key = State()


class FAQManagementStates(StatesGroup):
    """States for FAQ management."""

    waiting_for_question = State()
    waiting_for_answer = State()
    waiting_for_faq_selection = State()
    confirm_delete = State()
    confirm_add = State()
    confirm_edit = State()


class WelcomeMessageStates(StatesGroup):
    """States for welcome message management."""

    waiting_for_message_text = State()
    waiting_for_image_upload = State()
    confirm_changes = State()
    preview_message = State()


class SysAuth(StatesGroup):
    password = State()
    add_admin = State()
    add_owner = State()
    remove_admin = State()
    remove_owner = State()
    change_password = State()
    confirm_mongo_removal = State()
    edit_config = State()


class UnifiedUploadStates(StatesGroup):
    """States for unified file upload management."""

    waiting_for_file_upload = State()
    product_id = State()
    file_type = State()
    upload_context = State()


class BonusManagementStates(StatesGroup):
    """States for bonus tier management."""

    waiting_for_threshold = State()
    waiting_for_bonus_type = State()
    waiting_for_percentage = State()
    waiting_for_fixed_amount = State()
    waiting_for_description = State()

    # Edit states
    editing_threshold = State()
    editing_bonus_type = State()
    editing_percentage = State()
    editing_fixed_amount = State()
    editing_description = State()

    # Confirmation state
    confirm_tier = State()


class UnifiedProductStates(StatesGroup):
    """States for unified product management."""

    waiting_for_stock_update = State()
