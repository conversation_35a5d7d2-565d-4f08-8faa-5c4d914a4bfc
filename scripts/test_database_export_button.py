#!/usr/bin/env python3
"""
Test Database Export Button Functionality
Verifies that the database export button is working correctly.
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add the testbots directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_database_export_handler():
    """Test the database export handler directly."""
    print("🧪 Testing Database Export Handler...")
    
    try:
        # Import the handler
        from handlers.sys_db import database_export_menu
        from aiogram.types import CallbackQuery, User, Message, Chat
        from unittest.mock import AsyncMock, Mock
        
        # Create a mock callback query
        mock_user = User(id=12345, is_bot=False, first_name="Test", username="testuser")
        mock_chat = Chat(id=12345, type="private")
        mock_message = Message(
            message_id=1,
            date=1234567890,
            chat=mock_chat,
            from_user=mock_user,
            content_type="text"
        )
        
        mock_callback = CallbackQuery(
            id="test_callback",
            from_user=mock_user,
            chat_instance="test_instance",
            data="database_export_menu",
            message=mock_message
        )
        
        # Mock the answer method
        mock_callback.answer = AsyncMock()
        mock_message.edit_text = AsyncMock()
        
        # Mock the privilege check to return True
        import handlers.sys_db
        original_is_privileged = handlers.sys_db.is_privileged
        handlers.sys_db.is_privileged = lambda user_id, role: True
        
        # Mock safe_edit_message
        handlers.sys_db.safe_edit_message = AsyncMock()
        
        try:
            # Call the handler
            await database_export_menu(mock_callback)
            
            # Check if the handler was called without errors
            print("✅ Database export handler executed successfully")
            
            # Check if answer was called
            if mock_callback.answer.called:
                print("✅ Callback answer was called")
            else:
                print("⚠️  Callback answer was not called")
            
            # Check if safe_edit_message was called
            if handlers.sys_db.safe_edit_message.called:
                print("✅ Message edit was attempted")
                
                # Get the call arguments
                call_args = handlers.sys_db.safe_edit_message.call_args
                if call_args:
                    message_text = call_args[0][1]  # Second argument is the message text
                    if "DATABASE EXPORT MANAGEMENT" in message_text:
                        print("✅ Correct message content generated")
                    else:
                        print("❌ Incorrect message content")
                        print(f"Message preview: {message_text[:100]}...")
            else:
                print("❌ Message edit was not attempted")
            
            return True
            
        finally:
            # Restore original function
            handlers.sys_db.is_privileged = original_is_privileged
        
    except Exception as e:
        print(f"❌ Error testing database export handler: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_keyboard_integration():
    """Test that the database export button is in the keyboard."""
    print("\n⌨️ Testing Keyboard Integration...")
    
    try:
        from keyboards.admin_kb import additional_features_keyboard
        
        keyboard = additional_features_keyboard()
        
        # Check if database export button is in the keyboard
        found_button = False
        for row in keyboard.inline_keyboard:
            for button in row:
                if button.callback_data == "database_export_menu":
                    print(f"✅ Database export button found: \"{button.text}\" -> {button.callback_data}")
                    found_button = True
                    break
            if found_button:
                break
        
        if not found_button:
            print("❌ Database export button NOT found in keyboard")
            print("Available buttons:")
            for row in keyboard.inline_keyboard:
                for button in row:
                    print(f"  - \"{button.text}\" -> {button.callback_data}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing keyboard integration: {e}")
        return False

def test_handler_registration():
    """Test that the database export handlers are registered."""
    print("\n🔧 Testing Handler Registration...")
    
    try:
        # Check if the handlers exist
        from handlers.sys_db import (
            database_export_menu,
            database_export_create,
            database_export_format_selected,
            database_export_download,
            database_export_history
        )
        
        handlers = [
            "database_export_menu",
            "database_export_create", 
            "database_export_format_selected",
            "database_export_download",
            "database_export_history"
        ]
        
        print(f"✅ Found {len(handlers)} database export handlers")
        
        # Check if they have the correct decorators
        import inspect
        for handler_name in handlers:
            handler_func = globals().get(handler_name) or locals().get(handler_name)
            if handler_func:
                source = inspect.getsource(handler_func)
                if "@sys_router.callback_query" in source:
                    print(f"✅ {handler_name} has correct decorator")
                else:
                    print(f"❌ {handler_name} missing decorator")
        
        return True
        
    except ImportError as e:
        print(f"❌ Error importing handlers: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing handler registration: {e}")
        return False

def test_dependencies():
    """Test that all dependencies are available."""
    print("\n📚 Testing Dependencies...")
    
    try:
        # Test core modules
        from utils.database_export import get_export_formats, database_exporter
        print("✅ database_export module available")
        
        from utils.database_export_security import export_security
        print("✅ database_export_security module available")
        
        from utils.secure_download import download_manager
        print("✅ secure_download module available")
        
        from utils.export_progress_tracker import progress_tracker
        print("✅ export_progress_tracker module available")
        
        # Test functionality
        formats = get_export_formats()
        if len(formats) >= 4:
            print(f"✅ Export formats available: {list(formats.keys())}")
        else:
            print(f"❌ Insufficient export formats: {list(formats.keys())}")
            return False
        
        stats = database_exporter.get_export_statistics()
        if isinstance(stats, dict):
            print("✅ Export statistics working")
        else:
            print("❌ Export statistics not working")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing dependencies: {e}")
        return False

async def main():
    """Run all tests."""
    print("🚀 Database Export Button Test")
    print("=" * 50)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("Handler Registration", test_handler_registration),
        ("Keyboard Integration", test_keyboard_integration),
        ("Database Export Handler", test_database_export_handler),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Database export button should be working.")
        print("\n📋 To test manually:")
        print("1. Go to the bot in Telegram")
        print("2. Use /admin_panel command as an owner")
        print("3. Click 'Advanced Features'")
        print("4. Look for '💾 Database Export' button")
        print("5. Click it to test the functionality")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
