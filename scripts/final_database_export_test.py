#!/usr/bin/env python3
"""
Final Database Export Test
Comprehensive test to verify the database export button is working correctly.
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add the testbots directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_privilege_checking():
    """Test that privilege checking is working correctly."""
    print("🔐 Testing Privilege Checking...")
    
    try:
        from database.operations import is_owner, is_admin
        from database.connection import db
        
        # Get the actual owner from database
        admins = list(db.admins.find({"role": "owner"}))
        if not admins:
            print("❌ No owners found in database")
            return False
        
        owner = admins[0]
        owner_id = owner["user_id"]
        
        print(f"Testing with owner ID: {owner_id}")
        
        # Test privilege functions
        is_owner_result = is_owner(owner_id)
        is_admin_result = is_admin(owner_id)
        
        print(f"  is_owner({owner_id}): {is_owner_result}")
        print(f"  is_admin({owner_id}): {is_admin_result}")
        
        if is_owner_result and is_admin_result:
            print("✅ Privilege checking working correctly")
            return True
        else:
            print("❌ Privilege checking not working")
            return False
        
    except Exception as e:
        print(f"❌ Error testing privilege checking: {e}")
        return False

def test_database_export_security():
    """Test database export security validation."""
    print("\n🛡️ Testing Database Export Security...")
    
    try:
        from utils.database_export_security import validate_export_request
        from database.connection import db
        
        # Get owner ID
        admins = list(db.admins.find({"role": "owner"}))
        if not admins:
            print("❌ No owners found")
            return False
        
        owner_id = admins[0]["user_id"]
        
        # Test security validation
        request_data = {
            "format": "json",
            "collections": ["users", "products"]
        }
        
        result = validate_export_request(owner_id, "test_owner", request_data)
        
        print(f"Security validation result: {result}")
        
        if result.get("allowed"):
            print("✅ Security validation working correctly")
            return True
        else:
            print(f"❌ Security validation failed: {result.get('reason')}")
            return False
        
    except Exception as e:
        print(f"❌ Error testing security: {e}")
        return False

def test_keyboard_button():
    """Test that the database export button is in the keyboard."""
    print("\n⌨️ Testing Keyboard Button...")
    
    try:
        from keyboards.admin_kb import additional_features_keyboard
        
        keyboard = additional_features_keyboard()
        
        # Look for the database export button
        found = False
        for row in keyboard.inline_keyboard:
            for button in row:
                if button.callback_data == "database_export_menu":
                    print(f"✅ Database export button found: '{button.text}' -> {button.callback_data}")
                    found = True
                    break
            if found:
                break
        
        if not found:
            print("❌ Database export button not found in keyboard")
            print("Available buttons:")
            for row in keyboard.inline_keyboard:
                for button in row:
                    print(f"  - '{button.text}' -> {button.callback_data}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing keyboard: {e}")
        return False

def test_export_modules():
    """Test that all export modules are working."""
    print("\n📦 Testing Export Modules...")
    
    try:
        # Test database export module
        from utils.database_export import get_export_formats, database_exporter
        
        formats = get_export_formats()
        if len(formats) >= 4:
            print(f"✅ Export formats available: {list(formats.keys())}")
        else:
            print(f"❌ Insufficient export formats: {list(formats.keys())}")
            return False
        
        # Test statistics
        stats = database_exporter.get_export_statistics()
        if isinstance(stats, dict) and "total_exports" in stats:
            print("✅ Export statistics working")
        else:
            print("❌ Export statistics not working")
            return False
        
        # Test security module
        from utils.database_export_security import export_security
        security_stats = export_security.get_security_statistics()
        if isinstance(security_stats, dict):
            print("✅ Security module working")
        else:
            print("❌ Security module not working")
            return False
        
        # Test download manager
        from utils.secure_download import download_manager
        download_stats = download_manager.get_download_statistics()
        if isinstance(download_stats, dict):
            print("✅ Download manager working")
        else:
            print("❌ Download manager not working")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing export modules: {e}")
        return False

async def test_handler_execution():
    """Test that the database export handler can execute."""
    print("\n🎯 Testing Handler Execution...")
    
    try:
        from handlers.sys_db import database_export_menu
        from aiogram.types import CallbackQuery, User, Message, Chat
        from unittest.mock import AsyncMock, Mock
        from database.connection import db
        
        # Get real owner ID
        admins = list(db.admins.find({"role": "owner"}))
        if not admins:
            print("❌ No owners found")
            return False
        
        owner_id = admins[0]["user_id"]
        
        # Create mock objects
        mock_user = User(id=owner_id, is_bot=False, first_name="Test", username="testowner")
        mock_chat = Chat(id=owner_id, type="private")
        mock_message = Message(
            message_id=1,
            date=1234567890,
            chat=mock_chat,
            from_user=mock_user,
            content_type="text"
        )
        
        mock_callback = CallbackQuery(
            id="test_callback",
            from_user=mock_user,
            chat_instance="test_instance",
            data="database_export_menu",
            message=mock_message
        )
        
        # Mock the methods
        mock_callback.answer = AsyncMock()
        
        # Mock safe_edit_message
        import handlers.sys_db
        handlers.sys_db.safe_edit_message = AsyncMock()
        
        # Execute the handler
        await database_export_menu(mock_callback)
        
        # Check if it executed without errors
        print("✅ Handler executed successfully")
        
        # Check if safe_edit_message was called
        if handlers.sys_db.safe_edit_message.called:
            print("✅ Message edit was attempted")
            
            # Check message content
            call_args = handlers.sys_db.safe_edit_message.call_args
            if call_args:
                message_text = call_args[0][1]
                if "DATABASE EXPORT MANAGEMENT" in message_text:
                    print("✅ Correct message content generated")
                    return True
                else:
                    print("❌ Incorrect message content")
                    return False
        else:
            print("❌ Message edit was not attempted")
            return False
        
    except Exception as e:
        print(f"❌ Error testing handler execution: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🚀 Final Database Export Test")
    print("=" * 60)
    
    tests = [
        ("Privilege Checking", test_privilege_checking),
        ("Database Export Security", test_database_export_security),
        ("Keyboard Button", test_keyboard_button),
        ("Export Modules", test_export_modules),
        ("Handler Execution", test_handler_execution),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Final Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Database export button should be working correctly!")
        print("\n📋 Manual Testing Instructions:")
        print("1. Open the bot in Telegram")
        print("2. Use /admin_panel command as an owner")
        print("3. Click 'Advanced Features'")
        print("4. Look for '💾 Database Export' button")
        print("5. Click it - it should open the database export menu")
        print("6. Try creating an export to test the full functionality")
        return 0
    else:
        print("⚠️  Some tests failed. The database export button may not work correctly.")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
