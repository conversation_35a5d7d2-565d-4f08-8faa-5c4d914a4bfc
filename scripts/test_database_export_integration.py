#!/usr/bin/env python3
"""
Database Export Integration Test Script
Validates the complete database export functionality without requiring a full bot setup.
"""

import sys
import os
import asyncio
import logging
from pathlib import Path

# Add the testbots directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_module_imports():
    """Test that all database export modules can be imported successfully."""
    print("🔍 Testing module imports...")
    
    try:
        from utils.database_export import DatabaseExporter, get_export_formats, database_exporter
        print("✅ database_export module imported successfully")
        
        from utils.database_export_security import DatabaseExportSecurity, export_security
        print("✅ database_export_security module imported successfully")
        
        from utils.secure_download import SecureDownloadManager, download_manager
        print("✅ secure_download module imported successfully")
        
        from utils.export_progress_tracker import ExportProgressTracker, progress_tracker
        print("✅ export_progress_tracker module imported successfully")
        
        from utils.database_export_scheduler import DatabaseExportScheduler, export_scheduler
        print("✅ database_export_scheduler module imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_export_formats():
    """Test export format configuration."""
    print("\n📦 Testing export formats...")
    
    try:
        from utils.database_export import get_export_formats
        
        formats = get_export_formats()
        expected_formats = ["bson_archive", "bson_compressed", "json", "json_compressed"]
        
        for fmt in expected_formats:
            if fmt in formats:
                format_info = formats[fmt]
                print(f"✅ {fmt}: {format_info['description']}")
                
                # Validate format structure
                required_keys = ["extension", "description", "mongodump_args", "compress"]
                if all(key in format_info for key in required_keys):
                    print(f"   ✓ Format structure valid")
                else:
                    print(f"   ❌ Missing required keys in format {fmt}")
                    return False
            else:
                print(f"❌ Missing format: {fmt}")
                return False
        
        print(f"✅ All {len(expected_formats)} export formats configured correctly")
        return True
        
    except Exception as e:
        print(f"❌ Error testing export formats: {e}")
        return False

def test_security_validation():
    """Test security validation functionality."""
    print("\n🔒 Testing security validation...")
    
    try:
        from utils.database_export_security import DatabaseExportSecurity
        
        security = DatabaseExportSecurity()
        
        # Test parameter validation
        valid_params = {
            "format": "json",
            "collections": ["users", "products"],
        }
        
        result = security._validate_export_parameters(valid_params)
        if result["valid"]:
            print("✅ Valid parameters accepted")
        else:
            print(f"❌ Valid parameters rejected: {result['reason']}")
            return False
        
        # Test invalid parameters
        invalid_params = {"format": "invalid_format"}
        result = security._validate_export_parameters(invalid_params)
        if not result["valid"]:
            print("✅ Invalid parameters correctly rejected")
        else:
            print("❌ Invalid parameters incorrectly accepted")
            return False
        
        # Test security statistics
        stats = security.get_security_statistics()
        if isinstance(stats, dict) and "total_security_events" in stats:
            print("✅ Security statistics generation working")
        else:
            print("❌ Security statistics generation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing security validation: {e}")
        return False

def test_download_manager():
    """Test secure download manager functionality."""
    print("\n📥 Testing secure download manager...")
    
    try:
        from utils.secure_download import SecureDownloadManager
        import tempfile
        import uuid
        
        download_manager = SecureDownloadManager()
        
        # Create a temporary test file
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b"test export data")
            temp_file_path = temp_file.name
        
        try:
            # Test token creation
            result = download_manager.create_download_token(
                user_id=12345,
                username="test_user",
                export_id=str(uuid.uuid4()),
                file_path=temp_file_path,
                filename="test_export.json",
                file_size=1024
            )
            
            if result["success"]:
                print("✅ Download token creation successful")
                token_id = result["token_id"]
                
                # Test token validation
                validation = download_manager.validate_download_token(token_id, 12345)
                if validation["valid"]:
                    print("✅ Download token validation successful")
                else:
                    print(f"❌ Download token validation failed: {validation['reason']}")
                    return False
                
                # Test unauthorized access
                validation = download_manager.validate_download_token(token_id, 99999)
                if not validation["valid"]:
                    print("✅ Unauthorized access correctly blocked")
                else:
                    print("❌ Unauthorized access not blocked")
                    return False
                
            else:
                print(f"❌ Download token creation failed: {result['error']}")
                return False
            
            # Test download statistics
            stats = download_manager.get_download_statistics()
            if isinstance(stats, dict) and "total_tokens" in stats:
                print("✅ Download statistics generation working")
            else:
                print("❌ Download statistics generation failed")
                return False
            
        finally:
            # Clean up temporary file
            os.unlink(temp_file_path)
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing download manager: {e}")
        return False

def test_progress_tracker():
    """Test export progress tracking functionality."""
    print("\n📊 Testing progress tracker...")
    
    try:
        from utils.export_progress_tracker import ExportProgressTracker, ExportStatus
        import uuid
        
        tracker = ExportProgressTracker()
        export_id = str(uuid.uuid4())
        
        # Test starting tracking
        result = tracker.start_tracking(
            export_id=export_id,
            user_id=12345,
            username="test_user",
            total_collections=5
        )
        
        if result and "export_id" in result:
            print("✅ Progress tracking started successfully")
        else:
            print("❌ Progress tracking start failed")
            return False
        
        # Test progress updates
        success = tracker.update_progress(
            export_id=export_id,
            status=ExportStatus.EXPORTING,
            progress_percentage=50,
            collections_processed=2
        )
        
        if success:
            print("✅ Progress update successful")
        else:
            print("❌ Progress update failed")
            return False
        
        # Test progress retrieval
        progress = tracker.get_progress(export_id)
        if progress and progress["progress_percentage"] == 50:
            print("✅ Progress retrieval successful")
        else:
            print("❌ Progress retrieval failed")
            return False
        
        # Test completion
        success = tracker.complete_export(export_id, success=True)
        if success:
            print("✅ Export completion tracking successful")
        else:
            print("❌ Export completion tracking failed")
            return False
        
        # Test message formatting
        message = tracker.format_progress_message(export_id)
        if message and "100%" in message:
            print("✅ Progress message formatting working")
        else:
            print("❌ Progress message formatting failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing progress tracker: {e}")
        return False

def test_database_exporter():
    """Test core database exporter functionality."""
    print("\n💾 Testing database exporter...")
    
    try:
        from utils.database_export import DatabaseExporter
        
        exporter = DatabaseExporter()
        
        # Test filename generation
        filename = exporter._generate_export_filename("json", ["users", "products"])
        if filename and filename.endswith(".json"):
            print("✅ Export filename generation working")
        else:
            print("❌ Export filename generation failed")
            return False
        
        # Test collection list (this might fail if no database connection)
        try:
            collections = exporter._get_collection_list()
            print(f"✅ Collection list retrieved: {len(collections)} collections")
        except Exception:
            print("⚠️  Collection list retrieval failed (expected without database connection)")
        
        # Test export statistics
        stats = exporter.get_export_statistics()
        if isinstance(stats, dict) and "total_exports" in stats:
            print("✅ Export statistics generation working")
        else:
            print("❌ Export statistics generation failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing database exporter: {e}")
        return False

def test_scheduler():
    """Test export scheduler functionality."""
    print("\n⏰ Testing export scheduler...")
    
    try:
        from utils.database_export_scheduler import DatabaseExportScheduler
        
        scheduler = DatabaseExportScheduler()
        
        # Test scheduler status
        status = scheduler.get_scheduler_status()
        if isinstance(status, dict) and "running" in status:
            print("✅ Scheduler status retrieval working")
        else:
            print("❌ Scheduler status retrieval failed")
            return False
        
        # Test force cleanup (should work even without files)
        cleanup_result = scheduler.force_cleanup()
        if isinstance(cleanup_result, dict) and "success" in cleanup_result:
            print("✅ Force cleanup functionality working")
        else:
            print("❌ Force cleanup functionality failed")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing scheduler: {e}")
        return False

def test_template_integration():
    """Test template integration."""
    print("\n🎨 Testing template integration...")
    
    try:
        # Check if templates exist
        templates_dir = Path(__file__).parent.parent / "templates"
        
        owner_templates = templates_dir / "owner.json"
        if owner_templates.exists():
            import json
            with open(owner_templates, 'r') as f:
                templates = json.load(f)
            
            # Check for database export templates
            export_templates = [key for key in templates.keys() if "database_export" in key]
            if export_templates:
                print(f"✅ Found {len(export_templates)} database export templates")
            else:
                print("❌ No database export templates found")
                return False
        else:
            print("⚠️  Owner templates file not found")
        
        # Check buttons templates
        buttons_templates = templates_dir / "buttons.json"
        if buttons_templates.exists():
            import json
            with open(buttons_templates, 'r') as f:
                buttons = json.load(f)
            
            if "owner_advanced_database_export" in buttons:
                print("✅ Database export button template found")
            else:
                print("❌ Database export button template not found")
                return False
        else:
            print("⚠️  Buttons templates file not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing template integration: {e}")
        return False

def main():
    """Run all integration tests."""
    print("🚀 Starting Database Export Integration Tests")
    print("=" * 60)
    
    tests = [
        ("Module Imports", test_module_imports),
        ("Export Formats", test_export_formats),
        ("Security Validation", test_security_validation),
        ("Download Manager", test_download_manager),
        ("Progress Tracker", test_progress_tracker),
        ("Database Exporter", test_database_exporter),
        ("Scheduler", test_scheduler),
        ("Template Integration", test_template_integration),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! Database export functionality is ready.")
        return 0
    else:
        print("⚠️  Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
