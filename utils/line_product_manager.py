"""
Enhanced Line-Based Product Management System with Optimized Performance and Consolidated Logic
Provides centralized management for all line-based product operations with consistent UI theming.
"""

import logging
import os
import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from concurrent.futures import ThreadPoolExecutor

from aiogram.fsm.context import FSMContext
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

from utils.line_inventory import line_inventory_manager
from utils.state_helpers import safe_update_data
from database.operations import (
    get_product,
    reserve_inventory_lines
)

logger = logging.getLogger(__name__)

# Thread pool for file operations
_file_executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="line_product")

# ===== UI THEME CONSTANTS FOR LINE-BASED PRODUCTS =====

class LineProductTheme:
    """Consistent theming for line-based product interfaces."""

    # Header and separator styling
    HEADER_DIVIDER = "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>"
    SECTION_DIVIDER = "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰"

    # Emoji patterns for different content types
    EMOJIS = {
        "line_product": "📋",
        "regular_product": "📄",
        "price": "💰",
        "stock": "📦",
        "quantity": "🔢",
        "cart": "🛒",
        "success": "✅",
        "error": "❌",
        "warning": "⚠️",
        "info": "ℹ️",
        "delivery": "📤",
        "inventory": "📊",
        "preview": "👁️",
        "format": "📝",
        "available": "🟢",
        "unavailable": "🔴",
        "processing": "🔄",
        "product": "📦",
        "order": "🔢",
        "date": "📅",
        "delivery_method": "🚚",
        "file": "📁",
        "save": "💾",
        "security": "🔒"
    }

    # Status indicators
    STATUS_INDICATORS = {
        "in_stock": "🟢 Available",
        "low_stock": "🟡 Low Stock",
        "out_of_stock": "🔴 Out of Stock",
        "unlimited": "♾️ Unlimited"
    }

    @staticmethod
    def create_header(title: str, subtitle: str = "") -> str:
        """Create consistent header formatting."""
        header = f"📋 <b>• {title.upper()} •</b>\n\n"
        header += f"{LineProductTheme.HEADER_DIVIDER}\n"
        if subtitle:
            header += f"<b>{subtitle.upper()}</b>\n"
            header += f"{LineProductTheme.HEADER_DIVIDER}\n"
        return header

    @staticmethod
    def create_section_break() -> str:
        """Create section break with divider."""
        return f"\n{LineProductTheme.SECTION_DIVIDER}\n"

    @staticmethod
    def format_price(price: float, label: str = "Price") -> str:
        """Format price with consistent styling."""
        return f"{LineProductTheme.EMOJIS['price']} <b>{label}:</b> <code>${price:.2f}</code>"

    @staticmethod
    def format_stock_status(available: int, max_per_order: int = None, is_shared_inventory: bool = False, user_specific: bool = False) -> str:
        """
        Format stock status with appropriate indicators and user-specific context.

        Args:
            available: Available stock count
            max_per_order: Maximum per order (reserved for future features)
            is_shared_inventory: Whether this is a shared inventory product
            user_specific: Whether the count is user-specific (user has made previous purchases)
        """
        # Determine the appropriate label based on inventory type and user history
        if is_shared_inventory and user_specific:
            stock_label = "New items for you"
        elif is_shared_inventory and not user_specific:
            stock_label = "Stock"  # First-time user or no previous purchases
        else:
            stock_label = "Stock"  # Regular/exclusive inventory

        if available <= 0:
            if is_shared_inventory and user_specific:
                return f"{LineProductTheme.EMOJIS['stock']} <b>{stock_label}:</b> {LineProductTheme.STATUS_INDICATORS['out_of_stock']} (You've purchased all available content)"
            elif is_shared_inventory:
                return f"{LineProductTheme.EMOJIS['stock']} <b>{stock_label}:</b> {LineProductTheme.STATUS_INDICATORS['out_of_stock']} (No new content available for you)"
            else:
                return f"{LineProductTheme.EMOJIS['stock']} <b>{stock_label}:</b> {LineProductTheme.STATUS_INDICATORS['out_of_stock']}"
        elif available <= 5:
            return f"{LineProductTheme.EMOJIS['stock']} <b>{stock_label}:</b> {LineProductTheme.STATUS_INDICATORS['low_stock']} ({available} items)"
        else:
            return f"{LineProductTheme.EMOJIS['stock']} <b>{stock_label}:</b> {LineProductTheme.STATUS_INDICATORS['in_stock']} ({available} items)"

    @staticmethod
    def create_error_message(title: str, content: str, details: str = None) -> str:
        """Create consistent error messages for line products."""
        message = f"⚠️ <b>• {title.upper()} •</b>\n\n"
        message += f"{LineProductTheme.HEADER_DIVIDER}\n"
        message += f"{content}\n"
        if details:
            message += f"\n<i>{details}</i>\n"
        message += f"\n{LineProductTheme.SECTION_DIVIDER}\n"
        message += "Please try again or contact support if the issue persists."
        return message

    @staticmethod
    def create_success_message(title: str, content: str, details: Dict[str, Any] = None) -> str:
        """Create consistent success messages for line products."""
        message = f"✅ <b>• {title.upper()} •</b>\n\n"
        message += f"{LineProductTheme.HEADER_DIVIDER}\n"
        message += f"{content}\n"
        if details:
            message += "\n"
            for key, value in details.items():
                message += f"<b>{key}:</b> {value}\n"
        message += f"\n{LineProductTheme.SECTION_DIVIDER}\n"
        return message

    @staticmethod
    def create_step_message(step: str, title: str, content: str) -> str:
        """Create consistent step-by-step workflow messages."""
        message = f"🛒 <b>• NEW PRODUCT CREATION •</b>\n\n"
        message += f"{LineProductTheme.HEADER_DIVIDER}\n"
        message += f"📝 <b>{step}: {title.upper()}</b>\n"
        message += f"{LineProductTheme.HEADER_DIVIDER}\n\n"
        message += f"{content}\n\n"
        message += f"{LineProductTheme.SECTION_DIVIDER}\n"
        return message

    @staticmethod
    def format_cart_item(item: Dict[str, Any]) -> str:
        """Format cart item with consistent theming."""
        # Check for exclusive single-use products first
        if item.get("is_exclusive_single_use"):
            # Exclusive product display
            name = item.get("name", "Unnamed Product")
            price = item.get("price", 0)

            # Import exclusive theming
            try:
                from utils.exclusive_product_manager import ExclusiveProductTheme
                exclusive_emoji = ExclusiveProductTheme.EMOJIS['exclusive_product']
                exclusive_icon = ExclusiveProductTheme.EMOJIS['exclusive']
            except ImportError:
                exclusive_emoji = "📄"
                exclusive_icon = "⭐"

            return (
                f"{exclusive_emoji} <b>{name}</b>\n"
                f"{exclusive_icon} <b>Type:</b> <code>Exclusive Single-Use</code>\n"
                f"{LineProductTheme.EMOJIS['price']} <b>Price:</b> <code>${price:.2f}</code>"
            )

        elif item.get("is_line_based"):
            # Line-based product display
            name = item.get("name", "Unnamed Product")
            quantity = item.get("quantity", 1)
            line_price = item.get("line_price") or 0
            total_price = item.get("price") or 0

            return (
                f"{LineProductTheme.EMOJIS['line_product']} <b>{name}</b>\n"
                f"{LineProductTheme.EMOJIS['quantity']} <b>Quantity:</b> <code>{quantity} items</code>\n"
                f"{LineProductTheme.EMOJIS['price']} <b>Unit Price:</b> <code>${line_price:.2f}</code>\n"
                f"💵 <b>Total:</b> <code>${total_price:.2f}</code>"
            )
        else:
            # Regular product display
            return (
                f"{LineProductTheme.EMOJIS['regular_product']} <b>{item.get('name', 'Product')}</b>\n"
                f"{LineProductTheme.EMOJIS['price']} <code>${item.get('price') or 0:.2f}</code>"
            )

    @staticmethod
    def truncate_description(description: str, max_length: int = 150) -> str:
        """Truncate description with consistent formatting."""
        if not description:
            return "No description"

        if len(description) <= max_length:
            return description

        # Get first line and truncate if needed
        first_line = description.split("\n", 1)[0]
        if len(first_line) <= max_length:
            return first_line

        return first_line[:max_length-3] + "..."


class LineProductManager:
    """
    Centralized manager for line-based product operations with enhanced performance and consolidated logic.
    Handles all aspects of line-based products from creation to delivery.
    """

    def __init__(self):
        self.inventory_manager = line_inventory_manager
        self._validation_cache = {}  # Cache for product validations
        self._cache_ttl = 300  # 5 minutes cache TTL

    def _get_cache_key(self, product_id: int, operation: str, user_id: Optional[int] = None, use_time_bucket: bool = True) -> str:
        """
        Generate cache key for operations with optional timezone-aware timestamp.

        Args:
            product_id: Product ID
            operation: Operation type (e.g., 'validation')
            user_id: Optional user ID for user-specific caching
            use_time_bucket: Whether to include time bucket (for automatic expiration)

        Returns:
            Cache key string
        """
        if use_time_bucket:
            utc_now = datetime.now(timezone.utc)
            time_bucket = int(utc_now.timestamp() // self._cache_ttl)
            time_suffix = f":{time_bucket}"
        else:
            time_suffix = ""

        if user_id is not None:
            # Include user_id in cache key for user-specific data
            return f"{operation}:{product_id}:{user_id}{time_suffix}"
        else:
            # Standard cache key for non-user-specific data
            return f"{operation}:{product_id}{time_suffix}"

    def _is_line_based_product(self, product: Dict[str, Any]) -> bool:
        """Check if product is line-based with validation."""
        return bool(product and product.get("is_line_based", False))

    def _validate_product_data(self, product: Dict[str, Any]) -> Dict[str, Any]:
        """Validate product data structure for line-based operations."""
        if not product:
            return {"valid": False, "error": "Product not found"}

        if not self._is_line_based_product(product):
            return {"valid": False, "error": "Product is not line-based"}

        required_fields = ["available_lines", "max_quantity_per_order", "inventory_file_path"]
        missing_fields = [field for field in required_fields if field not in product]

        if missing_fields:
            return {
                "valid": False,
                "error": f"Missing required fields: {', '.join(missing_fields)}"
            }

        return {"valid": True}

    def _calculate_line_price(self, product: Dict[str, Any]) -> float:
        """Calculate line price using consolidated product price calculator."""
        from utils.product_price_calculator import product_price_calculator

        return product_price_calculator.calculate_line_price(product)

    async def validate_line_product_purchase(
        self,
        product_id: Any,
        quantity: int,
        use_cache: bool = True,
        user_id: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Optimized validation for line-based product purchases with caching.

        Args:
            product_id: Product ID
            quantity: Requested quantity
            use_cache: Whether to use validation cache
            user_id: Optional user ID for shared inventory validation

        Returns:
            Dict with validation results
        """
        # For shared inventory products with user_id, we need user-specific caching
        # to avoid sharing availability data between users
        try:
            product = get_product(product_id)
            allow_shared_inventory = product.get("allow_shared_inventory", False) if product else False

            # Use user-specific cache key for shared inventory products
            # For shared inventory, don't use time buckets to allow immediate invalidation
            if allow_shared_inventory and user_id:
                cache_key = self._get_cache_key(product_id, "validation", user_id, use_time_bucket=False)
            else:
                cache_key = self._get_cache_key(product_id, "validation", use_time_bucket=True)

            if use_cache and cache_key in self._validation_cache:
                cached_result = self._validation_cache[cache_key].copy()
                # Update quantity-specific fields
                if cached_result.get("valid"):
                    cached_result["quantity"] = quantity
                    cached_result["total_price"] = cached_result["line_price"] * quantity
                return cached_result
        except Exception as cache_check_e:
            # If cache check fails, continue with fresh validation
            logger.debug(f"Cache check failed for product {product_id}: {cache_check_e}")
            # Use same cache key logic as above
            if allow_shared_inventory and user_id:
                cache_key = self._get_cache_key(product_id, "validation", user_id, use_time_bucket=False)
            else:
                cache_key = self._get_cache_key(product_id, "validation", use_time_bucket=True)

        try:
            product = get_product(product_id)

            # Validate product structure
            validation = self._validate_product_data(product)
            if not validation["valid"]:
                return validation

            # Extract product data
            available_lines = product.get("available_lines", 0)
            max_quantity_per_order = product.get("max_quantity_per_order", 1)
            inventory_file = product.get("inventory_file_path")
            line_price = self._calculate_line_price(product)
            allow_shared_inventory = product.get("allow_shared_inventory", False)

            # Validate quantity constraints
            if quantity <= 0:
                return {"valid": False, "error": "Quantity must be greater than zero"}

            if quantity > max_quantity_per_order:
                return {
                    "valid": False,
                    "error": f"Maximum {max_quantity_per_order} items per order"
                }

            # For shared inventory, check user-specific availability
            if allow_shared_inventory and user_id:
                from database.operations import get_available_lines_for_user
                total_lines = product.get("total_lines", 0)
                user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)

                logger.debug(f"Shared inventory validation: user_id={user_id}, product_id={product_id}, "
                           f"total_lines={total_lines}, user_available={len(user_available_lines)}, "
                           f"requested_quantity={quantity}")

                if len(user_available_lines) < quantity:
                    return {
                        "valid": False,
                        "error": f"You have already purchased most available content. New lines available: {len(user_available_lines)}"
                    }

                # For shared inventory, use user-specific availability
                effective_available_lines = len(user_available_lines)
            else:
                # For exclusive inventory, use standard availability check
                if available_lines < quantity:
                    return {
                        "valid": False,
                        "error": f"Insufficient stock. Available: {available_lines}"
                    }

                effective_available_lines = available_lines

            # Validate inventory file
            if not inventory_file or not os.path.exists(inventory_file):
                return {"valid": False, "error": "Inventory file not available"}

            # Check actual file inventory (async operation)
            actual_lines = await asyncio.get_event_loop().run_in_executor(
                _file_executor,
                self.inventory_manager.get_available_lines_count,
                inventory_file
            )

            if actual_lines < quantity:
                return {
                    "valid": False,
                    "error": f"Inventory file has insufficient lines. Available: {actual_lines}"
                }

            result = {
                "valid": True,
                "quantity": quantity,
                "total_price": line_price * quantity,
                "available_stock": effective_available_lines,
                "actual_file_lines": actual_lines,
                "line_price": line_price,
                "max_quantity_per_order": max_quantity_per_order,
                "product_name": product.get("name", "Unnamed Product"),
                "allow_shared_inventory": allow_shared_inventory,
                "user_id": user_id
            }

            # Cache the result (without quantity-specific data)
            if use_cache:
                cache_result = result.copy()
                cache_result.pop("quantity", None)
                cache_result.pop("total_price", None)
                self._validation_cache[cache_key] = cache_result

            return result

        except Exception as e:
            logger.error(f"Error validating line product purchase for {product_id}: {e}")
            return {"valid": False, "error": "Validation failed"}
    
    async def process_inventory_file_upload(
        self,
        file_path: str,
        product_id: Any,
        state: FSMContext,
        chunk_size: int = 8192
    ) -> Dict[str, Any]:
        """
        Optimized processing of uploaded inventory files with memory efficiency.

        Args:
            file_path: Path to uploaded file
            product_id: Product ID
            state: FSM context
            chunk_size: File reading chunk size for memory optimization (reserved for future use)

        Returns:
            Dict with processing results
        """
        # Note: chunk_size parameter reserved for future memory optimization features
        try:
            # Validate file exists and is readable
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "error": "File not found",
                    "details": "Uploaded file could not be located"
                }

            # Validate the inventory file asynchronously
            validation = await asyncio.get_event_loop().run_in_executor(
                _file_executor,
                self.inventory_manager.validate_inventory_file,
                file_path
            )

            if not validation["valid"]:
                return {
                    "success": False,
                    "error": validation["error"],
                    "details": "File validation failed",
                    "validation_details": validation
                }

            # Create inventory file for the product asynchronously
            logger.debug(f"Creating inventory file for product {product_id} from {file_path}")
            inventory_file_path = await asyncio.get_event_loop().run_in_executor(
                _file_executor,
                self.inventory_manager.create_inventory_file,
                product_id,
                file_path
            )

            if not inventory_file_path:
                logger.error(f"Inventory file creation returned None for product {product_id}")
                return {
                    "success": False,
                    "error": "Failed to create inventory file",
                    "details": "File processing error during creation"
                }

            logger.info(f"Successfully created inventory file: {inventory_file_path}")

            # Update state with inventory information
            state_data = {
                "inventory_file_path": inventory_file_path,
                "total_lines": validation["total_lines"],
                "available_lines": validation["total_lines"],
                "reserved_lines": 0,
                "file_size": validation.get("file_size", 0),
                "sample_lines": validation.get("sample_lines", []),
                "duplicates_removed": validation.get("duplicates", 0)
            }

            await safe_update_data(state, **state_data)

            # Clear validation cache for this product
            cache_pattern = f"validation:{product_id}:"
            self._validation_cache = {
                k: v for k, v in self._validation_cache.items()
                if not k.startswith(cache_pattern)
            }

            return {
                "success": True,
                "inventory_file_path": inventory_file_path,
                "total_lines": validation["total_lines"],
                "validation": validation,
                "processing_details": {
                    "file_size": validation.get("file_size", 0),
                    "duplicates_removed": validation.get("duplicates", 0),
                    "sample_count": len(validation.get("sample_lines", []))
                }
            }

        except Exception as e:
            logger.error(f"Error processing inventory file for product {product_id}: {e}", exc_info=True)
            return {
                "success": False,
                "error": "Processing failed",
                "details": str(e),
                "exception_type": type(e).__name__
            }
    
    def create_quantity_selection_keyboard(
        self,
        product_id: Any,
        max_quantity: int,
        available_stock: int,
        buttons_per_row: int = 5,
        max_display_buttons: int = 10,
        show_price_preview: bool = False,
        line_price: float = 0.0,
        user_id: int = None
    ) -> InlineKeyboardMarkup:
        """
        Create optimized quantity selection keyboard with configurable layout and optional price preview.

        Args:
            product_id: Product ID
            max_quantity: Maximum quantity per order
            available_stock: Available stock (will be overridden for shared inventory)
            buttons_per_row: Number of buttons per row
            max_display_buttons: Maximum buttons to display before custom option
            show_price_preview: Whether to show price in button text
            line_price: Price per line for preview calculations
            user_id: Optional user ID for user-specific availability calculation

        Returns:
            InlineKeyboardMarkup for quantity selection
        """
        # Calculate user-specific availability for shared inventory
        effective_available_stock = available_stock  # Default to provided stock
        if user_id:
            from database.operations import get_product
            product = get_product(product_id)
            if product and product.get("allow_shared_inventory", False):
                # Clear cache to ensure fresh availability calculation
                self.clear_validation_cache(product_id, user_id)

                from database.operations import get_available_lines_for_user
                total_lines = product.get("total_lines", 0)
                user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
                effective_available_stock = len(user_available_lines)

                logger.warning(f"DEPRECATED: create_quantity_selection_keyboard doing own calculation. "
                             f"User {user_id}, Product {product_id}, Effective: {effective_available_stock}/{total_lines}. "
                             f"Use create_quantity_selection_keyboard_with_availability instead.")

        # Determine the actual maximum selectable quantity
        actual_max = min(max_quantity, effective_available_stock)
        max_selectable = min(actual_max, max_display_buttons)

        # Handle edge case of no stock
        if actual_max <= 0:
            return InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(
                    text="❌ Out of Stock",
                    callback_data=f"view_product:{product_id}"
                )],
                [InlineKeyboardButton(
                    text="🔙 Back",
                    callback_data=f"view_product:{product_id}"
                )]
            ])

        keyboard = []
        current_row = []

        # Generate quantity list with increments of 5 (plus 1) and optional price preview
        from keyboards.product_kb import generate_quantity_increments
        quantities_to_show = generate_quantity_increments(max_quantity, effective_available_stock, max_selectable)

        # Create buttons for the selected quantities with optional price preview
        for qty in quantities_to_show:
            if show_price_preview and line_price > 0:
                total_price = line_price * qty
                button_text = f"{qty} (${total_price:.2f})"
            else:
                button_text = str(qty)

            button = InlineKeyboardButton(
                text=button_text,
                callback_data=f"line_quantity:{product_id}:{qty}"
            )
            current_row.append(button)

            # Add row when we have enough buttons or reached the end
            if len(current_row) == buttons_per_row or qty == quantities_to_show[-1]:
                keyboard.append(current_row)
                current_row = []

        # Add custom quantity option if needed
        if max_selectable < actual_max:
            keyboard.append([
                InlineKeyboardButton(
                    text=f"{LineProductTheme.EMOJIS['format']} Custom (Max: {actual_max})",
                    callback_data=f"line_custom_quantity:{product_id}"
                )
            ])

        # Add quick action buttons for larger quantities
        if actual_max >= 10 and max_selectable < actual_max:
            quick_actions = []

            # Add "Max" button if reasonable
            if actual_max <= 100:
                quick_actions.append(
                    InlineKeyboardButton(
                        text=f"🔝 Max ({actual_max})",
                        callback_data=f"line_quantity:{product_id}:{actual_max}"
                    )
                )

            # Add "Half" button if applicable
            if actual_max >= 20:
                half_qty = actual_max // 2
                quick_actions.append(
                    InlineKeyboardButton(
                        text=f"⚡ Half ({half_qty})",
                        callback_data=f"line_quantity:{product_id}:{half_qty}"
                    )
                )

            if quick_actions:
                keyboard.append(quick_actions)

        # Add navigation buttons
        keyboard.append([
            InlineKeyboardButton(text="🔙 Back", callback_data=f"view_product:{product_id}")
        ])

        return InlineKeyboardMarkup(inline_keyboard=keyboard)

    def create_quantity_selection_keyboard_with_availability(
        self,
        product_id: int,
        max_quantity_per_order: int,
        available_lines: int,
        allow_shared_inventory: bool,
        buttons_per_row: int = 3,
        max_display_buttons: int = 8,
        show_price_preview: bool = False,
        line_price: float = None,
        user_id: int = None
    ) -> InlineKeyboardMarkup:
        """
        Create quantity selection keyboard with pre-calculated availability.
        This ensures consistency with text display by using the same availability data.

        Args:
            product_id: Product ID
            max_quantity_per_order: Maximum quantity per order
            available_lines: Pre-calculated user-specific available lines
            allow_shared_inventory: Whether this is a shared inventory product
            buttons_per_row: Number of buttons per row
            max_display_buttons: Maximum buttons to display before custom option
            show_price_preview: Whether to show price in button text
            line_price: Price per line for preview calculations
            user_id: Optional user ID for logging context

        Returns:
            InlineKeyboardMarkup for quantity selection
        """
        # Use the pre-calculated availability directly (no internal calculation)
        effective_available_stock = available_lines

        logger.debug(f"Keyboard with availability: User {user_id}, Product {product_id}, "
                   f"Pre-calculated stock: {effective_available_stock}, Shared: {allow_shared_inventory}")

        # Determine the actual maximum selectable quantity
        max_selectable = min(max_quantity_per_order, effective_available_stock)

        if max_selectable <= 0:
            # No items available - return empty keyboard with back button
            return InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="🔙 Back", callback_data=f"view_product:{product_id}")]
            ])

        # Generate quantity buttons using the standard keyboard generation logic
        from keyboards.product_kb import quantity_selection_keyboard
        return quantity_selection_keyboard(
            max_quantity=max_quantity_per_order,
            available_stock=effective_available_stock,
            product_id=product_id,
            buttons_per_row=buttons_per_row,
            max_display_buttons=max_display_buttons
        )

    def create_line_product_display(self, product: Dict[str, Any], context: str = "shop", user_id: int = None) -> str:
        """
        Create comprehensive line product display for different contexts with user-specific stock information.

        DEPRECATED: Use create_line_product_display_with_availability for consistent stock calculations.

        Args:
            product: Product dictionary
            context: Display context (shop, cart, admin, etc.)
            user_id: Optional user ID for user-specific stock display

        Returns:
            Formatted HTML string
        """
        # Log deprecation warning
        product_id = product.get("id") or product.get("_id")
        logger.warning(f"DEPRECATED: create_line_product_display called for product {product_id}. "
                      f"Use create_line_product_display_with_availability for consistent stock calculations.")

        name = product.get("name", "Unnamed Product")
        description = product.get("description", "")
        line_price = self._calculate_line_price(product)
        available_lines = product.get("available_lines", 0)
        max_quantity = product.get("max_quantity_per_order", 1)
        preview_format = product.get("preview_format", "")
        allow_shared_inventory = product.get("allow_shared_inventory", False)

        # Calculate user-specific availability for shared inventory
        if allow_shared_inventory and user_id:
            from database.operations import get_available_lines_for_user
            product_id = product.get("id") or product.get("_id")
            total_lines = product.get("total_lines", 0)

            # Clear any cached validation data for this user to ensure fresh stock counts
            self.clear_validation_cache(product_id, user_id)

            # Get fresh user-specific availability
            user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
            display_available_lines = len(user_available_lines)

            # Only show "New items for you" if user has made previous purchases
            # (i.e., their available lines are less than total lines)
            is_user_specific = display_available_lines < total_lines

            logger.debug(f"Display: User {user_id}, Product {product_id}, "
                        f"Available: {display_available_lines}/{total_lines}, "
                        f"User-specific: {is_user_specific}")
        else:
            display_available_lines = available_lines
            is_user_specific = False

        if context == "shop" or context == "express_checkout":
            # Shop display with full details (also used for express checkout)
            text = LineProductTheme.create_header("LINE-BASED PRODUCT", "DIGITAL INVENTORY")
            text += f"{LineProductTheme.EMOJIS['line_product']} <b>{name}</b>\n\n"
            text += f"{LineProductTheme.EMOJIS['info']} <b>Description:</b>\n<i>{LineProductTheme.truncate_description(description)}</i>\n\n"
            text += f"{LineProductTheme.format_price(line_price, 'Price per item')}\n"
            text += f"{LineProductTheme.format_stock_status(display_available_lines, max_quantity, allow_shared_inventory, is_user_specific)}\n"
            text += f"{LineProductTheme.EMOJIS['quantity']} <b>Max per order:</b> <code>{max_quantity} items</code>\n"

            if preview_format:
                text += f"{LineProductTheme.EMOJIS['format']} <b>Item format:</b> <code>{preview_format}</code>\n"

            text += LineProductTheme.create_section_break()
            if context == "express_checkout":
                text += "<i>Express checkout - items will be delivered immediately</i>"
            else:
                text += "<i>Each item is delivered individually from our digital inventory</i>"

        elif context == "cart":
            # Cart display with quantity info
            quantity = product.get("quantity", 1)
            total_price = product.get("price", line_price * quantity)
            text = LineProductTheme.format_cart_item({
                "name": name,
                "quantity": quantity,
                "line_price": line_price,
                "price": total_price,
                "is_line_based": True
            })

        elif context == "admin":
            # Admin display with management info
            total_lines = product.get("total_lines", 0)
            reserved_lines = product.get("reserved_lines", 0)
            text = f"{LineProductTheme.EMOJIS['line_product']} <b>{name}</b>\n"
            text += f"{LineProductTheme.EMOJIS['price']} <b>Line Price:</b> <code>${line_price:.2f}</code>\n"
            text += f"{LineProductTheme.EMOJIS['inventory']} <b>Total Lines:</b> <code>{total_lines}</code>\n"
            text += f"{LineProductTheme.EMOJIS['available']} <b>Available:</b> <code>{available_lines}</code>\n"
            text += f"{LineProductTheme.EMOJIS['processing']} <b>Reserved:</b> <code>{reserved_lines}</code>\n"
            text += f"{LineProductTheme.EMOJIS['quantity']} <b>Max per order:</b> <code>{max_quantity}</code>\n"

        else:
            # Default minimal display
            text = f"{LineProductTheme.EMOJIS['line_product']} <b>{name}</b>\n"
            text += f"{LineProductTheme.format_price(line_price, 'Price per item')}\n"
            text += f"{LineProductTheme.format_stock_status(display_available_lines, None, allow_shared_inventory, is_user_specific)}"

        return text

    def create_line_product_display_with_availability(self, product: Dict[str, Any], available_lines: int, allow_shared_inventory: bool, context: str = "shop", user_id: int = None) -> str:
        """
        Create comprehensive line product display with pre-calculated availability.
        This ensures consistency between different display contexts by using the same stock data.

        Args:
            product: Product dictionary
            available_lines: Pre-calculated user-specific available lines
            allow_shared_inventory: Whether this is a shared inventory product
            context: Display context (shop, cart, admin, etc.)
            user_id: Optional user ID for user-specific context

        Returns:
            Formatted HTML string
        """
        name = product.get("name", "Unnamed Product")
        description = product.get("description", "")
        line_price = self._calculate_line_price(product)
        max_quantity = product.get("max_quantity_per_order", 1)
        preview_format = product.get("preview_format", "")
        total_lines = product.get("total_lines", 0)

        # Use pre-calculated availability
        display_available_lines = available_lines

        # Determine if this is user-specific (user has made previous purchases)
        is_user_specific = allow_shared_inventory and user_id and available_lines < total_lines

        logger.debug(f"Display with availability: User {user_id}, Context {context}, "
                    f"Available: {available_lines}/{total_lines}, "
                    f"User-specific: {is_user_specific}, Shared: {allow_shared_inventory}")

        if context == "shop" or context == "express_checkout":
            # Shop display with full details (also used for express checkout)
            text = LineProductTheme.create_header("LINE-BASED PRODUCT", "DIGITAL INVENTORY")
            text += f"{LineProductTheme.EMOJIS['line_product']} <b>{name}</b>\n\n"
            text += f"{LineProductTheme.EMOJIS['info']} <b>Description:</b>\n<i>{LineProductTheme.truncate_description(description)}</i>\n\n"
            text += f"{LineProductTheme.format_price(line_price, 'Price per item')}\n"
            text += f"{LineProductTheme.format_stock_status(display_available_lines, max_quantity, allow_shared_inventory, is_user_specific)}\n"
            text += f"{LineProductTheme.EMOJIS['quantity']} <b>Max per order:</b> <code>{max_quantity} items</code>\n"

            if preview_format:
                text += f"{LineProductTheme.EMOJIS['format']} <b>Item format:</b> <code>{preview_format}</code>\n"

            text += LineProductTheme.create_section_break()
            if context == "express_checkout":
                text += "<i>Express checkout - items will be delivered immediately</i>"
            else:
                text += "<i>Each item is delivered individually from our digital inventory</i>"

        elif context == "cart":
            # Cart display with quantity info
            quantity = product.get("quantity", 1)
            total_price = product.get("price", line_price * quantity)
            text = LineProductTheme.format_cart_item({
                "name": name,
                "quantity": quantity,
                "line_price": line_price,
                "price": total_price,
                "is_line_based": True
            })

        elif context == "admin":
            # Admin display with management info
            reserved_lines = product.get("reserved_lines", 0)
            text = f"{LineProductTheme.EMOJIS['line_product']} <b>{name}</b>\n"
            text += f"{LineProductTheme.EMOJIS['price']} <b>Line Price:</b> <code>${line_price:.2f}</code>\n"
            text += f"{LineProductTheme.EMOJIS['inventory']} <b>Total Lines:</b> <code>{total_lines}</code>\n"
            text += f"{LineProductTheme.EMOJIS['available']} <b>Available:</b> <code>{display_available_lines}</code>\n"
            text += f"{LineProductTheme.EMOJIS['processing']} <b>Reserved:</b> <code>{reserved_lines}</code>\n"
            text += f"{LineProductTheme.EMOJIS['quantity']} <b>Max per order:</b> <code>{max_quantity}</code>\n"

        else:
            # Default minimal display
            text = f"{LineProductTheme.EMOJIS['line_product']} <b>{name}</b>\n"
            text += f"{LineProductTheme.format_price(line_price, 'Price per item')}\n"
            text += f"{LineProductTheme.format_stock_status(display_available_lines, None, allow_shared_inventory, is_user_specific)}"

        return text

    def create_express_checkout_display(self, product: Dict[str, Any], user_id: int = None) -> str:
        """
        Create compact product display specifically for express checkout context.
        Provides essential product information without full headers for clean integration.

        Args:
            product: Product dictionary
            user_id: Optional user ID for user-specific stock display

        Returns:
            Formatted HTML string optimized for express checkout
        """
        name = product.get("name", "Unnamed Product")
        line_price = self._calculate_line_price(product)
        available_lines = product.get("available_lines", 0)
        max_quantity = product.get("max_quantity_per_order", 1)
        allow_shared_inventory = product.get("allow_shared_inventory", False)

        # Calculate user-specific availability for shared inventory
        if allow_shared_inventory and user_id:
            from database.operations import get_available_lines_for_user
            product_id = product.get("id") or product.get("_id")
            total_lines = product.get("total_lines", 0)

            # Clear any cached validation data for this user to ensure fresh stock counts
            self.clear_validation_cache(product_id, user_id)

            # Get fresh user-specific availability
            user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
            display_available_lines = len(user_available_lines)

            # Only show "New items for you" if user has made previous purchases
            is_user_specific = display_available_lines < total_lines

            logger.warning(f"DEPRECATED: create_express_checkout_display doing own calculation. "
                         f"User {user_id}, Product {product_id}, Available: {display_available_lines}/{total_lines}. "
                         f"Use create_express_checkout_display_with_availability instead.")
        else:
            display_available_lines = available_lines
            is_user_specific = False

        # Create compact display with clear product information section
        text = f"<b>📋 Product Information</b>\n"
        text += f"▫️ <b>Name:</b> {name}\n"
        text += f"▫️ {LineProductTheme.format_price(line_price, 'Price per item')}\n"
        text += f"▫️ {LineProductTheme.format_stock_status(display_available_lines, max_quantity, allow_shared_inventory, is_user_specific)}\n"
        text += f"▫️ <b>Max per order:</b> <code>{max_quantity} items</code>"

        return text

    def create_express_checkout_display_with_availability(self, product: Dict[str, Any], available_lines: int, allow_shared_inventory: bool, user_id: int = None) -> str:
        """
        Create compact product display for express checkout with pre-calculated availability.
        This ensures consistency between text display and button generation.

        Args:
            product: Product dictionary
            available_lines: Pre-calculated user-specific available lines
            allow_shared_inventory: Whether this is a shared inventory product
            user_id: Optional user ID for user-specific context

        Returns:
            Formatted HTML string optimized for express checkout
        """
        name = product.get("name", "Unnamed Product")
        line_price = self._calculate_line_price(product)
        max_quantity = product.get("max_quantity_per_order", 1)
        total_lines = product.get("total_lines", 0)

        # Determine if this is user-specific (user has made previous purchases)
        is_user_specific = allow_shared_inventory and user_id and available_lines < total_lines

        logger.debug(f"Express checkout display with availability: User {user_id}, "
                    f"Available: {available_lines}/{total_lines}, "
                    f"User-specific: {is_user_specific}, Shared: {allow_shared_inventory}")

        # Create compact display with pre-calculated availability
        text = f"<b>📋 Product Information</b>\n"
        text += f"▫️ <b>Name:</b> {name}\n"
        text += f"▫️ {LineProductTheme.format_price(line_price, 'Price per item')}\n"
        text += f"▫️ {LineProductTheme.format_stock_status(available_lines, max_quantity, allow_shared_inventory, is_user_specific)}\n"
        text += f"▫️ <b>Max per order:</b> <code>{max_quantity} items</code>"

        return text

    def create_cart_quantity_display(self, product: Dict[str, Any], user_id: int = None) -> str:
        """
        Create compact product display specifically for cart quantity selection.
        Provides essential product information without full headers for clean integration.

        Args:
            product: Product dictionary
            user_id: Optional user ID for user-specific stock display

        Returns:
            Formatted HTML string optimized for cart quantity selection
        """
        name = product.get("name", "Unnamed Product")
        line_price = self._calculate_line_price(product)
        available_lines = product.get("available_lines", 0)
        max_quantity = product.get("max_quantity_per_order", 1)
        allow_shared_inventory = product.get("allow_shared_inventory", False)

        # Calculate user-specific availability for shared inventory
        if allow_shared_inventory and user_id:
            from database.operations import get_available_lines_for_user
            product_id = product.get("id") or product.get("_id")
            total_lines = product.get("total_lines", 0)

            # Clear any cached validation data for this user to ensure fresh stock counts
            self.clear_validation_cache(product_id, user_id)

            # Get fresh user-specific availability
            user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
            display_available_lines = len(user_available_lines)

            # Only show "New items for you" if user has made previous purchases
            is_user_specific = display_available_lines < total_lines

            logger.warning(f"DEPRECATED: create_cart_quantity_display doing own calculation. "
                         f"User {user_id}, Product {product_id}, Available: {display_available_lines}/{total_lines}. "
                         f"Use create_cart_quantity_display_with_availability instead.")
        else:
            display_available_lines = available_lines
            is_user_specific = False

        # Create well-structured display for cart context
        text = f"{LineProductTheme.EMOJIS['cart']} <b>Add to Cart</b>\n"
        text += f"{LineProductTheme.SECTION_DIVIDER}\n\n"
        text += f"<b>📋 Product Information</b>\n"
        text += f"▫️ <b>Name:</b> {name}\n"
        text += f"▫️ {LineProductTheme.format_price(line_price, 'Price per item')}\n"
        text += f"▫️ {LineProductTheme.format_stock_status(display_available_lines, max_quantity, allow_shared_inventory, is_user_specific)}\n"
        text += f"▫️ <b>Max per order:</b> <code>{max_quantity} items</code>\n\n"
        text += f"{LineProductTheme.SECTION_DIVIDER}\n"
        text += f"<b>🔢 Select Quantity:</b> Choose how many items to add to cart"

        return text

    def create_cart_quantity_display_with_availability(self, product: Dict[str, Any], available_lines: int, allow_shared_inventory: bool, user_id: int = None) -> str:
        """
        Create compact product display for cart quantity selection with pre-calculated availability.
        This ensures consistency between text display and button generation.

        Args:
            product: Product dictionary
            available_lines: Pre-calculated user-specific available lines
            allow_shared_inventory: Whether this is a shared inventory product
            user_id: Optional user ID for user-specific context

        Returns:
            Formatted HTML string optimized for cart quantity selection
        """
        name = product.get("name", "Unnamed Product")
        line_price = self._calculate_line_price(product)
        max_quantity = product.get("max_quantity_per_order", 1)
        total_lines = product.get("total_lines", 0)

        # Determine if this is user-specific (user has made previous purchases)
        is_user_specific = allow_shared_inventory and user_id and available_lines < total_lines

        logger.debug(f"Cart quantity display with availability: User {user_id}, "
                    f"Available: {available_lines}/{total_lines}, "
                    f"User-specific: {is_user_specific}, Shared: {allow_shared_inventory}")

        # Create well-structured display for cart context
        text = f"{LineProductTheme.EMOJIS['cart']} <b>Add to Cart</b>\n"
        text += f"{LineProductTheme.SECTION_DIVIDER}\n\n"
        text += f"<b>📋 Product Information</b>\n"
        text += f"▫️ <b>Name:</b> {name}\n"
        text += f"▫️ {LineProductTheme.format_price(line_price, 'Price per item')}\n"
        text += f"▫️ {LineProductTheme.format_stock_status(available_lines, max_quantity, allow_shared_inventory, is_user_specific)}\n"
        text += f"▫️ <b>Max per order:</b> <code>{max_quantity} items</code>\n\n"
        text += f"{LineProductTheme.SECTION_DIVIDER}\n"
        text += f"<b>🔢 Select Quantity:</b> Choose how many items to add to cart"

        return text

    def create_line_product_summary(self, product: Dict[str, Any], user_id: int = None) -> str:
        """
        Create a formatted summary for line-based products (legacy method for compatibility).

        Args:
            product: Product dictionary
            user_id: Optional user ID for user-specific stock display

        Returns:
            Formatted HTML string with consistent UI theme
        """
        # Use the new consolidated display method with consistent stock calculation
        allow_shared_inventory = product.get("allow_shared_inventory", False)

        # Calculate user-specific availability once for consistency
        if allow_shared_inventory and user_id:
            from database.operations import get_available_lines_for_user
            product_id = product.get("id") or product.get("_id")
            total_lines = product.get("total_lines", 0)

            # Clear cache to ensure fresh data
            self.clear_validation_cache(product_id, user_id)

            # Get fresh user-specific availability
            user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
            available_lines = len(user_available_lines)

            # Use the new method with pre-calculated availability for consistency
            summary = self.create_line_product_display_with_availability(
                product, available_lines, allow_shared_inventory, context="shop", user_id=user_id
            )
        else:
            # For non-shared inventory, use the original method
            available_lines = product.get("available_lines", 0)
            summary = self.create_line_product_display_with_availability(
                product, available_lines, allow_shared_inventory, context="shop", user_id=user_id
            )

        summary += LineProductTheme.create_section_break()
        summary += "<i>Select quantity to add to cart</i>"
        return summary

    async def create_cart_item(
        self,
        product: Dict[str, Any],
        product_id: Any,
        quantity: int
    ) -> Dict[str, Any]:
        """
        Create optimized cart item for line-based products using consolidated factory.

        Args:
            product: Product dictionary
            product_id: Product ID
            quantity: Quantity to add

        Returns:
            Cart item dictionary with enhanced metadata
        """
        # Use the consolidated cart item factory which handles validation internally
        from utils.cart_item_factory import cart_item_factory

        return cart_item_factory.create_line_based_cart_item(
            product=product,
            product_id=product_id,
            quantity=quantity
        )

    async def process_purchase_workflow(
        self,
        user_id: int,
        product_id: Any,
        quantity: int,
        workflow_type: str = "cart"  # cart, buy_now, admin
    ) -> Dict[str, Any]:
        # Note: user_id parameter reserved for future user-specific workflow features
        """
        Consolidated purchase workflow processing for different contexts.

        Args:
            user_id: User ID
            product_id: Product ID
            quantity: Requested quantity
            workflow_type: Type of workflow (cart, buy_now, admin)

        Returns:
            Dict with workflow results
        """
        try:
            # Validate the purchase with user-specific logic
            validation = await self.validate_line_product_purchase(product_id, quantity, user_id=user_id)
            if not validation["valid"]:
                return {
                    "success": False,
                    "error": validation["error"],
                    "workflow_type": workflow_type
                }

            # Get product data
            product = get_product(product_id)
            if not product:
                return {
                    "success": False,
                    "error": "Product not found",
                    "workflow_type": workflow_type
                }

            # Note: Inventory reservation is now handled during order processing confirmation
            # This prevents premature reservations that may never be confirmed
            # The workflow_type is preserved for future use if needed

            # Create cart item
            cart_item = await self.create_cart_item(product, product_id, quantity)

            return {
                "success": True,
                "cart_item": cart_item,
                "validation": validation,
                "workflow_type": workflow_type,
                "reserved_inventory": False  # Reservation now happens during order confirmation
            }

        except Exception as e:
            logger.error(f"Error in purchase workflow for product {product_id}: {e}")
            return {
                "success": False,
                "error": "Workflow processing failed",
                "details": str(e),
                "workflow_type": workflow_type
            }
    
    async def get_line_product_cart_item(
        self,
        product: Dict[str, Any],
        product_id: Any,
        quantity: int
    ) -> Dict[str, Any]:
        """
        Legacy method for backward compatibility. Use create_cart_item instead.
        """
        return await self.create_cart_item(product, product_id, quantity)

    def clear_validation_cache(self, product_id: int = None, user_id: int = None) -> None:
        """
        Clear validation cache for specific product or all products.
        For shared inventory products, can clear cache for specific user or all users.

        Args:
            product_id: Specific product ID to clear, or None for all
            user_id: Specific user ID to clear (only relevant for shared inventory), or None for all users
        """
        if product_id is None:
            self._validation_cache.clear()
            logger.info("Cleared all validation cache")
        else:
            if user_id is not None:
                # Clear cache for specific product and user (shared inventory)
                # Handle both time-bucketed and non-time-bucketed keys
                # Patterns: validation:product_id:user_id and validation:product_id:user_id:time_bucket
                cache_pattern = f"validation:{product_id}:{user_id}"
                cleared_count = 0
                keys_to_remove = []
                for k in self._validation_cache.keys():
                    # Match exact key (no time bucket) or key with time bucket
                    if k == cache_pattern or k.startswith(cache_pattern + ":"):
                        keys_to_remove.append(k)
                        cleared_count += 1

                for k in keys_to_remove:
                    del self._validation_cache[k]

                logger.info(f"Cleared {cleared_count} validation cache entries for product {product_id}, user {user_id}")
            else:
                # Clear cache for all users of this product
                # Handle both time-bucketed and non-time-bucketed keys
                # Patterns: validation:product_id and validation:product_id:*
                cache_pattern = f"validation:{product_id}"
                cleared_count = 0
                keys_to_remove = []
                for k in self._validation_cache.keys():
                    # Match exact key or key with additional segments
                    if k == cache_pattern or k.startswith(cache_pattern + ":"):
                        keys_to_remove.append(k)
                        cleared_count += 1

                for k in keys_to_remove:
                    del self._validation_cache[k]

                logger.info(f"Cleared {cleared_count} validation cache entries for product {product_id} (all users)")

    def invalidate_all_caches_for_product(self, product_id: Any) -> None:
        """
        Invalidate all caches related to a specific product to ensure fresh data.
        This is useful after purchases to ensure user-specific data is recalculated.

        Args:
            product_id: Product ID to invalidate caches for
        """
        try:
            # Clear validation cache
            self.clear_validation_cache(product_id)

            # Invalidate unified product cache
            from utils.unified_product_performance import invalidate_product_cache, ProductType
            invalidate_product_cache(str(product_id), ProductType.LINE_BASED)

            logger.info(f"Invalidated all caches for line-based product {product_id}")

        except Exception as e:
            logger.error(f"Error invalidating caches for product {product_id}: {e}")

    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics for monitoring."""
        return {
            "cache_size": len(self._validation_cache),
            "cache_ttl": self._cache_ttl,
            "cache_keys": list(self._validation_cache.keys())
        }

    async def bulk_validate_products(self, product_ids: List[int]) -> Dict[int, Dict[str, Any]]:
        """
        Bulk validate multiple products for efficiency.

        Args:
            product_ids: List of product IDs to validate

        Returns:
            Dict mapping product_id to validation results
        """
        results = {}

        # Process in batches to avoid overwhelming the system
        batch_size = 5
        for i in range(0, len(product_ids), batch_size):
            batch = product_ids[i:i + batch_size]
            batch_tasks = [
                self.validate_line_product_purchase(pid, 1, use_cache=True)
                for pid in batch
            ]

            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

            for product_id, result in zip(batch, batch_results):
                if isinstance(result, Exception):
                    results[product_id] = {
                        "valid": False,
                        "error": f"Validation exception: {str(result)}"
                    }
                else:
                    results[product_id] = result

        return results

    @staticmethod
    def create_cart_display_item(cart_item: Dict[str, Any]) -> str:
        """
        Legacy method for backward compatibility. Use LineProductTheme.format_cart_item instead.
        """
        return LineProductTheme.format_cart_item(cart_item)


# Global instance
line_product_manager = LineProductManager()
