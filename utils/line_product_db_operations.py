"""
Consolidated Database Operations for Line-Based Products
Reduces code duplication and provides optimized database operations with unified monitoring.
"""

import logging
import time
import re
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone
from bson import ObjectId

from database.operations import (
    get_product,
    update_product,
    confirm_line_purchase,
    confirm_line_purchase_with_history,
    release_reserved_lines,
    add_transaction,
    reserve_inventory_lines,
    reserve_inventory_lines_for_user
)
from utils.unified_product_performance import (
    unified_product_cache,
    unified_product_performance_monitor,
    ProductType,
    get_product_with_monitoring,
    invalidate_product_cache
)
from utils.unified_validation import unified_validation

logger = logging.getLogger(__name__)


class LineProductDBOperations:
    """Consolidated database operations for line-based products with unified monitoring."""

    @staticmethod
    def _validate_product_id(product_id: Any) -> Dict[str, Any]:
        """
        Validate and sanitize product ID to prevent NoSQL injection.
        Uses unified validation to eliminate duplication.

        Args:
            product_id: Product ID to validate

        Returns:
            Dict with validation result and sanitized ID
        """
        return unified_validation.validate_product_id(product_id)

    @staticmethod
    def _validate_user_id(user_id: Any) -> Dict[str, Any]:
        """
        Enhanced validation of user ID to prevent injection attacks.
        Uses unified validation with enhanced mode for line-based products.

        Args:
            user_id: User ID to validate

        Returns:
            Dict with validation result
        """
        return unified_validation.validate_user_id(user_id, enhanced=True)

    @staticmethod
    def _validate_quantity(quantity: Any) -> Dict[str, Any]:
        """
        Validate quantity parameter for line-based operations.

        Args:
            quantity: Quantity to validate

        Returns:
            Dict with validation result
        """
        return unified_validation.validate_quantity(quantity)

    @staticmethod
    def _get_utc_now() -> datetime:
        """
        Get current UTC datetime with timezone awareness.
        Uses unified validation to eliminate duplication.

        Returns:
            Timezone-aware UTC datetime
        """
        return unified_validation.get_utc_now()

    @staticmethod
    def get_line_product_with_validation(product_id: Any) -> Optional[Dict[str, Any]]:
        """
        Get line-based product with validation and monitoring.

        Args:
            product_id: Product ID

        Returns:
            Product dict if valid line-based product, None otherwise
        """
        start_time = time.time()
        operation_name = "get_line_product_with_validation"

        try:
            # Validate and sanitize product ID
            validation = LineProductDBOperations._validate_product_id(product_id)
            if not validation["valid"]:
                logger.warning(f"Invalid product ID: {validation['error']}")
                unified_product_performance_monitor.record_error(operation_name, ProductType.LINE_BASED)
                return None

            sanitized_product_id = validation["sanitized_id"]

            # Use unified monitoring for product retrieval (preserve type)
            product = get_product_with_monitoring(sanitized_product_id, ProductType.LINE_BASED)
            if not product:
                logger.warning(f"Product {sanitized_product_id} not found")
                duration = time.time() - start_time
                unified_product_performance_monitor.record_query_time(operation_name, duration, ProductType.LINE_BASED)
                return None

            if not product.get("is_line_based", False):
                logger.warning(f"Product {sanitized_product_id} is not line-based")
                unified_product_performance_monitor.record_error(f"{operation_name}_not_line_based", ProductType.LINE_BASED)
                return None

            # Record successful operation
            duration = time.time() - start_time
            unified_product_performance_monitor.record_query_time(operation_name, duration, ProductType.LINE_BASED)
            return product

        except Exception as e:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_error(operation_name, ProductType.LINE_BASED)
            logger.error(f"Error getting line product {product_id}: {e}")
            return None

    @staticmethod
    def reserve_line_inventory_with_monitoring(product_id: Any, quantity: int, user_id: Optional[int] = None) -> Dict[str, Any]:
        """
        Reserve line inventory with comprehensive validation and monitoring.

        Args:
            product_id: Product ID
            quantity: Quantity to reserve
            user_id: Optional user ID for tracking

        Returns:
            Dict with operation result and details
        """
        start_time = time.time()
        operation_name = "reserve_line_inventory_with_monitoring"

        try:
            # Validate inputs
            product_validation = LineProductDBOperations._validate_product_id(product_id)
            if not product_validation["valid"]:
                logger.error(f"Invalid product ID in reserve_line_inventory: {product_validation['error']}")
                return {
                    "success": False,
                    "error": f"Invalid product ID: {product_validation['error']}",
                    "error_code": "INVALID_PRODUCT_ID"
                }

            if user_id is not None:
                user_validation = LineProductDBOperations._validate_user_id(user_id)
                if not user_validation["valid"]:
                    logger.error(f"Invalid user ID in reserve_line_inventory: {user_validation['error']}")
                    return {
                        "success": False,
                        "error": f"Invalid user ID: {user_validation['error']}",
                        "error_code": "INVALID_USER_ID"
                    }
                user_id = user_validation["sanitized_id"]

            quantity_validation = LineProductDBOperations._validate_quantity(quantity)
            if not quantity_validation["valid"]:
                logger.error(f"Invalid quantity in reserve_line_inventory: {quantity_validation['error']}")
                return {
                    "success": False,
                    "error": f"Invalid quantity: {quantity_validation['error']}",
                    "error_code": "INVALID_QUANTITY"
                }
            quantity = quantity_validation["sanitized_quantity"]

            sanitized_product_id = product_validation["sanitized_id"]

            # Invalidate cache before operation to prevent race conditions
            invalidate_product_cache(str(sanitized_product_id), ProductType.LINE_BASED)

            # Perform atomic reservation with user-aware logic
            if user_id:
                success = reserve_inventory_lines_for_user(sanitized_product_id, quantity, user_id)
            else:
                success = reserve_inventory_lines(sanitized_product_id, quantity)

            duration = time.time() - start_time

            if success:
                unified_product_performance_monitor.record_query_time(operation_name, duration, ProductType.LINE_BASED)
                return {
                    "success": True,
                    "product_id": sanitized_product_id,
                    "quantity_reserved": quantity,
                    "user_id": user_id,
                    "operation_duration": duration,
                    "timestamp": LineProductDBOperations._get_utc_now()
                }
            else:
                unified_product_performance_monitor.record_error(f"{operation_name}_failed", ProductType.LINE_BASED)
                return {
                    "success": False,
                    "error": "Failed to reserve inventory - insufficient stock or concurrent modification",
                    "error_code": "RESERVATION_FAILED",
                    "product_id": sanitized_product_id,
                    "quantity_requested": quantity,
                    "operation_duration": duration
                }

        except Exception as e:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_error(operation_name, ProductType.LINE_BASED)
            logger.error(f"Error reserving line inventory for product {product_id}: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "error_code": "EXCEPTION",
                "operation_duration": duration
            }

    @staticmethod
    def confirm_line_purchase_with_monitoring(product_id: Any, quantity: int, user_id: Optional[int] = None, line_indices: Optional[List[int]] = None) -> Dict[str, Any]:
        """
        Confirm line purchase with comprehensive validation and monitoring.

        Args:
            product_id: Product ID
            quantity: Quantity to confirm
            user_id: Optional user ID for tracking
            line_indices: Optional list of line indices purchased (for shared inventory)

        Returns:
            Dict with operation result and details
        """
        start_time = time.time()
        operation_name = "confirm_line_purchase_with_monitoring"

        try:
            # Validate inputs
            product_validation = LineProductDBOperations._validate_product_id(product_id)
            if not product_validation["valid"]:
                logger.error(f"Invalid product ID in confirm_line_purchase: {product_validation['error']}")
                return {
                    "success": False,
                    "error": f"Invalid product ID: {product_validation['error']}",
                    "error_code": "INVALID_PRODUCT_ID"
                }

            if user_id is not None:
                user_validation = LineProductDBOperations._validate_user_id(user_id)
                if not user_validation["valid"]:
                    logger.error(f"Invalid user ID in confirm_line_purchase: {user_validation['error']}")
                    return {
                        "success": False,
                        "error": f"Invalid user ID: {user_validation['error']}",
                        "error_code": "INVALID_USER_ID"
                    }
                user_id = user_validation["sanitized_id"]

            if not isinstance(quantity, int) or quantity <= 0:
                return {
                    "success": False,
                    "error": "Quantity must be a positive integer",
                    "error_code": "INVALID_QUANTITY"
                }

            sanitized_product_id = product_validation["sanitized_id"]

            # Invalidate cache before operation
            invalidate_product_cache(str(sanitized_product_id), ProductType.LINE_BASED)

            # Perform atomic purchase confirmation with history support
            # For shared inventory, we need to extract line indices during purchase
            if user_id:
                # Get product to check if it uses shared inventory
                product = get_product(sanitized_product_id)
                if product and product.get("allow_shared_inventory", False):
                    # For shared inventory, extract line indices that would be delivered
                    from database.operations import get_available_lines_for_user
                    total_lines = product.get("total_lines", 0)
                    available_for_user = get_available_lines_for_user(user_id, sanitized_product_id, total_lines)

                    if len(available_for_user) >= quantity:
                        # Use the first available lines for this user
                        selected_indices = available_for_user[:quantity]
                        success = confirm_line_purchase_with_history(sanitized_product_id, quantity, user_id, selected_indices)
                    else:
                        success = False
                        logger.warning(f"User {user_id} has insufficient available lines for product {sanitized_product_id}")
                else:
                    # For exclusive inventory or when line_indices provided
                    if line_indices:
                        success = confirm_line_purchase_with_history(sanitized_product_id, quantity, user_id, line_indices)
                    else:
                        success = confirm_line_purchase(sanitized_product_id, quantity)
            else:
                success = confirm_line_purchase(sanitized_product_id, quantity)

            duration = time.time() - start_time

            if success:
                unified_product_performance_monitor.record_query_time(operation_name, duration, ProductType.LINE_BASED)

                # Record transaction if user_id provided
                if user_id:
                    try:
                        product = get_product_with_monitoring(sanitized_product_id, ProductType.LINE_BASED)
                        if product:
                            add_transaction(
                                user_id,
                                "line_purchase",
                                product.get("price", 0.0) * quantity,
                                product_id=sanitized_product_id,
                                product_name=product.get("name", "Line-Based Product"),
                                quantity=quantity,
                                status="completed",
                                timestamp=LineProductDBOperations._get_utc_now(),
                                product_type="line_based"
                            )
                    except Exception as e:
                        logger.warning(f"Failed to record transaction for line product {sanitized_product_id}: {e}")

                return {
                    "success": True,
                    "product_id": sanitized_product_id,
                    "quantity_confirmed": quantity,
                    "user_id": user_id,
                    "operation_duration": duration,
                    "timestamp": LineProductDBOperations._get_utc_now()
                }
            else:
                unified_product_performance_monitor.record_error(f"{operation_name}_failed", ProductType.LINE_BASED)
                return {
                    "success": False,
                    "error": "Failed to confirm purchase - insufficient reserved lines or concurrent modification",
                    "error_code": "CONFIRMATION_FAILED",
                    "product_id": sanitized_product_id,
                    "quantity_requested": quantity,
                    "operation_duration": duration
                }

        except Exception as e:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_error(operation_name, ProductType.LINE_BASED)
            logger.error(f"Error confirming line purchase for product {product_id}: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "error_code": "EXCEPTION",
                "operation_duration": duration
            }

    @staticmethod
    def get_line_product_inventory_status(product_id: Any) -> Dict[str, Any]:
        """
        Get comprehensive inventory status for line-based product.

        Args:
            product_id: Product ID

        Returns:
            Dict with inventory status and details
        """
        start_time = time.time()
        operation_name = "get_line_product_inventory_status"

        try:
            # Validate product ID
            validation = LineProductDBOperations._validate_product_id(product_id)
            if not validation["valid"]:
                logger.warning(f"Invalid product ID: {validation['error']}")
                return {
                    "success": False,
                    "error": f"Invalid product ID: {validation['error']}",
                    "error_code": "INVALID_PRODUCT_ID"
                }

            sanitized_product_id = validation["sanitized_id"]

            # Get product with monitoring (preserve type)
            product = get_product_with_monitoring(sanitized_product_id, ProductType.LINE_BASED)
            if not product:
                duration = time.time() - start_time
                unified_product_performance_monitor.record_query_time(operation_name, duration, ProductType.LINE_BASED)
                return {
                    "success": False,
                    "error": "Product not found",
                    "error_code": "PRODUCT_NOT_FOUND"
                }

            if not product.get("is_line_based", False):
                unified_product_performance_monitor.record_error(f"{operation_name}_not_line_based", ProductType.LINE_BASED)
                return {
                    "success": False,
                    "error": "Product is not line-based",
                    "error_code": "NOT_LINE_BASED"
                }

            # Extract inventory information
            total_lines = product.get("total_lines", 0)
            available_lines = product.get("available_lines", 0)
            reserved_lines = product.get("reserved_lines", 0)

            # Calculate derived metrics
            sold_lines = total_lines - available_lines - reserved_lines
            utilization_rate = ((total_lines - available_lines) / max(total_lines, 1)) * 100

            duration = time.time() - start_time
            unified_product_performance_monitor.record_query_time(operation_name, duration, ProductType.LINE_BASED)

            return {
                "success": True,
                "product_id": sanitized_product_id,
                "product_name": product.get("name", "Unknown"),
                "inventory": {
                    "total_lines": total_lines,
                    "available_lines": available_lines,
                    "reserved_lines": reserved_lines,
                    "sold_lines": sold_lines,
                    "utilization_rate_percent": utilization_rate
                },
                "status": {
                    "in_stock": available_lines > 0,
                    "low_stock": available_lines > 0 and available_lines <= (total_lines * 0.1),  # Less than 10%
                    "out_of_stock": available_lines == 0,
                    "has_reservations": reserved_lines > 0
                },
                "operation_duration": duration,
                "timestamp": LineProductDBOperations._get_utc_now()
            }

        except Exception as e:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_error(operation_name, ProductType.LINE_BASED)
            logger.error(f"Error getting inventory status for product {product_id}: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "error_code": "EXCEPTION",
                "operation_duration": duration
            }
    
    @staticmethod
    def update_line_product_inventory(
        product_id: Any,
        available_lines: int = None,
        reserved_lines: int = None,
        total_lines: int = None
    ) -> bool:
        """
        Update line product inventory with multiple fields.
        
        Args:
            product_id: Product ID
            available_lines: Available lines count
            reserved_lines: Reserved lines count
            total_lines: Total lines count
            
        Returns:
            True if successful, False otherwise
        """
        try:
            update_data = {}
            
            if available_lines is not None:
                update_data["available_lines"] = available_lines
            if reserved_lines is not None:
                update_data["reserved_lines"] = reserved_lines
            if total_lines is not None:
                update_data["total_lines"] = total_lines
                
            if not update_data:
                logger.warning("No inventory data provided for update")
                return False
                
            result = update_product(product_id, update_data)
            return result is not None
            
        except Exception as e:
            logger.error(f"Error updating inventory for product {product_id}: {e}")
            return False
    
    @staticmethod
    def process_line_purchase_transaction(
        user_id: int,
        product_id: Any,
        quantity: int,
        order_number: int,
        line_price: float,
        payment_track_id: str = None,
        delivered_items: List[str] = None
    ) -> bool:
        """
        Process complete line purchase transaction.
        
        Args:
            user_id: User ID
            product_id: Product ID
            quantity: Quantity purchased
            order_number: Order number
            line_price: Price per line
            payment_track_id: Payment tracking ID
            delivered_items: List of delivered items
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Get product for transaction details
            product = get_product(product_id)
            if not product:
                logger.error(f"Product {product_id} not found for transaction")
                return False
            
            # Confirm the line purchase (updates inventory)
            if not confirm_line_purchase(product_id, quantity):
                logger.error(f"Failed to confirm line purchase for product {product_id}")
                return False
            
            # Log the transaction
            transaction_data = {
                "product_id": product_id,
                "product_name": product.get("name", "Unknown Product"),
                "quantity": quantity,
                "order_number": order_number,
                "line_price": line_price,
                "total_amount": line_price * quantity,
                "payment_track_id": payment_track_id,
                "transaction_type": "line_purchase",
                "delivered_at": datetime.now().isoformat()
            }
            
            # Add sample items if provided
            if delivered_items:
                transaction_data["sample_items"] = delivered_items[:3]  # Store first 3 for reference
            
            add_transaction(
                user_id,
                "line_purchase",
                line_price * quantity,
                **transaction_data
            )
            
            logger.info(f"Processed line purchase transaction: user={user_id}, product={product_id}, quantity={quantity}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing line purchase transaction: {e}")
            return False
    
    @staticmethod
    def rollback_line_purchase(
        product_id: Any,
        quantity: int,
        reason: str = "Purchase rollback",
        user_id: int = None
    ) -> bool:
        """
        Rollback a line purchase by releasing reserved lines.

        Args:
            product_id: Product ID
            quantity: Quantity to rollback
            reason: Reason for rollback
            user_id: Optional user ID for shared inventory products

        Returns:
            True if successful, False otherwise
        """
        try:
            success = release_reserved_lines(product_id, quantity, user_id)
            if success:
                logger.info(f"Rolled back line purchase: product={product_id}, quantity={quantity}, reason={reason}, user_id={user_id}")
            else:
                logger.error(f"Failed to rollback line purchase: product={product_id}, quantity={quantity}")
            return success

        except Exception as e:
            logger.error(f"Error rolling back line purchase: {e}")
            return False
    
    @staticmethod
    def get_line_product_stats(product_id: Any) -> Dict[str, Any]:
        """
        Get comprehensive statistics for a line-based product.

        Args:
            product_id: Product ID

        Returns:
            Dict with product statistics
        """
        try:
            product = LineProductDBOperations.get_line_product_with_validation(product_id)
            if not product:
                return {"error": "Product not found or not line-based"}

            total_lines = product.get("total_lines", 0)
            available_lines = product.get("available_lines", 0)
            reserved_lines = product.get("reserved_lines", 0)

            # Calculate derived statistics
            availability_rate = (available_lines / total_lines * 100) if total_lines > 0 else 0

            return {
                "product_id": product_id,
                "product_name": product.get("name", "Unknown"),
                "total_lines": total_lines,
                "available_lines": available_lines,
                "reserved_lines": reserved_lines,
                "availability_rate": round(availability_rate, 2),
                "line_price": product.get("line_price", product.get("price", 0)),
                "max_quantity_per_order": product.get("max_quantity_per_order", 1),
                "inventory_file_path": product.get("inventory_file_path"),
                "created_at": product.get("created_at"),
                "last_updated": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"Error getting line product stats for {product_id}: {e}")
            return {"error": f"Failed to get statistics: {str(e)}"}
    
    @staticmethod
    def bulk_update_line_products(updates: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Bulk update multiple line products efficiently.
        
        Args:
            updates: List of update dictionaries with product_id and update_data
            
        Returns:
            Dict with bulk update results
        """
        results = {
            "successful": [],
            "failed": [],
            "total": len(updates)
        }
        
        for update in updates:
            try:
                product_id = update.get("product_id")
                update_data = update.get("update_data", {})
                
                if not product_id:
                    results["failed"].append({
                        "product_id": None,
                        "error": "Missing product_id"
                    })
                    continue
                
                success = LineProductDBOperations.update_line_product_inventory(
                    product_id,
                    available_lines=update_data.get("available_lines"),
                    reserved_lines=update_data.get("reserved_lines"),
                    total_lines=update_data.get("total_lines")
                )
                
                if success:
                    results["successful"].append(product_id)
                else:
                    results["failed"].append({
                        "product_id": product_id,
                        "error": "Update failed"
                    })
                    
            except Exception as e:
                results["failed"].append({
                    "product_id": update.get("product_id"),
                    "error": str(e)
                })
        
        logger.info(f"Bulk update completed: {len(results['successful'])} successful, {len(results['failed'])} failed")
        return results


# Global instance
line_product_db_ops = LineProductDBOperations()
