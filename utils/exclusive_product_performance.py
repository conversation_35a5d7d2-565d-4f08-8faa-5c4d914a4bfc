"""
Performance Optimization Utilities for Exclusive Single-Use Products
Provides caching, indexing, and query optimization for exclusive product operations.
"""

import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime

logger = logging.getLogger(__name__)

class ExclusiveProductCache:
    """Memory-efficient caching system for exclusive product operations with leak prevention."""

    def __init__(self, cache_ttl_seconds: int = 300, max_tracked_products: int = 10000):  # 5 minutes default TTL
        """Initialize the cache system with bounded storage."""
        self.cache_ttl = cache_ttl_seconds
        self.max_tracked_products = max_tracked_products
        self._product_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
        self._delivered_products: Set[str] = set()  # Track delivered products
        self._removed_products: Set[str] = set()    # Track removed products
        self._last_cleanup = datetime.now()
        self._last_deep_cleanup = datetime.now()
    
    def _is_cache_valid(self, key: str) -> bool:
        """Check if cache entry is still valid."""
        if key not in self._cache_timestamps:
            return False
        
        age = datetime.now() - self._cache_timestamps[key]
        return age.total_seconds() < self.cache_ttl
    
    def _cleanup_expired_cache(self):
        """Remove expired cache entries and prevent memory leaks."""
        now = datetime.now()

        # Only cleanup every minute to avoid overhead
        if (now - self._last_cleanup).total_seconds() < 60:
            return

        # Clean up expired cache entries
        expired_keys = []
        for key, timestamp in self._cache_timestamps.items():
            if (now - timestamp).total_seconds() >= self.cache_ttl:
                expired_keys.append(key)

        for key in expired_keys:
            self._product_cache.pop(key, None)
            self._cache_timestamps.pop(key, None)

        self._last_cleanup = now

        # Perform deep cleanup every hour to prevent unbounded growth
        if (now - self._last_deep_cleanup).total_seconds() >= 3600:  # 1 hour
            self._deep_cleanup()
            self._last_deep_cleanup = now

        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

    def _deep_cleanup(self):
        """Perform deep cleanup to prevent memory leaks in tracking sets."""
        initial_delivered = len(self._delivered_products)
        initial_removed = len(self._removed_products)

        # If tracking sets are too large, keep only recent entries
        if len(self._delivered_products) > self.max_tracked_products:
            # Convert to list, sort, and keep most recent entries
            # Since we don't have timestamps for these, we'll just truncate
            delivered_list = list(self._delivered_products)
            self._delivered_products = set(delivered_list[-self.max_tracked_products//2:])

        if len(self._removed_products) > self.max_tracked_products:
            removed_list = list(self._removed_products)
            self._removed_products = set(removed_list[-self.max_tracked_products//2:])

        cleaned_delivered = initial_delivered - len(self._delivered_products)
        cleaned_removed = initial_removed - len(self._removed_products)

        if cleaned_delivered > 0 or cleaned_removed > 0:
            logger.info(f"Deep cleanup completed - removed {cleaned_delivered} delivered entries, {cleaned_removed} removed entries")
    
    def get_product(self, product_id: str) -> Optional[Dict[str, Any]]:
        """Get product from cache if available and valid."""
        self._cleanup_expired_cache()
        
        cache_key = f"product_{product_id}"
        if self._is_cache_valid(cache_key):
            return self._product_cache.get(cache_key)
        
        return None
    
    def cache_product(self, product_id: str, product_data: Dict[str, Any]):
        """Cache product data."""
        cache_key = f"product_{product_id}"
        self._product_cache[cache_key] = product_data.copy()
        self._cache_timestamps[cache_key] = datetime.now()
    
    def invalidate_product(self, product_id: str):
        """Invalidate cached product data."""
        cache_key = f"product_{product_id}"
        self._product_cache.pop(cache_key, None)
        self._cache_timestamps.pop(cache_key, None)
    
    def mark_product_delivered(self, product_id: str):
        """Mark product as delivered for fast lookup."""
        self._delivered_products.add(str(product_id))
        self.invalidate_product(product_id)  # Invalidate cache since status changed
    
    def mark_product_removed(self, product_id: str):
        """Mark product as removed for fast lookup."""
        self._removed_products.add(str(product_id))
        self.invalidate_product(product_id)  # Invalidate cache since status changed
    
    def is_product_delivered(self, product_id: str) -> bool:
        """Quick check if product is delivered."""
        return str(product_id) in self._delivered_products
    
    def is_product_removed(self, product_id: str) -> bool:
        """Quick check if product is removed."""
        return str(product_id) in self._removed_products
    
    def clear_all(self):
        """Clear all cache data."""
        self._product_cache.clear()
        self._cache_timestamps.clear()
        self._delivered_products.clear()
        self._removed_products.clear()
        logger.info("Cleared all exclusive product cache data")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics with memory usage information."""
        self._cleanup_expired_cache()

        return {
            "cached_products": len(self._product_cache),
            "delivered_products": len(self._delivered_products),
            "removed_products": len(self._removed_products),
            "cache_ttl_seconds": self.cache_ttl,
            "max_tracked_products": self.max_tracked_products,
            "last_cleanup": self._last_cleanup.isoformat(),
            "last_deep_cleanup": self._last_deep_cleanup.isoformat(),
            "memory_usage": {
                "cache_entries": len(self._product_cache),
                "timestamp_entries": len(self._cache_timestamps),
                "delivered_tracking": len(self._delivered_products),
                "removed_tracking": len(self._removed_products),
                "total_memory_objects": len(self._product_cache) + len(self._cache_timestamps) + len(self._delivered_products) + len(self._removed_products)
            }
        }

    def force_deep_cleanup(self):
        """Force immediate deep cleanup of all cache data."""
        logger.info("Forcing cache deep cleanup")
        initial_stats = self.get_cache_stats()

        # Force cleanup of expired entries
        self._cleanup_expired_cache()

        # Force deep cleanup regardless of time
        self._deep_cleanup()

        final_stats = self.get_cache_stats()
        logger.info(f"Forced deep cleanup completed - reduced memory objects from {initial_stats['memory_usage']['total_memory_objects']} to {final_stats['memory_usage']['total_memory_objects']}")

    def reset_tracking_sets(self):
        """Reset delivered and removed product tracking sets (use with caution)."""
        logger.warning("Resetting product tracking sets")
        delivered_count = len(self._delivered_products)
        removed_count = len(self._removed_products)

        self._delivered_products.clear()
        self._removed_products.clear()

        logger.info(f"Reset tracking sets - cleared {delivered_count} delivered and {removed_count} removed product entries")


class ExclusiveProductQueryOptimizer:
    """Query optimization utilities for exclusive product operations."""
    
    @staticmethod
    def get_optimized_availability_query(category_id: Optional[Any] = None) -> Dict[str, Any]:
        """
        Get optimized query for available exclusive products.
        
        Args:
            category_id: Optional category filter
            
        Returns:
            Optimized MongoDB query
        """
        # Base query for available exclusive products
        query = {
            "is_exclusive_single_use": True,
            "is_purchased": {"$ne": True},
            "removed_from_listings": {"$ne": True}
        }
        
        # Add category filter if specified
        if category_id is not None:
            query["category_id"] = category_id
        
        # Add expiration filter
        current_time = datetime.now()
        query["$or"] = [
            {"expiration_date": {"$exists": False}},
            {"expiration_date": None},
            {"expiration_date": {"$gt": current_time}}
        ]
        
        return query
    
    @staticmethod
    def get_optimized_projection() -> Dict[str, int]:
        """Get optimized projection for customer-facing queries."""
        return {
            "_id": 1,
            "name": 1,
            "price": 1,
            "description": 1,
            "exclusive_file_type": 1,
            "exclusive_file_size": 1,
            "created_at": 1,
            "category_id": 1,
            "expiration_date": 1
        }
    
    @staticmethod
    def get_admin_projection() -> Dict[str, int]:
        """Get full projection for admin queries."""
        return {}  # Return all fields for admin
    
    @staticmethod
    def get_optimized_sort() -> List[tuple]:
        """Get optimized sort order for exclusive products."""
        return [
            ("removed_from_listings", 1),  # Available products first
            ("is_purchased", 1),           # Then unpurchased first
            ("created_at", -1)             # Then newest first
        ]


class ExclusiveProductIndexManager:
    """Database index management for optimal query performance."""
    
    @staticmethod
    def get_recommended_indexes() -> List[Dict[str, Any]]:
        """
        Get list of recommended database indexes for exclusive products.
        
        Returns:
            List of index specifications
        """
        return [
            # Compound index for availability queries
            {
                "name": "exclusive_availability_idx",
                "keys": [
                    ("is_exclusive_single_use", 1),
                    ("is_purchased", 1),
                    ("removed_from_listings", 1),
                    ("expiration_date", 1)
                ],
                "background": True
            },
            
            # Index for category-based queries
            {
                "name": "exclusive_category_idx",
                "keys": [
                    ("is_exclusive_single_use", 1),
                    ("category_id", 1),
                    ("is_purchased", 1),
                    ("removed_from_listings", 1)
                ],
                "background": True
            },
            
            # Index for admin lifecycle queries
            {
                "name": "exclusive_lifecycle_idx",
                "keys": [
                    ("is_exclusive_single_use", 1),
                    ("product_lifecycle_status", 1),
                    ("purchase_date", -1)
                ],
                "background": True
            },
            
            # Index for user purchase history
            {
                "name": "exclusive_user_purchases_idx",
                "keys": [
                    ("is_exclusive_single_use", 1),
                    ("purchased_by_user_id", 1),
                    ("purchase_date", -1)
                ],
                "background": True
            },
            
            # Index for removal tracking
            {
                "name": "exclusive_removal_idx",
                "keys": [
                    ("is_exclusive_single_use", 1),
                    ("removed_from_listings", 1),
                    ("removal_timestamp", -1)
                ],
                "background": True
            }
        ]
    
    @staticmethod
    async def create_indexes_if_needed():
        """Create recommended indexes if they don't exist."""
        try:
            from database.operations import products_collection
            
            existing_indexes = await products_collection.list_indexes().to_list(length=None)
            existing_names = {idx.get("name") for idx in existing_indexes}
            
            recommended = ExclusiveProductIndexManager.get_recommended_indexes()
            created_count = 0
            
            for index_spec in recommended:
                index_name = index_spec["name"]
                if index_name not in existing_names:
                    try:
                        await products_collection.create_index(
                            index_spec["keys"],
                            name=index_name,
                            background=index_spec.get("background", True)
                        )
                        created_count += 1
                        logger.info(f"Created index: {index_name}")
                    except Exception as e:
                        logger.error(f"Failed to create index {index_name}: {e}")
            
            if created_count > 0:
                logger.info(f"Created {created_count} new indexes for exclusive products")
            else:
                logger.debug("All recommended indexes already exist")
                
        except Exception as e:
            logger.error(f"Error creating exclusive product indexes: {e}")


class ExclusiveProductPerformanceMonitor:
    """Performance monitoring for exclusive product operations with memory leak prevention."""

    def __init__(self, max_metrics: int = 1000, max_operations: int = 100):
        """Initialize the performance monitor with bounded storage."""
        self.query_times: List[float] = []
        self.operation_counts: Dict[str, int] = {}
        self.error_counts: Dict[str, int] = {}
        self.start_time = datetime.now()
        self.last_cleanup = datetime.now()
        self.max_metrics = max_metrics
        self.max_operations = max_operations
        self._cleanup_interval = 300  # 5 minutes

    def _cleanup_old_metrics(self):
        """Clean up old metrics to prevent memory leaks."""
        now = datetime.now()

        # Only cleanup every 5 minutes to avoid overhead
        if (now - self.last_cleanup).total_seconds() < self._cleanup_interval:
            return

        # Limit query times to max_metrics
        if len(self.query_times) > self.max_metrics:
            self.query_times = self.query_times[-self.max_metrics:]

        # Limit operation counts - keep only top operations by count
        if len(self.operation_counts) > self.max_operations:
            # Sort by count and keep top operations
            sorted_ops = sorted(self.operation_counts.items(), key=lambda x: x[1], reverse=True)
            self.operation_counts = dict(sorted_ops[:self.max_operations])

        # Limit error counts - keep only top errors by count
        if len(self.error_counts) > self.max_operations:
            sorted_errors = sorted(self.error_counts.items(), key=lambda x: x[1], reverse=True)
            self.error_counts = dict(sorted_errors[:self.max_operations])

        self.last_cleanup = now
        logger.debug(f"Performance monitor cleanup completed - metrics: {len(self.query_times)}, operations: {len(self.operation_counts)}, errors: {len(self.error_counts)}")

    def record_query_time(self, operation: str, duration: float):
        """Record query execution time with automatic cleanup."""
        self._cleanup_old_metrics()

        self.query_times.append(duration)
        self.operation_counts[operation] = self.operation_counts.get(operation, 0) + 1

        # Immediate cleanup if we exceed limits significantly
        if len(self.query_times) > self.max_metrics * 1.2:
            self.query_times = self.query_times[-self.max_metrics:]

    def record_error(self, operation: str):
        """Record operation error with automatic cleanup."""
        self._cleanup_old_metrics()
        self.error_counts[operation] = self.error_counts.get(operation, 0) + 1
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get performance statistics with memory usage information."""
        if not self.query_times:
            return {"error": "No performance data available"}

        avg_time = sum(self.query_times) / len(self.query_times)
        max_time = max(self.query_times)
        min_time = min(self.query_times)

        return {
            "uptime_seconds": (datetime.now() - self.start_time).total_seconds(),
            "total_operations": sum(self.operation_counts.values()),
            "total_errors": sum(self.error_counts.values()),
            "average_query_time_ms": avg_time * 1000,
            "max_query_time_ms": max_time * 1000,
            "min_query_time_ms": min_time * 1000,
            "operation_breakdown": self.operation_counts.copy(),
            "error_breakdown": self.error_counts.copy(),
            "error_rate": sum(self.error_counts.values()) / max(sum(self.operation_counts.values()), 1) * 100,
            "memory_usage": {
                "query_times_count": len(self.query_times),
                "operation_types_tracked": len(self.operation_counts),
                "error_types_tracked": len(self.error_counts),
                "last_cleanup": self.last_cleanup.isoformat(),
                "max_metrics_limit": self.max_metrics,
                "max_operations_limit": self.max_operations
            }
        }

    def force_cleanup(self):
        """Force immediate cleanup of all metrics."""
        logger.info("Forcing performance monitor cleanup")
        self.query_times = self.query_times[-self.max_metrics//2:] if self.query_times else []

        # Keep only top operations and errors
        if len(self.operation_counts) > self.max_operations//2:
            sorted_ops = sorted(self.operation_counts.items(), key=lambda x: x[1], reverse=True)
            self.operation_counts = dict(sorted_ops[:self.max_operations//2])

        if len(self.error_counts) > self.max_operations//2:
            sorted_errors = sorted(self.error_counts.items(), key=lambda x: x[1], reverse=True)
            self.error_counts = dict(sorted_errors[:self.max_operations//2])

        self.last_cleanup = datetime.now()
        logger.info(f"Forced cleanup completed - metrics: {len(self.query_times)}, operations: {len(self.operation_counts)}, errors: {len(self.error_counts)}")

    def reset_all_metrics(self):
        """Reset all performance metrics (use with caution)."""
        logger.warning("Resetting all performance metrics")
        self.query_times.clear()
        self.operation_counts.clear()
        self.error_counts.clear()
        self.start_time = datetime.now()
        self.last_cleanup = datetime.now()


# Global instances
exclusive_product_cache = ExclusiveProductCache()
exclusive_product_performance_monitor = ExclusiveProductPerformanceMonitor()
