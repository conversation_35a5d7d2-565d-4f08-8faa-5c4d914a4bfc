"""
Cart Item Factory Module
Consolidated factory for creating cart items for all product types.
Eliminates duplication and ensures consistent cart item structure.
"""

import logging
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

# Try to import ObjectId for type hints
try:
    from bson import ObjectId
    BSON_AVAILABLE = True
except ImportError:
    # Create a dummy ObjectId class for type hints when BSON is not available
    class ObjectId:
        pass
    BSON_AVAILABLE = False

logger = logging.getLogger(__name__)


class CartItemFactory:
    """
    Factory class for creating standardized cart items for all product types.
    Ensures consistent structure and eliminates code duplication.
    """
    
    @staticmethod
    def create_cart_item(
        product: Dict[str, Any],
        product_id: Union[int, str, ObjectId],
        quantity: int = 1,
        additional_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a standardized cart item for any product type.
        
        Args:
            product: Product data dictionary
            product_id: Product ID
            quantity: Quantity (default 1)
            additional_data: Additional data to include in cart item
            
        Returns:
            Standardized cart item dictionary
        """
        try:
            # Validate inputs
            if not product:
                raise ValueError("Product data is required")

            # Add debugging info for product_id
            logger.debug(f"CartItemFactory: Validating product_id: {repr(product_id)} (type: {type(product_id).__name__})")

            # Validate product_id - accept integers, strings, and ObjectId instances
            if isinstance(product_id, int):
                if product_id <= 0:
                    raise ValueError("Product ID must be a positive integer")
            elif isinstance(product_id, str):
                if not product_id.strip():
                    raise ValueError(f"Product ID cannot be empty (received: {repr(product_id)})")
                # Check if it's a valid ObjectId string
                try:
                    from bson import ObjectId
                    if not ObjectId.is_valid(product_id):
                        raise ValueError(f"Product ID must be a valid ObjectId string or positive integer (received: {repr(product_id)})")
                except ImportError:
                    # BSON not available, accept any non-empty string
                    pass
            else:
                # Check if it's an ObjectId instance
                try:
                    from bson import ObjectId
                    if isinstance(product_id, ObjectId):
                        # ObjectId instance is valid, convert to string for consistency
                        product_id = str(product_id)
                        logger.debug(f"Converted ObjectId to string: {product_id}")
                    else:
                        raise ValueError(f"Product ID must be an integer, string, or ObjectId (got {type(product_id).__name__}: {repr(product_id)})")
                except ImportError:
                    # BSON not available, reject non-string/non-int types
                    raise ValueError(f"Product ID must be an integer or string (got {type(product_id).__name__}: {repr(product_id)})")

            if not isinstance(quantity, int) or quantity <= 0:
                raise ValueError("Quantity must be a positive integer")
            
            # Extract product type flags
            is_line_based = product.get("is_line_based", False)
            is_exclusive_single_use = product.get("is_exclusive_single_use", False)
            
            # Calculate price based on product type with error handling
            try:
                base_price = float(product.get("price") or 0)
                if is_line_based:
                    line_price = product.get("line_price", base_price) or base_price
                    total_price = float(line_price) * quantity
                else:
                    total_price = base_price * quantity

                # Ensure price is never negative
                total_price = max(0.0, total_price)

            except (ValueError, TypeError) as e:
                logger.error(f"Error calculating price for product {product.get('name', 'Unknown')}: {e}")
                total_price = 0.0
            
            # Create base cart item structure
            cart_item = {
                # Core identifiers (multiple formats for compatibility)
                "product_id": product_id,
                "id": product_id,
                "_id": product_id,
                
                # Basic product information
                "name": product.get("name", "Unnamed Product"),
                "description": product.get("description", ""),
                "price": total_price,
                "quantity": quantity,
                
                # Product type flags
                "is_line_based": is_line_based,
                "is_exclusive_single_use": is_exclusive_single_use,
                
                # File and media
                "file_link": product.get("file_link", ""),
                "file_path": product.get("file_path"),
                "file_name": product.get("file_name"),
                "file_size": product.get("file_size"),
                "file_type": product.get("file_type"),
                "file_mime_type": product.get("file_mime_type"),
                "image_url": product.get("image_url"),
                
                # Metadata
                "added_at": datetime.now().isoformat(),
                "cart_item_version": "2.0"  # Version for tracking cart item structure
            }
            
            # Add line-based specific fields
            if is_line_based:
                cart_item.update({
                    "line_price": float(product.get("line_price", base_price)),
                    "max_quantity_per_order": product.get("max_quantity_per_order", 1),
                    "allow_shared_inventory": product.get("allow_shared_inventory", False),
                    "product_type": "Line-Based Product"
                })
            
            # Add exclusive product specific fields
            elif is_exclusive_single_use:
                cart_item.update({
                    "exclusive_file_path": product.get("exclusive_file_path"),
                    "exclusive_file_type": product.get("exclusive_file_type"),
                    "exclusive_file_size": product.get("exclusive_file_size"),
                    "exclusive_file_mime_type": product.get("exclusive_file_mime_type"),
                    "product_type": "Exclusive Single-Use Product"
                })
            else:
                cart_item["product_type"] = "Regular Digital Product"
            
            # Add any additional data provided
            if additional_data and isinstance(additional_data, dict):
                cart_item.update(additional_data)
            
            logger.debug(f"Created cart item for product {product_id} (type: {cart_item['product_type']})")
            return cart_item
            
        except Exception as e:
            logger.error(f"Error creating cart item for product {product_id}: {e}")
            raise ValueError(f"Failed to create cart item: {str(e)}")
    
    @staticmethod
    def create_line_based_cart_item(
        product: Dict[str, Any],
        product_id: Union[int, str, ObjectId],
        quantity: int,
        validation_data: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Create a cart item specifically for line-based products with enhanced metadata.
        
        Args:
            product: Product data dictionary
            product_id: Product ID
            quantity: Quantity of lines
            validation_data: Validation results to include
            
        Returns:
            Enhanced cart item for line-based products
        """
        # Import here to avoid circular imports
        from utils.line_product_manager import LineProductTheme
        
        additional_data = {
            "display_icon": LineProductTheme.EMOJIS.get('line_product', '📦'),
            "price_label": f"${product.get('line_price') or product.get('price') or 0:.2f} × {quantity}",
            "total_label": f"${float(product.get('line_price') or product.get('price') or 0) * quantity:.2f}"
        }
        
        if validation_data:
            additional_data.update({
                "available_stock": validation_data.get("available_stock", 0),
                "stock_status": LineProductTheme.format_stock_status(validation_data.get("available_stock", 0)),
                "validated_at": datetime.now().isoformat()
            })
        
        return CartItemFactory.create_cart_item(product, product_id, quantity, additional_data)
    
    @staticmethod
    def create_exclusive_cart_item(
        product: Dict[str, Any], 
        product_id: int
    ) -> Dict[str, Any]:
        """
        Create a cart item specifically for exclusive single-use products.
        
        Args:
            product: Product data dictionary
            product_id: Product ID
            
        Returns:
            Cart item for exclusive products (quantity always 1)
        """
        # Import here to avoid circular imports
        from utils.exclusive_product_manager import ExclusiveProductTheme
        
        additional_data = {
            "display_icon": ExclusiveProductTheme.EMOJIS.get('exclusive_product', '💎'),
            "price_label": f"${product.get('price') or 0:.2f}",
            "availability_status": ExclusiveProductTheme.format_availability_status(
                product.get("is_purchased", False),
                product.get("purchased_by_user_id"),
                product.get("expiration_date")
            ),
            "file_type": product.get("exclusive_file_type"),
            "file_size": product.get("exclusive_file_size"),
            "mime_type": product.get("exclusive_file_mime_type")
        }
        
        return CartItemFactory.create_cart_item(product, product_id, 1, additional_data)
    
    @staticmethod
    def validate_cart_item(cart_item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a cart item structure.

        Args:
            cart_item: Cart item to validate

        Returns:
            Validation result dictionary
        """
        try:
            required_fields = ["product_id", "name", "price", "quantity"]
            missing_fields = [field for field in required_fields if field not in cart_item]

            if missing_fields:
                return {
                    "valid": False,
                    "error": f"Missing required fields: {', '.join(missing_fields)}"
                }

            # Validate data types - accept integers, strings, and ObjectId instances
            product_id = cart_item["product_id"]
            if isinstance(product_id, int):
                if product_id <= 0:
                    return {"valid": False, "error": "Product ID must be a positive integer"}
            elif isinstance(product_id, str):
                if not product_id.strip():
                    return {"valid": False, "error": "Product ID cannot be empty"}
                # Check if it's a valid ObjectId string
                try:
                    from bson import ObjectId
                    if not ObjectId.is_valid(product_id):
                        return {"valid": False, "error": "Product ID must be a valid ObjectId string or positive integer"}
                except ImportError:
                    # BSON not available, accept any non-empty string
                    pass
            else:
                # Check if it's an ObjectId instance
                try:
                    from bson import ObjectId
                    if not isinstance(product_id, ObjectId):
                        return {"valid": False, "error": f"Product ID must be an integer, string, or ObjectId (got {type(product_id).__name__})"}
                except ImportError:
                    return {"valid": False, "error": f"Product ID must be an integer or string (got {type(product_id).__name__})"}

            if not isinstance(cart_item["quantity"], int) or cart_item["quantity"] <= 0:
                return {"valid": False, "error": "Quantity must be a positive integer"}

            try:
                float(cart_item["price"])
            except (ValueError, TypeError):
                return {"valid": False, "error": "Price must be a valid number"}

            return {"valid": True}

        except Exception as e:
            logger.error(f"Error validating cart item: {e}")
            return {"valid": False, "error": f"Validation error: {str(e)}"}

    @staticmethod
    async def validate_cart_items_before_checkout(cart_items: List[Dict[str, Any]], user_id: int = None) -> Dict[str, Any]:
        """
        Validate all cart items before checkout to prevent race conditions.

        Args:
            cart_items: List of cart items to validate
            user_id: User ID for shared inventory validation

        Returns:
            Validation result with details about any issues
        """
        try:
            from database.operations import get_product

            validation_errors = []

            for item in cart_items:
                # Basic structure validation
                structure_validation = CartItemFactory.validate_cart_item(item)
                if not structure_validation["valid"]:
                    validation_errors.append(f"Item {item.get('name', 'Unknown')}: {structure_validation['error']}")
                    continue

                # Product existence validation
                product_id = item.get("product_id")
                product = get_product(product_id)
                if not product:
                    validation_errors.append(f"Product {product_id} no longer exists")
                    continue

                # Product type specific validation
                if item.get("is_line_based", False):
                    # Check if this is a shared inventory product
                    allow_shared_inventory = product.get("allow_shared_inventory", False)
                    requested_quantity = item.get("quantity", 1)

                    if allow_shared_inventory and user_id:
                        # For shared inventory, check user-specific availability
                        from database.operations import get_available_lines_for_user
                        total_lines = product.get("total_lines", 0)
                        user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
                        available_lines = len(user_available_lines)

                        if available_lines < requested_quantity:
                            validation_errors.append(
                                f"You have already purchased most available content from {item.get('name')}: "
                                f"requested {requested_quantity}, available {available_lines}"
                            )
                    else:
                        # For exclusive inventory, use standard availability check
                        available_lines = product.get("available_lines", 0)
                        if available_lines < requested_quantity:
                            validation_errors.append(
                                f"Insufficient stock for {item.get('name')}: "
                                f"requested {requested_quantity}, available {available_lines}"
                            )

                elif item.get("is_exclusive_single_use", False):
                    # Validate exclusive product availability
                    if product.get("is_purchased", False):
                        validation_errors.append(f"Exclusive product {item.get('name')} is no longer available")

            if validation_errors:
                return {
                    "valid": False,
                    "errors": validation_errors,
                    "error_count": len(validation_errors)
                }

            return {"valid": True}

        except Exception as e:
            logger.error(f"Error validating cart items before checkout: {e}")
            return {
                "valid": False,
                "errors": [f"Validation error: {str(e)}"],
                "error_count": 1
            }


# Global instance for easy access
cart_item_factory = CartItemFactory()
