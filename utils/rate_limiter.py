"""
Rate limiter utility to prevent hitting Telegram API limits.
"""

import asyncio
import logging
import time
from typing import Dict, Optional, Callable, Any, Awaitable, Tuple
from functools import wraps
from aiogram import BaseMiddleware
from aiogram.types import TelegramObject

logger = logging.getLogger(__name__)

# Default rate limits (per minute)
DEFAULT_RATE_LIMIT = 20  # 20 requests per minute for most methods
MESSAGE_RATE_LIMIT = 30  # 30 messages per minute to the same chat
MEDIA_RATE_LIMIT = 15  # 15 media messages per minute

# Burst capacity settings
BURST_MULTIPLIER = 1.5  # Allow bursts up to 150% of the limit
MAX_BURST_SECONDS = 10   # Maximum burst duration in seconds

# Store of last request timestamps by key (chat_id or function name)
last_request_time: Dict[str, float] = {}
# Store recent request times for better rate calculation
recent_requests: Dict[str, list] = {}
request_counts: Dict[str, int] = {}
request_locks: Dict[str, asyncio.Lock] = {}


async def rate_limited_api_call(
    func: Callable[..., Awaitable[Any]], key: str, limit: int, *args, **kwargs
) -> Any:
    """
    Execute a rate-limited API call with proper spacing between requests.

    Args:
        func: Async function to call
        key: Unique key for this rate limit (e.g., chat_id)
        limit: Number of requests allowed per minute
        args, kwargs: Arguments to pass to the function

    Returns:
        The result of the function call
    """
    # Get or create lock for this key
    if key not in request_locks:
        request_locks[key] = asyncio.Lock()
    
    # Initialize if this is the first request for this key
    if key not in recent_requests:
        recent_requests[key] = []

    # Ensure only one request at a time is processed for this key
    async with request_locks[key]:
        current_time = time.time()
        
        # Clean up old requests from tracking (older than 60 seconds)
        recent_requests[key] = [t for t in recent_requests[key] if current_time - t < 60]
        
        # Calculate current rate based on recent requests
        minute_ago = current_time - 60
        recent_count = len(recent_requests[key])
        
        # Initialize if this is the first request for this key
        if key not in last_request_time:
            last_request_time[key] = current_time - (60.0 / limit)  # Allow first request immediately
            request_counts[key] = 0
        
        # Calculate the current rate and how many more requests we can make
        min_interval = 60.0 / limit  # Minimum seconds between requests
        
        # Calculate adaptive delay
        delay_needed = 0
        
        # If we're under the rate limit, calculate minimal delay
        if recent_count < limit:
            # If we have previous requests, space them out evenly
            if recent_requests[key]:
                last_req = recent_requests[key][-1] if recent_requests[key] else (current_time - min_interval)
                delay_needed = max(0, min_interval - (current_time - last_req))
        else:
            # We're at or over the rate limit, calculate when we can send again
            # Find the time when the oldest request will expire from the window
            if recent_count >= limit and recent_requests[key]:
                # Wait until the oldest request is out of the 60-second window
                time_until_slot_available = 60 - (current_time - recent_requests[key][0])
                delay_needed = max(0, time_until_slot_available)
                
                # Cap the maximum delay to avoid excessive waits
                delay_needed = min(delay_needed, 10.0)
                
                logger.warning(
                    f"Rate limit for {key} would be exceeded. Waiting {delay_needed:.2f}s"
                )

        # Wait if needed
        if delay_needed > 0:
            await asyncio.sleep(delay_needed)

        # Execute the API call
        try:
            result = await func(*args, **kwargs)
            
            # Record this request time
            recent_requests[key].append(time.time())
            last_request_time[key] = time.time()
            
            return result
        except Exception as e:
            if "Too Many Requests" in str(e):
                logger.warning(
                    f"Hit Telegram rate limit for {key} despite precautions. Waiting 5s and retrying."
                )
                await asyncio.sleep(5)
                return await func(*args, **kwargs)
            raise


def rate_limit(limit: int = DEFAULT_RATE_LIMIT, key_func=None):
    """
    Decorator to apply rate limiting to a function.

    Args:
        limit: Number of requests allowed per minute
        key_func: Optional function to generate a key from the function arguments
                  If None, the function name is used as the key

    Returns:
        Decorated function with rate limiting
    """

    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Generate a key for this call
            if key_func:
                key = key_func(*args, **kwargs)
            else:
                key = func.__name__

            return await rate_limited_api_call(func, key, limit, *args, **kwargs)

        return wrapper

    return decorator


def chat_key(message=None, *args, **kwargs):
    """Extract chat_id from a message for use as a rate limit key."""
    if message is None:
        return "default_chat"

    if hasattr(message, "chat") and hasattr(message.chat, "id"):
        return f"chat_{message.chat.id}"

    # If the first argument is a chat_id directly (int or str)
    if isinstance(message, (int, str)):
        return f"chat_{message}"

    return "default_chat"


# Decorators for common Telegram API methods
def rate_limit_message_to_chat(limit: int = MESSAGE_RATE_LIMIT):
    """Rate limit messages to the same chat."""
    return rate_limit(limit, key_func=chat_key)


def rate_limit_media(limit: int = MEDIA_RATE_LIMIT):
    """Rate limit media messages (photos, videos, etc.)."""
    return rate_limit(limit, key_func=chat_key)
