from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from database.operations import (
    payments_collection,
    get_user_by_id,
    get_user_balance,
)
import logging


def get_recent_payments(limit: int = 10) -> List[Dict[str, Any]]:
    """Get most recent payment attempts."""
    return list(payments_collection.find().sort("created_at", -1).limit(limit))


def get_payments_by_status(status: str, limit: int = 50) -> List[Dict[str, Any]]:
    """Get payments with specific status."""
    return list(
        payments_collection.find({"status": status}).sort("created_at", -1).limit(limit)
    )


def get_payments_by_user(user_id: int, limit: int = 50) -> List[Dict[str, Any]]:
    """Get payments made by a specific user."""
    return list(
        payments_collection.find({"user_id": user_id})
        .sort("created_at", -1)
        .limit(limit)
    )
