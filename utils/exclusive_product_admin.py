"""
Admin Interface for Exclusive Single-Use Product Management
Provides comprehensive admin tools for managing exclusive products and their lifecycle.
"""

import logging
from typing import Dict, Any, List
from datetime import datetime

from utils.exclusive_product_db_operations import ExclusiveProductDBOperations
from utils.exclusive_product_manager import ExclusiveProductTheme
from database.operations import (
    get_exclusive_product_lifecycle_status
)

logger = logging.getLogger(__name__)

class ExclusiveProductAdmin:
    """Admin interface for comprehensive exclusive product management."""
    
    def __init__(self):
        """Initialize the admin interface."""
        pass
    
    def get_admin_dashboard_data(self) -> Dict[str, Any]:
        """
        Get comprehensive dashboard data for exclusive products admin view.
        
        Returns:
            Dict with dashboard statistics and data
        """
        try:
            # Get all exclusive products for admin
            all_exclusive = ExclusiveProductDBOperations.get_all_exclusive_products_for_admin(
                include_removed=True
            )
            
            # Calculate statistics
            total_exclusive = len(all_exclusive)
            available_count = len([p for p in all_exclusive if not p.get("is_purchased", False) and not p.get("removed_from_listings", False)])
            removed_count = len([p for p in all_exclusive if p.get("removed_from_listings", False)])

            # Calculate revenue
            total_revenue = sum(p.get("price", 0.0) for p in all_exclusive if p.get("is_purchased", False))
            
            # Get recent activity
            recent_purchases = sorted(
                [p for p in all_exclusive if p.get("purchase_date")],
                key=lambda x: x.get("purchase_date", datetime.min),
                reverse=True
            )[:5]
            
            recent_removals = sorted(
                [p for p in all_exclusive if p.get("removal_timestamp")],
                key=lambda x: x.get("removal_timestamp", datetime.min),
                reverse=True
            )[:5]
            
            return {
                "total_exclusive_products": total_exclusive,
                "available_products": available_count,
                "removed_products": removed_count,
                "total_revenue": total_revenue,
                "recent_purchases": recent_purchases,
                "recent_removals": recent_removals,
                "last_updated": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting admin dashboard data: {e}")
            return {"error": f"Failed to get dashboard data: {str(e)}"}
    
    def create_admin_product_listing(
        self, 
        products: List[Dict[str, Any]], 
        show_removed: bool = True,
        category_name: str = None
    ) -> str:
        """
        Create formatted admin product listing with full details.
        
        Args:
            products: List of products to display
            show_removed: Whether to show removed products
            category_name: Optional category name for header
            
        Returns:
            Formatted HTML string for admin display
        """
        try:
            if category_name:
                header = ExclusiveProductTheme.create_header("ADMIN EXCLUSIVE PRODUCTS", f"CATEGORY: {category_name}")
            else:
                header = ExclusiveProductTheme.create_header("ADMIN EXCLUSIVE PRODUCTS", "ALL CATEGORIES")
            
            if not products:
                return header + "\n<i>No exclusive products found.</i>"
            
            # Filter products if needed
            if not show_removed:
                products = [p for p in products if not p.get("removed_from_listings", False)]
            
            # Group products by status
            available = [p for p in products if not p.get("is_purchased", False) and not p.get("removed_from_listings", False)]
            removed = [p for p in products if p.get("removed_from_listings", False)]
            
            text = header
            
            # Available products section
            if available:
                text += f"\n{ExclusiveProductTheme.EMOJIS['available']} <b>AVAILABLE PRODUCTS ({len(available)})</b>\n"
                text += f"{ExclusiveProductTheme.HEADER_DIVIDER}\n"
                for i, product in enumerate(available, 1):
                    text += self._format_admin_product_entry(product, i, "available")
                text += "\n"
            
            # Removed products section
            if removed and show_removed:
                text += f"\n{ExclusiveProductTheme.EMOJIS['delivery']} <b>DELIVERED & REMOVED ({len(removed)})</b>\n"
                text += f"{ExclusiveProductTheme.HEADER_DIVIDER}\n"
                for i, product in enumerate(removed, 1):
                    text += self._format_admin_product_entry(product, i, "removed")
                text += "\n"

            # Summary
            text += f"\n{ExclusiveProductTheme.create_section_break()}"
            text += f"<b>SUMMARY:</b> {len(available)} Available | {len(removed)} Delivered"
            
            return text
            
        except Exception as e:
            logger.error(f"Error creating admin product listing: {e}")
            return f"Error creating product listing: {str(e)}"
    
    def _format_admin_product_entry(self, product: Dict[str, Any], index: int, status: str) -> str:
        """Format a single product entry for admin display."""
        name = product.get("name", "Unknown Product")
        price = product.get("price", 0.0)
        product_id = product.get("_id") or product.get("id")
        
        entry = f"<b>{index}. {name}</b> (ID: <code>{product_id}</code>)\n"
        entry += f"   {ExclusiveProductTheme.format_price(price)}\n"
        
        if status == "available":
            created_at = product.get("created_at")
            if created_at:
                # Safe date formatting
                if isinstance(created_at, str):
                    date_str = created_at
                elif hasattr(created_at, 'strftime'):
                    date_str = created_at.strftime('%Y-%m-%d %H:%M')
                else:
                    date_str = str(created_at)
                entry += f"   📅 <b>Created:</b> <code>{date_str}</code>\n"
        

        elif status == "removed":
            removal_timestamp = product.get("removal_timestamp")
            final_buyer = product.get("final_buyer_user_id")
            order_number = product.get("completion_order_number")
            if removal_timestamp:
                # Safe date formatting
                if isinstance(removal_timestamp, str):
                    date_str = removal_timestamp
                elif hasattr(removal_timestamp, 'strftime'):
                    date_str = removal_timestamp.strftime('%Y-%m-%d %H:%M')
                else:
                    date_str = str(removal_timestamp)
                entry += f"   📤 <b>Delivered:</b> <code>{date_str}</code>\n"
            if final_buyer:
                entry += f"   👤 <b>Final Buyer:</b> <code>{final_buyer}</code>\n"
            if order_number:
                entry += f"   🧾 <b>Order:</b> <code>#{order_number}</code>\n"
        
        entry += f"   {ExclusiveProductTheme.SECTION_DIVIDER}\n"
        return entry
    
    def get_product_lifecycle_details(self, product_id: Any) -> str:
        """
        Get detailed lifecycle information for a specific product.
        
        Args:
            product_id: Product ID
            
        Returns:
            Formatted lifecycle details
        """
        try:
            status = get_exclusive_product_lifecycle_status(product_id)
            
            if "error" in status:
                return f"❌ Error: {status['error']}"
            
            text = ExclusiveProductTheme.create_header("PRODUCT LIFECYCLE", status.get("product_name", "Unknown"))
            
            # Basic info
            text += f"🆔 <b>Product ID:</b> <code>{status.get('product_id')}</code>\n"
            text += f"📊 <b>Status:</b> <code>{status.get('lifecycle_status', 'unknown')}</code>\n"
            text += f"🔒 <b>Purchased:</b> {'✅ Yes' if status.get('is_purchased') else '❌ No'}\n"
            text += f"📤 <b>Delivered:</b> {'✅ Yes' if status.get('delivery_confirmed') else '❌ No'}\n"
            text += f"🚫 <b>Removed:</b> {'✅ Yes' if status.get('removed_from_listings') else '❌ No'}\n"
            
            # Timeline
            text += f"\n{ExclusiveProductTheme.create_section_break()}"
            text += "<b>TIMELINE:</b>\n"
            
            # Helper function for safe date formatting
            def format_date_safe(date_obj):
                if not date_obj:
                    return None
                if isinstance(date_obj, str):
                    return date_obj
                elif hasattr(date_obj, 'strftime'):
                    return date_obj.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    return str(date_obj)

            created_at = status.get("created_at")
            if created_at:
                date_str = format_date_safe(created_at)
                if date_str:
                    text += f"📅 <b>Created:</b> <code>{date_str}</code>\n"

            purchase_date = status.get("purchase_date")
            if purchase_date:
                date_str = format_date_safe(purchase_date)
                if date_str:
                    text += f"💰 <b>Purchased:</b> <code>{date_str}</code>\n"

            removal_timestamp = status.get("removal_timestamp")
            if removal_timestamp:
                date_str = format_date_safe(removal_timestamp)
                if date_str:
                    text += f"📤 <b>Delivered & Removed:</b> <code>{date_str}</code>\n"
            
            # User info
            purchased_by = status.get("purchased_by_user_id")
            final_buyer = status.get("final_buyer_user_id")
            if purchased_by or final_buyer:
                text += f"\n{ExclusiveProductTheme.create_section_break()}"
                text += "<b>USER INFO:</b>\n"
                if purchased_by:
                    text += f"👤 <b>Purchased by:</b> <code>{purchased_by}</code>\n"
                if final_buyer and final_buyer != purchased_by:
                    text += f"👤 <b>Final buyer:</b> <code>{final_buyer}</code>\n"
            
            # Order info
            order_number = status.get("completion_order_number")
            if order_number:
                text += f"🧾 <b>Completion order:</b> <code>#{order_number}</code>\n"
            
            # Rollback info
            rollback_reason = status.get("rollback_reason")
            rollback_timestamp = status.get("rollback_timestamp")
            if rollback_reason or rollback_timestamp:
                text += f"\n{ExclusiveProductTheme.create_section_break()}"
                text += "<b>ROLLBACK INFO:</b>\n"
                if rollback_reason:
                    text += f"⚠️ <b>Reason:</b> <code>{rollback_reason}</code>\n"
                if rollback_timestamp:
                    date_str = format_date_safe(rollback_timestamp)
                    if date_str:
                        text += f"🕐 <b>Timestamp:</b> <code>{date_str}</code>\n"
            
            return text
            
        except Exception as e:
            logger.error(f"Error getting product lifecycle details for {product_id}: {e}")
            return f"❌ Error getting lifecycle details: {str(e)}"


# Create global instance
exclusive_product_admin = ExclusiveProductAdmin()
