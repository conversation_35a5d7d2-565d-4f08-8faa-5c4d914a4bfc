"""
Line-based inventory management for digital products.
Handles text files where each line represents a sellable item.
"""

import os
import logging
import asyncio
import tempfile
import shutil
import re
from typing import List, Optional, Dict, Any, Tuple
from pathlib import Path
from datetime import datetime
import threading

logger = logging.getLogger(__name__)

# Thread lock for file operations to prevent race conditions
_file_lock = threading.Lock()

class LineInventoryManager:
    """Manages line-based inventory for digital products."""

    def __init__(self, base_inventory_dir: str = "uploads/inventory"):
        self.base_inventory_dir = Path(base_inventory_dir)
        self.base_inventory_dir.mkdir(parents=True, exist_ok=True)
        # Separator pattern for structured records
        self.record_separator = "-" * 35  # Matches the demo data format
        
    def _validate_file_path_security(self, file_path: str) -> Dict[str, Any]:
        """
        Enhanced file path validation for security issues including directory traversal.

        Args:
            file_path: Path to validate

        Returns:
            Dict with validation results
        """
        try:
            # Input sanitization - reject null bytes and control characters
            if '\x00' in file_path or any(ord(c) < 32 for c in file_path if c not in '\t\n\r'):
                return {
                    "valid": False,
                    "error": "File path contains invalid characters",
                    "security_issue": "invalid_characters"
                }

            # Convert to Path object and resolve to absolute path
            path_obj = Path(file_path).resolve()

            # Check if the resolved path is within allowed directories
            allowed_bases = [
                self.base_inventory_dir.resolve(),
                Path("uploads").resolve(),
                Path("uploads/inventory").resolve(),
                Path("uploads/temp").resolve(),
                Path("uploads/user_uploads").resolve()
            ]

            # Ensure the file path is within one of the allowed directories
            is_safe = any(
                str(path_obj).startswith(str(base))
                for base in allowed_bases
            )

            if not is_safe:
                return {
                    "valid": False,
                    "error": "File path is outside allowed directories",
                    "security_issue": "path_traversal"
                }

            # Enhanced check for suspicious path components
            path_parts = path_obj.parts
            suspicious_parts = {"..", "~", "$", "|", "&", ";", "`", "<", ">", ":", "*", "?", '"'}
            if any(part in suspicious_parts for part in path_parts):
                return {
                    "valid": False,
                    "error": "File path contains suspicious components",
                    "security_issue": "suspicious_path"
                }

            # Check for Windows reserved names
            reserved_names = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4",
                            "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2",
                            "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"}
            if any(part.upper() in reserved_names for part in path_parts):
                return {
                    "valid": False,
                    "error": "File path contains reserved system names",
                    "security_issue": "reserved_names"
                }

            # Validate file extension
            if not str(path_obj).lower().endswith('.txt'):
                return {
                    "valid": False,
                    "error": "Only .txt files are allowed for inventory",
                    "security_issue": "invalid_extension"
                }

            return {
                "valid": True,
                "resolved_path": str(path_obj)
            }

        except Exception as e:
            logger.error(f"Error validating file path security for {file_path}: {e}")
            return {
                "valid": False,
                "error": f"Path validation error: {str(e)}",
                "security_issue": "validation_error"
            }

    def validate_inventory_file(self, file_path: str) -> Dict[str, Any]:
        """
        Validate an inventory file and return metadata with security checks.

        Args:
            file_path: Path to the inventory file

        Returns:
            Dict with validation results and metadata
        """
        try:
            # First, validate path security
            path_security = self._validate_file_path_security(file_path)
            if not path_security["valid"]:
                return {
                    "valid": False,
                    "error": path_security["error"],
                    "security_issue": path_security.get("security_issue")
                }

            # Use the resolved secure path
            secure_path = path_security["resolved_path"]

            if not os.path.exists(secure_path):
                return {"valid": False, "error": "File does not exist"}

            if not secure_path.lower().endswith('.txt'):
                return {"valid": False, "error": "File must be a .txt file"}

            # Read and validate file content
            with open(secure_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Check if this is structured data
            is_structured = (self.record_separator in content and
                           ('Company name:' in content or 'SSN:' in content or 'EIN:' in content))

            if is_structured:
                # For structured data, count records
                records = self.parse_structured_records(content)

                if not records:
                    return {"valid": False, "error": "File contains no valid records"}

                # Check for duplicate records (based on raw content)
                record_contents = [record.get('raw_content', '').strip() for record in records]
                unique_records = set(record_contents)
                duplicates = len(record_contents) - len(unique_records)

                # Sample format detection - show first few records
                sample_lines = []
                for i, record in enumerate(records[:3]):
                    sample_lines.append(f"Record {i+1}: {record.get('raw_content', '')[:100]}...")

                return {
                    "valid": True,
                    "total_lines": len(records),  # This is actually record count for structured data
                    "duplicates": duplicates,
                    "sample_lines": sample_lines,
                    "file_size": os.path.getsize(secure_path),
                    "encoding": "utf-8",
                    "secure_path": secure_path,
                    "is_structured": True
                }
            else:
                # For non-structured data, use original line counting
                lines = content.splitlines()
                valid_lines = [line.strip() for line in lines if line.strip()]

                if not valid_lines:
                    return {"valid": False, "error": "File contains no valid lines"}

                # Check for duplicate lines
                unique_lines = set(valid_lines)
                duplicates = len(valid_lines) - len(unique_lines)

                # Sample format detection
                sample_lines = valid_lines[:3] if len(valid_lines) >= 3 else valid_lines

                return {
                    "valid": True,
                    "total_lines": len(valid_lines),
                    "duplicates": duplicates,
                    "sample_lines": sample_lines,
                    "file_size": os.path.getsize(secure_path),
                    "encoding": "utf-8",
                    "secure_path": secure_path,
                    "is_structured": False
                }

        except UnicodeDecodeError:
            return {"valid": False, "error": "File encoding not supported. Please use UTF-8."}
        except Exception as e:
            logger.error(f"Error validating inventory file {file_path}: {e}")
            return {"valid": False, "error": f"Validation error: {str(e)}"}
    
    def create_inventory_file(self, product_id: int, source_file_path: str) -> Optional[str]:
        """
        Create a new inventory file for a product.
        
        Args:
            product_id: The product ID
            source_file_path: Path to the source inventory file
            
        Returns:
            Path to the created inventory file or None if failed
        """
        try:
            # Validate source file first
            validation = self.validate_inventory_file(source_file_path)
            if not validation["valid"]:
                logger.error(f"Invalid inventory file: {validation['error']}")
                return None
                
            # Ensure base inventory directory exists
            if not self.base_inventory_dir.exists():
                logger.info(f"Creating base inventory directory: {self.base_inventory_dir}")
                self.base_inventory_dir.mkdir(parents=True, exist_ok=True)

            # Create product-specific inventory directory
            product_dir = self.base_inventory_dir / f"product_{product_id}"
            logger.debug(f"Creating product directory: {product_dir}")
            product_dir.mkdir(parents=True, exist_ok=True)
            
            # Create inventory file path
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            inventory_file = product_dir / f"inventory_{timestamp}.txt"
            
            # Import file resource manager
            from utils.file_resource_manager import file_resource_manager

            # Get the secure path from validation
            secure_path = validation.get("secure_path")
            if not secure_path:
                logger.error(f"No secure path found in validation for {source_file_path}")
                return None

            # Copy and clean the source file using managed file operations
            with file_resource_manager.managed_file(secure_path, 'r', encoding='utf-8') as source:
                lines = source.readlines()

            # Clean lines (remove empty lines and strip whitespace)
            clean_lines = [line.strip() for line in lines if line.strip()]

            # Validate that we have clean lines after processing
            if not clean_lines:
                logger.error(f"No valid lines found after cleaning file {source_file_path}")
                return None

            # Write cleaned inventory using managed file operations
            with file_resource_manager.managed_file(str(inventory_file), 'w', encoding='utf-8') as target:
                for line in clean_lines:
                    target.write(line + '\n')
                    
            # Create backup
            backup_file = product_dir / f"backup_{timestamp}.txt"
            shutil.copy2(inventory_file, backup_file)
            
            logger.info(f"Created inventory file for product {product_id}: {inventory_file}")
            return str(inventory_file)
            
        except Exception as e:
            logger.error(f"Error creating inventory file for product {product_id}: {e}", exc_info=True)
            return None
    
    def get_available_lines_count(self, inventory_file_path: str) -> int:
        """
        Get the number of available lines/records in an inventory file.
        For structured data, counts complete records. For simple data, counts individual lines.

        Args:
            inventory_file_path: Path to the inventory file

        Returns:
            Number of available lines/records
        """
        try:
            if not os.path.exists(inventory_file_path):
                return 0

            # Check if this is structured data
            if self.is_structured_inventory(inventory_file_path):
                return self.get_available_records_count(inventory_file_path)

            # For non-structured data, use original line counting
            from utils.file_resource_manager import file_resource_manager

            with file_resource_manager.managed_file(inventory_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            # Count non-empty lines that are not marked as used
            available_lines = [line.strip() for line in lines if line.strip() and not line.strip().startswith("USED:")]
            return len(available_lines)

        except Exception as e:
            logger.error(f"Error counting lines in {inventory_file_path}: {e}")
            return 0

    def get_available_records_count(self, inventory_file_path: str) -> int:
        """
        Get the number of available structured records in an inventory file.
        Each record is delimited by dashed line separators.

        Args:
            inventory_file_path: Path to the inventory file

        Returns:
            Number of available records
        """
        try:
            if not os.path.exists(inventory_file_path):
                return 0

            from utils.file_resource_manager import file_resource_manager

            with file_resource_manager.managed_file(inventory_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parse all records using existing method
            all_records = self.parse_structured_records(content)

            # Count records that are not marked as used
            available_records = []
            for record in all_records:
                raw_content = record.get('raw_content', '')
                # Check if the record is marked as used (starts with USED: or contains USED: at the beginning)
                if not raw_content.strip().startswith("USED:"):
                    available_records.append(record)

            logger.debug(f"Found {len(available_records)} available records out of {len(all_records)} total records in {inventory_file_path}")
            return len(available_records)

        except Exception as e:
            logger.error(f"Error counting records in {inventory_file_path}: {e}")
            return 0

    def get_inventory_stats(self, inventory_file_path: str) -> Dict[str, int]:
        """
        Get detailed inventory statistics including used and available lines/records.
        For structured data, counts complete records. For simple data, counts individual lines.

        Args:
            inventory_file_path: Path to the inventory file

        Returns:
            Dictionary with total_lines, available_lines, used_lines counts
        """
        try:
            if not os.path.exists(inventory_file_path):
                return {"total_lines": 0, "available_lines": 0, "used_lines": 0}

            # Check if this is structured data
            if self.is_structured_inventory(inventory_file_path):
                return self.get_record_stats(inventory_file_path)

            # For non-structured data, use original line counting
            from utils.file_resource_manager import file_resource_manager

            with file_resource_manager.managed_file(inventory_file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            total_lines = 0
            used_lines = 0
            available_lines = 0

            for line in lines:
                line = line.strip()
                if line:
                    total_lines += 1
                    if line.startswith("USED:"):
                        used_lines += 1
                    else:
                        available_lines += 1

            return {
                "total_lines": total_lines,
                "available_lines": available_lines,
                "used_lines": used_lines
            }

        except Exception as e:
            logger.error(f"Error getting inventory stats for {inventory_file_path}: {e}")
            return {"total_lines": 0, "available_lines": 0, "used_lines": 0}

    def get_record_stats(self, inventory_file_path: str) -> Dict[str, int]:
        """
        Get detailed inventory statistics for structured records.

        Args:
            inventory_file_path: Path to the inventory file

        Returns:
            Dictionary with total_lines, available_lines, used_lines counts (for records)
        """
        try:
            if not os.path.exists(inventory_file_path):
                return {"total_lines": 0, "available_lines": 0, "used_lines": 0}

            from utils.file_resource_manager import file_resource_manager

            with file_resource_manager.managed_file(inventory_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parse all records using existing method
            all_records = self.parse_structured_records(content)

            total_records = len(all_records)
            used_records = 0
            available_records = 0

            for record in all_records:
                raw_content = record.get('raw_content', '')
                # Check if the record is marked as used
                if raw_content.strip().startswith("USED:"):
                    used_records += 1
                else:
                    available_records += 1

            logger.debug(f"Record stats for {inventory_file_path}: Total={total_records}, Available={available_records}, Used={used_records}")

            return {
                "total_lines": total_records,  # Keep the same key names for compatibility
                "available_lines": available_records,
                "used_lines": used_records
            }

        except Exception as e:
            logger.error(f"Error getting record stats for {inventory_file_path}: {e}")
            return {"total_lines": 0, "available_lines": 0, "used_lines": 0}
    
    def extract_lines(self, inventory_file_path: str, quantity: int) -> Tuple[List[str], bool]:
        """
        Extract specified number of lines/records from inventory file.
        For structured data, extracts complete records. For simple data, extracts individual lines.
        This operation is atomic and thread-safe.

        Args:
            inventory_file_path: Path to the inventory file
            quantity: Number of lines/records to extract

        Returns:
            Tuple of (extracted_lines, success)
        """
        with _file_lock:
            try:
                if not os.path.exists(inventory_file_path):
                    logger.error(f"Inventory file not found: {inventory_file_path}")
                    return [], False

                # Check if this is structured data
                if self.is_structured_inventory(inventory_file_path):
                    # For structured data, use record extraction (without additional file lock)
                    extracted_records, success = self._extract_structured_records_unlocked(inventory_file_path, quantity)
                    if success:
                        # Convert records to their raw content for compatibility
                        extracted_lines = [record.get('raw_content', '') for record in extracted_records]
                        return extracted_lines, True
                    else:
                        return [], False

                # For non-structured data, use original line extraction
                from utils.file_resource_manager import file_resource_manager

                # Read current inventory using managed file operations
                with file_resource_manager.managed_file(inventory_file_path, 'r', encoding='utf-8') as f:
                    file_lines = f.readlines()

                # Filter out used lines for exclusive inventory
                all_lines = []
                for line in file_lines:
                    line = line.strip()
                    if line and not line.startswith("USED:"):
                        all_lines.append(line)

                if len(all_lines) < quantity:
                    logger.warning(f"Not enough lines available. Requested: {quantity}, Available: {len(all_lines)}")
                    return [], False

                # Extract requested lines
                extracted_lines = all_lines[:quantity]
                remaining_lines = all_lines[quantity:]

                # Create backup before modifying using safe file operations
                backup_path = inventory_file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                backup_result = file_resource_manager.safe_file_copy(inventory_file_path, backup_path, create_backup=False)

                if not backup_result["success"]:
                    logger.error(f"Failed to create backup: {backup_result['error']}")
                    return [], False

                # Write remaining lines back to file using managed file operations
                with file_resource_manager.managed_file(inventory_file_path, 'w', encoding='utf-8') as f:
                    for line in remaining_lines:
                        f.write(line + '\n')

                logger.info(f"Extracted {len(extracted_lines)} lines from {inventory_file_path}")
                return extracted_lines, True

            except Exception as e:
                logger.error(f"Error extracting lines from {inventory_file_path}: {e}",
                           extra={
                               "file_path": inventory_file_path,
                               "quantity": quantity,
                               "error_type": type(e).__name__,
                               "operation": "extract_lines"
                           })
                return [], False

    def extract_lines_for_user(self, inventory_file_path: str, quantity: int, user_id: int, product_id: Any, allow_shared_inventory: bool = False) -> Tuple[List[str], bool, List[int]]:
        """
        Extract lines/records from inventory with support for shared inventory mode.
        For structured data, extracts complete records. For simple data, extracts individual lines.

        Args:
            inventory_file_path: Path to the inventory file
            quantity: Number of lines/records to extract
            user_id: User ID making the purchase
            product_id: Product ID
            allow_shared_inventory: Whether this product uses shared inventory

        Returns:
            Tuple of (extracted_lines, success, line_indices)
        """
        with _file_lock:
            try:
                if not os.path.exists(inventory_file_path):
                    logger.error(f"Inventory file not found: {inventory_file_path}")
                    return [], False, []

                # Check if this is structured data
                if self.is_structured_inventory(inventory_file_path):
                    return self._extract_structured_records_for_user(
                        inventory_file_path, quantity, user_id, product_id, allow_shared_inventory
                    )

                # For non-structured data, use original logic
                from utils.file_resource_manager import file_resource_manager

                # Read current inventory using managed file operations
                with file_resource_manager.managed_file(inventory_file_path, 'r', encoding='utf-8') as f:
                    file_lines = f.readlines()

                # For shared inventory, we need to work with all lines (including used ones) for proper indexing
                # but filter out used lines when determining availability
                all_lines = [line.strip() for line in file_lines if line.strip()]

                # Create a mapping of original indices to actual line content (without USED: prefix)
                clean_lines = []
                original_indices = []
                for i, line in enumerate(all_lines):
                    if line.startswith("USED:"):
                        # Keep track of used lines but extract clean content
                        clean_lines.append(line[5:])  # Remove "USED:" prefix
                    else:
                        clean_lines.append(line)
                    original_indices.append(i)

                if allow_shared_inventory:
                    # For shared inventory, get lines that user hasn't purchased before
                    from database.operations import get_user_purchased_lines
                    purchased_indices = get_user_purchased_lines(user_id, product_id)

                    # For shared inventory, all lines are available to users who haven't purchased them
                    # We ignore "USED:" prefix since lines can be purchased by multiple users
                    available_indices = []
                    for i, line in enumerate(all_lines):
                        # Skip empty lines but allow "USED:" lines for shared inventory
                        clean_line = line.replace("USED:", "") if line.startswith("USED:") else line
                        if clean_line.strip() and i not in purchased_indices:
                            available_indices.append(i)

                    available_indices = sorted(available_indices)  # Ensure consistent ordering

                    if len(available_indices) < quantity:
                        logger.warning(f"User {user_id} has insufficient new lines available. Requested: {quantity}, Available: {len(available_indices)}, Total lines: {len(all_lines)}, Already purchased: {len(purchased_indices)}")
                        return [], False, []

                    # Select the first available lines for this user (ensures consistent ordering)
                    selected_indices = available_indices[:quantity]
                    extracted_lines = [clean_lines[i] for i in selected_indices]  # Use clean lines without USED: prefix

                    # Validate that we extracted the correct number of lines
                    if len(extracted_lines) != quantity:
                        logger.error(f"Line extraction mismatch for user {user_id}. Expected: {quantity}, Got: {len(extracted_lines)}")
                        return [], False, []

                    # For shared inventory, we DON'T modify the inventory file
                    # Lines remain available for other users to purchase
                    # Only the purchase history tracks who has purchased what

                    logger.info(f"Extracted {len(extracted_lines)} lines (indices: {selected_indices}) for user {user_id} from shared inventory {inventory_file_path} - file unchanged for shared access")
                    return extracted_lines, True, selected_indices

                else:
                    # For exclusive inventory, use the original logic
                    if len(all_lines) < quantity:
                        logger.warning(f"Not enough lines available. Requested: {quantity}, Available: {len(all_lines)}")
                        return [], False, []

                    # Extract requested lines
                    extracted_lines = all_lines[:quantity]
                    selected_indices = list(range(quantity))
                    remaining_lines = all_lines[quantity:]

                    # Create backup before modifying using safe file operations
                    backup_path = inventory_file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                    backup_result = file_resource_manager.safe_file_copy(inventory_file_path, backup_path, create_backup=False)

                    if not backup_result["success"]:
                        logger.error(f"Failed to create backup: {backup_result['error']}")
                        return [], False, []

                    # Write remaining lines back to file using managed file operations
                    with file_resource_manager.managed_file(inventory_file_path, 'w', encoding='utf-8') as f:
                        for line in remaining_lines:
                            f.write(line + '\n')

                    logger.info(f"Extracted {len(extracted_lines)} lines from exclusive inventory {inventory_file_path}")
                    return extracted_lines, True, selected_indices

            except Exception as e:
                logger.error(f"Error extracting lines for user {user_id} from {inventory_file_path}: {e}",
                           extra={
                               "user_id": user_id,
                               "product_id": product_id,
                               "quantity": quantity,
                               "allow_shared_inventory": allow_shared_inventory,
                               "file_path": inventory_file_path,
                               "error_type": type(e).__name__
                           })
                return [], False, []

    def _extract_structured_records_for_user(self, inventory_file_path: str, quantity: int, user_id: int, product_id: Any, allow_shared_inventory: bool = False) -> Tuple[List[str], bool, List[int]]:
        """
        Extract structured records for a specific user with shared inventory support.

        Args:
            inventory_file_path: Path to the inventory file
            quantity: Number of records to extract
            user_id: User ID making the purchase
            product_id: Product ID
            allow_shared_inventory: Whether this product uses shared inventory

        Returns:
            Tuple of (extracted_record_contents, success, record_indices)
        """
        try:
            from utils.file_resource_manager import file_resource_manager

            # Read current inventory using managed file operations
            with file_resource_manager.managed_file(inventory_file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parse all records
            all_records = self.parse_structured_records(content)

            if allow_shared_inventory:
                # For shared inventory, get records that user hasn't purchased before
                from database.operations import get_user_purchased_lines
                purchased_indices = get_user_purchased_lines(user_id, product_id)

                # Find available records (not purchased by this user)
                available_indices = []
                for i, record in enumerate(all_records):
                    raw_content = record.get('raw_content', '')
                    # For shared inventory, records can be marked as USED: but still available to other users
                    if i not in purchased_indices and raw_content.strip():
                        available_indices.append(i)

                available_indices = sorted(available_indices)  # Ensure consistent ordering

                if len(available_indices) < quantity:
                    logger.warning(f"User {user_id} has insufficient new records available. Requested: {quantity}, Available: {len(available_indices)}, Total records: {len(all_records)}, Already purchased: {len(purchased_indices)}")
                    return [], False, []

                # Select the first available records for this user
                selected_indices = available_indices[:quantity]
                extracted_records = [all_records[i] for i in selected_indices]

                # Convert records to their raw content for delivery
                extracted_lines = [record.get('raw_content', '') for record in extracted_records]

                # For shared inventory, we DON'T modify the inventory file
                # Records remain available for other users to purchase
                logger.info(f"Extracted {len(extracted_lines)} records (indices: {selected_indices}) for user {user_id} from shared structured inventory {inventory_file_path}")
                return extracted_lines, True, selected_indices

            else:
                # For exclusive inventory, filter out already used records
                available_records = []
                available_indices = []
                for i, record in enumerate(all_records):
                    raw_content = record.get('raw_content', '')
                    if not raw_content.strip().startswith("USED:"):
                        available_records.append(record)
                        available_indices.append(i)

                if len(available_records) < quantity:
                    logger.warning(f"Not enough records available. Requested: {quantity}, Available: {len(available_records)}")
                    return [], False, []

                # Extract requested records
                extracted_records = available_records[:quantity]
                selected_indices = available_indices[:quantity]
                remaining_records = available_records[quantity:]

                # Create backup before modifying
                backup_path = inventory_file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                backup_result = file_resource_manager.safe_file_copy(inventory_file_path, backup_path, create_backup=False)

                if not backup_result["success"]:
                    logger.error(f"Failed to create backup: {backup_result['error']}")
                    return [], False, []

                # Write remaining records back to file
                with file_resource_manager.managed_file(inventory_file_path, 'w', encoding='utf-8') as f:
                    for i, record in enumerate(remaining_records):
                        if i > 0:  # Add separator before each record except the first
                            f.write(f"\n{self.record_separator}\n")
                        f.write(record['raw_content'])
                    if remaining_records:  # Add final separator if there are remaining records
                        f.write(f"\n{self.record_separator}\n")

                # Convert records to their raw content for delivery
                extracted_lines = [record.get('raw_content', '') for record in extracted_records]

                logger.info(f"Extracted {len(extracted_lines)} records from exclusive structured inventory {inventory_file_path}")
                return extracted_lines, True, selected_indices

        except Exception as e:
            logger.error(f"Error extracting structured records for user {user_id} from {inventory_file_path}: {e}")
            return [], False, []

    def create_delivery_file(self, extracted_lines: List[str], product_name: str, order_number: int) -> Optional[str]:
        """
        Create a delivery file containing the extracted lines.
        
        Args:
            extracted_lines: Lines to include in delivery file
            product_name: Name of the product
            order_number: Order number for filename
            
        Returns:
            Path to the created delivery file or None if failed
        """
        try:
            # Create temporary delivery file
            delivery_dir = self.base_inventory_dir / "deliveries"
            delivery_dir.mkdir(parents=True, exist_ok=True)
            
            # Clean product name for filename
            clean_name = "".join(c for c in product_name if c.isalnum() or c in (' ', '-', '_')).strip()
            clean_name = clean_name.replace(' ', '_')
            
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            delivery_file = delivery_dir / f"{clean_name}_order_{order_number}_{timestamp}.txt"
            
            # Write delivery content
            with open(delivery_file, 'w', encoding='utf-8') as f:
                f.write(f"# {product_name} - Order #{order_number}\n")
                f.write(f"# Delivered on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Quantity: {len(extracted_lines)} items\n")
                f.write("#" + "="*50 + "\n\n")
                
                for i, line in enumerate(extracted_lines, 1):
                    f.write(f"{line}\n")
                    
            logger.info(f"Created delivery file: {delivery_file}")
            return str(delivery_file)
            
        except Exception as e:
            logger.error(f"Error creating delivery file: {e}")
            return None
    
    def add_inventory_lines(self, inventory_file_path: str, new_lines: List[str]) -> bool:
        """
        Add new lines to an existing inventory file.
        
        Args:
            inventory_file_path: Path to the inventory file
            new_lines: Lines to add
            
        Returns:
            True if successful, False otherwise
        """
        with _file_lock:
            try:
                from utils.file_resource_manager import file_resource_manager

                # Create backup using safe file operations
                backup_path = inventory_file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                if os.path.exists(inventory_file_path):
                    backup_result = file_resource_manager.safe_file_copy(inventory_file_path, backup_path, create_backup=False)
                    if not backup_result["success"]:
                        logger.error(f"Failed to create backup: {backup_result['error']}")
                        return False

                # Append new lines using managed file operations
                with file_resource_manager.managed_file(inventory_file_path, 'a', encoding='utf-8') as f:
                    for line in new_lines:
                        if line.strip():  # Only add non-empty lines
                            f.write(line.strip() + '\n')

                logger.info(f"Added {len(new_lines)} lines to {inventory_file_path}")
                return True

            except Exception as e:
                logger.error(f"Error adding lines to {inventory_file_path}: {e}")
                return False
    
    def get_preview_lines(self, inventory_file_path: str, count: int = 3) -> List[str]:
        """
        Get preview lines from inventory file without revealing actual content.
        
        Args:
            inventory_file_path: Path to the inventory file
            count: Number of preview lines to return
            
        Returns:
            List of preview lines with content masked
        """
        try:
            if not os.path.exists(inventory_file_path):
                return []
                
            with open(inventory_file_path, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f.readlines() if line.strip()]
                
            if not lines:
                return []
                
            # Create masked preview
            preview_lines = []
            sample_lines = lines[:count] if len(lines) >= count else lines
            
            for line in sample_lines:
                if len(line) <= 10:
                    # For short lines, show first 2 and last 2 characters
                    masked = line[:2] + "*" * (len(line) - 4) + line[-2:] if len(line) > 4 else "*" * len(line)
                else:
                    # For longer lines, show first 3 and last 3 characters
                    masked = line[:3] + "*" * (len(line) - 6) + line[-3:]
                    
                preview_lines.append(masked)
                
            return preview_lines
            
        except Exception as e:
            logger.error(f"Error getting preview lines from {inventory_file_path}: {e}")
            return []

    def parse_structured_records(self, file_content: str) -> List[Dict[str, Any]]:
        """
        Parse structured records from file content.
        Each record is separated by dashes and contains personal/company information.

        Args:
            file_content: Raw file content containing structured records

        Returns:
            List of parsed record dictionaries
        """
        try:
            records = []
            # Split by the separator pattern
            raw_records = file_content.split(self.record_separator)

            for raw_record in raw_records:
                raw_record = raw_record.strip()
                if not raw_record:
                    continue

                record = self._parse_single_record(raw_record)
                if record:
                    records.append(record)

            logger.info(f"Parsed {len(records)} structured records")
            return records

        except Exception as e:
            logger.error(f"Error parsing structured records: {e}")
            return []

    def _parse_single_record(self, raw_record: str) -> Optional[Dict[str, Any]]:
        """
        Parse a single structured record into a dictionary.

        Args:
            raw_record: Raw record text

        Returns:
            Parsed record dictionary or None if parsing fails
        """
        try:
            record = {
                'personal_info': {},
                'company_info': {},
                'raw_content': raw_record.strip()
            }

            lines = [line.strip() for line in raw_record.split('\n') if line.strip()]

            # Parse personal information (first section)
            personal_section = True
            for line in lines:
                if line.startswith('Company name:'):
                    personal_section = False

                if personal_section:
                    self._parse_personal_line(line, record['personal_info'])
                else:
                    self._parse_company_line(line, record['company_info'])

            # Validate that we have essential information
            if record['personal_info'].get('name') or record['company_info'].get('name'):
                return record
            else:
                logger.warning(f"Record missing essential information: {raw_record[:100]}...")
                return None

        except Exception as e:
            logger.error(f"Error parsing single record: {e}")
            return None

    def _parse_personal_line(self, line: str, personal_info: Dict[str, str]):
        """Parse a line from the personal information section."""
        try:
            if line.startswith('SSN:'):
                personal_info['ssn'] = line.replace('SSN:', '').strip()
            elif line.startswith('DOB:'):
                personal_info['dob'] = line.replace('DOB:', '').strip()
            elif line.startswith('Phone:'):
                personal_info['phone'] = line.replace('Phone:', '').strip()
            elif line.startswith('E-mal:'):  # Note: keeping original typo from demo data
                personal_info['email'] = line.replace('E-mal:', '').strip()
            elif not line.startswith(('Company', 'EIN:', 'BUSINESS', 'Incorporation')) and ':' not in line:
                # This is likely a name or address line
                if 'name' not in personal_info:
                    # First non-prefixed line is the name
                    personal_info['name'] = line.strip()
                else:
                    # Subsequent non-prefixed lines are address
                    if 'address' not in personal_info:
                        personal_info['address'] = line.strip()
                    else:
                        personal_info['address'] += ', ' + line.strip()
        except Exception as e:
            logger.error(f"Error parsing personal line '{line}': {e}")

    def _parse_company_line(self, line: str, company_info: Dict[str, str]):
        """Parse a line from the company information section."""
        try:
            if line.startswith('Company name:'):
                company_info['name'] = line.replace('Company name:', '').strip()
            elif line.startswith('EIN:'):
                company_info['ein'] = line.replace('EIN:', '').strip()
            elif line.startswith('BUSINESS ADDRESS:'):
                company_info['address'] = line.replace('BUSINESS ADDRESS:', '').strip()
            elif line.startswith('Incorporation Date:'):
                company_info['incorporation_date'] = line.replace('Incorporation Date:', '').strip()
            elif line.startswith('Business Phone:'):
                company_info['phone'] = line.replace('Business Phone:', '').strip()
            elif line.startswith('Website:'):
                company_info['website'] = line.replace('Website:', '').strip()
            elif line.startswith('Revenue:'):
                company_info['revenue'] = line.replace('Revenue:', '').strip()
            elif line.startswith('Employees Here:'):
                company_info['employees'] = line.replace('Employees Here:', '').strip()
            elif line.startswith('Sector:'):
                company_info['sector'] = line.replace('Sector:', '').strip()
            elif line.startswith('Category:'):
                company_info['category'] = line.replace('Category:', '').strip()
            elif line.startswith('Industry:'):
                company_info['industry'] = line.replace('Industry:', '').strip()
            elif line.startswith('SIC Code:'):
                company_info['sic_code'] = line.replace('SIC Code:', '').strip()
            elif line.startswith('NAICS Name:'):
                company_info['naics_name'] = line.replace('NAICS Name:', '').strip()
            elif line.startswith('NAICS Code:'):
                company_info['naics_code'] = line.replace('NAICS Code:', '').strip()
        except Exception as e:
            logger.error(f"Error parsing company line '{line}': {e}")

    def _extract_structured_records_unlocked(self, inventory_file_path: str, quantity: int) -> Tuple[List[Dict[str, Any]], bool]:
        """
        Extract structured records from inventory file (without file lock - for internal use).

        Args:
            inventory_file_path: Path to the inventory file
            quantity: Number of records to extract

        Returns:
            Tuple of (extracted_records, success)
        """
        try:
            if not os.path.exists(inventory_file_path):
                logger.error(f"Inventory file not found: {inventory_file_path}")
                return [], False

            from utils.file_resource_manager import file_resource_manager

            # Read current inventory using managed file operations
            with file_resource_manager.managed_file(inventory_file_path, 'r', encoding='utf-8') as f:
                file_content = f.read()

            # Parse all records
            all_records = self.parse_structured_records(file_content)

            if len(all_records) < quantity:
                logger.warning(f"Not enough records available. Requested: {quantity}, Available: {len(all_records)}")
                return [], False

            # Extract requested records
            extracted_records = all_records[:quantity]
            remaining_records = all_records[quantity:]

            # Create backup before modifying using safe file operations
            backup_path = inventory_file_path + f".backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            backup_result = file_resource_manager.safe_file_copy(inventory_file_path, backup_path, create_backup=False)

            if not backup_result["success"]:
                logger.error(f"Failed to create backup: {backup_result['error']}")
                return [], False

            # Write remaining records back to file
            with file_resource_manager.managed_file(inventory_file_path, 'w', encoding='utf-8') as f:
                for i, record in enumerate(remaining_records):
                    if i > 0:  # Add separator before each record except the first
                        f.write(f"\n{self.record_separator}\n")
                    f.write(record['raw_content'])
                if remaining_records:  # Add final separator if there are remaining records
                    f.write(f"\n{self.record_separator}\n")

            logger.info(f"Extracted {len(extracted_records)} structured records from {inventory_file_path}")
            return extracted_records, True

        except Exception as e:
            logger.error(f"Error extracting structured records from {inventory_file_path}: {e}")
            return [], False

    def extract_structured_records(self, inventory_file_path: str, quantity: int) -> Tuple[List[Dict[str, Any]], bool]:
        """
        Extract structured records from inventory file (with file lock - for external use).

        Args:
            inventory_file_path: Path to the inventory file
            quantity: Number of records to extract

        Returns:
            Tuple of (extracted_records, success)
        """
        with _file_lock:
            return self._extract_structured_records_unlocked(inventory_file_path, quantity)

    def create_structured_delivery_file(self, extracted_records: List[Dict[str, Any]], product_name: str, order_number: int) -> Optional[str]:
        """
        Create a delivery file containing the extracted structured records.

        Args:
            extracted_records: Records to include in delivery file
            product_name: Name of the product
            order_number: Order number for filename

        Returns:
            Path to the created delivery file or None if failed
        """
        try:
            # Create temporary delivery file
            delivery_dir = self.base_inventory_dir / "deliveries"
            delivery_dir.mkdir(parents=True, exist_ok=True)

            # Clean product name for filename
            clean_name = "".join(c for c in product_name if c.isalnum() or c in (' ', '-', '_')).strip()
            clean_name = clean_name.replace(' ', '_')

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            delivery_file = delivery_dir / f"{clean_name}_order_{order_number}_{timestamp}.txt"

            # Write delivery content
            with open(delivery_file, 'w', encoding='utf-8') as f:
                f.write(f"# {product_name} - Order #{order_number}\n")
                f.write(f"# Delivered on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"# Quantity: {len(extracted_records)} records\n")
                f.write("#" + "="*50 + "\n\n")

                for i, record in enumerate(extracted_records, 1):
                    f.write(f"# Record {i}\n")
                    f.write(self.record_separator + "\n")
                    f.write(record['raw_content'])
                    f.write(f"\n{self.record_separator}\n\n")

            logger.info(f"Created structured delivery file: {delivery_file}")
            return str(delivery_file)

        except Exception as e:
            logger.error(f"Error creating structured delivery file: {e}")
            return None

    def is_structured_inventory(self, inventory_file_path: str) -> bool:
        """
        Check if an inventory file contains structured records.

        Args:
            inventory_file_path: Path to the inventory file

        Returns:
            True if file contains structured records, False otherwise
        """
        try:
            if not os.path.exists(inventory_file_path):
                return False

            from utils.file_resource_manager import file_resource_manager

            with file_resource_manager.managed_file(inventory_file_path, 'r', encoding='utf-8') as f:
                content = f.read(1000)  # Read first 1000 characters

            # Check for structured record indicators
            return (self.record_separator in content and
                    ('Company name:' in content or 'SSN:' in content or 'EIN:' in content))

        except Exception as e:
            logger.error(f"Error checking if inventory is structured: {e}")
            return False


# Global instance
line_inventory_manager = LineInventoryManager()
