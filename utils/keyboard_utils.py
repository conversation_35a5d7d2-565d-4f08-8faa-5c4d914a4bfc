"""
Utility functions for working with Telegram keyboards and handling size limits.

This module provides helper functions for creating and validating keyboards
to prevent "reply markup is too long" errors.
"""

from typing import List, Dict, Any, Union, Optional
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from utils.telegram_helpers import validate_keyboard_size, _validate_and_fix_markup
import logging

logger = logging.getLogger(__name__)


def create_safe_keyboard(buttons: List[List[InlineKeyboardButton]], 
                        max_buttons: int = 50) -> Optional[InlineKeyboardMarkup]:
    """
    Create a keyboard with automatic size validation and truncation.
    
    Args:
        buttons: List of button rows
        max_buttons: Maximum number of buttons to allow
        
    Returns:
        InlineKeyboardMarkup or None if the keyboard is too large
    """
    try:
        keyboard = InlineKeyboardMarkup(inline_keyboard=buttons)
        
        # Validate the keyboard
        validation = validate_keyboard_size(keyboard)
        
        if not validation["valid"]:
            logger.warning(f"Keyboard too large: {validation['button_count']} buttons, "
                         f"{validation['estimated_size']} bytes")
            
            if validation["truncated_markup"]:
                logger.info("Using truncated keyboard")
                return validation["truncated_markup"]
            else:
                logger.error("Keyboard could not be truncated, returning None")
                return None
        
        return keyboard
    except Exception as e:
        logger.error(f"Error creating safe keyboard: {e}")
        return None


def create_paginated_keyboard(items: List[Dict[str, Any]], 
                             page: int = 1, 
                             items_per_page: int = 10,
                             callback_prefix: str = "item",
                             back_callback: str = "back") -> InlineKeyboardMarkup:
    """
    Create a paginated keyboard that automatically handles size limits.
    
    Args:
        items: List of items to display
        page: Current page number (1-based)
        items_per_page: Number of items per page
        callback_prefix: Prefix for item callback data
        back_callback: Callback data for back button
        
    Returns:
        InlineKeyboardMarkup with pagination
    """
    # Calculate pagination
    total_pages = (len(items) + items_per_page - 1) // items_per_page
    start_idx = (page - 1) * items_per_page
    end_idx = min(start_idx + items_per_page, len(items))
    page_items = items[start_idx:end_idx]
    
    # Create buttons for items
    buttons = []
    for item in page_items:
        item_id = item.get("id") or item.get("_id")
        item_name = item.get("name", "Unnamed Item")
        
        # Truncate long names
        if len(item_name) > 30:
            item_name = item_name[:27] + "..."
        
        button = InlineKeyboardButton(
            text=item_name,
            callback_data=f"{callback_prefix}:{item_id}"
        )
        buttons.append([button])
    
    # Add pagination buttons if needed
    if total_pages > 1:
        pagination_row = []
        
        if page > 1:
            pagination_row.append(
                InlineKeyboardButton(
                    text="⬅️ Previous",
                    callback_data=f"page:{page-1}"
                )
            )
        
        pagination_row.append(
            InlineKeyboardButton(
                text=f"📄 {page}/{total_pages}",
                callback_data="current_page"
            )
        )
        
        if page < total_pages:
            pagination_row.append(
                InlineKeyboardButton(
                    text="➡️ Next",
                    callback_data=f"page:{page+1}"
                )
            )
        
        buttons.append(pagination_row)
    
    # Add back button
    buttons.append([
        InlineKeyboardButton(
            text="🔙 Back",
            callback_data=back_callback
        )
    ])
    
    # Create and validate the keyboard
    return create_safe_keyboard(buttons) or InlineKeyboardMarkup(
        inline_keyboard=[[
            InlineKeyboardButton(text="🔙 Back", callback_data=back_callback)
        ]]
    )


def create_chunked_keyboard(items: List[Dict[str, Any]], 
                           chunk_size: int = 20,
                           callback_prefix: str = "item") -> List[InlineKeyboardMarkup]:
    """
    Split a large list of items into multiple keyboards to avoid size limits.
    
    Args:
        items: List of items to display
        chunk_size: Maximum items per keyboard
        callback_prefix: Prefix for item callback data
        
    Returns:
        List of InlineKeyboardMarkup objects
    """
    keyboards = []
    
    for i in range(0, len(items), chunk_size):
        chunk = items[i:i + chunk_size]
        buttons = []
        
        for item in chunk:
            item_id = item.get("id") or item.get("_id")
            item_name = item.get("name", "Unnamed Item")
            
            # Truncate long names
            if len(item_name) > 30:
                item_name = item_name[:27] + "..."
            
            button = InlineKeyboardButton(
                text=item_name,
                callback_data=f"{callback_prefix}:{item_id}"
            )
            buttons.append([button])
        
        # Add navigation buttons if there are multiple chunks
        if len(keyboards) > 0 or i + chunk_size < len(items):
            nav_row = []
            
            if len(keyboards) > 0:
                nav_row.append(
                    InlineKeyboardButton(
                        text="⬅️ Previous",
                        callback_data=f"chunk:{len(keyboards)-1}"
                    )
                )
            
            if i + chunk_size < len(items):
                nav_row.append(
                    InlineKeyboardButton(
                        text="➡️ Next",
                        callback_data=f"chunk:{len(keyboards)+1}"
                    )
                )
            
            if nav_row:
                buttons.append(nav_row)
        
        keyboard = create_safe_keyboard(buttons)
        if keyboard:
            keyboards.append(keyboard)
    
    return keyboards if keyboards else [InlineKeyboardMarkup(inline_keyboard=[])]
