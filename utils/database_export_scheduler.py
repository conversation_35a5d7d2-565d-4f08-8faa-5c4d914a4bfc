"""
Database Export Cleanup Scheduler
Provides automated cleanup of expired export files and audit log management.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any
import schedule
import threading
import time

from utils.database_export import database_exporter, cleanup_expired_exports
from utils.monitoring_system import record_security_event, emit_alert, AlertLevel
from database.operations import get_log_settings

logger = logging.getLogger(__name__)

class DatabaseExportScheduler:
    """Handles scheduled cleanup and maintenance of database exports."""
    
    def __init__(self):
        """Initialize the scheduler."""
        self.running = False
        self.scheduler_thread = None
        self._last_cleanup = None
        self._cleanup_stats = {
            "total_cleanups": 0,
            "total_files_cleaned": 0,
            "last_cleanup_time": None,
            "last_cleanup_count": 0
        }
    
    def start_scheduler(self):
        """Start the cleanup scheduler."""
        if self.running:
            logger.warning("Database export scheduler is already running")
            return
        
        self.running = True
        
        # Schedule cleanup every hour
        schedule.every().hour.do(self._scheduled_cleanup)
        
        # Schedule daily audit log cleanup
        schedule.every().day.at("02:00").do(self._scheduled_audit_cleanup)
        
        # Schedule weekly statistics report
        schedule.every().week.do(self._scheduled_statistics_report)
        
        # Start scheduler thread
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("Database export scheduler started")
        
        # Record scheduler start
        record_security_event("db_export_scheduler_started", {
            "timestamp": datetime.now().isoformat(),
            "cleanup_interval": "hourly",
            "audit_cleanup": "daily"
        })
    
    def stop_scheduler(self):
        """Stop the cleanup scheduler."""
        if not self.running:
            return
        
        self.running = False
        schedule.clear()
        
        if self.scheduler_thread and self.scheduler_thread.is_alive():
            self.scheduler_thread.join(timeout=5)
        
        logger.info("Database export scheduler stopped")
        
        # Record scheduler stop
        record_security_event("db_export_scheduler_stopped", {
            "timestamp": datetime.now().isoformat(),
            "total_cleanups": self._cleanup_stats["total_cleanups"],
            "total_files_cleaned": self._cleanup_stats["total_files_cleaned"]
        })
    
    def _run_scheduler(self):
        """Run the scheduler in a separate thread."""
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"Error in scheduler thread: {e}")
                time.sleep(300)  # Wait 5 minutes on error
    
    def _scheduled_cleanup(self):
        """Perform scheduled cleanup of expired export files."""
        try:
            logger.info("Starting scheduled database export cleanup")
            
            # Perform cleanup
            cleaned_count = cleanup_expired_exports()
            
            # Update statistics
            self._cleanup_stats["total_cleanups"] += 1
            self._cleanup_stats["total_files_cleaned"] += cleaned_count
            self._cleanup_stats["last_cleanup_time"] = datetime.now()
            self._cleanup_stats["last_cleanup_count"] = cleaned_count
            self._last_cleanup = datetime.now()
            
            # Log cleanup results
            logger.info(f"Scheduled cleanup completed: {cleaned_count} files cleaned")
            
            # Record security event
            record_security_event("db_export_scheduled_cleanup", {
                "files_cleaned": cleaned_count,
                "cleanup_time": datetime.now().isoformat(),
                "total_cleanups": self._cleanup_stats["total_cleanups"]
            })
            
            # Alert if many files were cleaned (potential issue)
            if cleaned_count > 10:
                emit_alert(
                    AlertLevel.WARNING,
                    f"Large number of export files cleaned: {cleaned_count}",
                    {"files_cleaned": cleaned_count, "cleanup_time": datetime.now().isoformat()}
                )
            
        except Exception as e:
            logger.error(f"Error in scheduled cleanup: {e}")
            
            # Record error
            record_security_event("db_export_cleanup_error", {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            
            # Alert on cleanup failure
            emit_alert(
                AlertLevel.ERROR,
                f"Database export cleanup failed: {e}",
                {"error": str(e)}
            )
    
    def _scheduled_audit_cleanup(self):
        """Clean up old audit logs and export history."""
        try:
            logger.info("Starting scheduled audit log cleanup")
            
            # Clean up export history older than 30 days
            cutoff_date = datetime.now() - timedelta(days=30)
            
            # Get current export statistics before cleanup
            stats_before = database_exporter.get_export_statistics()
            
            # Clean up old history entries
            original_history = database_exporter._export_history.copy()
            database_exporter._export_history = [
                export for export in database_exporter._export_history
                if export.get("started_at", datetime.now()) > cutoff_date
            ]
            
            cleaned_history_count = len(original_history) - len(database_exporter._export_history)
            
            logger.info(f"Audit cleanup completed: {cleaned_history_count} history entries cleaned")
            
            # Record audit cleanup
            record_security_event("db_export_audit_cleanup", {
                "history_entries_cleaned": cleaned_history_count,
                "cutoff_date": cutoff_date.isoformat(),
                "cleanup_time": datetime.now().isoformat()
            })
            
        except Exception as e:
            logger.error(f"Error in scheduled audit cleanup: {e}")
            
            record_security_event("db_export_audit_cleanup_error", {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    def _scheduled_statistics_report(self):
        """Generate weekly statistics report."""
        try:
            logger.info("Generating weekly database export statistics report")
            
            stats = database_exporter.get_export_statistics()
            
            # Calculate weekly statistics
            week_ago = datetime.now() - timedelta(days=7)
            weekly_exports = [
                export for export in database_exporter._export_history
                if export.get("started_at", datetime.now()) > week_ago
            ]
            
            weekly_stats = {
                "total_exports_this_week": len(weekly_exports),
                "successful_exports_this_week": len([e for e in weekly_exports if e["status"] == "completed"]),
                "failed_exports_this_week": len([e for e in weekly_exports if e["status"] == "failed"]),
                "total_data_exported_this_week": sum(
                    e.get("file_size", 0) for e in weekly_exports if e["status"] == "completed"
                ),
                "unique_users_this_week": len(set(e["user_id"] for e in weekly_exports)),
                "report_date": datetime.now().isoformat()
            }
            
            # Combine with overall stats
            report_data = {**stats, **weekly_stats, **self._cleanup_stats}
            
            logger.info(f"Weekly report: {weekly_stats['total_exports_this_week']} exports, "
                       f"{weekly_stats['successful_exports_this_week']} successful")
            
            # Record statistics report
            record_security_event("db_export_weekly_report", report_data)
            
            # Alert if there are concerning trends
            if weekly_stats["failed_exports_this_week"] > weekly_stats["successful_exports_this_week"]:
                emit_alert(
                    AlertLevel.WARNING,
                    "High database export failure rate this week",
                    weekly_stats
                )
            
        except Exception as e:
            logger.error(f"Error generating statistics report: {e}")
            
            record_security_event("db_export_statistics_error", {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
    
    def get_scheduler_status(self) -> Dict[str, Any]:
        """Get current scheduler status and statistics."""
        return {
            "running": self.running,
            "last_cleanup": self._last_cleanup.isoformat() if self._last_cleanup else None,
            "cleanup_stats": self._cleanup_stats.copy(),
            "next_cleanup": "Every hour",
            "next_audit_cleanup": "Daily at 02:00",
            "scheduler_thread_alive": self.scheduler_thread.is_alive() if self.scheduler_thread else False
        }
    
    def force_cleanup(self) -> Dict[str, Any]:
        """Force an immediate cleanup and return results."""
        try:
            logger.info("Force cleanup requested")
            
            cleaned_count = cleanup_expired_exports()
            
            # Update statistics
            self._cleanup_stats["total_cleanups"] += 1
            self._cleanup_stats["total_files_cleaned"] += cleaned_count
            self._cleanup_stats["last_cleanup_time"] = datetime.now()
            self._cleanup_stats["last_cleanup_count"] = cleaned_count
            self._last_cleanup = datetime.now()
            
            # Record forced cleanup
            record_security_event("db_export_forced_cleanup", {
                "files_cleaned": cleaned_count,
                "cleanup_time": datetime.now().isoformat(),
                "trigger": "manual"
            })
            
            return {
                "success": True,
                "files_cleaned": cleaned_count,
                "cleanup_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error in forced cleanup: {e}")
            
            record_security_event("db_export_forced_cleanup_error", {
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
            
            return {
                "success": False,
                "error": str(e),
                "cleanup_time": datetime.now().isoformat()
            }

# Global scheduler instance
export_scheduler = DatabaseExportScheduler()

# Convenience functions
def start_export_scheduler():
    """Start the database export scheduler."""
    export_scheduler.start_scheduler()

def stop_export_scheduler():
    """Stop the database export scheduler."""
    export_scheduler.stop_scheduler()

def get_scheduler_status() -> Dict[str, Any]:
    """Get scheduler status."""
    return export_scheduler.get_scheduler_status()

def force_cleanup() -> Dict[str, Any]:
    """Force immediate cleanup."""
    return export_scheduler.force_cleanup()
