"""
Unified Product Operations Interface
Provides a single interface for all product types while maintaining backward compatibility.
Handles exclusive, line-based, and regular products with consistent APIs.
"""

import logging
import time
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone

from utils.unified_product_performance import (
    ProductType,
    unified_product_cache,
    unified_product_performance_monitor,
    get_product_with_monitoring,
    invalidate_product_cache
)
from utils.exclusive_product_db_operations import ExclusiveProductDBOperations
from utils.line_product_db_operations import LineProductDBOperations
from utils.regular_product_db_operations import RegularProductDBOperations

logger = logging.getLogger(__name__)


class UnifiedProductOperations:
    """
    Unified interface for all product operations.
    Automatically routes operations to the appropriate product type handler.
    """
    
    @staticmethod
    def determine_product_type(product_data: Dict[str, Any]) -> ProductType:
        """
        Determine the product type from product data.
        
        Args:
            product_data: Product data dictionary
            
        Returns:
            ProductType enum value
        """
        if product_data.get("is_exclusive_single_use", False):
            return ProductType.EXCLUSIVE
        elif product_data.get("is_line_based", False):
            return ProductType.LINE_BASED
        else:
            return ProductType.REGULAR
    
    @staticmethod
    def get_product_with_validation(product_id: Any) -> Dict[str, Any]:
        """
        Get product with validation, automatically determining type.

        Args:
            product_id: Product ID

        Returns:
            Dict with product data and metadata
        """
        start_time = time.time()
        operation_name = "unified_get_product_with_validation"

        try:
            # Import here to avoid circular imports
            from utils.unified_validation import unified_validation

            # Validate the product ID first
            validation_result = unified_validation.validate_product_id(product_id)
            if not validation_result["valid"]:
                logger.warning(f"Product ID validation failed: {product_id} -> {validation_result['error']}")
                return {
                    "success": False,
                    "error": f"Invalid product ID: {validation_result['error']}",
                    "error_code": "INVALID_PRODUCT_ID",
                    "product": None,
                    "product_type": None
                }

            # Use the sanitized product ID (preserving the correct type)
            sanitized_product_id = validation_result["sanitized_id"]
            logger.debug(f"Product ID validated: {product_id} -> {sanitized_product_id} (type: {type(sanitized_product_id)})")

            # Get the product using the sanitized ID (don't convert to string)
            product = get_product_with_monitoring(sanitized_product_id)
            if not product:
                logger.warning(f"Product not found in database: ID={sanitized_product_id}, Type={type(sanitized_product_id)}")
                return {
                    "success": False,
                    "error": f"Product not found in database (ID: {sanitized_product_id})",
                    "error_code": "PRODUCT_NOT_FOUND",
                    "product": None,
                    "product_type": None
                }
            
            # Determine product type
            product_type = UnifiedProductOperations.determine_product_type(product)

            # Product is already validated and retrieved successfully
            # No need for additional validation that might reject valid products
            duration = time.time() - start_time
            unified_product_performance_monitor.record_query_time(operation_name, duration, product_type)

            return {
                "success": True,
                "product": product,
                "product_type": product_type.value,
                "operation_duration": duration
            }
            
        except Exception as e:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_error(operation_name, ProductType.UNKNOWN)
            logger.error(f"Error in unified get_product_with_validation for {product_id}: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "error_code": "EXCEPTION",
                "product": None,
                "product_type": None,
                "operation_duration": duration
            }
    
    @staticmethod
    def process_purchase(product_id: Any, quantity: int, user_id: int, **kwargs) -> Dict[str, Any]:
        """
        Process purchase for any product type.
        
        Args:
            product_id: Product ID
            quantity: Quantity to purchase
            user_id: User ID making the purchase
            **kwargs: Additional parameters specific to product type
            
        Returns:
            Dict with purchase result and details
        """
        start_time = time.time()
        operation_name = "unified_process_purchase"
        
        try:
            # Get product and determine type
            product_result = UnifiedProductOperations.get_product_with_validation(product_id)
            if not product_result["success"]:
                return product_result
            
            product = product_result["product"]
            product_type_str = product_result["product_type"]
            product_type = ProductType(product_type_str)
            
            # Route to appropriate purchase handler
            if product_type == ProductType.EXCLUSIVE:
                # Exclusive products don't support quantity > 1
                if quantity != 1:
                    return {
                        "success": False,
                        "error": "Exclusive products can only be purchased with quantity 1",
                        "error_code": "INVALID_QUANTITY_FOR_EXCLUSIVE",
                        "product_type": product_type_str
                    }
                
                result = ExclusiveProductDBOperations.mark_exclusive_product_as_purchased(product_id, user_id)
                
            elif product_type == ProductType.LINE_BASED:
                # For line-based products, we need to reserve and confirm
                reserve_result = LineProductDBOperations.reserve_line_inventory_with_monitoring(
                    product_id, quantity, user_id
                )
                if not reserve_result["success"]:
                    return reserve_result
                
                # Confirm the purchase
                result = LineProductDBOperations.confirm_line_purchase_with_monitoring(
                    product_id, quantity, user_id
                )
                
            else:  # REGULAR
                result = RegularProductDBOperations.process_regular_purchase_with_monitoring(
                    product_id, quantity, user_id, kwargs.get("order_number")
                )

            # Validate purchase result
            if result is None:
                duration = time.time() - start_time
                unified_product_performance_monitor.record_error(operation_name, product_type)
                return {
                    "success": False,
                    "error": f"Purchase operation returned None for {product_type.value} product",
                    "error_code": "NULL_RESULT",
                    "product_type": product_type_str,
                    "operation_duration": duration
                }

            duration = time.time() - start_time
            unified_product_performance_monitor.record_query_time(operation_name, duration, product_type)

            # Enhance result with unified metadata
            if isinstance(result, dict):
                result["product_type"] = product_type_str
                result["unified_operation_duration"] = duration
                return result
            else:
                # Handle legacy boolean returns
                return {
                    "success": bool(result),
                    "product_type": product_type_str,
                    "unified_operation_duration": duration,
                    "legacy_result": result
                }
            
        except Exception as e:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_error(operation_name, ProductType.UNKNOWN)
            logger.error(f"Error in unified process_purchase for {product_id}: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "error_code": "EXCEPTION",
                "operation_duration": duration
            }
    
    @staticmethod
    def check_availability(product_id: Any) -> Dict[str, Any]:
        """
        Check availability for any product type.
        
        Args:
            product_id: Product ID
            
        Returns:
            Dict with availability status and details
        """
        start_time = time.time()
        operation_name = "unified_check_availability"
        
        try:
            # Get product and determine type
            product_result = UnifiedProductOperations.get_product_with_validation(product_id)
            if not product_result["success"]:
                return product_result
            
            product = product_result["product"]
            product_type_str = product_result["product_type"]
            product_type = ProductType(product_type_str)
            
            # Route to appropriate availability checker
            if product_type == ProductType.EXCLUSIVE:
                # Check if exclusive product is already purchased
                is_purchased = product.get("is_purchased", False)
                is_removed = product.get("removed_from_listings", False)
                
                result = {
                    "success": True,
                    "product_id": product_id,
                    "product_name": product.get("name", "Unknown"),
                    "availability": {
                        "is_available": not is_purchased and not is_removed,
                        "is_purchased": is_purchased,
                        "is_removed": is_removed,
                        "is_exclusive": True,
                        "max_quantity": 1 if not is_purchased else 0
                    }
                }
                
            elif product_type == ProductType.LINE_BASED:
                result = LineProductDBOperations.get_line_product_inventory_status(product_id)

            else:  # REGULAR
                result = RegularProductDBOperations.check_regular_product_availability(product_id)

            # Validate availability check result
            if result is None:
                duration = time.time() - start_time
                unified_product_performance_monitor.record_error(operation_name, product_type)
                return {
                    "success": False,
                    "error": f"Availability check returned None for {product_type.value} product",
                    "error_code": "NULL_RESULT",
                    "product_type": product_type_str,
                    "operation_duration": duration
                }

            duration = time.time() - start_time
            unified_product_performance_monitor.record_query_time(operation_name, duration, product_type)

            # Enhance result with unified metadata
            if isinstance(result, dict):
                result["product_type"] = product_type_str
                result["unified_operation_duration"] = duration
                return result
            else:
                return {
                    "success": True,
                    "product_type": product_type_str,
                    "unified_operation_duration": duration,
                    "availability": {"is_available": bool(result)}
                }
            
        except Exception as e:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_error(operation_name, ProductType.UNKNOWN)
            logger.error(f"Error in unified check_availability for {product_id}: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "error_code": "EXCEPTION",
                "operation_duration": duration
            }
    
    @staticmethod
    def delete_product(product_id: Any, admin_action: bool = False) -> Dict[str, Any]:
        """
        Delete product for any product type.

        Args:
            product_id: Product ID
            admin_action: Whether this is an admin action (for logging/validation)

        Returns:
            Dict with deletion status and details
        """
        start_time = time.time()
        operation_name = "unified_delete_product"

        try:
            # Get product and determine type first
            product_result = UnifiedProductOperations.get_product_with_validation(product_id)
            if not product_result["success"]:
                return {
                    "success": False,
                    "error": f"Product not found or invalid: {product_result.get('error', 'Unknown error')}",
                    "error_code": "PRODUCT_NOT_FOUND",
                    "operation_duration": time.time() - start_time
                }

            product = product_result["product"]
            product_type_str = product_result["product_type"]
            product_type = ProductType(product_type_str)
            product_name = product.get("name", "Unknown Product")

            # Route to appropriate delete handler
            if product_type == ProductType.EXCLUSIVE:
                result = ExclusiveProductDBOperations.delete_exclusive_product(product_id, admin_action=admin_action)

            elif product_type == ProductType.LINE_BASED:
                # For line-based products, use the general delete_product function
                # as there's no specific line-based delete operation
                from database.operations import delete_product
                success = delete_product(product_id)
                result = {
                    "success": success,
                    "error": "Failed to delete line-based product" if not success else None
                }

            else:  # REGULAR
                # For regular products, use the general delete_product function
                from database.operations import delete_product
                success = delete_product(product_id)
                result = {
                    "success": success,
                    "error": "Failed to delete regular product" if not success else None
                }

            # Invalidate cache after deletion
            if result.get("success"):
                UnifiedProductOperations.invalidate_cache(product_id, product_type)

            # Record performance metrics
            duration = time.time() - start_time
            if result.get("success"):
                unified_product_performance_monitor.record_query_time(operation_name, duration, product_type)
            else:
                unified_product_performance_monitor.record_error(operation_name, product_type)

            # Enhance result with additional info
            result.update({
                "product_id": product_id,
                "product_name": product_name,
                "product_type": product_type_str,
                "operation_duration": duration,
                "timestamp": datetime.now(timezone.utc).isoformat()
            })

            return result

        except Exception as e:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_error(operation_name, ProductType.UNKNOWN)
            logger.error(f"Error in unified delete_product for {product_id}: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "error_code": "EXCEPTION",
                "product_id": product_id,
                "operation_duration": duration
            }

    @staticmethod
    def invalidate_cache(product_id: Any, product_type: Optional[ProductType] = None):
        """
        Invalidate cache for any product type.

        Args:
            product_id: Product ID
            product_type: Optional product type (will be determined if not provided)
        """
        try:
            if product_type is None:
                # Determine product type
                product_result = UnifiedProductOperations.get_product_with_validation(product_id)
                if product_result["success"]:
                    product_type = ProductType(product_result["product_type"])

            invalidate_product_cache(str(product_id), product_type)

        except Exception as e:
            logger.error(f"Error invalidating cache for product {product_id}: {e}")
    
    @staticmethod
    def get_performance_summary() -> Dict[str, Any]:
        """
        Get comprehensive performance summary for all product types.
        
        Returns:
            Dict with performance statistics
        """
        try:
            # Get unified performance stats
            unified_stats = unified_product_performance_monitor.get_performance_stats()
            
            # Get cache stats
            cache_stats = unified_product_cache.get_cache_stats()
            
            return {
                "success": True,
                "performance": unified_stats,
                "cache": cache_stats,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting performance summary: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "error_code": "EXCEPTION"
            }


# Convenience functions for backward compatibility
def get_product_unified(product_id: Any) -> Optional[Dict[str, Any]]:
    """Get product using unified operations (backward compatibility)."""
    result = UnifiedProductOperations.get_product_with_validation(product_id)
    return result.get("product") if result["success"] else None


def process_purchase_unified(product_id: Any, quantity: int, user_id: int, **kwargs) -> Dict[str, Any]:
    """Process purchase using unified operations."""
    return UnifiedProductOperations.process_purchase(product_id, quantity, user_id, **kwargs)


def check_availability_unified(product_id: Any) -> Dict[str, Any]:
    """Check availability using unified operations."""
    return UnifiedProductOperations.check_availability(product_id)


# Global instance for easy access
unified_product_operations = UnifiedProductOperations()
