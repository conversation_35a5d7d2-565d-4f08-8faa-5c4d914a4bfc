"""
File Handling for Exclusive Single-Use Products
Manages file upload, validation, storage, and delivery for exclusive products.
"""

import logging
import os
import shutil
import mimetypes
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, Tuple

from utils.file_metadata import store_file_metadata, normalize_file_path
from utils.local_file_handling import generate_file_path

logger = logging.getLogger(__name__)

# Configuration
EXCLUSIVE_FILES_DIR = Path("uploads/exclusive_files")
EXCLUSIVE_PREVIEWS_DIR = Path("uploads/exclusive_previews")
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
SUPPORTED_IMAGE_TYPES = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}
SUPPORTED_DOCUMENT_TYPES = {'.pdf', '.txt', '.doc', '.docx', '.zip', '.rar', '.7z', '.tmp'}

class ExclusiveFileHandler:
    """Handles file operations for exclusive single-use products."""
    
    def __init__(self):
        """Initialize the file handler."""
        # Ensure directories exist
        EXCLUSIVE_FILES_DIR.mkdir(parents=True, exist_ok=True)
        EXCLUSIVE_PREVIEWS_DIR.mkdir(parents=True, exist_ok=True)
    
    def validate_file_for_exclusive_product(self, file_path: str) -> Dict[str, Any]:
        """
        Validate a file for use as an exclusive product with enhanced security checks.

        Args:
            file_path: Path to the file to validate

        Returns:
            Dict with validation results
        """
        try:
            if not os.path.exists(file_path):
                return {
                    "valid": False,
                    "error": "File not found"
                }

            # Security check: ensure file is within allowed directories
            real_path = os.path.realpath(file_path)
            allowed_dirs = [
                os.path.realpath(str(EXCLUSIVE_FILES_DIR)),
                os.path.realpath("uploads"),
                os.path.realpath("/tmp")  # For temporary files
            ]

            if not any(real_path.startswith(allowed_dir) for allowed_dir in allowed_dirs):
                logger.warning(f"Security violation: File outside allowed directories: {real_path}")
                return {
                    "valid": False,
                    "error": "File location not allowed"
                }

            # Get file info
            file_size = os.path.getsize(file_path)
            file_ext = Path(file_path).suffix.lower()

            # Check file size
            if file_size > MAX_FILE_SIZE:
                return {
                    "valid": False,
                    "error": f"File too large: {file_size / (1024*1024):.1f}MB (max: {MAX_FILE_SIZE / (1024*1024):.0f}MB)"
                }

            if file_size == 0:
                return {
                    "valid": False,
                    "error": "File is empty"
                }

            # Security check: prevent dangerous file types
            dangerous_extensions = {'.exe', '.bat', '.cmd', '.com', '.scr', '.pif', '.vbs', '.js', '.jar', '.sh'}
            if file_ext in dangerous_extensions:
                logger.warning(f"Security violation: Dangerous file type attempted: {file_ext}")
                return {
                    "valid": False,
                    "error": f"File type not allowed for security reasons: {file_ext}"
                }

            # Check file type
            if file_ext not in (SUPPORTED_IMAGE_TYPES | SUPPORTED_DOCUMENT_TYPES):
                return {
                    "valid": False,
                    "error": f"Unsupported file type: {file_ext}"
                }

            # Determine MIME type
            mime_type, _ = mimetypes.guess_type(file_path)

            # Additional MIME type validation for security
            if mime_type:
                if file_ext in SUPPORTED_IMAGE_TYPES and not mime_type.startswith("image/"):
                    return {
                        "valid": False,
                        "error": "File extension doesn't match content type"
                    }
                elif mime_type.startswith("application/x-executable"):
                    return {
                        "valid": False,
                        "error": "Executable files are not allowed"
                    }

            # Determine file category
            if file_ext in SUPPORTED_IMAGE_TYPES:
                file_category = "image"
            else:
                file_category = "document"

            return {
                "valid": True,
                "file_size": file_size,
                "file_type": file_ext,
                "file_category": file_category,
                "mime_type": mime_type
            }

        except Exception as e:
            logger.error(f"Error validating file {file_path}: {e}")
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}"
            }
    
    async def store_exclusive_file(self, source_path: str, product_id: int, original_filename: str) -> Optional[Dict[str, Any]]:
        """
        Store a file for an exclusive product using the unified file processing system.

        Args:
            source_path: Path to the source file
            product_id: Product ID
            original_filename: Original filename

        Returns:
            Dict with file storage info or None if failed
        """
        try:
            # Validate the file first
            validation = self.validate_file_for_exclusive_product(source_path)
            if not validation["valid"]:
                logger.error(f"File validation failed: {validation['error']}")
                return None

            # Import the unified file processing function
            from utils.local_file_handling import process_file_upload

            # For exclusive products, we want to preserve original filename but in a product-specific folder
            # Create a custom folder structure: exclusive_files/product_{product_id}
            exclusive_folder = f"exclusive_files/product_{product_id}"

            # Use the unified processing system
            result = await process_file_upload(
                source_path=source_path,
                original_filename=original_filename,
                folder=exclusive_folder,
                product_id=str(product_id),
                file_size=validation["file_size"],
                mime_type=validation["mime_type"]
            )

            if not result["success"]:
                logger.error(f"Unified file processing failed: {result.get('error')}")
                return None

            logger.info(f"Stored exclusive file for product {product_id}: {result['absolute_path']}")

            return {
                "file_path": result["absolute_path"],
                "relative_path": result["relative_path"],
                "original_filename": result["original_filename"],
                "sanitized_filename": result["sanitized_filename"],
                "file_size": result["file_size"],
                "file_type": validation["file_type"],
                "file_category": validation["file_category"],
                "mime_type": validation["mime_type"],
                "original_filename": original_filename
            }
            
        except Exception as e:
            logger.error(f"Error storing exclusive file for product {product_id}: {e}")
            return None
    
    def create_file_preview_info(self, file_path: str, file_type: str, file_size: int) -> Dict[str, Any]:
        """
        Create preview information for a file without exposing the actual content.
        
        Args:
            file_path: Path to the file
            file_type: File extension
            file_size: File size in bytes
            
        Returns:
            Dict with preview information
        """
        try:
            # Format file size
            if file_size < 1024:
                size_str = f"{file_size} B"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} KB"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} MB"
            
            # Determine file category and icon
            if file_type in SUPPORTED_IMAGE_TYPES:
                category = "Image"
                icon = "🖼️"
            else:
                category = "Document"
                icon = "📋"
            
            return {
                "category": category,
                "icon": icon,
                "type_display": f"{category} ({file_type.upper()})",
                "size_display": size_str,
                "preview_text": f"{icon} {category} file ({file_type.upper()}) - {size_str}"
            }
            
        except Exception as e:
            logger.error(f"Error creating file preview info: {e}")
            return {
                "category": "Unknown",
                "icon": "📁",
                "type_display": "Unknown file",
                "size_display": "Unknown size",
                "preview_text": "📁 File information unavailable"
            }
    
    def get_file_delivery_info(self, file_path: str, product_name: str, order_number: int) -> Dict[str, Any]:
        """
        Prepare file information for delivery.
        
        Args:
            file_path: Path to the file
            product_name: Name of the product
            order_number: Order number
            
        Returns:
            Dict with delivery information
        """
        try:
            if not os.path.exists(file_path):
                return {
                    "success": False,
                    "error": "File not found"
                }
            
            # Get file info
            file_size = os.path.getsize(file_path)
            file_ext = Path(file_path).suffix.lower()
            original_filename = Path(file_path).name
            
            # Get MIME type
            mime_type, _ = mimetypes.guess_type(file_path)
            
            # Create delivery filename
            clean_product_name = "".join(c for c in product_name if c.isalnum() or c in (' ', '-', '_')).strip()
            clean_product_name = clean_product_name.replace(' ', '_')
            delivery_filename = f"{clean_product_name}_order_{order_number}{file_ext}"
            
            return {
                "success": True,
                "file_path": file_path,
                "file_size": file_size,
                "file_type": file_ext,
                "mime_type": mime_type,
                "original_filename": original_filename,
                "delivery_filename": delivery_filename,
                "preview_info": self.create_file_preview_info(file_path, file_ext, file_size)
            }
            
        except Exception as e:
            logger.error(f"Error preparing file delivery info: {e}")
            return {
                "success": False,
                "error": f"Delivery preparation error: {str(e)}"
            }
    
    def cleanup_exclusive_file(self, file_path: str) -> bool:
        """
        Clean up an exclusive file (for admin use only).
        
        Args:
            file_path: Path to the file to remove
            
        Returns:
            True if successful, False otherwise
        """
        try:
            if os.path.exists(file_path):
                # Create backup before deletion
                backup_dir = EXCLUSIVE_FILES_DIR / "deleted_backups"
                backup_dir.mkdir(parents=True, exist_ok=True)
                
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_filename = f"deleted_{timestamp}_{Path(file_path).name}"
                backup_path = backup_dir / backup_filename
                
                # Move to backup instead of deleting
                shutil.move(file_path, backup_path)
                
                logger.info(f"Moved exclusive file to backup: {file_path} -> {backup_path}")
                return True
            else:
                logger.warning(f"File not found for cleanup: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"Error cleaning up exclusive file {file_path}: {e}")
            return False
    
    def get_supported_file_types(self) -> Dict[str, Any]:
        """
        Get information about supported file types.
        
        Returns:
            Dict with supported file types information
        """
        return {
            "image_types": list(SUPPORTED_IMAGE_TYPES),
            "document_types": list(SUPPORTED_DOCUMENT_TYPES),
            "all_types": list(SUPPORTED_IMAGE_TYPES | SUPPORTED_DOCUMENT_TYPES),
            "max_file_size": MAX_FILE_SIZE,
            "max_file_size_mb": MAX_FILE_SIZE / (1024 * 1024),
            "categories": {
                "images": {
                    "types": list(SUPPORTED_IMAGE_TYPES),
                    "description": "Image files (photos, graphics, etc.)"
                },
                "documents": {
                    "types": list(SUPPORTED_DOCUMENT_TYPES),
                    "description": "Document files (PDFs, text files, archives, etc.)"
                }
            }
        }


# Create global instance
exclusive_file_handler = ExclusiveFileHandler()
