"""Utility functions for button layout in the UI."""

# Button layout configuration for horizontal arrangement
# Buttons with short text can be arranged horizontally to prevent wrapping
NAME_LENGTH_THRESHOLD = 20  # Threshold for determining if button text is too long for horizontal layout


def should_use_single_row(text: str) -> bool:
    """
    Determine if a button should be displayed in its own row based on text length.

    This function enables horizontal arrangement of buttons with short text while
    ensuring buttons with long text get their own row to maintain readability.
    This prevents button wrapping to multiple lines while maintaining proper spacing.

    Usage in button creation:
        button = InlineKeyboardButton(text=name, callback_data="some_data")

        if should_use_single_row(name):
            inline_keyboard.append([button])  # Long text - gets its own row
        else:
            buttons_collection.append(button)  # Short text - can be paired horizontally
            if len(buttons_collection) == 2:  # Arrange 2 buttons per row
                inline_keyboard.append(buttons_collection)
                buttons_collection = []

    Args:
        text: The text to be displayed on the button

    Returns:
        bool: True if button text is long and should get its own row,
              False if text is short and can be arranged horizontally
    """
    # Return True for long text (single row), False for short text (can be paired)
    # This enables horizontal arrangement while preventing text overflow
    return len(text) > NAME_LENGTH_THRESHOLD
