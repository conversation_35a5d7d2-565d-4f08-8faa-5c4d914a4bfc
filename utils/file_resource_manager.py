"""
File Resource Manager
Handles file operations with proper resource management and cleanup.
Prevents file handle leaks and ensures efficient file processing.
"""

import logging
import os
import shutil
import tempfile
import threading
from contextlib import contextmanager
from typing import Dict, Any, List, Optional, Union, BinaryIO, TextIO
from datetime import datetime
from pathlib import Path
import weakref

logger = logging.getLogger(__name__)


class FileResourceError(Exception):
    """Custom exception for file resource management errors."""
    pass


class FileHandle:
    """Wrapper for file handles with automatic cleanup tracking."""
    
    def __init__(self, file_path: str, mode: str = 'r', encoding: Optional[str] = None):
        self.file_path = file_path
        self.mode = mode
        self.encoding = encoding
        self.handle: Optional[Union[BinaryIO, TextIO]] = None
        self.opened_at = None
        self.closed_at = None
        self.is_open = False
        
    def open(self) -> Union[BinaryIO, TextIO]:
        """Open the file handle."""
        if self.is_open:
            raise FileResourceError(f"File {self.file_path} is already open")
        
        try:
            if self.encoding and 'b' not in self.mode:
                self.handle = open(self.file_path, self.mode, encoding=self.encoding)
            else:
                self.handle = open(self.file_path, self.mode)
            
            self.opened_at = datetime.now()
            self.is_open = True
            logger.debug(f"Opened file handle: {self.file_path}")
            return self.handle
            
        except Exception as e:
            logger.error(f"Failed to open file {self.file_path}: {e}")
            raise FileResourceError(f"Failed to open file: {str(e)}")
    
    def close(self) -> None:
        """Close the file handle."""
        if self.handle and self.is_open:
            try:
                self.handle.close()
                self.closed_at = datetime.now()
                self.is_open = False
                logger.debug(f"Closed file handle: {self.file_path}")
            except Exception as e:
                logger.error(f"Error closing file {self.file_path}: {e}")
        
    def __enter__(self):
        return self.open()
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()


class FileResourceManager:
    """
    Manages file resources with automatic cleanup and leak detection.
    Ensures proper file handle management and prevents resource leaks.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self._open_handles: Dict[str, FileHandle] = {}
        self._handle_lock = threading.Lock()
        self._temp_files: List[str] = []
        self._temp_dirs: List[str] = []
        
        # Register cleanup on exit
        import atexit
        atexit.register(self.cleanup_all_resources)
    
    @contextmanager
    def managed_file(self, file_path: str, mode: str = 'r', encoding: Optional[str] = None):
        """
        Context manager for file operations with automatic cleanup.
        
        Args:
            file_path: Path to the file
            mode: File open mode
            encoding: File encoding (for text files)
            
        Yields:
            File handle
        """
        file_handle = None
        handle_id = f"{file_path}_{id(threading.current_thread())}"
        
        try:
            with self._handle_lock:
                if handle_id in self._open_handles:
                    raise FileResourceError(f"File {file_path} already has an open handle in this thread")
                
                file_handle = FileHandle(file_path, mode, encoding)
                self._open_handles[handle_id] = file_handle
            
            with file_handle as handle:
                yield handle
                
        except Exception as e:
            self.logger.error(f"Error in managed file operation for {file_path}: {e}")
            raise
        finally:
            with self._handle_lock:
                if handle_id in self._open_handles:
                    del self._open_handles[handle_id]
    
    def safe_file_copy(self, source: str, destination: str, create_backup: bool = True) -> Dict[str, Any]:
        """
        Safely copy a file with proper error handling and optional backup.
        
        Args:
            source: Source file path
            destination: Destination file path
            create_backup: Whether to create a backup of existing destination
            
        Returns:
            Dict with operation results
        """
        try:
            self.logger.info(f"Copying file from {source} to {destination}")
            
            # Validate source file
            if not os.path.exists(source):
                raise FileResourceError(f"Source file does not exist: {source}")
            
            if not os.path.isfile(source):
                raise FileResourceError(f"Source is not a file: {source}")
            
            # Create destination directory if needed
            dest_dir = os.path.dirname(destination)
            if dest_dir:
                os.makedirs(dest_dir, exist_ok=True)
            
            # Create backup if requested and destination exists
            backup_path = None
            if create_backup and os.path.exists(destination):
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"{destination}.backup_{timestamp}"
                shutil.copy2(destination, backup_path)
                self.logger.info(f"Created backup: {backup_path}")
            
            # Perform the copy
            shutil.copy2(source, destination)
            
            # Verify the copy
            if not os.path.exists(destination):
                raise FileResourceError("Copy operation failed - destination file not found")
            
            source_size = os.path.getsize(source)
            dest_size = os.path.getsize(destination)
            
            if source_size != dest_size:
                raise FileResourceError(f"Copy verification failed - size mismatch: {source_size} != {dest_size}")
            
            result = {
                "success": True,
                "source": source,
                "destination": destination,
                "backup_created": backup_path,
                "file_size": dest_size,
                "timestamp": datetime.now().isoformat()
            }
            
            self.logger.info(f"Successfully copied file: {source} -> {destination}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to copy file {source} to {destination}: {e}")
            return {
                "success": False,
                "error": str(e),
                "source": source,
                "destination": destination
            }
    
    def safe_file_move(self, source: str, destination: str) -> Dict[str, Any]:
        """
        Safely move a file with proper error handling.
        
        Args:
            source: Source file path
            destination: Destination file path
            
        Returns:
            Dict with operation results
        """
        try:
            self.logger.info(f"Moving file from {source} to {destination}")
            
            # First copy, then delete source if copy succeeds
            copy_result = self.safe_file_copy(source, destination, create_backup=False)
            
            if not copy_result["success"]:
                return copy_result
            
            # Remove source file
            os.remove(source)
            
            result = {
                "success": True,
                "source": source,
                "destination": destination,
                "file_size": copy_result["file_size"],
                "timestamp": datetime.now().isoformat()
            }
            
            self.logger.info(f"Successfully moved file: {source} -> {destination}")
            return result
            
        except Exception as e:
            self.logger.error(f"Failed to move file {source} to {destination}: {e}")
            return {
                "success": False,
                "error": str(e),
                "source": source,
                "destination": destination
            }
    
    def create_temp_file(self, suffix: str = '', prefix: str = 'tmp', dir: Optional[str] = None) -> str:
        """
        Create a temporary file with automatic cleanup tracking.
        
        Args:
            suffix: File suffix
            prefix: File prefix
            dir: Directory for temp file
            
        Returns:
            Path to temporary file
        """
        try:
            fd, temp_path = tempfile.mkstemp(suffix=suffix, prefix=prefix, dir=dir)
            os.close(fd)  # Close the file descriptor immediately
            
            self._temp_files.append(temp_path)
            self.logger.debug(f"Created temporary file: {temp_path}")
            
            return temp_path
            
        except Exception as e:
            self.logger.error(f"Failed to create temporary file: {e}")
            raise FileResourceError(f"Failed to create temporary file: {str(e)}")
    
    def create_temp_directory(self, suffix: str = '', prefix: str = 'tmp', dir: Optional[str] = None) -> str:
        """
        Create a temporary directory with automatic cleanup tracking.
        
        Args:
            suffix: Directory suffix
            prefix: Directory prefix
            dir: Parent directory for temp directory
            
        Returns:
            Path to temporary directory
        """
        try:
            temp_dir = tempfile.mkdtemp(suffix=suffix, prefix=prefix, dir=dir)
            self._temp_dirs.append(temp_dir)
            self.logger.debug(f"Created temporary directory: {temp_dir}")
            
            return temp_dir
            
        except Exception as e:
            self.logger.error(f"Failed to create temporary directory: {e}")
            raise FileResourceError(f"Failed to create temporary directory: {str(e)}")
    
    def cleanup_temp_files(self) -> Dict[str, Any]:
        """
        Clean up all tracked temporary files.
        
        Returns:
            Dict with cleanup results
        """
        cleaned_files = []
        failed_files = []
        
        for temp_file in self._temp_files[:]:  # Copy list to avoid modification during iteration
            try:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
                    cleaned_files.append(temp_file)
                    self.logger.debug(f"Cleaned up temporary file: {temp_file}")
                
                self._temp_files.remove(temp_file)
                
            except Exception as e:
                failed_files.append({"file": temp_file, "error": str(e)})
                self.logger.error(f"Failed to clean up temporary file {temp_file}: {e}")
        
        return {
            "cleaned_files": cleaned_files,
            "failed_files": failed_files,
            "success": len(failed_files) == 0
        }
    
    def cleanup_temp_directories(self) -> Dict[str, Any]:
        """
        Clean up all tracked temporary directories.
        
        Returns:
            Dict with cleanup results
        """
        cleaned_dirs = []
        failed_dirs = []
        
        for temp_dir in self._temp_dirs[:]:  # Copy list to avoid modification during iteration
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
                    cleaned_dirs.append(temp_dir)
                    self.logger.debug(f"Cleaned up temporary directory: {temp_dir}")
                
                self._temp_dirs.remove(temp_dir)
                
            except Exception as e:
                failed_dirs.append({"directory": temp_dir, "error": str(e)})
                self.logger.error(f"Failed to clean up temporary directory {temp_dir}: {e}")
        
        return {
            "cleaned_directories": cleaned_dirs,
            "failed_directories": failed_dirs,
            "success": len(failed_dirs) == 0
        }
    
    def cleanup_all_resources(self) -> Dict[str, Any]:
        """
        Clean up all managed resources.
        
        Returns:
            Dict with cleanup results
        """
        self.logger.info("Cleaning up all file resources")
        
        # Close any open file handles
        open_handles = []
        with self._handle_lock:
            for handle_id, file_handle in self._open_handles.items():
                try:
                    file_handle.close()
                    open_handles.append(handle_id)
                except Exception as e:
                    self.logger.error(f"Error closing file handle {handle_id}: {e}")
            
            self._open_handles.clear()
        
        # Clean up temporary files and directories
        temp_file_cleanup = self.cleanup_temp_files()
        temp_dir_cleanup = self.cleanup_temp_directories()
        
        result = {
            "closed_handles": len(open_handles),
            "temp_file_cleanup": temp_file_cleanup,
            "temp_directory_cleanup": temp_dir_cleanup,
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.info(f"Resource cleanup completed: {len(open_handles)} handles closed")
        
        return result
    
    def get_resource_status(self) -> Dict[str, Any]:
        """
        Get current status of managed resources.
        
        Returns:
            Dict with resource status
        """
        with self._handle_lock:
            open_handle_count = len(self._open_handles)
            open_handle_details = [
                {
                    "handle_id": handle_id,
                    "file_path": file_handle.file_path,
                    "mode": file_handle.mode,
                    "opened_at": file_handle.opened_at.isoformat() if file_handle.opened_at else None,
                    "is_open": file_handle.is_open
                }
                for handle_id, file_handle in self._open_handles.items()
            ]
        
        return {
            "open_file_handles": open_handle_count,
            "handle_details": open_handle_details,
            "temp_files_tracked": len(self._temp_files),
            "temp_directories_tracked": len(self._temp_dirs),
            "temp_files": self._temp_files.copy(),
            "temp_directories": self._temp_dirs.copy(),
            "timestamp": datetime.now().isoformat()
        }


# Global instance for easy access
file_resource_manager = FileResourceManager()
