"""
Bonus Tier Error Handling Module.
Provides comprehensive error handling, categorization, and recovery for bonus tier operations.
"""

import logging
from enum import Enum
from typing import Dict, Any, List, Optional
from datetime import datetime

# Optional aiogram import for keyboard functionality
try:
    from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
    AIOGRAM_AVAILABLE = True
except ImportError:
    # Create dummy classes if aiogram is not available
    class InlineKeyboardMarkup:
        def __init__(self, inline_keyboard=None):
            self.inline_keyboard = inline_keyboard or []

    class InlineKeyboardButton:
        def __init__(self, text="", callback_data=""):
            self.text = text
            self.callback_data = callback_data

    AIOGRAM_AVAILABLE = False

logger = logging.getLogger(__name__)


class TierErrorCategory(Enum):
    """Categories for bonus tier operation errors."""
    VALIDATION = "validation"
    PERMISSION = "permission"
    RATE_LIMIT = "rate_limit"
    CONFLICT = "conflict"
    DATABASE = "database"
    SYSTEM = "system"
    NETWORK = "network"
    TIMEOUT = "timeout"


class TierErrorHandler:
    """Handles bonus tier operation errors with categorization and recovery suggestions."""
    
    def __init__(self):
        self.error_patterns = {
            TierErrorCategory.VALIDATION: [
                "invalid", "validation", "format", "range", "threshold", "percentage"
            ],
            TierErrorCategory.PERMISSION: [
                "permission", "access", "denied", "unauthorized", "forbidden"
            ],
            TierErrorCategory.RATE_LIMIT: [
                "rate limit", "too many", "throttle", "quota"
            ],
            TierErrorCategory.CONFLICT: [
                "already exists", "duplicate", "conflict", "exists"
            ],
            TierErrorCategory.DATABASE: [
                "database", "db", "connection", "query", "mongo", "collection"
            ],
            TierErrorCategory.NETWORK: [
                "network", "connection", "timeout", "unreachable"
            ],
            TierErrorCategory.SYSTEM: [
                "system", "internal", "server", "unexpected"
            ]
        }
        
        self.user_messages = {
            TierErrorCategory.VALIDATION: "❌ <b>Invalid Input</b>\n\nPlease check your input and try again.",
            TierErrorCategory.PERMISSION: "🔒 <b>Access Denied</b>\n\nYou don't have permission for this operation.",
            TierErrorCategory.RATE_LIMIT: "⏱️ <b>Rate Limit</b>\n\nPlease wait before trying again.",
            TierErrorCategory.CONFLICT: "⚠️ <b>Conflict</b>\n\nThis item already exists or conflicts with existing data.",
            TierErrorCategory.DATABASE: "💾 <b>Database Error</b>\n\nPlease try again later.",
            TierErrorCategory.NETWORK: "🌐 <b>Network Error</b>\n\nPlease check your connection and try again.",
            TierErrorCategory.SYSTEM: "⚙️ <b>System Error</b>\n\nAn unexpected error occurred. Please try again."
        }
    
    def categorize_error(self, operation: str, error_message: str) -> TierErrorCategory:
        """Categorize an error based on its message content."""
        error_lower = error_message.lower()
        
        for category, patterns in self.error_patterns.items():
            if any(pattern in error_lower for pattern in patterns):
                return category
        
        # Default to system error if no pattern matches
        return TierErrorCategory.SYSTEM
    
    def handle_tier_operation_error(
        self, 
        operation: str, 
        error: Exception, 
        user_id: Optional[int] = None,
        additional_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Handle a tier operation error with comprehensive logging and user feedback.
        
        Args:
            operation: The operation that failed
            error: The exception that occurred
            user_id: User ID for logging purposes
            additional_context: Additional context for error analysis
            
        Returns:
            Dict with error information and user message
        """
        error_message = str(error)
        category = self.categorize_error(operation, error_message)
        
        # Generate error ID for tracking
        error_id = f"TIER-{datetime.now().strftime('%Y%m%d%H%M%S')}-{hash(error_message) % 10000}"
        
        # Log the error with full context
        logger.error(
            f"Tier operation error [{error_id}]: {operation} failed for user {user_id}. "
            f"Category: {category.value}, Error: {error_message}",
            exc_info=True
        )
        
        # Create user-friendly message
        base_message = self.user_messages.get(category, self.user_messages[TierErrorCategory.SYSTEM])
        
        # Add specific details based on operation and category
        if category == TierErrorCategory.VALIDATION:
            if "threshold" in operation:
                base_message += "\n\n💡 <b>Tip:</b> Threshold must be a positive number (e.g., 50.00)"
            elif "percentage" in operation:
                base_message += "\n\n💡 <b>Tip:</b> Percentage must be between 0.01 and 1.00 (e.g., 0.15 for 15%)"
        
        elif category == TierErrorCategory.CONFLICT:
            if "create" in operation:
                base_message += "\n\n💡 <b>Suggestion:</b> Try a different threshold value or update the existing tier."
        
        # Add error ID for support purposes
        base_message += f"\n\n🆔 <i>Error ID: {error_id}</i>"
        
        return {
            "success": False,
            "error_id": error_id,
            "category": category,
            "operation": operation,
            "error_message": error_message,
            "user_message": base_message,
            "user_id": user_id,
            "additional_context": additional_context or {}
        }
    
    def create_recovery_keyboard(
        self,
        operation: str,
        retry_allowed: bool = True
    ) -> InlineKeyboardMarkup:
        """Create a recovery keyboard with appropriate options."""
        if not AIOGRAM_AVAILABLE:
            # Return a dummy keyboard if aiogram is not available
            return InlineKeyboardMarkup(inline_keyboard=[])

        buttons = []

        if retry_allowed:
            buttons.append([InlineKeyboardButton(text="🔄 Try Again", callback_data=f"retry_{operation}")])

        # Add operation-specific recovery options
        if "create" in operation:
            buttons.extend([
                [InlineKeyboardButton(text="📊 View Existing Tiers", callback_data="bonus_view_all")],
                [InlineKeyboardButton(text="🔙 Back to Management", callback_data="bonus_management")]
            ])
        elif "edit" in operation:
            buttons.extend([
                [InlineKeyboardButton(text="📊 View All Tiers", callback_data="bonus_view_all")],
                [InlineKeyboardButton(text="🔙 Back to Management", callback_data="bonus_management")]
            ])
        elif "delete" in operation:
            buttons.extend([
                [InlineKeyboardButton(text="📊 View All Tiers", callback_data="bonus_view_all")],
                [InlineKeyboardButton(text="🔙 Back to Management", callback_data="bonus_management")]
            ])
        else:
            # Generic recovery options
            buttons.append([InlineKeyboardButton(text="🔙 Back to Management", callback_data="bonus_management")])

        return InlineKeyboardMarkup(inline_keyboard=buttons)
    
    def suggest_alternative_actions(
        self, 
        operation: str, 
        category: TierErrorCategory
    ) -> List[str]:
        """Suggest alternative actions based on the failed operation and error category."""
        suggestions = []
        
        if category == TierErrorCategory.VALIDATION:
            if "threshold" in operation:
                suggestions.extend([
                    "Check that threshold is a positive number",
                    "Ensure threshold doesn't conflict with existing tiers",
                    "Try a different threshold value"
                ])
            elif "percentage" in operation:
                suggestions.extend([
                    "Use decimal format (e.g., 0.15 for 15%)",
                    "Ensure percentage is between 0.01 and 1.00",
                    "Check for valid number format"
                ])
        
        elif category == TierErrorCategory.CONFLICT:
            suggestions.extend([
                "Check existing tiers for conflicts",
                "Use a different threshold value",
                "Update existing tier instead of creating new one"
            ])
        
        elif category == TierErrorCategory.PERMISSION:
            suggestions.extend([
                "Contact an administrator",
                "Check your access permissions",
                "Try logging in again"
            ])
        
        elif category == TierErrorCategory.DATABASE:
            suggestions.extend([
                "Try again in a few moments",
                "Check system status",
                "Contact support if problem persists"
            ])
        
        else:
            # Generic suggestions
            suggestions.extend([
                "Try the operation again",
                "Check your input values",
                "Contact support if problem persists"
            ])
        
        return suggestions[:5]  # Limit to 5 suggestions


# Global instance for easy access
tier_error_handler = TierErrorHandler()
