from aiogram.filters.callback_data import CallbackData
from aiogram.types import Callback<PERSON>uery
from typing import Dict, Any, Optional


class DepositCallback(CallbackData, prefix="deposit"):
    """Callback data for deposit operations"""

    action: str
    amount: Optional[float] = None
    invoice_id: Optional[str] = None


class ProductCallback(CallbackData, prefix="product"):
    """Callback data for product operations"""

    action: str
    product_id: Optional[str] = None
    category_id: Optional[str] = None


class PaymentVerificationCallback(CallbackData, prefix="payment"):
    """Callback data for payment verification operations"""

    action: str
    track_id: Optional[str] = None


# Export only the factories (classes)
__all__ = [
    "DepositCallback",
    "ProductCallback",
    "PaymentVerificationCallback",
]