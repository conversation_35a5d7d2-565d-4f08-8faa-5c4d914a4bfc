"""
Database Optimizer <PERSON><PERSON><PERSON>
Handles database index creation and query optimization for improved performance.
Ensures efficient database operations across all collections.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
import pymongo

from database.connection import (
    db, products_collection, users_collection, transactions_collection,
    carts_collection, categories_collection, admins_collection
)

logger = logging.getLogger(__name__)


class DatabaseOptimizer:
    """
    Handles database optimization including index creation and query optimization.
    Ensures efficient database operations and monitors performance.
    """
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        self.optimization_history = []
    
    def create_all_indexes(self) -> Dict[str, Any]:
        """
        Create all necessary indexes for optimal performance.
        
        Returns:
            Dict with index creation results
        """
        self.logger.info("Starting comprehensive database index creation")
        start_time = datetime.now()
        
        results = {
            "products": self._create_product_indexes(),
            "users": self._create_user_indexes(),
            "transactions": self._create_transaction_indexes(),
            "carts": self._create_cart_indexes(),
            "categories": self._create_category_indexes(),
            "admins": self._create_admin_indexes()
        }
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Count successful index creations
        total_indexes = sum(len(result.get("indexes_created", [])) for result in results.values())
        failed_indexes = sum(len(result.get("errors", [])) for result in results.values())
        
        summary = {
            "success": failed_indexes == 0,
            "total_indexes_created": total_indexes,
            "failed_indexes": failed_indexes,
            "duration_seconds": duration,
            "timestamp": datetime.now().isoformat(),
            "details": results
        }
        
        self.optimization_history.append(summary)
        
        if failed_indexes == 0:
            self.logger.info(f"Successfully created {total_indexes} indexes in {duration:.2f}s")
        else:
            self.logger.warning(f"Created {total_indexes} indexes with {failed_indexes} failures in {duration:.2f}s")
        
        return summary
    
    def _create_product_indexes(self) -> Dict[str, Any]:
        """Create indexes for products collection."""
        indexes_to_create = [
            # Primary lookup indexes
            {"keys": [("id", 1)], "unique": True, "name": "id_unique"},
            {"keys": [("name", 1)], "name": "name_index"},
            {"keys": [("category_id", 1)], "name": "category_lookup"},
            
            # Product type indexes
            {"keys": [("is_line_based", 1)], "name": "line_based_filter"},
            {"keys": [("is_exclusive_single_use", 1)], "name": "exclusive_filter"},
            
            # Inventory management indexes
            {"keys": [("is_line_based", 1), ("available_lines", -1)], "name": "line_inventory_lookup"},
            {"keys": [("is_exclusive_single_use", 1), ("is_purchased", 1)], "name": "exclusive_availability"},
            
            # Performance indexes
            {"keys": [("created_at", -1)], "name": "creation_date_desc"},
            {"keys": [("price", 1)], "name": "price_range_queries"},
            
            # Compound indexes for common queries
            {"keys": [("category_id", 1), ("created_at", -1)], "name": "category_recent"},
            {"keys": [("is_line_based", 1), ("available_lines", -1), ("price", 1)], "name": "line_product_search"},
            
            # Text search index
            {"keys": [("name", "text"), ("description", "text")], "name": "text_search"}
        ]
        
        return self._create_indexes_for_collection(products_collection, "products", indexes_to_create)
    
    def _create_user_indexes(self) -> Dict[str, Any]:
        """Create indexes for users collection."""
        indexes_to_create = [
            # Primary lookup
            {"keys": [("user_id", 1)], "unique": True, "name": "user_id_unique"},
            
            # Performance indexes
            {"keys": [("created_at", -1)], "name": "registration_date"},
            {"keys": [("last_seen", -1)], "name": "activity_tracking"},
            {"keys": [("balance", -1)], "name": "balance_queries"},
            
            # Username lookup (if exists)
            {"keys": [("username", 1)], "sparse": True, "name": "username_lookup"}
        ]
        
        return self._create_indexes_for_collection(users_collection, "users", indexes_to_create)
    
    def _create_transaction_indexes(self) -> Dict[str, Any]:
        """Create indexes for transactions collection."""
        indexes_to_create = [
            # User transaction lookup
            {"keys": [("user_id", 1), ("timestamp", -1)], "name": "user_transactions"},
            
            # Transaction type filtering
            {"keys": [("type", 1), ("timestamp", -1)], "name": "transaction_type_time"},
            
            # Amount range queries
            {"keys": [("amount", -1)], "name": "amount_queries"},
            
            # Recent transactions
            {"keys": [("timestamp", -1)], "name": "recent_transactions"},
            
            # Order tracking
            {"keys": [("additional_data.order_id", 1)], "sparse": True, "name": "order_tracking"}
        ]
        
        return self._create_indexes_for_collection(transactions_collection, "transactions", indexes_to_create)
    
    def _create_cart_indexes(self) -> Dict[str, Any]:
        """Create indexes for carts collection."""
        indexes_to_create = [
            # User cart lookup
            {"keys": [("user_id", 1)], "unique": True, "name": "user_cart_unique"},
            
            # Cart activity tracking
            {"keys": [("created_at", -1)], "name": "cart_creation_time"},
            
            # Product lookup in carts
            {"keys": [("items.product_id", 1)], "name": "cart_product_lookup"}
        ]
        
        return self._create_indexes_for_collection(carts_collection, "carts", indexes_to_create)
    
    def _create_category_indexes(self) -> Dict[str, Any]:
        """Create indexes for categories collection."""
        indexes_to_create = [
            # Primary lookup
            {"keys": [("id", 1)], "unique": True, "name": "category_id_unique"},
            
            # Name lookup
            {"keys": [("name", 1)], "unique": True, "name": "category_name_unique"},
            
            # Slug lookup
            {"keys": [("slug", 1)], "unique": True, "sparse": True, "name": "category_slug_unique"},
            
            # Creation date
            {"keys": [("created_at", -1)], "name": "category_creation_date"}
        ]
        
        return self._create_indexes_for_collection(categories_collection, "categories", indexes_to_create)
    
    def _create_admin_indexes(self) -> Dict[str, Any]:
        """Create indexes for admins collection."""
        indexes_to_create = [
            # Admin lookup
            {"keys": [("user_id", 1)], "unique": True, "name": "admin_user_id_unique"},
            
            # Role-based queries
            {"keys": [("role", 1)], "name": "admin_role_lookup"},
            
            # Activity tracking
            {"keys": [("created_at", -1)], "name": "admin_creation_date"}
        ]
        
        return self._create_indexes_for_collection(admins_collection, "admins", indexes_to_create)
    
    def _create_indexes_for_collection(self, collection, collection_name: str, indexes: List[Dict]) -> Dict[str, Any]:
        """
        Create indexes for a specific collection.
        
        Args:
            collection: MongoDB collection object
            collection_name: Name of the collection
            indexes: List of index specifications
            
        Returns:
            Dict with creation results
        """
        created_indexes = []
        errors = []
        
        self.logger.info(f"Creating {len(indexes)} indexes for {collection_name} collection")
        
        for index_spec in indexes:
            try:
                keys = index_spec["keys"]
                options = {k: v for k, v in index_spec.items() if k != "keys"}
                
                # Check if index already exists
                existing_indexes = collection.list_indexes()
                index_name = options.get("name", f"auto_{hash(str(keys))}")
                
                if any(idx.get("name") == index_name for idx in existing_indexes):
                    self.logger.debug(f"Index {index_name} already exists in {collection_name}")
                    continue
                
                # Create the index
                result = collection.create_index(keys, **options)
                created_indexes.append({
                    "name": index_name,
                    "keys": keys,
                    "options": options,
                    "result": result
                })
                
                self.logger.debug(f"Created index {index_name} in {collection_name}")
                
            except Exception as e:
                error_info = {
                    "index_spec": index_spec,
                    "error": str(e),
                    "collection": collection_name
                }
                errors.append(error_info)
                self.logger.error(f"Failed to create index in {collection_name}: {e}")
        
        return {
            "collection": collection_name,
            "indexes_created": created_indexes,
            "errors": errors,
            "success": len(errors) == 0
        }
    
    def analyze_query_performance(self, collection_name: str, query: Dict, limit: int = 100) -> Dict[str, Any]:
        """
        Analyze query performance and suggest optimizations.
        
        Args:
            collection_name: Name of the collection
            query: MongoDB query to analyze
            limit: Limit for the query
            
        Returns:
            Dict with performance analysis
        """
        try:
            collection = getattr(db, collection_name)
            
            # Execute query with explain
            explain_result = collection.find(query).limit(limit).explain()
            
            # Extract performance metrics
            execution_stats = explain_result.get("executionStats", {})
            
            analysis = {
                "query": query,
                "collection": collection_name,
                "execution_time_ms": execution_stats.get("executionTimeMillis", 0),
                "documents_examined": execution_stats.get("docsExamined", 0),
                "documents_returned": execution_stats.get("docsReturned", 0),
                "index_used": execution_stats.get("indexUsed", False),
                "winning_plan": explain_result.get("queryPlanner", {}).get("winningPlan", {}),
                "timestamp": datetime.now().isoformat()
            }
            
            # Calculate efficiency ratio
            docs_examined = analysis["documents_examined"]
            docs_returned = analysis["documents_returned"]
            
            if docs_returned > 0:
                analysis["efficiency_ratio"] = docs_returned / max(docs_examined, 1)
            else:
                analysis["efficiency_ratio"] = 0
            
            # Provide optimization suggestions
            analysis["suggestions"] = self._generate_optimization_suggestions(analysis)
            
            return analysis
            
        except Exception as e:
            self.logger.error(f"Error analyzing query performance: {e}")
            return {
                "error": str(e),
                "query": query,
                "collection": collection_name
            }
    
    def _generate_optimization_suggestions(self, analysis: Dict[str, Any]) -> List[str]:
        """Generate optimization suggestions based on query analysis."""
        suggestions = []
        
        efficiency_ratio = analysis.get("efficiency_ratio", 0)
        execution_time = analysis.get("execution_time_ms", 0)
        index_used = analysis.get("index_used", False)
        
        if not index_used:
            suggestions.append("Consider creating an index for this query pattern")
        
        if efficiency_ratio < 0.1:
            suggestions.append("Query examines too many documents - consider more selective criteria")
        
        if execution_time > 100:
            suggestions.append("Query execution time is high - consider optimization")
        
        docs_examined = analysis.get("documents_examined", 0)
        if docs_examined > 1000:
            suggestions.append("Consider adding pagination or more restrictive filters")
        
        return suggestions
    
    def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics for all collections."""
        collections = [
            "products", "users", "transactions", 
            "carts", "categories", "admins"
        ]
        
        stats = {}
        
        for collection_name in collections:
            try:
                collection = getattr(db, collection_name)
                collection_stats = db.command("collStats", collection_name)
                
                stats[collection_name] = {
                    "document_count": collection_stats.get("count", 0),
                    "size_bytes": collection_stats.get("size", 0),
                    "average_document_size": collection_stats.get("avgObjSize", 0),
                    "index_count": collection_stats.get("nindexes", 0),
                    "total_index_size": collection_stats.get("totalIndexSize", 0)
                }
                
            except Exception as e:
                stats[collection_name] = {"error": str(e)}
        
        return {
            "timestamp": datetime.now().isoformat(),
            "collections": stats
        }
    
    def optimize_database(self) -> Dict[str, Any]:
        """
        Perform comprehensive database optimization.
        
        Returns:
            Dict with optimization results
        """
        self.logger.info("Starting comprehensive database optimization")
        start_time = datetime.now()
        
        # Create indexes
        index_results = self.create_all_indexes()
        
        # Get collection statistics
        collection_stats = self.get_collection_stats()
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        optimization_result = {
            "success": index_results["success"],
            "duration_seconds": duration,
            "index_creation": index_results,
            "collection_stats": collection_stats,
            "timestamp": datetime.now().isoformat(),
            "recommendations": self._generate_database_recommendations(collection_stats)
        }
        
        self.logger.info(f"Database optimization completed in {duration:.2f}s")
        
        return optimization_result
    
    def _generate_database_recommendations(self, stats: Dict[str, Any]) -> List[str]:
        """Generate database optimization recommendations."""
        recommendations = []
        
        for collection_name, collection_stats in stats.get("collections", {}).items():
            if "error" in collection_stats:
                continue
            
            doc_count = collection_stats.get("document_count", 0)
            avg_doc_size = collection_stats.get("average_document_size", 0)
            index_count = collection_stats.get("index_count", 0)
            
            if doc_count > 10000 and index_count < 3:
                recommendations.append(f"Consider adding more indexes to {collection_name} collection")
            
            if avg_doc_size > 16000:  # 16KB
                recommendations.append(f"Large average document size in {collection_name} - consider data normalization")
            
            if doc_count > 100000:
                recommendations.append(f"Large collection {collection_name} - consider archiving old data")
        
        return recommendations


# Global instance for easy access
database_optimizer = DatabaseOptimizer()
