﻿"""
Image handling utilities to download and store images from Telegram to local storage.
This module centralizes image processing operations for consistency.
"""

import os
import logging
from aiogram import Bo<PERSON>
from aiogram.types import Message, InputMediaPhoto, InputFile
from typing import Optional, Union, Dict, Any
from utils.helpers import ensure_upload_directories

from utils.local_file_handling import (
    download_file_from_telegram,
    process_image_message,
    save_external_image,
    get_file_url_path,
)

# Logger setup
logger = logging.getLogger(__name__)

# Define image folder constants
PRODUCT_IMAGES_FOLDER = "product_images"
CATEGORY_IMAGES_FOLDER = "category_images"
USER_UPLOADS_FOLDER = "user_uploads"
FILE_UPLOADS_FOLDER = "files"
TEMP_IMAGES_FOLDER = "temp"
WELCOME_IMAGES_FOLDER = "welcome_images"


async def download_telegram_image(
    bot: Bot,
    image_source: str,
    folder: str = PRODUCT_IMAGES_FOLDER,
    original_filename: str = None,
) -> Dict[str, Any]:
    """
    Download an image from Telegram using file_id or process a URL.

    Args:
        bot: Bot instance
        image_source: Either a Telegram file_id or a URL
        folder: Target folder for storage (default: product_images)
        original_filename: Original filename if available

    Returns:
        dict: Dictionary containing file info including local path
              or None if download fails
    """
    try:
        # Check if image_source is a URL
        if isinstance(image_source, str) and image_source.startswith(
            ("http://", "https://")
        ):
            logger.info(f"Processing image from URL: {image_source[:50]}...")
            return await save_external_image(image_source, folder)

        # Otherwise assume it's a file_id
        logger.info(f"Processing image from Telegram file_id: {image_source[:15]}...")
        return await download_file_from_telegram(
            bot, image_source, folder, original_filename
        )

    except Exception as e:
        logger.error(f"Error downloading image: {e}", exc_info=True)
        return None


async def process_product_image(
    message: Message, folder: str = PRODUCT_IMAGES_FOLDER
) -> dict:
    """
    Process an image from a message for product usage.

    Args:
        message: Telegram message containing photo or document
        folder: Destination folder (product_images by default)

    Returns:
        dict: File data including url_path, local_path, original_file_id
    """
    # Ensure directories exist
    ensure_upload_directories()

    try:
        if message.photo:
            # Get the largest photo
            file_id = message.photo[-1].file_id
            file_name = f"photo_{file_id}.jpg"

            # Process the file
            file_data = await download_file_from_telegram(
                message.bot, file_id, folder, file_name
            )
            return file_data

        # Also handle document messages (for GIFs and other image formats)
        elif message.document:
            # Check if it's an image by MIME type or file extension for GIFs
            is_image = False

            # Check MIME type
            if message.document.mime_type and message.document.mime_type.startswith(
                "image/"
            ):
                is_image = True

            # Check file extension for GIFs specifically
            if (
                message.document.file_name
                and message.document.file_name.lower().endswith(".gif")
            ):
                is_image = True
                logger.info(
                    f"GIF file detected in process_product_image: {message.document.file_name}"
                )

            if is_image:
                # Download the file
                file_id = message.document.file_id
                file_name = message.document.file_name or f"file_{file_id}"

                # Process the file
                file_data = await download_file_from_telegram(
                    message.bot, file_id, folder, file_name
                )
                return file_data

        return None
    except Exception as e:
        logger.error(f"Error processing product image: {e}", exc_info=True)
        return None


async def get_image_path_for_display(image_source: str, bot: Bot = None) -> str:
    """
    Get the appropriate path to use for displaying an image.

    This function handles different image source types:
    - If it's a local path, it returns the absolute file path
    - If it's a Telegram file_id and bot is provided, it downloads and returns the local file path
    - If it's already a URL, it returns it as is

    Args:
        image_source: Image source (file_id, URL, or local path)
        bot: Bot instance (needed if image_source is a file_id to download)

    Returns:
        str: Path to use for displaying the image
    """
    if not image_source:
        return None

    # If it's already a URL
    if isinstance(image_source, str) and image_source.startswith(
        ("http://", "https://")
    ):
        return image_source

    # Get the base directory for resolving paths
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # Try multiple path resolution strategies to find the local file
    possible_paths = []

    # If it's a path with known folders
    if isinstance(image_source, str):
        # 1. Direct path as provided
        possible_paths.append(image_source)

        # 2. Path relative to base directory
        possible_paths.append(os.path.join(base_dir, image_source))

        # 3. Handle paths with 'uploads/'
        if "uploads/" in image_source:
            if image_source.startswith("uploads/"):
                possible_paths.append(os.path.join(base_dir, image_source))
            else:
                path_parts = image_source.split("uploads/")
                if len(path_parts) > 1:
                    possible_paths.append(
                        os.path.join(base_dir, "uploads", path_parts[1])
                    )

        # 4. Try with uploads directory
        possible_paths.append(os.path.join(base_dir, "uploads", image_source))

        # 5. Try specific image folders
        filename = os.path.basename(image_source)
        possible_paths.append(
            os.path.join(base_dir, "uploads", PRODUCT_IMAGES_FOLDER, filename)
        )
        possible_paths.append(
            os.path.join(base_dir, "uploads", CATEGORY_IMAGES_FOLDER, filename)
        )

        # Check all possible paths
        for path in possible_paths:
            if os.path.isfile(path):
                logger.debug(f"Found local file at: {path}")
                return path

    # If it seems to be a file_id and we have a bot
    if (
        bot
        and isinstance(image_source, str)
        and not image_source.startswith(("/", "http://", "https://"))
    ):
        # Download the image
        try:
            file_data = await download_telegram_image(
                bot, image_source, PRODUCT_IMAGES_FOLDER
            )
            if file_data:
                # Return the local path instead of URL path
                local_path = file_data.get("local_path")
                if os.path.isfile(local_path):
                    return local_path
        except Exception as e:
            logger.error(f"Error getting image path: {e}", exc_info=True)

    # If all else fails, return the original
    logger.warning(f"Could not resolve image path for: {image_source}")
    return image_source


async def process_document_file(
    message: Message, folder: str = FILE_UPLOADS_FOLDER
) -> dict:
    """
    Process a document message for file storage.

    Args:
        message: Telegram message containing document
        folder: Destination folder (files by default)

    Returns:
        dict: File data including url_path, local_path, original_file_id
    """
    # Ensure directories exist
    ensure_upload_directories()

    try:
        if message.document:
            file_id = message.document.file_id
            file_name = message.document.file_name or f"file_{file_id}"
            file_size = message.document.file_size or 0

            # Process the file
            file_data = await download_file_from_telegram(
                message.bot, file_id, folder, file_name
            )

            if file_data:
                file_data["file_size"] = file_size
                file_data["original_filename"] = file_name

            return file_data

        return None
    except Exception as e:
        logger.error(f"Error processing document file: {e}", exc_info=True)
        return None


async def get_file_path(
    file_source, bot: Bot = None, folder: str = FILE_UPLOADS_FOLDER
) -> str:
    """
    Gets a usable file path from various file sources (path, url, file_id).

    Args:
        file_source: File source (file_id, URL, or local path)
        bot: Bot instance (needed if file_source is a file_id to download)
        folder: Target folder if downloading is needed

    Returns:
        str: Local file path or None if file cannot be found/downloaded
    """
    if not file_source:
        return None

    # If it's already a local file path that exists
    if os.path.isfile(file_source):
        return file_source

    # Get the base directory for resolving paths
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # Try multiple path resolution strategies to find the local file
    possible_paths = []

    # If it's a path with known folders
    if isinstance(file_source, str):
        # 1. Direct path as provided
        possible_paths.append(file_source)

        # 2. Path relative to base directory
        possible_paths.append(os.path.join(base_dir, file_source))

        # 3. Handle paths with 'uploads/'
        if "uploads/" in file_source:
            if file_source.startswith("uploads/"):
                possible_paths.append(os.path.join(base_dir, file_source))
            else:
                path_parts = file_source.split("uploads/")
                if len(path_parts) > 1:
                    possible_paths.append(
                        os.path.join(base_dir, "uploads", path_parts[1])
                    )

        # 4. Try with uploads directory
        possible_paths.append(os.path.join(base_dir, "uploads", file_source))

        # 5. Try specific file folders
        filename = os.path.basename(file_source)
        possible_paths.append(
            os.path.join(base_dir, "uploads", FILE_UPLOADS_FOLDER, filename)
        )

        # Check all possible paths
        for path in possible_paths:
            if os.path.isfile(path):
                logger.debug(f"Found local file at: {path}")
                return path

    # If it seems to be a file_id and we have a bot
    if (
        bot
        and isinstance(file_source, str)
        and not file_source.startswith(("/", "http://", "https://"))
    ):
        # Download the file
        try:
            file_data = await download_file_from_telegram(bot, file_source, folder)
            if file_data:
                local_path = file_data.get("local_path")
                if os.path.isfile(local_path):
                    return local_path
        except Exception as e:
            logger.error(f"Error getting file path: {e}", exc_info=True)

    # If all else fails, log warning and return None
    logger.warning(f"Could not resolve file path for: {file_source}")
    return None


def extract_file_from_product(product_data):
    """
    Extract file information from a product.

    Args:
        product_data: The product data dictionary

    Returns:
        str: File path or None if not found
    """
    if not product_data:
        return None

    # Check various possible field names for file data
    if product_data.get("file_path"):
        return product_data.get("file_path")

    if product_data.get("file_link"):
        return product_data.get("file_link")

    if product_data.get("file_url"):
        return product_data.get("file_url")

    if product_data.get("file") and isinstance(product_data.get("file"), dict):
        file_data = product_data.get("file")
        if file_data.get("path"):
            return file_data.get("path")
        if file_data.get("url"):
            return file_data.get("url")

    return None


async def get_product_file_path(file_link, bot: Bot = None) -> str:
    """
    Get actual file path from a product's file_link.
    This handles file_id: prefixes and looks for files in the uploads/files directory.

    Args:
        file_link: File link stored in the product
        bot: Bot instance for downloading if needed

    Returns:
        str: Path to the local file or None if not found/valid
    """
    if not file_link:
        return None

    # If it's a file_id format
    if file_link.startswith("file_id:"):
        file_id = file_link[len("file_id:") :]

        # First try to use the file metadata system to find the file
        from utils.file_metadata import resolve_file_path

        resolved_path = resolve_file_path(file_link)

        if resolved_path:
            # Convert the relative path to an absolute path
            base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            full_path = os.path.join(base_dir, "uploads", resolved_path)

            if os.path.isfile(full_path):
                logger.info(f"Found file using metadata system at: {full_path}")
                return full_path

        # If metadata lookup failed, try to find the file in the uploads/files directory
        base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        files_dir = os.path.join(base_dir, "uploads", FILE_UPLOADS_FOLDER)

        # Look for files that might contain the file_id in their name
        # This helps find files that were previously downloaded
        if os.path.exists(files_dir):
            for filename in os.listdir(files_dir):
                # If the file_id is part of the filename, it's likely the file we want
                if file_id[-8:] in filename:
                    file_path = os.path.join(files_dir, filename)
                    logger.info(
                        f"Found existing file for file_id {file_id[-8:]} at {file_path}"
                    )
                    return file_path

        # If not found locally, try to download it
        return await get_file_path(file_id, bot, FILE_UPLOADS_FOLDER)

    # If it's a URL, return as is
    if file_link.startswith(("http://", "https://")):
        return file_link

    # If it's text content, it doesn't have a file path
    if file_link.startswith("text:"):
        return None

    # If it's a direct path to the uploads/files directory
    if "uploads/files/" in file_link or file_link.startswith("files/"):
        # Try to resolve as a file path
        local_path = await get_file_path(file_link, bot, FILE_UPLOADS_FOLDER)
        if local_path:
            return local_path

    # For anything else, try to resolve as a file path
    return await get_file_path(file_link, bot, FILE_UPLOADS_FOLDER)
