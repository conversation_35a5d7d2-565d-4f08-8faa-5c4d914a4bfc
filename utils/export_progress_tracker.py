"""
Export Progress Tracker
Provides real-time progress tracking and user feedback for database export operations.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable, List
from enum import Enum
import json

from utils.monitoring_system import record_security_event

logger = logging.getLogger(__name__)

class ExportStatus(Enum):
    """Export operation status enumeration."""
    INITIALIZING = "initializing"
    VALIDATING = "validating"
    CONNECTING = "connecting"
    EXPORTING = "exporting"
    COMPRESSING = "compressing"
    FINALIZING = "finalizing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"

class ExportProgressTracker:
    """Tracks progress of database export operations."""
    
    def __init__(self):
        """Initialize the progress tracker."""
        self.active_exports = {}  # export_id -> progress_data
        self.progress_callbacks = {}  # export_id -> callback_function
        self.status_messages = {
            ExportStatus.INITIALIZING: "🔄 Initializing export process...",
            ExportStatus.VALIDATING: "🔍 Validating export parameters...",
            ExportStatus.CONNECTING: "🔗 Connecting to database...",
            ExportStatus.EXPORTING: "📦 Exporting database collections...",
            ExportStatus.COMPRESSING: "🗜️ Compressing export file...",
            ExportStatus.FINALIZING: "✅ Finalizing export...",
            ExportStatus.COMPLETED: "✅ Export completed successfully!",
            ExportStatus.FAILED: "❌ Export failed",
            ExportStatus.CANCELLED: "⏹️ Export cancelled"
        }
    
    def start_tracking(
        self, 
        export_id: str, 
        user_id: int, 
        username: str,
        total_collections: int = 0,
        callback_function: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Start tracking progress for an export operation.
        
        Args:
            export_id: Unique export identifier
            user_id: User ID performing export
            username: Username performing export
            total_collections: Total number of collections to export
            callback_function: Optional callback for progress updates
            
        Returns:
            Dict with initial progress data
        """
        try:
            progress_data = {
                "export_id": export_id,
                "user_id": user_id,
                "username": username,
                "status": ExportStatus.INITIALIZING,
                "progress_percentage": 0,
                "current_step": 1,
                "total_steps": 6,  # Standard export steps
                "collections_processed": 0,
                "total_collections": total_collections,
                "current_collection": None,
                "started_at": datetime.now(),
                "last_updated": datetime.now(),
                "estimated_completion": None,
                "error_message": None,
                "detailed_status": self.status_messages[ExportStatus.INITIALIZING],
                "performance_metrics": {
                    "collections_per_minute": 0,
                    "data_processed_mb": 0,
                    "average_collection_time": 0
                }
            }
            
            self.active_exports[export_id] = progress_data
            
            if callback_function:
                self.progress_callbacks[export_id] = callback_function
            
            # Record progress tracking start
            record_security_event("export_progress_tracking_started", {
                "export_id": export_id,
                "user_id": user_id,
                "username": username,
                "total_collections": total_collections
            })
            
            logger.info(f"Started progress tracking for export {export_id}")
            
            return progress_data.copy()
            
        except Exception as e:
            logger.error(f"Error starting progress tracking: {e}")
            return {"error": str(e)}
    
    def update_progress(
        self, 
        export_id: str, 
        status: ExportStatus = None,
        progress_percentage: int = None,
        current_step: int = None,
        collections_processed: int = None,
        current_collection: str = None,
        detailed_status: str = None,
        error_message: str = None,
        performance_data: Dict[str, Any] = None
    ) -> bool:
        """
        Update progress for an export operation.
        
        Args:
            export_id: Export identifier
            status: New export status
            progress_percentage: Progress percentage (0-100)
            current_step: Current step number
            collections_processed: Number of collections processed
            current_collection: Name of current collection being processed
            detailed_status: Detailed status message
            error_message: Error message if failed
            performance_data: Performance metrics
            
        Returns:
            True if update successful, False otherwise
        """
        try:
            if export_id not in self.active_exports:
                logger.warning(f"Attempted to update non-existent export progress: {export_id}")
                return False
            
            progress_data = self.active_exports[export_id]
            
            # Update provided fields
            if status is not None:
                progress_data["status"] = status
                if detailed_status is None:
                    progress_data["detailed_status"] = self.status_messages.get(status, str(status))
            
            if progress_percentage is not None:
                progress_data["progress_percentage"] = min(100, max(0, progress_percentage))
            
            if current_step is not None:
                progress_data["current_step"] = current_step
                # Auto-calculate progress if not provided
                if progress_percentage is None:
                    progress_data["progress_percentage"] = int((current_step / progress_data["total_steps"]) * 100)
            
            if collections_processed is not None:
                progress_data["collections_processed"] = collections_processed
                
                # Calculate collection-based progress if total is known
                if progress_data["total_collections"] > 0:
                    collection_progress = int((collections_processed / progress_data["total_collections"]) * 100)
                    # Use the higher of step-based or collection-based progress
                    progress_data["progress_percentage"] = max(progress_data["progress_percentage"], collection_progress)
            
            if current_collection is not None:
                progress_data["current_collection"] = current_collection
            
            if detailed_status is not None:
                progress_data["detailed_status"] = detailed_status
            
            if error_message is not None:
                progress_data["error_message"] = error_message
                progress_data["status"] = ExportStatus.FAILED
            
            # Update performance metrics
            if performance_data:
                progress_data["performance_metrics"].update(performance_data)
            
            # Update timestamps
            progress_data["last_updated"] = datetime.now()
            
            # Calculate estimated completion
            if (progress_data["progress_percentage"] > 0 and 
                progress_data["status"] not in [ExportStatus.COMPLETED, ExportStatus.FAILED, ExportStatus.CANCELLED]):
                
                elapsed_time = (datetime.now() - progress_data["started_at"]).total_seconds()
                if elapsed_time > 0:
                    estimated_total_time = (elapsed_time / progress_data["progress_percentage"]) * 100
                    estimated_remaining = estimated_total_time - elapsed_time
                    progress_data["estimated_completion"] = datetime.now() + timedelta(seconds=estimated_remaining)
            
            # Trigger callback if registered
            if export_id in self.progress_callbacks:
                try:
                    asyncio.create_task(self.progress_callbacks[export_id](progress_data.copy()))
                except Exception as callback_error:
                    logger.error(f"Error in progress callback: {callback_error}")
            
            logger.debug(f"Updated progress for export {export_id}: {progress_data['progress_percentage']}%")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating progress: {e}")
            return False
    
    def complete_export(self, export_id: str, success: bool = True, final_message: str = None) -> bool:
        """
        Mark an export as completed.
        
        Args:
            export_id: Export identifier
            success: Whether export completed successfully
            final_message: Final status message
            
        Returns:
            True if completion recorded successfully
        """
        try:
            if export_id not in self.active_exports:
                return False
            
            progress_data = self.active_exports[export_id]
            
            if success:
                progress_data["status"] = ExportStatus.COMPLETED
                progress_data["progress_percentage"] = 100
                progress_data["detailed_status"] = final_message or self.status_messages[ExportStatus.COMPLETED]
            else:
                progress_data["status"] = ExportStatus.FAILED
                progress_data["detailed_status"] = final_message or self.status_messages[ExportStatus.FAILED]
            
            progress_data["completed_at"] = datetime.now()
            progress_data["last_updated"] = datetime.now()
            
            # Calculate final performance metrics
            total_time = (progress_data["completed_at"] - progress_data["started_at"]).total_seconds()
            if progress_data["collections_processed"] > 0 and total_time > 0:
                progress_data["performance_metrics"]["collections_per_minute"] = (
                    progress_data["collections_processed"] / (total_time / 60)
                )
                progress_data["performance_metrics"]["average_collection_time"] = (
                    total_time / progress_data["collections_processed"]
                )
            
            # Record completion
            record_security_event("export_progress_completed", {
                "export_id": export_id,
                "user_id": progress_data["user_id"],
                "success": success,
                "total_time_seconds": total_time,
                "collections_processed": progress_data["collections_processed"],
                "final_status": progress_data["detailed_status"]
            })
            
            # Trigger final callback
            if export_id in self.progress_callbacks:
                try:
                    asyncio.create_task(self.progress_callbacks[export_id](progress_data.copy()))
                except Exception as callback_error:
                    logger.error(f"Error in final progress callback: {callback_error}")
            
            logger.info(f"Export {export_id} completed: success={success}, time={total_time:.2f}s")
            
            return True
            
        except Exception as e:
            logger.error(f"Error completing export progress: {e}")
            return False
    
    def get_progress(self, export_id: str) -> Optional[Dict[str, Any]]:
        """Get current progress for an export."""
        return self.active_exports.get(export_id, {}).copy() if export_id in self.active_exports else None
    
    def get_user_exports(self, user_id: int) -> List[Dict[str, Any]]:
        """Get all active exports for a user."""
        user_exports = []
        for export_id, progress_data in self.active_exports.items():
            if progress_data["user_id"] == user_id:
                user_exports.append(progress_data.copy())
        
        return sorted(user_exports, key=lambda x: x["started_at"], reverse=True)
    
    def cancel_export(self, export_id: str, reason: str = "User cancelled") -> bool:
        """Cancel an active export."""
        try:
            if export_id not in self.active_exports:
                return False
            
            progress_data = self.active_exports[export_id]
            progress_data["status"] = ExportStatus.CANCELLED
            progress_data["detailed_status"] = f"⏹️ Export cancelled: {reason}"
            progress_data["cancelled_at"] = datetime.now()
            progress_data["last_updated"] = datetime.now()
            
            # Record cancellation
            record_security_event("export_progress_cancelled", {
                "export_id": export_id,
                "user_id": progress_data["user_id"],
                "reason": reason
            })
            
            # Trigger callback
            if export_id in self.progress_callbacks:
                try:
                    asyncio.create_task(self.progress_callbacks[export_id](progress_data.copy()))
                except Exception as callback_error:
                    logger.error(f"Error in cancellation callback: {callback_error}")
            
            logger.info(f"Export {export_id} cancelled: {reason}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error cancelling export: {e}")
            return False
    
    def cleanup_completed_exports(self, max_age_hours: int = 24) -> int:
        """Clean up old completed exports."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            exports_to_remove = []
            
            for export_id, progress_data in self.active_exports.items():
                if (progress_data["status"] in [ExportStatus.COMPLETED, ExportStatus.FAILED, ExportStatus.CANCELLED] and
                    progress_data["last_updated"] < cutoff_time):
                    exports_to_remove.append(export_id)
            
            for export_id in exports_to_remove:
                del self.active_exports[export_id]
                if export_id in self.progress_callbacks:
                    del self.progress_callbacks[export_id]
            
            if exports_to_remove:
                logger.info(f"Cleaned up {len(exports_to_remove)} old export progress records")
            
            return len(exports_to_remove)
            
        except Exception as e:
            logger.error(f"Error cleaning up export progress: {e}")
            return 0
    
    def get_progress_statistics(self) -> Dict[str, Any]:
        """Get statistics about export progress tracking."""
        try:
            total_exports = len(self.active_exports)
            active_exports = len([e for e in self.active_exports.values() 
                                if e["status"] not in [ExportStatus.COMPLETED, ExportStatus.FAILED, ExportStatus.CANCELLED]])
            completed_exports = len([e for e in self.active_exports.values() if e["status"] == ExportStatus.COMPLETED])
            failed_exports = len([e for e in self.active_exports.values() if e["status"] == ExportStatus.FAILED])
            
            # Calculate average completion time for completed exports
            completed_times = []
            for progress_data in self.active_exports.values():
                if progress_data["status"] == ExportStatus.COMPLETED and "completed_at" in progress_data:
                    completion_time = (progress_data["completed_at"] - progress_data["started_at"]).total_seconds()
                    completed_times.append(completion_time)
            
            avg_completion_time = sum(completed_times) / len(completed_times) if completed_times else 0
            
            return {
                "total_tracked_exports": total_exports,
                "active_exports": active_exports,
                "completed_exports": completed_exports,
                "failed_exports": failed_exports,
                "success_rate": (completed_exports / total_exports * 100) if total_exports > 0 else 0,
                "average_completion_time_seconds": avg_completion_time,
                "average_completion_time_minutes": avg_completion_time / 60,
                "registered_callbacks": len(self.progress_callbacks)
            }
            
        except Exception as e:
            logger.error(f"Error generating progress statistics: {e}")
            return {"error": str(e)}
    
    def format_progress_message(self, export_id: str) -> str:
        """Format a user-friendly progress message."""
        try:
            progress_data = self.get_progress(export_id)
            if not progress_data:
                return "❓ Export progress not found"
            
            status_emoji = {
                ExportStatus.INITIALIZING: "🔄",
                ExportStatus.VALIDATING: "🔍",
                ExportStatus.CONNECTING: "🔗",
                ExportStatus.EXPORTING: "📦",
                ExportStatus.COMPRESSING: "🗜️",
                ExportStatus.FINALIZING: "✅",
                ExportStatus.COMPLETED: "✅",
                ExportStatus.FAILED: "❌",
                ExportStatus.CANCELLED: "⏹️"
            }
            
            emoji = status_emoji.get(progress_data["status"], "❓")
            
            message = f"{emoji} <b>{progress_data['detailed_status']}</b>\n\n"
            
            # Progress bar
            if progress_data["progress_percentage"] > 0:
                progress_bar = "█" * (progress_data["progress_percentage"] // 10)
                progress_bar += "░" * (10 - len(progress_bar))
                message += f"📊 <b>Progress:</b> {progress_bar} {progress_data['progress_percentage']}%\n"
            
            # Step information
            message += f"📋 <b>Step:</b> {progress_data['current_step']}/{progress_data['total_steps']}\n"
            
            # Collection information
            if progress_data["total_collections"] > 0:
                message += f"🗃️ <b>Collections:</b> {progress_data['collections_processed']}/{progress_data['total_collections']}\n"
            
            if progress_data["current_collection"]:
                message += f"📄 <b>Current:</b> {progress_data['current_collection']}\n"
            
            # Time information
            elapsed = datetime.now() - progress_data["started_at"]
            message += f"⏱️ <b>Elapsed:</b> {elapsed.total_seconds():.0f}s\n"
            
            if progress_data["estimated_completion"]:
                remaining = progress_data["estimated_completion"] - datetime.now()
                if remaining.total_seconds() > 0:
                    message += f"⏰ <b>ETA:</b> {remaining.total_seconds():.0f}s\n"
            
            # Error message if failed
            if progress_data["error_message"]:
                message += f"\n❌ <b>Error:</b> {progress_data['error_message']}\n"
            
            return message
            
        except Exception as e:
            logger.error(f"Error formatting progress message: {e}")
            return f"❌ Error formatting progress: {str(e)}"

# Global progress tracker instance
progress_tracker = ExportProgressTracker()

# Convenience functions
def start_export_tracking(export_id: str, user_id: int, username: str, total_collections: int = 0, callback: Callable = None) -> Dict[str, Any]:
    """Start tracking export progress."""
    return progress_tracker.start_tracking(export_id, user_id, username, total_collections, callback)

def update_export_progress(export_id: str, **kwargs) -> bool:
    """Update export progress."""
    return progress_tracker.update_progress(export_id, **kwargs)

def complete_export_tracking(export_id: str, success: bool = True, final_message: str = None) -> bool:
    """Complete export tracking."""
    return progress_tracker.complete_export(export_id, success, final_message)

def get_export_progress(export_id: str) -> Optional[Dict[str, Any]]:
    """Get export progress."""
    return progress_tracker.get_progress(export_id)

def format_export_progress_message(export_id: str) -> str:
    """Format progress message."""
    return progress_tracker.format_progress_message(export_id)
