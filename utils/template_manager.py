import json
import os
from pathlib import Path
from typing import Dict, Any, List
import logging
import time
import re

# Import the robust sanitizer
from .telegram_helpers import sanitize_html

# Get logger
logger = logging.getLogger(__name__)

# Global registry of template managers across the application
# This helps ensure all template caches can be cleared when updates occur
_template_managers = []

# Commonly used templates that should be preloaded
PRELOAD_TEMPLATES = [
    "user.json",
    "admin.json",
    "owner.json",
    "shop.json",
    "payment.json",
    "support.json",
]

# Commonly used format variables regex pattern (compiled once for efficiency)
FORMAT_VAR_PATTERN = re.compile(r"\{([^{}:]+)(?::[^{}]+)?\}")
FORMAT_SPEC_PATTERN = re.compile(r"\{([^{}:]+):\.(\d+)f\}")
FORMAT_EXPR_PATTERN = re.compile(r"\{([^{}]+:[^{}]+)\}")


def _clear_all_template_caches():
    """
    Clear template caches in all TemplateManager instances across the application.
    This function should be called after template updates to ensure changes propagate everywhere.
    """
    global _template_managers
    for manager in _template_managers:
        try:
            manager.clear_cache()
        except Exception as e:
            logger.error(f"Error clearing template cache: {e}")

    logger.info(f"Cleared {len(_template_managers)} template cache instances")


class TemplateManager:
    """
    Manages bot template files stored in the templates directory.
    Allows reading and writing template values with caching for better performance.
    """

    def __init__(
        self, templates_dir: str = None, cache_timeout: int = 300, preload: bool = True
    ):
        """
        Initialize the template manager with path to templates directory

        Args:
            templates_dir: Path to templates directory (optional)
            cache_timeout: Time in seconds to keep templates cached (default: 5 minutes)
            preload: Whether to preload common templates on initialization (default: True)
        """
        if templates_dir is None:
            # Default to a 'templates' folder in the bot's root directory
            base_dir = Path(__file__).parent.parent
            self.templates_dir = os.path.join(base_dir, "templates")
        else:
            self.templates_dir = templates_dir

        # Create templates directory if it doesn't exist
        os.makedirs(self.templates_dir, exist_ok=True)

        # Cache for template data
        self._template_cache = {}
        self._cache_timestamp = {}
        self._cache_timeout = cache_timeout

        # Track file modification times to avoid unnecessary disk access
        self._file_mtimes = {}

        # Cache for frequently accessed template keys
        self._key_cache = {}

        # Register this instance in the global registry
        global _template_managers
        _template_managers.append(self)

        # Preload common templates if enabled
        if preload:
            self._preload_templates()

    def get_template_files(self) -> List[str]:
        """Get a list of all template files"""
        try:
            return [
                f
                for f in os.listdir(self.templates_dir)
                if f.endswith(".json")
                and os.path.isfile(os.path.join(self.templates_dir, f))
            ]
        except Exception as e:
            logger.error(f"Error listing template files: {e}")
            return []

    def _preload_templates(self):
        """Preload commonly used templates to improve performance"""
        try:
            for template_file in PRELOAD_TEMPLATES:
                if os.path.exists(os.path.join(self.templates_dir, template_file)):
                    logger.debug(f"Preloading template: {template_file}")
                    self.get_template(template_file)
            logger.info(f"Preloaded {len(PRELOAD_TEMPLATES)} template files")
        except Exception as e:
            logger.error(f"Error preloading templates: {e}")

    def _is_cache_valid(self, filename: str) -> bool:
        """
        Check if cache for a template file is still valid
        Uses an optimized approach to minimize disk access
        """
        if filename not in self._cache_timestamp:
            return False

        # Check if cache has expired
        current_time = time.time()
        if current_time - self._cache_timestamp[filename] > self._cache_timeout:
            # Only check file modification if cache timeout has expired
            file_path = os.path.join(self.templates_dir, filename)
            if not os.path.exists(file_path):
                return False

            # Get current file modification time
            current_mtime = os.path.getmtime(file_path)

            # Compare with stored mtime (if available)
            stored_mtime = self._file_mtimes.get(filename, 0)

            if current_mtime > stored_mtime:
                # File has been modified, cache is invalid
                self._file_mtimes[filename] = current_mtime
                return False

            # File hasn't changed, update timestamp to extend cache life
            self._cache_timestamp[filename] = current_time
            return True

        # Cache hasn't expired yet
        return True

    def get_template(self, filename: str) -> Dict[str, Any]:
        """
        Read and return template data from a file with caching

        Args:
            filename: The template file name (with or without .json extension)

        Returns:
            Dictionary containing template data
        """
        try:
            # Normalize filename to ensure it has exactly one .json extension
            filename = os.path.splitext(filename)[0] + ".json"

            # Check if we have a valid cached version
            if filename in self._template_cache and self._is_cache_valid(filename):
                return self._template_cache[filename]

            # Read from file
            file_path = os.path.join(self.templates_dir, filename)
            if not os.path.exists(file_path):
                logger.warning(f"Template file not found: {filename}")
                return {}

            # Use a try-except block with a file size check to handle large files more efficiently
            file_size = os.path.getsize(file_path)
            if file_size > 1024 * 1024:  # If file is larger than 1MB
                logger.warning(
                    f"Large template file detected: {filename} ({file_size/1024:.1f}KB)"
                )

            with open(file_path, "r", encoding="utf-8") as f:
                template_data = json.load(f)

            # Update cache
            self._template_cache[filename] = template_data
            self._cache_timestamp[filename] = time.time()
            self._file_mtimes[filename] = os.path.getmtime(file_path)

            return template_data
        except json.JSONDecodeError:
            logger.error(f"Invalid JSON in template file: {filename}")
            return {}
        except Exception as e:
            logger.error(f"Error reading template file {filename}: {e}")
            return {}

    def save_template(self, filename: str, data: Dict[str, Any]) -> bool:
        """
        Save template data to a file and update cache

        Args:
            filename: The template file name (with or without .json extension)
            data: Dictionary containing template data

        Returns:
            True if successful, False otherwise
        """
        try:
            if not filename.endswith(".json"):
                filename += ".json"

            file_path = os.path.join(self.templates_dir, filename)

            # Create a backup before saving (for safety)
            if os.path.exists(file_path):
                backup_dir = os.path.join(self.templates_dir, "backups", "autosave")
                os.makedirs(backup_dir, exist_ok=True)
                backup_file = os.path.join(
                    backup_dir,
                    f"{os.path.splitext(filename)[0]}_{int(time.time())}.json",
                )
                try:
                    with open(file_path, "r", encoding="utf-8") as src:
                        with open(backup_file, "w", encoding="utf-8") as dst:
                            dst.write(src.read())
                except Exception as backup_err:
                    logger.warning(
                        f"Failed to create backup of {filename}: {backup_err}"
                    )

            # Save the new data
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, indent=4, ensure_ascii=False)

            # Update all caches
            self._template_cache[filename] = data
            self._cache_timestamp[filename] = time.time()
            self._file_mtimes[filename] = os.path.getmtime(file_path)

            # Clear any key-specific caches for this template
            keys_to_remove = [
                k for k in self._key_cache if k.startswith(f"{filename}:")
            ]
            for key in keys_to_remove:
                self._key_cache.pop(key, None)

            logger.info(f"Template file saved: {filename}")
            return True
        except Exception as e:
            logger.error(f"Error saving template file {filename}: {e}")
            return False

    def update_text(self, template_file: str, key: str, value: str) -> bool:
        """
        Update a specific text value in a template file, sanitizing HTML.
        This is an optimized version that only updates a single key without loading the entire template.

        Args:
            template_file: The template file name (with or without .json extension)
            key: The key to update
            value: The new value to set

        Returns:
            True if update successful, False otherwise
        """
        try:
            # Sanitize the HTML content before saving
            sanitized_value = sanitize_html(value)
            if sanitized_value != value:
                logger.info(
                    f"HTML content for {template_file}/{key} was sanitized before saving."
                )

            # Get normalized filename first (with .json extension)
            normalized_filename = (
                template_file
                if template_file.endswith(".json")
                else f"{template_file}.json"
            )

            # Get the template data (using cache if available)
            template_data = self.get_template(normalized_filename)

            # Check if the value is actually changing
            if key in template_data and template_data[key] == sanitized_value:
                logger.debug(f"Template value unchanged for {template_file}/{key}")
                return True

            # Update the value
            template_data[key] = sanitized_value  # Use the sanitized value

            # Clear specific key cache
            cache_key = f"{normalized_filename}:{key}"
            if cache_key in self._key_cache:
                del self._key_cache[cache_key]

            # Save the updated template
            return self.save_template(normalized_filename, template_data)
        except Exception as e:
            logger.error(f"Error updating template text {template_file}/{key}: {e}")
            return False

    def format_text(
        self, template_file: str, key: str, default: str = "", **kwargs
    ) -> str:
        """
        Get template text and optionally format it with provided variables.
        Combines get_text and formatting in a single function.
        This optimized version uses caching for frequently accessed template keys.

        Args:
            template_file: The template file name (with or without .json extension)
            key: The key to retrieve from the template
            default: Default text to return if key is not found
            **kwargs: Format parameters to apply to the template text

        Returns:
            Formatted template text, or default if key not found
        """
        # Normalize filename
        normalized_filename = (
            template_file
            if template_file.endswith(".json")
            else f"{template_file}.json"
        )

        # Check if we need to format or just return the template
        if not kwargs:
            # For simple template retrieval without formatting, use key-specific cache
            cache_key = f"{normalized_filename}:{key}"
            if cache_key in self._key_cache:
                return self._key_cache[cache_key]

            # Get template data
            template_data = self.get_template(normalized_filename)
            template = template_data.get(key, default)

            # Check if the template is a string (can be formatted)
            if not isinstance(template, str):
                logger.warning(
                    f"Template value for {template_file}/{key} is not a string and cannot be formatted. Type: {type(template)}"
                )
                # For non-string values, return a string representation instead of an error message
                # This allows viewing the content while preventing string operation errors
                if isinstance(template, list):
                    # For lists, return a formatted representation
                    return f"[List with {len(template)} items]"
                elif isinstance(template, dict):
                    # For dictionaries, return a formatted representation
                    return f"[Dictionary with {len(template)} keys]"
                else:
                    # For other types, convert to string
                    return f"[{type(template).__name__}: {str(template)}]"

            # Cache the result for future use
            self._key_cache[cache_key] = template
            return template

        # Get template data directly
        template_data = self.get_template(normalized_filename)
        template = template_data.get(key, default)

        # Check if the template is a string (can be formatted)
        if not isinstance(template, str):
            logger.warning(
                f"Template value for {template_file}/{key} is not a string and cannot be formatted. Type: {type(template)}"
            )
            # For non-string values, return a string representation instead of an error message
            # This allows viewing the content while preventing string operation errors
            if isinstance(template, list):
                # For lists, return a formatted representation
                return f"[List with {len(template)} items]"
            elif isinstance(template, dict):
                # For dictionaries, return a formatted representation
                return f"[Dictionary with {len(template)} keys]"
            else:
                # For other types, convert to string
                return f"[{type(template).__name__}: {str(template)}]"

        # Map common variable name mismatches before formatting
        # This helps when code uses a different name than what template expects
        common_mappings = {
            "product_price": "price",
            "product_name": "name",
            "user_name": "name",
        }

        # Create a new dictionary with both original and mapped keys
        mapped_kwargs = dict(kwargs)
        for template_key, code_key in common_mappings.items():
            # Only map if target key doesn't exist but source key does
            if template_key not in mapped_kwargs and code_key in mapped_kwargs:
                mapped_kwargs[template_key] = mapped_kwargs[code_key]

        # Pre-process common numeric fields that need formatting as floats
        numeric_fields = [
            "product_price",
            "price",
            "amount",
            "total",
            "balance",
            "total_amount",
            "current_balance",
            "amount_required",
            "amount_needed",
            "refund_amount",
            "new_balance",
        ]

        for field in numeric_fields:
            if field in mapped_kwargs and not isinstance(
                mapped_kwargs[field], (int, float)
            ):
                try:
                    mapped_kwargs[field] = float(mapped_kwargs[field])
                except (ValueError, TypeError):
                    # If conversion fails, just continue with the string value
                    pass

        try:
            return template.format(**mapped_kwargs)
        except KeyError as e:
            # Log the error with all the details
            logger.error(
                f"Missing key when formatting template {template_file}/{key}: {e}. "
                f"Available keys: {list(mapped_kwargs.keys())}"
            )

            # Instead of failing, return template with placeholders for missing keys
            # Find all format variables in the template using regex
            # Use the pre-compiled regex pattern for better performance
            format_vars = set()
            for match in FORMAT_VAR_PATTERN.finditer(template):
                var_name = match.group(1).strip()
                format_vars.add(var_name)

            missing_vars = [var for var in format_vars if var not in mapped_kwargs]

            # Create a dictionary with default values for missing keys
            defaults = {var: f"[missing:{var}]" for var in missing_vars}
            # Combine with provided kwargs, giving priority to actual provided values
            combined_kwargs = {**defaults, **mapped_kwargs}

            try:
                return template.format(**combined_kwargs)
            except ValueError as format_err:
                # Handle format specifier errors (like ':f' with strings)
                logger.error(
                    f"Format specifier error in template {template_file}/{key}: {format_err}"
                )

                # Try to convert numeric format specifiers
                # First, find all format expressions with numeric specifiers
                fixed_kwargs = dict(mapped_kwargs)  # Make a copy of the kwargs

                # Try to identify and convert values for any numeric format specifiers
                # Use the pre-compiled regex pattern for better performance
                format_specs = FORMAT_SPEC_PATTERN.findall(template)
                for var_name, _ in format_specs:
                    if var_name in fixed_kwargs:
                        try:
                            # Always try to convert to float for .f format specifiers
                            if not isinstance(fixed_kwargs[var_name], (int, float)):
                                fixed_kwargs[var_name] = float(fixed_kwargs[var_name])
                        except (ValueError, TypeError):
                            # If conversion fails, we'll need to remove the format specifier
                            pass

                try:
                    # Try again with the converted values
                    return template.format(**{**defaults, **fixed_kwargs})
                except Exception:
                    # If that still fails, try removing all format specifiers
                    safe_template = template

                    # Find all format expressions with specifiers
                    # Use the pre-compiled regex pattern for better performance
                    format_exprs_with_specifiers = FORMAT_EXPR_PATTERN.findall(template)

                    # Remove specifiers from each format expression
                    for expr in format_exprs_with_specifiers:
                        var_name = expr.split(":", 1)[0].strip()
                        safe_template = safe_template.replace(
                            f"{{{expr}}}", f"{{{var_name}}}"
                        )

                    try:
                        return safe_template.format(**combined_kwargs)
                    except Exception as safe_format_err:
                        logger.error(
                            f"Failed to format template even with safe formatting: {safe_format_err}"
                        )
                        return f"{template} (Error: Incompatible format specifiers)"
            except Exception as format_err:
                logger.error(
                    f"Failed to format template even with defaults: {format_err}"
                )
                # Return template with a clear indication that formatting failed
                return f"{template} (Error: Unable to format template)"
        except Exception as e:
            logger.error(f"Error formatting template {template_file}/{key}: {e}")
            return template

    def clear_cache(self):
        """
        Clear all template caches
        This improved version clears all caches including the key-specific cache
        """
        self._template_cache = {}
        self._cache_timestamp = {}
        self._file_mtimes = {}
        self._key_cache = {}
        logger.info("All template caches cleared")
