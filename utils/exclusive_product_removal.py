"""
Exclusive Product Removal Manager

Handles automatic removal of exclusive single-use digital products from all listings
after successful purchase and delivery completion.
"""

import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class ExclusiveProductRemovalManager:
    """
    Manages automatic removal of exclusive products from customer-facing interfaces
    after successful purchase and delivery.
    """

    def __init__(self):
        """Initialize the removal manager."""
        self.logger = logging.getLogger(__name__)

    async def remove_product_from_listings(
        self,
        product_id: Any,
        user_id: int,
        order_number: int,
        delivery_confirmed: bool = False
    ) -> Dict[str, Any]:
        """
        Remove exclusive product from all customer-facing listings after successful delivery.

        Args:
            product_id: Product ID to remove
            user_id: User who purchased the product
            order_number: Order number for audit logging
            delivery_confirmed: Whether delivery was confirmed successful

        Returns:
            Dict with removal operation results
        """
        try:
            # Only proceed if delivery was confirmed
            if not delivery_confirmed:
                return {
                    "success": False,
                    "error": "Product removal requires delivery confirmation"
                }

            # Verify the product is actually purchased and by the correct user
            verification_result = await self._verify_purchase_status(product_id, user_id)
            if not verification_result["valid"]:
                return {
                    "success": False,
                    "error": f"Purchase verification failed: {verification_result['error']}"
                }

            # Update product status to mark as sold and removed from listings
            update_result = await self._update_product_removal_status(
                product_id, user_id, order_number
            )
            if not update_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to update product status: {update_result['error']}"
                }

            # Log the removal operation for audit purposes
            await self._log_product_removal(product_id, user_id, order_number)

            # Clear any cached product data
            await self._clear_product_cache(product_id)

            return {
                "success": True,
                "product_id": product_id,
                "user_id": user_id,
                "order_number": order_number,
                "removal_timestamp": datetime.now(),
                "message": "Product successfully removed from customer listings"
            }

        except Exception as e:
            self.logger.error(f"Error removing exclusive product {product_id} from listings: {e}")
            return {
                "success": False,
                "error": f"Removal operation failed: {str(e)}"
            }

    async def _verify_purchase_status(self, product_id: Any, user_id: int) -> Dict[str, Any]:
        """
        Verify that the product is actually purchased by the specified user.

        Args:
            product_id: Product ID to verify
            user_id: User ID to verify

        Returns:
            Dict with verification results
        """
        try:
            from database.operations import get_product

            product = get_product(product_id)
            if not product:
                return {
                    "valid": False,
                    "error": "Product not found"
                }

            # Check if it's an exclusive product
            if not product.get("is_exclusive_single_use", False):
                return {
                    "valid": False,
                    "error": "Product is not an exclusive single-use product"
                }

            # Check if it's purchased
            if not product.get("is_purchased", False):
                return {
                    "valid": False,
                    "error": "Product is not marked as purchased"
                }

            # Check if purchased by the correct user
            if product.get("purchased_by_user_id") != user_id:
                return {
                    "valid": False,
                    "error": f"Product was purchased by different user: {product.get('purchased_by_user_id')}"
                }

            return {
                "valid": True,
                "product": product
            }

        except Exception as e:
            self.logger.error(f"Error verifying purchase status for product {product_id}: {e}")
            return {
                "valid": False,
                "error": f"Verification error: {str(e)}"
            }

    async def _update_product_removal_status(
        self,
        product_id: Any,
        user_id: int,
        order_number: int
    ) -> Dict[str, Any]:
        """
        Update product database record to mark as removed from listings.

        Args:
            product_id: Product ID to update
            user_id: User who purchased
            order_number: Order number

        Returns:
            Dict with update results
        """
        try:
            from database.operations import products_collection

            # Update product with removal status
            update_data = {
                "removed_from_listings": True,
                "removal_timestamp": datetime.now(),
                "delivery_confirmed": True,
                "final_buyer_user_id": user_id,
                "completion_order_number": order_number,
                "product_lifecycle_status": "sold_and_delivered"
            }

            # Perform atomic update
            update_result = products_collection.update_one(
                {
                    "_id": product_id,
                    "is_exclusive_single_use": True,
                    "is_purchased": True,
                    "purchased_by_user_id": user_id
                },
                {
                    "$set": update_data
                }
            )

            if update_result.modified_count > 0:
                self.logger.info(f"Updated product {product_id} removal status for user {user_id}")
                return {
                    "success": True,
                    "modified_count": update_result.modified_count
                }
            else:
                return {
                    "success": False,
                    "error": "No product records were updated"
                }

        except Exception as e:
            self.logger.error(f"Error updating product removal status for {product_id}: {e}")
            return {
                "success": False,
                "error": f"Database update error: {str(e)}"
            }

    async def _log_product_removal(self, product_id: Any, user_id: int, order_number: int):
        """
        Log the product removal operation for audit purposes.

        Args:
            product_id: Product ID that was removed
            user_id: User who purchased
            order_number: Order number
        """
        try:
            from database.operations import add_transaction

            # Log the removal operation
            transaction_data = {
                "operation_type": "exclusive_product_removal",
                "product_id": product_id,
                "user_id": user_id,
                "order_number": order_number,
                "removal_timestamp": datetime.now(),
                "reason": "automatic_removal_after_delivery"
            }

            add_transaction(
                user_id,
                "product_removal",
                0.0,  # No monetary value for removal operation
                **transaction_data
            )

            self.logger.info(f"Logged product removal for product {product_id}, user {user_id}, order {order_number}")

        except Exception as e:
            self.logger.error(f"Error logging product removal: {e}")
            # Don't raise exception - logging failure shouldn't stop removal

    async def _clear_product_cache(self, product_id: Any):
        """
        Clear any cached product data to ensure listings are updated.

        Args:
            product_id: Product ID to clear from cache
        """
        try:
            # Clear any application-level caches
            # This is a placeholder for future cache implementation
            self.logger.debug(f"Cleared cache for product {product_id}")

        except Exception as e:
            self.logger.error(f"Error clearing product cache for {product_id}: {e}")
            # Don't raise exception - cache clearing failure shouldn't stop removal

    async def rollback_product_removal(
        self,
        product_id: Any,
        user_id: int,
        reason: str = "delivery_failed"
    ) -> Dict[str, Any]:
        """
        Rollback product removal if delivery fails after purchase marking.

        Args:
            product_id: Product ID to restore
            user_id: User who attempted purchase
            reason: Reason for rollback

        Returns:
            Dict with rollback results
        """
        try:
            from database.operations import products_collection

            # Restore product to available status
            rollback_data = {
                "is_purchased": False,
                "purchased_by_user_id": None,
                "purchase_date": None,
                "removed_from_listings": False,
                "removal_timestamp": None,
                "delivery_confirmed": False,
                "rollback_reason": reason,
                "rollback_timestamp": datetime.now(),
                "product_lifecycle_status": "available"
            }

            # Perform atomic rollback
            update_result = products_collection.update_one(
                {
                    "_id": product_id,
                    "is_exclusive_single_use": True,
                    "purchased_by_user_id": user_id
                },
                {
                    "$set": rollback_data,
                    "$unset": {
                        "final_buyer_user_id": "",
                        "completion_order_number": ""
                    }
                }
            )

            if update_result.modified_count > 0:
                self.logger.info(f"Rolled back product {product_id} removal for user {user_id}, reason: {reason}")
                return {
                    "success": True,
                    "product_id": product_id,
                    "rollback_reason": reason
                }
            else:
                return {
                    "success": False,
                    "error": "No product records were rolled back"
                }

        except Exception as e:
            self.logger.error(f"Error rolling back product removal for {product_id}: {e}")
            return {
                "success": False,
                "error": f"Rollback error: {str(e)}"
            }


# Global instance
exclusive_product_removal_manager = ExclusiveProductRemovalManager()
