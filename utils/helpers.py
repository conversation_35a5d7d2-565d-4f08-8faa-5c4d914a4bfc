import random
import logging
import json
import sys
import os
from typing import Dict, Any, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)


def sanitize_file_path(file_path: str) -> Optional[str]:
    """
    Sanitize a file path to prevent path traversal attacks.

    Args:
        file_path: The file path to sanitize

    Returns:
        Sanitized file path or None if the path is invalid/dangerous
    """
    if not file_path or not isinstance(file_path, str):
        return None

    # Remove any null bytes
    file_path = file_path.replace('\0', '')

    # If it's a URL or file_id, return as is
    if file_path.startswith(('http://', 'https://', 'file_id:')):
        return file_path

    # Normalize the path to handle different separators
    file_path = os.path.normpath(file_path)

    # Check for dangerous path components
    dangerous_patterns = [
        '..',  # Parent directory traversal
        '~',   # Home directory
        '$',   # Environment variables
        '|',   # Command injection
        ';',   # Command injection
        '&',   # Command injection
        '`',   # Command injection
        '\n',  # Newline injection
        '\r',  # Carriage return injection
    ]

    for pattern in dangerous_patterns:
        if pattern in file_path:
            logger.warning(f"Dangerous pattern '{pattern}' found in path: {file_path}")
            return None

    # Check for absolute paths (except on Windows where drive letters are OK)
    if os.path.isabs(file_path):
        # On Windows, allow drive letters but not UNC paths
        if os.name == 'nt':
            if file_path.startswith('\\\\'):  # UNC path
                logger.warning(f"UNC path not allowed: {file_path}")
                return None
        else:
            # On Unix-like systems, don't allow absolute paths
            logger.warning(f"Absolute path not allowed: {file_path}")
            return None

    # Remove leading slashes and dots
    file_path = file_path.lstrip('./')
    file_path = file_path.lstrip('.\\')

    # Ensure the path doesn't start with a separator
    if file_path.startswith(('/', '\\')):
        file_path = file_path[1:]

    # Final check - ensure the path is not empty after sanitization
    if not file_path or file_path in ('.', '..'):
        return None

    return file_path


def is_path_safe(file_path: str, base_directory: str) -> bool:
    """
    Check if a file path is safe and within the allowed base directory.

    Args:
        file_path: The file path to check
        base_directory: The base directory that the path should be within

    Returns:
        True if the path is safe, False otherwise
    """
    try:
        # Get absolute paths
        abs_file_path = os.path.abspath(file_path)
        abs_base_dir = os.path.abspath(base_directory)

        # Check if the file path is within the base directory
        common_path = os.path.commonpath([abs_file_path, abs_base_dir])

        # The common path should be the base directory
        if common_path != abs_base_dir:
            logger.warning(f"Path {file_path} is outside base directory {base_directory}")
            return False

        # Additional check: ensure the resolved path starts with the base directory
        if not abs_file_path.startswith(abs_base_dir + os.sep) and abs_file_path != abs_base_dir:
            logger.warning(f"Path {file_path} does not start with base directory {base_directory}")
            return False

        return True

    except (ValueError, OSError) as e:
        logger.error(f"Error checking path safety for {file_path}: {e}")
        return False


def format_currency(amount: float) -> str:
    """Format a currency amount with 2 decimal places.

    Args:
        amount: The amount to format, can be float, int, or string representation of a number

    Returns:
        A formatted string with dollar sign and 2 decimal places
    """
    try:
        # Handle None values
        if amount is None:
            return "$0.00"

        # Try to convert to float first
        float_amount = float(amount)

        # Handle negative amounts with proper formatting
        if float_amount < 0:
            return f"-${abs(float_amount):.2f}"
        else:
            return f"${float_amount:.2f}"
    except (ValueError, TypeError) as e:
        # Log the error for debugging
        logger.warning(f"Currency formatting error for value '{amount}': {e}")
        # If conversion fails, return a default string
        return "$0.00"


def safe_format_datetime(date_obj, format_string: str = "%Y-%m-%d %H:%M", default: str = "Unknown") -> str:
    """
    Safely format a datetime object that might be a string, datetime, or None.

    Args:
        date_obj: The datetime object to format (can be datetime, string, or None)
        format_string: The strftime format string to use
        default: Default string to return if formatting fails

    Returns:
        Formatted datetime string or default value
    """
    if not date_obj:
        return default

    # If it's already a string, return it (assume it's already formatted)
    if isinstance(date_obj, str):
        return date_obj

    # If it's a datetime object, format it
    if hasattr(date_obj, 'strftime'):
        try:
            return date_obj.strftime(format_string)
        except (ValueError, TypeError) as e:
            logger.warning(f"Datetime formatting error for value '{date_obj}': {e}")
            return default

    # For any other type, try to convert to string
    try:
        return str(date_obj)
    except Exception as e:
        logger.warning(f"Failed to convert datetime object to string: {e}")
        return default


def get_admins() -> List[int]:
    """Get list of all admin user IDs from the database and config.

    Returns:
        A list of user IDs that have admin privileges
    """
    try:
        all_admins = set()  # Using a set to avoid duplicates

        # Add configured OWNER_ID and ADMIN_ID from config
        try:
            from config import OWNER_ID, ADMIN_ID

            if OWNER_ID and OWNER_ID > 0:
                all_admins.add(OWNER_ID)
                logger.info(f"Added OWNER_ID {OWNER_ID} from config")

            if ADMIN_ID and ADMIN_ID > 0 and ADMIN_ID != OWNER_ID:
                all_admins.add(ADMIN_ID)
                logger.info(f"Added ADMIN_ID {ADMIN_ID} from config")
        except ImportError as e:
            logger.warning(f"Could not import config: {e}")

        # Get admin data from database
        try:
            from database.operations import get_all_admins

            db_admins = get_all_admins()
            for admin in db_admins:
                user_id = admin.get("user_id")
                if user_id and user_id > 0:
                    all_admins.add(user_id)
                    logger.debug(f"Added admin {user_id} from database")
        except (ImportError, Exception) as e:
            logger.warning(f"Could not retrieve admins from database: {e}")

        admin_list = list(all_admins)
        logger.info(f"Total admins found: {len(admin_list)}")
        return admin_list
    except Exception as e:
        logger.error(f"Error getting admin list: {e}")
        return []


def is_maintenance_mode_for_user(user_id: int) -> bool:
    """Check if maintenance mode is active for this specific user.

    Args:
        user_id: The Telegram user ID to check

    Returns:
        bool: True if maintenance mode applies to this user (maintenance is on and user is not admin)
    """
    try:
        # Import maintenance mode settings
        from config import MAINTENANCE_MODE
        from database.operations import get_maintenance_mode

        # Check both environment variable and database setting
        env_maintenance_mode = MAINTENANCE_MODE
        db_maintenance_mode = get_maintenance_mode()

        # If maintenance mode is off in both places, return False immediately
        if not env_maintenance_mode and not db_maintenance_mode:
            return False

        # If maintenance mode is on in either place, check if user is admin
        admin_ids = get_admins()

        # Return True if user is NOT in admin list (maintenance applies to them)
        return user_id not in admin_ids
    except Exception as e:
        logger.error(f"Error checking maintenance mode for user {user_id}: {e}")
        # Default to False (no maintenance) in case of error
        return False


# Command-line interface for payment link creation
if __name__ == "__main__":
    # Import the function directly for the CLI
    from payments.payment_link import create_payment_link as create_link_impl

    # Get amount from command line arguments if provided
    amount = 100
    order_id = None

    if len(sys.argv) >= 2:
        try:
            amount = float(sys.argv[1])
        except ValueError:
            print(json.dumps({"status": "error", "message": "Invalid amount"}))
            sys.exit(1)

    if len(sys.argv) >= 3:
        order_id = sys.argv[2]

    # Create the payment link using the direct import
    result = create_link_impl(amount=amount, order_id=order_id)

    # Print the result as JSON for easy parsing by other scripts
    print(json.dumps(result))


def ensure_upload_directories():
    """Ensure all required upload directories exist."""
    base_dir = os.path.join(os.getcwd(), "uploads")
    directories = [
        base_dir,
        os.path.join(base_dir, "category_images"),
        os.path.join(base_dir, "product_images"),
        os.path.join(base_dir, "files"),
        os.path.join(base_dir, "user_uploads"),
        os.path.join(base_dir, "temp"),
        os.path.join(base_dir, ".metadata"),  # Add metadata directory
        os.path.join(base_dir, "welcome_images"),  # Add welcome images directory
    ]

    for directory in directories:
        if not os.path.exists(directory):
            try:
                os.makedirs(directory, exist_ok=True)
                logger.info(f"Created directory: {directory}")
            except Exception as e:
                logger.error(f"Failed to create directory {directory}: {e}")
                # Try to create with absolute permissions as fallback
                try:
                    os.makedirs(directory, mode=0o777, exist_ok=True)
                    logger.info(f"Created directory with full permissions: {directory}")
                except Exception as e2:
                    logger.error(
                        f"Critical failure creating directory {directory}: {e2}"
                    )


import uuid
from aiogram import Bot
from aiogram.types import InputFile


async def get_telegram_file_info(
    bot: Bot, file_id: str, folder: str, original_filename=None
):
    """
    Get information about a file stored on Telegram servers.
    Instead of downloading the file locally, this stores metadata about the file
    including its Telegram file_id, type, and folder categorization.

    Args:
        bot: Bot instance
        file_id: Telegram file_id
        folder: Target categorization (category_images, product_images, or files)
        original_filename: Original filename if available

    Returns:
        dict: File information including file_id, folder, filename, etc.
    """
    ensure_upload_directories()

    # Get file info from Telegram
    file_info = await bot.get_file(file_id)

    # Generate unique filename for reference purposes
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    unique_id = str(uuid.uuid4())[:8]

    # Use original filename if provided, otherwise create one
    if original_filename:
        # Extract extension from original filename
        _, ext = os.path.splitext(original_filename)
        filename = f"{timestamp}_{unique_id}{ext}"
    else:
        # Extract extension from file_path
        _, ext = os.path.splitext(file_info.file_path)
        filename = f"{timestamp}_{unique_id}{ext}"

    # Create file info dictionary
    file_data = {
        "file_id": file_id,
        "folder": folder,
        "filename": filename,
        "original_filename": original_filename,
        "timestamp": timestamp,
        "file_unique_id": file_info.file_unique_id,
        "file_size": file_info.file_size,
        "file_path": file_info.file_path,
    }

    # Store reference to the file in our directory structure
    # This is just for organization, the actual file stays on Telegram's servers
    reference_path = os.path.join("uploads", folder, f"{filename}.json")

    # Save the file metadata
    try:
        os.makedirs(os.path.dirname(reference_path), exist_ok=True)
        with open(reference_path, "w") as f:
            json.dump(file_data, f, indent=2)
    except Exception as e:
        logger.error(f"Error saving file metadata: {e}")

    return file_data


# For backward compatibility
async def download_telegram_file(
    bot: Bot, file_id: str, folder: str, original_filename=None
):
    """
    Legacy function that now uses the new file handling system.

    Args:
        bot: Bot instance
        file_id: Telegram file_id
        folder: Target folder (category_images, product_images, or files)
        original_filename: Original filename if available

    Returns:
        str: The file_id that can be used to reference the file
    """
    await get_telegram_file_info(bot, file_id, folder, original_filename)
    return file_id  # Return file_id as the reference


async def upload_to_telegram(bot: Bot, local_file_path: str, folder: str):
    """
    Upload a local file to Telegram and return the file_id.
    This is useful for migrating existing local files to Telegram storage.

    Args:
        bot: Bot instance
        local_file_path: Path to local file
        folder: Target categorization (category_images, product_images, or files)

    Returns:
        dict: File information including file_id and metadata
    """
    try:
        ensure_upload_directories()

        # Check if file exists
        if not os.path.exists(local_file_path):
            logger.error(f"File not found: {local_file_path}")
            return None

        # Determine type based on extension
        file_ext = os.path.splitext(local_file_path)[1].lower()
        original_filename = os.path.basename(local_file_path)

        # Upload to Telegram based on file type
        input_file = InputFile(local_file_path)

        if file_ext in [".jpg", ".jpeg", ".png", ".gif", ".webp"]:
            # Upload as photo
            message = await bot.send_photo(
                chat_id=bot.id,  # Send to the bot itself as storage
                photo=input_file,
                caption=f"Folder: {folder}",
            )
            file_id = message.photo[-1].file_id

        elif file_ext in [".mp4", ".avi", ".mov", ".mkv"]:
            # Upload as video
            message = await bot.send_video(
                chat_id=bot.id, video=input_file, caption=f"Folder: {folder}"
            )
            file_id = message.video.file_id

        elif file_ext in [".mp3", ".ogg", ".wav"]:
            # Upload as audio
            message = await bot.send_audio(
                chat_id=bot.id, audio=input_file, caption=f"Folder: {folder}"
            )
            file_id = message.audio.file_id

        else:
            # Upload as document (any file type)
            message = await bot.send_document(
                chat_id=bot.id, document=input_file, caption=f"Folder: {folder}"
            )
            file_id = message.document.file_id

        # Now store the file info using our existing function
        return await get_telegram_file_info(bot, file_id, folder, original_filename)

    except Exception as e:
        logger.error(f"Error uploading file to Telegram: {e}")
        return None


async def get_file_by_id(bot: Bot, file_id: str):
    """
    Get file information using a Telegram file_id.

    Args:
        bot: Bot instance
        file_id: Telegram file_id

    Returns:
        file_info: File information from Telegram
    """
    try:
        # Get file info from Telegram
        file_info = await bot.get_file(file_id)
        return file_info
    except Exception as e:
        logger.error(f"Error getting file by ID: {e}")
        return None


async def migrate_local_files_to_telegram(bot: Bot):
    """
    Migrate existing local files to Telegram storage.
    This function scans the uploads directory and uploads all files to Telegram,
    creating appropriate metadata records.

    Args:
        bot: Bot instance

    Returns:
        dict: Statistics about the migration
    """
    stats = {"total_files": 0, "successful": 0, "failed": 0, "by_folder": {}}

    ensure_upload_directories()

    # Get base directory
    base_dir = os.path.join(os.getcwd(), "uploads")

    # Process each target folder
    folders = ["category_images", "product_images", "files"]

    for folder in folders:
        folder_path = os.path.join(base_dir, folder)
        stats["by_folder"][folder] = {"total": 0, "success": 0, "failed": 0}

        if not os.path.exists(folder_path):
            continue

        # Process files in folder
        for file in os.listdir(folder_path):
            # Skip metadata JSON files and hidden files
            if file.endswith(".json") or file.startswith("."):
                continue

            file_path = os.path.join(folder_path, file)

            # Skip directories
            if os.path.isdir(file_path):
                continue

            stats["total_files"] += 1
            stats["by_folder"][folder]["total"] += 1

            try:
                # Upload to Telegram
                result = await upload_to_telegram(bot, file_path, folder)

                if result:
                    stats["successful"] += 1
                    stats["by_folder"][folder]["success"] += 1
                    logger.info(f"Successfully migrated {file_path} to Telegram")
                else:
                    stats["failed"] += 1
                    stats["by_folder"][folder]["failed"] += 1
                    logger.error(f"Failed to migrate {file_path}")
            except Exception as e:
                stats["failed"] += 1
                stats["by_folder"][folder]["failed"] += 1
                logger.error(f"Error migrating {file_path}: {e}")

    return stats


def resolve_image_path(image_source):
    """
    Resolves an image path to ensure it can be properly accessed.
    Works with local paths, uploads directory, and file_ids.
    Returns the appropriate path or object for use with send_photo.
    """
    if not image_source:
        return None

    # If it's already a file object or InputFile, return as is
    if not isinstance(image_source, str):
        return image_source

    # Clean the path
    image_source = image_source.strip()

    # Check if it's a URL
    if image_source.startswith(("http://", "https://")):
        # Validate and encode URL
        try:
            from urllib.parse import urlparse, urlunparse, quote

            # Parse the URL
            parsed_url = urlparse(image_source)

            # Encode the path component
            encoded_path = quote(parsed_url.path, safe="/")

            # Encode the query component
            encoded_query = quote(parsed_url.query, safe="=&?")

            # Reconstruct the URL
            encoded_url = urlunparse(
                (
                    parsed_url.scheme,
                    parsed_url.netloc,
                    encoded_path,
                    parsed_url.params,
                    encoded_query,
                    parsed_url.fragment,
                )
            )

            return encoded_url
        except Exception as e:
            logger.warning(f"URL encoding error: {e}, returning original URL")
            return image_source

    # Sanitize and validate the image source path to prevent path traversal attacks
    sanitized_source = sanitize_file_path(image_source)
    if not sanitized_source:
        logger.warning(f"Invalid or potentially dangerous image source: {image_source}")
        return image_source  # Return original if it might be a file_id

    # Check if it's a relative path to the uploads directory
    uploads_dir = os.path.join(os.getcwd(), "uploads")

    # Ensure uploads directory exists and get its absolute path
    try:
        uploads_dir = os.path.abspath(uploads_dir)
        if not os.path.exists(uploads_dir):
            os.makedirs(uploads_dir, exist_ok=True)
    except Exception as e:
        logger.error(f"Error accessing uploads directory: {e}")
        return image_source

    # Try different path combinations with proper validation
    potential_paths = []

    # Only add paths that are safe and within the uploads directory
    try:
        # 1. Original path (if it's safe)
        if not os.path.isabs(sanitized_source):
            potential_paths.append(sanitized_source)

        # 2. As upload path
        upload_path = os.path.join(uploads_dir, sanitized_source)
        upload_path = os.path.abspath(upload_path)
        if is_path_safe(upload_path, uploads_dir):
            potential_paths.append(upload_path)

        # 3. Just filename in uploads directory
        filename = os.path.basename(sanitized_source)
        if filename and filename != '.' and filename != '..':
            filename_path = os.path.join(uploads_dir, filename)
            filename_path = os.path.abspath(filename_path)
            if is_path_safe(filename_path, uploads_dir):
                potential_paths.append(filename_path)

    except Exception as e:
        logger.error(f"Error building potential paths for {image_source}: {e}")
        return image_source

    # Try all paths and return the first one that exists
    for path in potential_paths:
        try:
            if os.path.exists(path) and os.path.isfile(path):
                logger.debug(f"Found image file at: {path}")
                return path
        except Exception as e:
            logger.warning(f"Error checking path {path}: {e}")
            continue

    # If we get here, assume it's a telegram file_id
    logger.debug(f"No local file found for {image_source}, treating as file_id")
    return image_source
