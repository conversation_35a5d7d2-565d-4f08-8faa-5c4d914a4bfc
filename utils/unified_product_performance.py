"""
Unified performance optimization and caching utilities for all product types.
Provides memory-efficient caching and query optimization for exclusive, line-based, and regular products.
Maintains backward compatibility with existing exclusive product functionality.
"""

import logging
import time
import re
from typing import Dict, Any, Optional, List, Union, Literal
from datetime import datetime, timezone
from enum import Enum

# Conditional BSON import
try:
    from bson import ObjectId
    BSON_AVAILABLE = True
except ImportError:
    # Define dummy ObjectId class if BSON is not available
    class ObjectId:
        @staticmethod
        def is_valid(oid):
            return False
    BSON_AVAILABLE = False

from database.operations import (
    get_product,
    get_products,
    add_transaction
)

logger = logging.getLogger(__name__)


class ProductType(Enum):
    """Enumeration of product types for monitoring and caching."""
    EXCLUSIVE = "exclusive"
    LINE_BASED = "line_based"
    REGULAR = "regular"
    UNKNOWN = "unknown"


class OperationType(Enum):
    """Enumeration of operation types for performance monitoring."""
    # Exclusive product operations
    EXCLUSIVE_PURCHASE = "exclusive_purchase"
    EXCLUSIVE_DELIVERY = "exclusive_delivery"
    EXCLUSIVE_VALIDATION = "exclusive_validation"
    EXCLUSIVE_REMOVAL = "exclusive_removal"
    
    # Line-based product operations
    LINE_INVENTORY_UPDATE = "line_inventory_update"
    LINE_RESERVATION = "line_reservation"
    LINE_PURCHASE_CONFIRM = "line_purchase_confirm"
    LINE_RELEASE = "line_release"
    LINE_VALIDATION = "line_validation"
    
    # Regular product operations
    REGULAR_PURCHASE = "regular_purchase"
    REGULAR_DELIVERY = "regular_delivery"
    REGULAR_VALIDATION = "regular_validation"
    REGULAR_INVENTORY_CHECK = "regular_inventory_check"
    
    # General operations
    PRODUCT_LOOKUP = "product_lookup"
    CACHE_OPERATION = "cache_operation"
    DATABASE_QUERY = "database_query"


class UnifiedProductCache:
    """Memory-efficient caching system for all product types with leak prevention."""
    
    def __init__(self, cache_ttl_seconds: int = 300, max_tracked_products: int = 10000):
        """Initialize the unified cache system with bounded storage."""
        self.cache_ttl = cache_ttl_seconds
        self.max_tracked_products = max_tracked_products
        
        # Product caches by type
        self._product_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_timestamps: Dict[str, datetime] = {}
        
        # Type-specific tracking sets
        self._delivered_products: Dict[ProductType, set] = {
            ProductType.EXCLUSIVE: set(),
            ProductType.LINE_BASED: set(),
            ProductType.REGULAR: set()
        }
        self._removed_products: Dict[ProductType, set] = {
            ProductType.EXCLUSIVE: set(),
            ProductType.LINE_BASED: set(),
            ProductType.REGULAR: set()
        }
        
        # Cache hit/miss tracking
        self._cache_hits: Dict[ProductType, int] = {pt: 0 for pt in ProductType}
        self._cache_misses: Dict[ProductType, int] = {pt: 0 for pt in ProductType}
        
        # Cleanup tracking
        self._last_cleanup = datetime.now()
        self._last_deep_cleanup = datetime.now()
    
    def _determine_product_type(self, product_data: Dict[str, Any]) -> ProductType:
        """Determine the product type from product data."""
        if product_data.get("is_exclusive_single_use", False):
            return ProductType.EXCLUSIVE
        elif product_data.get("is_line_based", False):
            return ProductType.LINE_BASED
        else:
            return ProductType.REGULAR
    
    def _get_cache_key(self, product_id: str, product_type: ProductType) -> str:
        """Generate cache key for product."""
        return f"{product_type.value}_{product_id}"
    
    def _is_cache_valid(self, key: str) -> bool:
        """Check if cache entry is still valid."""
        if key not in self._cache_timestamps:
            return False
        
        age = datetime.now() - self._cache_timestamps[key]
        return age.total_seconds() < self.cache_ttl
    
    def get_product(self, product_id: str, product_type: Optional[ProductType] = None) -> Optional[Dict[str, Any]]:
        """Get product from cache if available and valid."""
        self._cleanup_expired_cache()
        
        # If product type not specified, try all types
        if product_type is None:
            for pt in ProductType:
                if pt == ProductType.UNKNOWN:
                    continue
                cache_key = self._get_cache_key(str(product_id), pt)
                if self._is_cache_valid(cache_key):
                    self._cache_hits[pt] += 1
                    return self._product_cache.get(cache_key)
            
            # Cache miss for all types
            for pt in ProductType:
                if pt != ProductType.UNKNOWN:
                    self._cache_misses[pt] += 1
            return None
        
        cache_key = self._get_cache_key(str(product_id), product_type)
        if self._is_cache_valid(cache_key):
            self._cache_hits[product_type] += 1
            return self._product_cache.get(cache_key)
        
        self._cache_misses[product_type] += 1
        return None
    
    def cache_product(self, product_id: str, product_data: Dict[str, Any], product_type: Optional[ProductType] = None):
        """Cache product data with automatic type detection."""
        if product_type is None:
            product_type = self._determine_product_type(product_data)
        
        cache_key = self._get_cache_key(str(product_id), product_type)
        self._product_cache[cache_key] = product_data.copy()
        self._cache_timestamps[cache_key] = datetime.now()
    
    def invalidate_product(self, product_id: str, product_type: Optional[ProductType] = None):
        """Invalidate cached product data."""
        if product_type is None:
            # Invalidate for all types
            for pt in ProductType:
                if pt == ProductType.UNKNOWN:
                    continue
                cache_key = self._get_cache_key(str(product_id), pt)
                self._product_cache.pop(cache_key, None)
                self._cache_timestamps.pop(cache_key, None)
        else:
            cache_key = self._get_cache_key(str(product_id), product_type)
            self._product_cache.pop(cache_key, None)
            self._cache_timestamps.pop(cache_key, None)
    
    def mark_product_delivered(self, product_id: str, product_type: ProductType):
        """Mark product as delivered for fast lookup."""
        self._delivered_products[product_type].add(str(product_id))
        self.invalidate_product(product_id, product_type)
    
    def mark_product_removed(self, product_id: str, product_type: ProductType):
        """Mark product as removed for fast lookup."""
        self._removed_products[product_type].add(str(product_id))
        self.invalidate_product(product_id, product_type)
    
    def is_product_delivered(self, product_id: str, product_type: ProductType) -> bool:
        """Quick check if product is delivered."""
        return str(product_id) in self._delivered_products[product_type]
    
    def is_product_removed(self, product_id: str, product_type: ProductType) -> bool:
        """Quick check if product is removed."""
        return str(product_id) in self._removed_products[product_type]
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        self._cleanup_expired_cache()
        
        total_delivered = sum(len(s) for s in self._delivered_products.values())
        total_removed = sum(len(s) for s in self._removed_products.values())
        total_hits = sum(self._cache_hits.values())
        total_misses = sum(self._cache_misses.values())
        
        hit_rate = (total_hits / (total_hits + total_misses) * 100) if (total_hits + total_misses) > 0 else 0
        
        return {
            "cached_products": len(self._product_cache),
            "cache_ttl_seconds": self.cache_ttl,
            "max_tracked_products": self.max_tracked_products,
            "last_cleanup": self._last_cleanup.isoformat(),
            "last_deep_cleanup": self._last_deep_cleanup.isoformat(),
            "hit_rate_percent": hit_rate,
            "performance": {
                "total_hits": total_hits,
                "total_misses": total_misses,
                "hits_by_type": {pt.value: hits for pt, hits in self._cache_hits.items()},
                "misses_by_type": {pt.value: misses for pt, misses in self._cache_misses.items()}
            },
            "tracking": {
                "total_delivered": total_delivered,
                "total_removed": total_removed,
                "delivered_by_type": {pt.value: len(s) for pt, s in self._delivered_products.items()},
                "removed_by_type": {pt.value: len(s) for pt, s in self._removed_products.items()}
            },
            "memory_usage": {
                "cache_entries": len(self._product_cache),
                "timestamp_entries": len(self._cache_timestamps),
                "total_tracking_objects": total_delivered + total_removed,
                "total_memory_objects": len(self._product_cache) + len(self._cache_timestamps) + total_delivered + total_removed
            }
        }
    
    def _cleanup_expired_cache(self):
        """Remove expired cache entries and prevent memory leaks."""
        now = datetime.now()
        
        # Only cleanup every minute to avoid overhead
        if (now - self._last_cleanup).total_seconds() < 60:
            return
        
        # Clean up expired cache entries
        expired_keys = []
        for key, timestamp in self._cache_timestamps.items():
            if (now - timestamp).total_seconds() >= self.cache_ttl:
                expired_keys.append(key)
        
        for key in expired_keys:
            self._product_cache.pop(key, None)
            self._cache_timestamps.pop(key, None)
        
        self._last_cleanup = now
        
        # Perform deep cleanup every hour to prevent unbounded growth
        if (now - self._last_deep_cleanup).total_seconds() >= 3600:  # 1 hour
            self._deep_cleanup()
            self._last_deep_cleanup = now
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def _deep_cleanup(self):
        """Perform deep cleanup to prevent memory leaks in tracking sets."""
        total_delivered = sum(len(s) for s in self._delivered_products.values())
        total_removed = sum(len(s) for s in self._removed_products.values())

        # If total tracking exceeds limits, perform aggressive cleanup
        if total_delivered > self.max_tracked_products or total_removed > self.max_tracked_products:
            # Calculate per-type limit
            per_type_limit = self.max_tracked_products // len([pt for pt in ProductType if pt != ProductType.UNKNOWN])

            for product_type in ProductType:
                if product_type == ProductType.UNKNOWN:
                    continue

                delivered_set = self._delivered_products[product_type]
                removed_set = self._removed_products[product_type]

                initial_delivered = len(delivered_set)
                initial_removed = len(removed_set)

                # Aggressive cleanup - keep only a fraction of the limit
                if len(delivered_set) > per_type_limit:
                    delivered_list = list(delivered_set)
                    self._delivered_products[product_type] = set(delivered_list[-per_type_limit//2:])

                if len(removed_set) > per_type_limit:
                    removed_list = list(removed_set)
                    self._removed_products[product_type] = set(removed_list[-per_type_limit//2:])

                cleaned_delivered = initial_delivered - len(self._delivered_products[product_type])
                cleaned_removed = initial_removed - len(self._removed_products[product_type])

                if cleaned_delivered > 0 or cleaned_removed > 0:
                    logger.info(f"Deep cleanup for {product_type.value} - removed {cleaned_delivered} delivered, {cleaned_removed} removed entries")
    
    def clear_all(self):
        """Clear all cache data."""
        self._product_cache.clear()
        self._cache_timestamps.clear()
        for product_type in ProductType:
            if product_type != ProductType.UNKNOWN:
                self._delivered_products[product_type].clear()
                self._removed_products[product_type].clear()
                self._cache_hits[product_type] = 0
                self._cache_misses[product_type] = 0
        logger.info("Cleared all unified product cache data")
    
    def force_deep_cleanup(self):
        """Force immediate deep cleanup of all cache data."""
        logger.info("Forcing unified cache deep cleanup")
        initial_stats = self.get_cache_stats()
        
        # Force cleanup of expired entries
        self._cleanup_expired_cache()
        
        # Force deep cleanup regardless of time
        self._deep_cleanup()
        
        final_stats = self.get_cache_stats()
        logger.info(f"Forced deep cleanup completed - reduced memory objects from {initial_stats['memory_usage']['total_memory_objects']} to {final_stats['memory_usage']['total_memory_objects']}")


# Backward compatibility: Create aliases for existing exclusive product cache
class ExclusiveProductCache(UnifiedProductCache):
    """Backward compatibility wrapper for exclusive product cache."""
    
    def __init__(self, cache_ttl_seconds: int = 300, max_tracked_products: int = 10000):
        super().__init__(cache_ttl_seconds, max_tracked_products)
    
    def get_product(self, product_id: str) -> Optional[Dict[str, Any]]:
        """Get exclusive product from cache (backward compatibility)."""
        return super().get_product(product_id, ProductType.EXCLUSIVE)
    
    def cache_product(self, product_id: str, product_data: Dict[str, Any]):
        """Cache exclusive product data (backward compatibility)."""
        super().cache_product(product_id, product_data, ProductType.EXCLUSIVE)
    
    def invalidate_product(self, product_id: str):
        """Invalidate exclusive product cache (backward compatibility)."""
        super().invalidate_product(product_id, ProductType.EXCLUSIVE)
    
    def mark_product_delivered(self, product_id: str):
        """Mark exclusive product as delivered (backward compatibility)."""
        super().mark_product_delivered(product_id, ProductType.EXCLUSIVE)
    
    def mark_product_removed(self, product_id: str):
        """Mark exclusive product as removed (backward compatibility)."""
        super().mark_product_removed(product_id, ProductType.EXCLUSIVE)
    
    def is_product_delivered(self, product_id: str) -> bool:
        """Check if exclusive product is delivered (backward compatibility)."""
        return super().is_product_delivered(product_id, ProductType.EXCLUSIVE)
    
    def is_product_removed(self, product_id: str) -> bool:
        """Check if exclusive product is removed (backward compatibility)."""
        return super().is_product_removed(product_id, ProductType.EXCLUSIVE)


class UnifiedProductPerformanceMonitor:
    """Performance monitoring for all product types with memory leak prevention."""

    def __init__(self, max_metrics: int = 1000, max_operations: int = 100):
        """Initialize the unified performance monitor with bounded storage."""
        self.max_metrics = max_metrics
        self.max_operations = max_operations
        self.start_time = datetime.now()
        self.last_cleanup = datetime.now()
        self._cleanup_interval = 300  # 5 minutes

        # Performance metrics by product type and operation type
        self.query_times: Dict[ProductType, List[float]] = {pt: [] for pt in ProductType}
        self.operation_counts: Dict[ProductType, Dict[str, int]] = {pt: {} for pt in ProductType}
        self.error_counts: Dict[ProductType, Dict[str, int]] = {pt: {} for pt in ProductType}

        # Operation type mapping for categorization
        self.operation_type_mapping = {
            # Exclusive operations
            "mark_exclusive_product_as_purchased": OperationType.EXCLUSIVE_PURCHASE,
            "deliver_exclusive_product": OperationType.EXCLUSIVE_DELIVERY,
            "validate_exclusive_product": OperationType.EXCLUSIVE_VALIDATION,
            "remove_exclusive_product": OperationType.EXCLUSIVE_REMOVAL,

            # Line-based operations
            "update_product_inventory": OperationType.LINE_INVENTORY_UPDATE,
            "reserve_inventory_lines": OperationType.LINE_RESERVATION,
            "confirm_line_purchase": OperationType.LINE_PURCHASE_CONFIRM,
            "release_reserved_lines": OperationType.LINE_RELEASE,
            "validate_line_product": OperationType.LINE_VALIDATION,

            # Regular operations
            "process_regular_purchase": OperationType.REGULAR_PURCHASE,
            "deliver_regular_product": OperationType.REGULAR_DELIVERY,
            "validate_regular_product": OperationType.REGULAR_VALIDATION,
            "check_regular_inventory": OperationType.REGULAR_INVENTORY_CHECK,

            # General operations
            "get_product": OperationType.PRODUCT_LOOKUP,
            "cache_operation": OperationType.CACHE_OPERATION,
            "database_query": OperationType.DATABASE_QUERY,
        }

    def _determine_product_type_from_operation(self, operation: str) -> ProductType:
        """Determine product type from operation name."""
        operation_type = self.operation_type_mapping.get(operation)
        if not operation_type:
            return ProductType.UNKNOWN

        if operation_type.value.startswith("exclusive"):
            return ProductType.EXCLUSIVE
        elif operation_type.value.startswith("line"):
            return ProductType.LINE_BASED
        elif operation_type.value.startswith("regular"):
            return ProductType.REGULAR
        else:
            return ProductType.UNKNOWN

    def _cleanup_old_metrics(self):
        """Enhanced cleanup of old metrics to prevent memory leaks."""
        now = datetime.now()

        # Only cleanup every 5 minutes to avoid overhead
        if (now - self.last_cleanup).total_seconds() < self._cleanup_interval:
            return

        for product_type in ProductType:
            # More aggressive cleanup for query times
            if len(self.query_times[product_type]) > self.max_metrics:
                keep_count = max(self.max_metrics // 2, 100)  # Keep at least 100, but reduce by half
                self.query_times[product_type] = self.query_times[product_type][-keep_count:]

            # Limit operation counts - keep only top operations by count
            if len(self.operation_counts[product_type]) > self.max_operations:
                sorted_ops = sorted(self.operation_counts[product_type].items(), key=lambda x: x[1], reverse=True)
                keep_count = max(self.max_operations // 2, 20)  # Keep at least 20 operations
                self.operation_counts[product_type] = dict(sorted_ops[:keep_count])

            # Limit error counts - keep only top errors by count
            if len(self.error_counts[product_type]) > self.max_operations:
                sorted_errors = sorted(self.error_counts[product_type].items(), key=lambda x: x[1], reverse=True)
                keep_count = max(self.max_operations // 2, 10)  # Keep at least 10 error types
                self.error_counts[product_type] = dict(sorted_errors[:keep_count])

        # Force garbage collection if memory usage is high
        import gc
        total_metrics = sum(len(self.query_times[pt]) for pt in ProductType)
        total_operations = sum(len(self.operation_counts[pt]) for pt in ProductType)
        total_errors = sum(len(self.error_counts[pt]) for pt in ProductType)

        if total_metrics + total_operations + total_errors > self.max_metrics * 2:
            gc.collect()
            logger.debug(f"Forced garbage collection due to high memory usage")

        self.last_cleanup = now
        logger.debug(f"Performance monitor cleanup completed for all product types")

    def record_query_time(self, operation: str, duration: float, product_type: Optional[ProductType] = None):
        """Record query execution time with automatic cleanup."""
        self._cleanup_old_metrics()

        if product_type is None:
            product_type = self._determine_product_type_from_operation(operation)

        self.query_times[product_type].append(duration)
        if operation not in self.operation_counts[product_type]:
            self.operation_counts[product_type][operation] = 0
        self.operation_counts[product_type][operation] += 1

        # Immediate cleanup if we exceed limits significantly
        if len(self.query_times[product_type]) > self.max_metrics * 1.2:
            self.query_times[product_type] = self.query_times[product_type][-self.max_metrics:]

    def record_error(self, operation: str, product_type: Optional[ProductType] = None):
        """Record operation error with automatic cleanup."""
        self._cleanup_old_metrics()

        if product_type is None:
            product_type = self._determine_product_type_from_operation(operation)

        if operation not in self.error_counts[product_type]:
            self.error_counts[product_type][operation] = 0
        self.error_counts[product_type][operation] += 1

    def get_performance_stats(self, product_type: Optional[ProductType] = None) -> Dict[str, Any]:
        """Get performance statistics with memory usage information."""
        if product_type is None:
            # Return aggregated stats for all product types
            return self._get_aggregated_stats()

        query_times = self.query_times[product_type]
        if not query_times:
            return {"error": f"No performance data available for {product_type.value}"}

        avg_time = sum(query_times) / len(query_times)
        max_time = max(query_times)
        min_time = min(query_times)

        total_operations = sum(self.operation_counts[product_type].values())
        total_errors = sum(self.error_counts[product_type].values())

        return {
            "product_type": product_type.value,
            "uptime_seconds": (datetime.now() - self.start_time).total_seconds(),
            "total_operations": total_operations,
            "total_errors": total_errors,
            "average_query_time_ms": avg_time * 1000,
            "max_query_time_ms": max_time * 1000,
            "min_query_time_ms": min_time * 1000,
            "operation_breakdown": self.operation_counts[product_type].copy(),
            "error_breakdown": self.error_counts[product_type].copy(),
            "error_rate": (total_errors / max(total_operations, 1)) * 100,
            "memory_usage": {
                "query_times_count": len(query_times),
                "operation_types_tracked": len(self.operation_counts[product_type]),
                "error_types_tracked": len(self.error_counts[product_type]),
                "last_cleanup": self.last_cleanup.isoformat(),
                "max_metrics_limit": self.max_metrics,
                "max_operations_limit": self.max_operations
            }
        }

    def _get_aggregated_stats(self) -> Dict[str, Any]:
        """Get aggregated statistics across all product types."""
        all_query_times = []
        total_operations = 0
        total_errors = 0

        stats_by_type = {}

        for product_type in ProductType:
            query_times = self.query_times[product_type]
            if query_times:
                all_query_times.extend(query_times)
                type_operations = sum(self.operation_counts[product_type].values())
                type_errors = sum(self.error_counts[product_type].values())

                total_operations += type_operations
                total_errors += type_errors

                stats_by_type[product_type.value] = {
                    "operations": type_operations,
                    "errors": type_errors,
                    "avg_time_ms": (sum(query_times) / len(query_times)) * 1000 if query_times else 0,
                    "error_rate": (type_errors / max(type_operations, 1)) * 100
                }

        if not all_query_times:
            return {"error": "No performance data available for any product type"}

        avg_time = sum(all_query_times) / len(all_query_times)
        max_time = max(all_query_times)
        min_time = min(all_query_times)

        return {
            "aggregated_stats": True,
            "uptime_seconds": (datetime.now() - self.start_time).total_seconds(),
            "total_operations": total_operations,
            "total_errors": total_errors,
            "average_query_time_ms": avg_time * 1000,
            "max_query_time_ms": max_time * 1000,
            "min_query_time_ms": min_time * 1000,
            "error_rate": (total_errors / max(total_operations, 1)) * 100,
            "stats_by_product_type": stats_by_type,
            "memory_usage": {
                "total_query_times": len(all_query_times),
                "total_operation_types": sum(len(ops) for ops in self.operation_counts.values()),
                "total_error_types": sum(len(errs) for errs in self.error_counts.values()),
                "last_cleanup": self.last_cleanup.isoformat()
            }
        }

    def force_cleanup(self):
        """Force immediate cleanup of all metrics."""
        logger.info("Forcing unified performance monitor cleanup")

        for product_type in ProductType:
            self.query_times[product_type] = self.query_times[product_type][-self.max_metrics//2:] if self.query_times[product_type] else []

            # Keep only top operations and errors
            if len(self.operation_counts[product_type]) > self.max_operations//2:
                sorted_ops = sorted(self.operation_counts[product_type].items(), key=lambda x: x[1], reverse=True)
                self.operation_counts[product_type] = dict(sorted_ops[:self.max_operations//2])

            if len(self.error_counts[product_type]) > self.max_operations//2:
                sorted_errors = sorted(self.error_counts[product_type].items(), key=lambda x: x[1], reverse=True)
                self.error_counts[product_type] = dict(sorted_errors[:self.max_operations//2])

        self.last_cleanup = datetime.now()
        logger.info("Forced cleanup completed for all product types")

    def reset_all_metrics(self):
        """Reset all performance metrics (use with caution)."""
        logger.warning("Resetting all performance metrics for all product types")

        for product_type in ProductType:
            self.query_times[product_type].clear()
            self.operation_counts[product_type].clear()
            self.error_counts[product_type].clear()

        self.start_time = datetime.now()
        self.last_cleanup = datetime.now()


# Backward compatibility: Create alias for existing exclusive product performance monitor
class ExclusiveProductPerformanceMonitor(UnifiedProductPerformanceMonitor):
    """Backward compatibility wrapper for exclusive product performance monitoring."""

    def __init__(self, max_metrics: int = 1000, max_operations: int = 100):
        super().__init__(max_metrics, max_operations)

    def record_query_time(self, operation: str, duration: float):
        """Record query time for exclusive products (backward compatibility)."""
        super().record_query_time(operation, duration, ProductType.EXCLUSIVE)

    def record_error(self, operation: str):
        """Record error for exclusive products (backward compatibility)."""
        super().record_error(operation, ProductType.EXCLUSIVE)

    def get_performance_stats(self) -> Dict[str, Any]:
        """Get exclusive product performance stats (backward compatibility)."""
        return super().get_performance_stats(ProductType.EXCLUSIVE)


# Global instances for unified system
unified_product_cache = UnifiedProductCache()
unified_product_performance_monitor = UnifiedProductPerformanceMonitor()

# Backward compatibility instances
exclusive_product_cache = ExclusiveProductCache()
exclusive_product_performance_monitor = ExclusiveProductPerformanceMonitor()


# Utility functions for easy integration
def get_product_with_monitoring(product_id: Any, product_type: Optional[ProductType] = None) -> Optional[Dict[str, Any]]:
    """Get product with automatic caching and performance monitoring."""
    start_time = time.time()

    try:
        # Convert product_id to string for cache key (but preserve original for database query)
        cache_key = str(product_id)

        # Try cache first
        cached_product = unified_product_cache.get_product(cache_key, product_type)
        if cached_product:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_query_time("cache_hit", duration, product_type)
            return cached_product

        # Cache miss - get from database using original product_id (preserving type)
        product = get_product(product_id)

        if product:
            # Determine product type if not specified
            if product_type is None:
                product_type = unified_product_cache._determine_product_type(product)

            # Cache the product using string key
            unified_product_cache.cache_product(cache_key, product, product_type)

            duration = time.time() - start_time
            unified_product_performance_monitor.record_query_time("database_fetch", duration, product_type)
            return product
        else:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_query_time("product_not_found", duration, product_type)
            return None

    except Exception as e:
        duration = time.time() - start_time
        unified_product_performance_monitor.record_error("get_product_error", product_type)
        logger.error(f"Error getting product {product_id}: {e}")
        return None


def invalidate_product_cache(product_id: str, product_type: Optional[ProductType] = None):
    """Invalidate product cache with monitoring."""
    start_time = time.time()

    try:
        unified_product_cache.invalidate_product(product_id, product_type)
        duration = time.time() - start_time
        unified_product_performance_monitor.record_query_time("cache_invalidation", duration, product_type)

    except Exception as e:
        duration = time.time() - start_time
        unified_product_performance_monitor.record_error("cache_invalidation_error", product_type)
        logger.error(f"Error invalidating cache for product {product_id}: {e}")


def get_unified_performance_report() -> str:
    """Generate a comprehensive performance report for all product types."""
    try:
        stats = unified_product_performance_monitor.get_performance_stats()
        cache_stats = unified_product_cache.get_cache_stats()

        lines = ["📊 <b>Unified Product Performance Report</b>\n"]

        if "aggregated_stats" in stats:
            lines.append(f"🔹 <b>Overall Performance</b>")
            lines.append(f"   • Total Operations: {stats['total_operations']}")
            lines.append(f"   • Total Errors: {stats['total_errors']}")
            lines.append(f"   • Overall Error Rate: {stats['error_rate']:.1f}%")
            lines.append(f"   • Average Response Time: {stats['average_query_time_ms']:.2f}ms")
            lines.append("")

            lines.append(f"🔹 <b>Performance by Product Type</b>")
            for product_type, type_stats in stats['stats_by_product_type'].items():
                lines.append(f"   • {product_type.title()}:")
                lines.append(f"     - Operations: {type_stats['operations']}")
                lines.append(f"     - Error Rate: {type_stats['error_rate']:.1f}%")
                lines.append(f"     - Avg Time: {type_stats['avg_time_ms']:.2f}ms")
            lines.append("")

        lines.append(f"🔹 <b>Cache Performance</b>")
        lines.append(f"   • Hit Rate: {cache_stats['hit_rate_percent']:.1f}%")
        lines.append(f"   • Cached Products: {cache_stats['cached_products']}")
        lines.append(f"   • Memory Objects: {cache_stats['memory_usage']['total_memory_objects']}")

        return "\n".join(lines)

    except Exception as e:
        logger.error(f"Error generating performance report: {e}")
        return "❌ Error generating performance report"
