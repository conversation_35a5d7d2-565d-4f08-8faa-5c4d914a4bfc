"""
Database Export Utility for MongoDB
Provides secure database export functionality for admin users with comprehensive
logging, security measures, and multiple export formats.
"""

import os
import json
import gzip
import shutil
import asyncio
import logging
import tempfile
import subprocess
import zipfile
import tarfile
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from urllib.parse import urlparse
import uuid

from config import MONGO_URI, DB_NAME
from utils.monitoring_system import record_security_event, emit_alert, AlertLevel
from utils.rate_limiter import rate_limit
from database.operations import get_log_settings

logger = logging.getLogger(__name__)

# Export configuration
EXPORT_TEMP_DIR = Path("temp/database_exports")
MAX_EXPORT_SIZE = 2 * 1024 * 1024 * 1024  # 2GB limit
EXPORT_TIMEOUT = 1800  # 30 minutes
CLEANUP_AFTER_HOURS = 24
RATE_LIMIT_EXPORTS = 3  # Max 3 exports per hour per admin

# Supported export formats
EXPORT_FORMATS = {
    "bson_archive": {
        "extension": ".archive",
        "description": "BSON Archive (MongoDB native format)",
        "mongodump_args": ["--archive"],
        "compress": False,
        "folder_based": False
    },
    "bson_compressed": {
        "extension": ".archive.gz",
        "description": "Compressed BSON Archive",
        "mongodump_args": ["--archive"],
        "compress": True,
        "folder_based": False
    },
    "json": {
        "extension": ".zip",
        "description": "JSON Export (structured folder format)",
        "mongodump_args": ["--jsonArray"],
        "compress": False,
        "folder_based": True
    },
    "json_compressed": {
        "extension": ".tar.gz",
        "description": "Compressed JSON Export (structured folder format)",
        "mongodump_args": ["--jsonArray"],
        "compress": True,
        "folder_based": True
    }
}

class DatabaseExportError(Exception):
    """Custom exception for database export errors."""
    pass

class DatabaseExporter:
    """Handles secure database export operations."""
    
    def __init__(self):
        """Initialize the database exporter."""
        self.temp_dir = EXPORT_TEMP_DIR
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._active_exports = {}
        self._export_history = []
        
    def _parse_mongo_uri(self) -> Dict[str, Any]:
        """Parse MongoDB URI to extract connection parameters."""
        try:
            parsed = urlparse(MONGO_URI)
            
            # Handle MongoDB Atlas and standard URIs
            if parsed.scheme not in ['mongodb', 'mongodb+srv']:
                raise ValueError(f"Unsupported MongoDB scheme: {parsed.scheme}")
            
            connection_params = {
                "host": parsed.hostname or "localhost",
                "port": parsed.port or 27017,
                "username": parsed.username,
                "password": parsed.password,
                "database": DB_NAME,
                "uri": MONGO_URI
            }
            
            return connection_params
            
        except Exception as e:
            logger.error(f"Error parsing MongoDB URI: {e}")
            raise DatabaseExportError(f"Invalid MongoDB URI configuration: {e}")
    
    def _generate_export_filename(self, export_format: str, collections: Optional[List[str]] = None) -> str:
        """Generate a unique filename for the export."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        format_info = EXPORT_FORMATS.get(export_format, EXPORT_FORMATS["bson_archive"])

        if format_info.get("folder_based", False):
            # For folder-based exports, create a folder name (archive extension will be added later)
            return f"{DB_NAME}_export_{timestamp}"
        else:
            # For single-file exports, use the traditional naming
            if collections and len(collections) < 5:  # Only show collection names if few
                collections_str = "_".join(collections[:3])
                if len(collections) > 3:
                    collections_str += "_and_more"
            else:
                collections_str = "full_db"

            extension = format_info["extension"]
            return f"{DB_NAME}_{collections_str}_{timestamp}{extension}"
    
    def _check_mongodump_available(self) -> bool:
        """Check if mongodump is available in the system."""
        try:
            result = subprocess.run(
                ["mongodump", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            return result.returncode == 0
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False
    
    def _get_collection_list(self) -> List[str]:
        """Get list of collections in the database."""
        try:
            from database.connection import db
            return db.list_collection_names()
        except Exception as e:
            logger.error(f"Error getting collection list: {e}")
            return []
    
    async def _run_mongodump(
        self, 
        output_path: Path, 
        export_format: str,
        collections: Optional[List[str]] = None,
        date_filter: Optional[Dict[str, datetime]] = None
    ) -> bool:
        """Run mongodump command with specified parameters."""
        try:
            connection_params = self._parse_mongo_uri()
            format_info = EXPORT_FORMATS.get(export_format, EXPORT_FORMATS["bson_archive"])
            
            # Build mongodump command
            cmd = ["mongodump", "--uri", connection_params["uri"]]
            
            # Add format-specific arguments
            if export_format.startswith("json"):
                # For JSON exports, we need to export each collection separately
                # and combine them into a single JSON file
                return await self._export_as_json(output_path, collections, date_filter)
            else:
                # BSON archive export
                cmd.extend(format_info["mongodump_args"])
                if not format_info["compress"]:
                    cmd.extend(["--out", str(output_path.parent / "dump")])
                
            # Add collection filter if specified
            if collections:
                for collection in collections:
                    cmd.extend(["--collection", collection])
            
            # Add date filter if specified (requires query)
            if date_filter:
                query = self._build_date_query(date_filter)
                if query:
                    cmd.extend(["--query", json.dumps(query)])
            
            logger.info(f"Running mongodump command: {' '.join(cmd[:3])}... (URI hidden)")
            
            # Run the command
            if format_info["compress"] and export_format.endswith("archive"):
                # For compressed archive, pipe through gzip
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                # Compress the output
                with gzip.open(output_path, 'wb') as gz_file:
                    while True:
                        chunk = await process.stdout.read(8192)
                        if not chunk:
                            break
                        gz_file.write(chunk)
                
                await process.wait()
                return process.returncode == 0
            else:
                # Regular export
                process = await asyncio.create_subprocess_exec(
                    *cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                
                stdout, stderr = await process.communicate()
                
                if process.returncode != 0:
                    logger.error(f"Mongodump failed: {stderr.decode()}")
                    return False
                
                # For non-archive formats, create archive from dump directory
                if not export_format.endswith("archive"):
                    dump_dir = output_path.parent / "dump"
                    if dump_dir.exists():
                        shutil.make_archive(str(output_path.with_suffix("")), 'zip', dump_dir)
                        shutil.rmtree(dump_dir)
                
                return True
                
        except Exception as e:
            logger.error(f"Error running mongodump: {e}")
            return False
    
    def _build_date_query(self, date_filter: Dict[str, datetime]) -> Optional[Dict]:
        """Build MongoDB query for date filtering."""
        if not date_filter:
            return None
        
        query = {}
        if "start_date" in date_filter:
            query["$gte"] = date_filter["start_date"]
        if "end_date" in date_filter:
            query["$lte"] = date_filter["end_date"]
        
        # Apply to common date fields
        if query:
            return {
                "$or": [
                    {"created_at": query},
                    {"updated_at": query},
                    {"timestamp": query},
                    {"date": query}
                ]
            }
        return None
    
    async def _export_as_json(
        self,
        output_path: Path,
        collections: Optional[List[str]] = None,
        date_filter: Optional[Dict[str, datetime]] = None
    ) -> bool:
        """Export database as JSON format with folder structure."""
        try:
            from database.connection import db

            target_collections = collections or self._get_collection_list()

            # Determine if this is a folder-based export
            is_folder_based = (output_path.suffix == '.zip' or
                             (output_path.suffix == '.gz' and output_path.name.endswith('.tar.gz')))

            if is_folder_based:
                return await self._export_as_json_folder(output_path, target_collections, date_filter)
            else:
                # Legacy single-file export for backward compatibility
                return await self._export_as_json_single_file(output_path, target_collections, date_filter)

        except Exception as e:
            logger.error(f"Error in JSON export: {e}")
            return False

    async def _export_as_json_folder(
        self,
        output_path: Path,
        target_collections: List[str],
        date_filter: Optional[Dict[str, datetime]] = None
    ) -> bool:
        """Export database as structured folder format."""
        try:
            from database.connection import db

            # Create temporary directory for the export folder
            with tempfile.TemporaryDirectory() as temp_dir:
                temp_path = Path(temp_dir)

                # Create the main export folder
                folder_name = output_path.stem.replace('.tar', '')  # Remove .tar from .tar.gz
                export_folder = temp_path / folder_name
                export_folder.mkdir(parents=True, exist_ok=True)

                # Create metadata file
                metadata = {
                    "export_info": {
                        "database_name": DB_NAME,
                        "export_timestamp": datetime.now().isoformat(),
                        "export_format": "json_folder",
                        "total_collections": len(target_collections),
                        "collections": target_collections,
                        "date_filter": date_filter.isoformat() if date_filter else None
                    }
                }

                metadata_file = export_folder / "export_metadata.json"
                with open(metadata_file, 'w', encoding='utf-8') as f:
                    json.dump(metadata, f, indent=2, default=str)

                # Export each collection as a separate JSON file
                for collection_name in target_collections:
                    try:
                        collection = db[collection_name]

                        # Build query
                        query = {}
                        if date_filter:
                            date_query = self._build_date_query(date_filter)
                            if date_query:
                                query = date_query

                        # Export collection data
                        documents = list(collection.find(query))

                        # Convert ObjectId and other non-JSON serializable types
                        for doc in documents:
                            for key, value in doc.items():
                                if hasattr(value, '__str__') and not isinstance(value, (str, int, float, bool, list, dict)):
                                    doc[key] = str(value)

                        # Write individual collection file
                        collection_file = export_folder / f"{collection_name}.json"
                        with open(collection_file, 'w', encoding='utf-8') as f:
                            json.dump(documents, f, indent=2, default=str)

                        logger.info(f"Exported {len(documents)} documents from {collection_name}")

                    except Exception as e:
                        logger.error(f"Error exporting collection {collection_name}: {e}")
                        # Create error file for failed collection
                        error_file = export_folder / f"{collection_name}_ERROR.json"
                        with open(error_file, 'w', encoding='utf-8') as f:
                            json.dump({"error": str(e), "collection": collection_name}, f, indent=2)

                # Create archive from the folder
                if output_path.suffix == '.zip':
                    return self._create_zip_archive(export_folder, output_path)
                elif output_path.suffix == '.gz' and output_path.name.endswith('.tar.gz'):
                    return self._create_tar_archive(export_folder, output_path)
                else:
                    logger.error(f"Unsupported archive format: {output_path.suffix}")
                    return False

        except Exception as e:
            logger.error(f"Error in folder-based JSON export: {e}")
            return False

    async def _export_as_json_single_file(
        self,
        output_path: Path,
        target_collections: List[str],
        date_filter: Optional[Dict[str, datetime]] = None
    ) -> bool:
        """Export database as single JSON file (legacy format)."""
        try:
            from database.connection import db

            export_data = {}

            for collection_name in target_collections:
                try:
                    collection = db[collection_name]

                    # Build query
                    query = {}
                    if date_filter:
                        date_query = self._build_date_query(date_filter)
                        if date_query:
                            query = date_query

                    # Export collection data
                    documents = list(collection.find(query))

                    # Convert ObjectId and other non-JSON serializable types
                    for doc in documents:
                        for key, value in doc.items():
                            if hasattr(value, '__str__') and not isinstance(value, (str, int, float, bool, list, dict)):
                                doc[key] = str(value)

                    export_data[collection_name] = documents
                    logger.info(f"Exported {len(documents)} documents from {collection_name}")

                except Exception as e:
                    logger.error(f"Error exporting collection {collection_name}: {e}")
                    export_data[collection_name] = {"error": str(e)}

            # Write JSON file
            if output_path.suffix == '.gz':
                with gzip.open(output_path, 'wt', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, default=str)
            else:
                with open(output_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, default=str)

            return True

        except Exception as e:
            logger.error(f"Error in single-file JSON export: {e}")
            return False

    def _create_zip_archive(self, source_folder: Path, output_path: Path) -> bool:
        """Create ZIP archive from folder."""
        try:
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                for file_path in source_folder.rglob('*'):
                    if file_path.is_file():
                        # Calculate relative path for archive
                        arcname = file_path.relative_to(source_folder.parent)
                        zipf.write(file_path, arcname)

            logger.info(f"Created ZIP archive: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error creating ZIP archive: {e}")
            return False

    def _create_tar_archive(self, source_folder: Path, output_path: Path) -> bool:
        """Create TAR.GZ archive from folder."""
        try:
            # Create TAR.GZ archive with proper compression
            with tarfile.open(output_path, 'w:gz', compresslevel=6) as tarf:
                # Add the entire folder to the archive
                tarf.add(source_folder, arcname=source_folder.name, recursive=True)

            logger.info(f"Created TAR.GZ archive: {output_path}")
            return True

        except Exception as e:
            logger.error(f"Error creating TAR.GZ archive: {e}")
            return False

    @rate_limit(limit=RATE_LIMIT_EXPORTS, key_func=lambda self, user_id, *args, **kwargs: f"db_export_{user_id}")
    async def create_export(
        self,
        user_id: int,
        username: str,
        export_format: str = "bson_archive",
        collections: Optional[List[str]] = None,
        date_filter: Optional[Dict[str, datetime]] = None
    ) -> Dict[str, Any]:
        """
        Create a database export with specified parameters.

        Args:
            user_id: ID of the admin user requesting export
            username: Username of the admin user
            export_format: Export format (bson_archive, bson_compressed, json, json_compressed)
            collections: List of collections to export (None for all)
            date_filter: Date range filter with 'start_date' and/or 'end_date'

        Returns:
            Dict with export information including file path and metadata
        """
        export_id = str(uuid.uuid4())

        try:
            # Security validation
            from utils.database_export_security import validate_export_request, record_export_failure, record_export_success

            request_data = {
                "format": export_format,
                "collections": collections,
                "date_filter": date_filter
            }

            security_validation = validate_export_request(user_id, username, request_data)
            if not security_validation["allowed"]:
                error_msg = f"Security validation failed: {security_validation['reason']}"
                record_export_failure(user_id, username, error_msg)
                raise DatabaseExportError(error_msg)

            # Validate export format
            if export_format not in EXPORT_FORMATS:
                error_msg = f"Unsupported export format: {export_format}"
                record_export_failure(user_id, username, error_msg)
                raise DatabaseExportError(error_msg)

            # Check if mongodump is available for BSON exports, fallback to JSON if not
            if export_format.startswith("bson") and not self._check_mongodump_available():
                logger.warning(f"mongodump not available, falling back to JSON format for BSON export")
                # Convert BSON format to equivalent JSON format
                if export_format == "bson_archive":
                    export_format = "json"
                elif export_format == "bson_compressed":
                    export_format = "json_compressed"

            # Generate filename and paths
            base_filename = self._generate_export_filename(export_format, collections)
            format_info = EXPORT_FORMATS.get(export_format, EXPORT_FORMATS["bson_archive"])

            if format_info.get("folder_based", False):
                # For folder-based exports, add the appropriate archive extension
                filename = f"{base_filename}{format_info['extension']}"
            else:
                # For single-file exports, use the base filename as-is
                filename = base_filename

            export_path = self.temp_dir / filename

            # Record export start
            export_info = {
                "export_id": export_id,
                "user_id": user_id,
                "username": username,
                "format": export_format,
                "collections": collections,
                "date_filter": date_filter,
                "filename": filename,
                "file_path": str(export_path),
                "status": "in_progress",
                "started_at": datetime.now(),
                "file_size": 0,
                "error": None
            }

            self._active_exports[export_id] = export_info

            # Log security event
            record_security_event("database_export_started", {
                "user_id": user_id,
                "username": username,
                "export_format": export_format,
                "collections": collections,
                "export_id": export_id
            })

            logger.info(f"Starting database export {export_id} for user {user_id} ({username})")

            # Perform the export
            success = await self._run_mongodump(export_path, export_format, collections, date_filter)

            if success and export_path.exists():
                file_size = export_path.stat().st_size

                # Check file size limit
                if file_size > MAX_EXPORT_SIZE:
                    export_path.unlink()  # Delete oversized file
                    error_msg = f"Export file too large: {file_size / (1024**3):.2f}GB (max: {MAX_EXPORT_SIZE / (1024**3):.2f}GB)"
                    record_export_failure(user_id, username, error_msg)
                    raise DatabaseExportError(error_msg)

                # Update export info
                export_info.update({
                    "status": "completed",
                    "completed_at": datetime.now(),
                    "file_size": file_size,
                    "download_expires_at": datetime.now() + timedelta(hours=CLEANUP_AFTER_HOURS)
                })

                # Log successful export
                record_security_event("database_export_completed", {
                    "user_id": user_id,
                    "username": username,
                    "export_id": export_id,
                    "file_size": file_size,
                    "format": export_format
                })

                # Record security success
                record_export_success(user_id, username, export_info)

                logger.info(f"Database export {export_id} completed successfully. File size: {file_size / (1024**2):.2f}MB")

            else:
                error_msg = "Export process failed"
                export_info.update({
                    "status": "failed",
                    "completed_at": datetime.now(),
                    "error": error_msg
                })

                # Log failed export
                record_security_event("database_export_failed", {
                    "user_id": user_id,
                    "username": username,
                    "export_id": export_id,
                    "error": error_msg
                })

                # Record security failure
                record_export_failure(user_id, username, error_msg)

                logger.error(f"Database export {export_id} failed")

            # Move to history
            self._export_history.append(export_info.copy())
            if export_id in self._active_exports:
                del self._active_exports[export_id]

            return export_info

        except Exception as e:
            # Update export info with error
            if export_id in self._active_exports:
                self._active_exports[export_id].update({
                    "status": "failed",
                    "completed_at": datetime.now(),
                    "error": str(e)
                })
                self._export_history.append(self._active_exports[export_id].copy())
                del self._active_exports[export_id]

            # Log error
            record_security_event("database_export_error", {
                "user_id": user_id,
                "username": username,
                "export_id": export_id,
                "error": str(e)
            })

            # Record security failure
            record_export_failure(user_id, username, str(e))

            logger.error(f"Database export {export_id} error: {e}")
            raise DatabaseExportError(f"Export failed: {e}")

    def get_export_status(self, export_id: str) -> Optional[Dict[str, Any]]:
        """Get the status of an export operation."""
        # Check active exports
        if export_id in self._active_exports:
            return self._active_exports[export_id].copy()

        # Check history
        for export_info in self._export_history:
            if export_info["export_id"] == export_id:
                return export_info.copy()

        return None

    def get_user_exports(self, user_id: int, limit: int = 10) -> List[Dict[str, Any]]:
        """Get export history for a specific user."""
        user_exports = []

        # Add active exports
        for export_info in self._active_exports.values():
            if export_info["user_id"] == user_id:
                user_exports.append(export_info.copy())

        # Add historical exports
        for export_info in self._export_history:
            if export_info["user_id"] == user_id:
                user_exports.append(export_info.copy())

        # Sort by start time (newest first) and limit
        user_exports.sort(key=lambda x: x["started_at"], reverse=True)
        return user_exports[:limit]

    def cleanup_expired_exports(self) -> int:
        """Clean up expired export files and return count of cleaned files."""
        cleaned_count = 0
        current_time = datetime.now()

        try:
            # Clean up files in temp directory
            for file_path in self.temp_dir.glob("*"):
                if file_path.is_file():
                    try:
                        # Check file age
                        file_age = current_time - datetime.fromtimestamp(file_path.stat().st_mtime)
                        if file_age > timedelta(hours=CLEANUP_AFTER_HOURS):
                            file_path.unlink()
                            cleaned_count += 1
                            logger.info(f"Cleaned up expired export file: {file_path.name}")
                    except Exception as e:
                        logger.error(f"Error cleaning up file {file_path}: {e}")

            # Clean up old history entries
            cutoff_time = current_time - timedelta(days=7)  # Keep history for 7 days
            self._export_history = [
                export for export in self._export_history
                if export.get("started_at", current_time) > cutoff_time
            ]

        except Exception as e:
            logger.error(f"Error during export cleanup: {e}")

        return cleaned_count

    def get_export_statistics(self) -> Dict[str, Any]:
        """Get statistics about database exports."""
        total_exports = len(self._export_history)
        successful_exports = len([e for e in self._export_history if e["status"] == "completed"])
        failed_exports = len([e for e in self._export_history if e["status"] == "failed"])
        active_exports = len(self._active_exports)

        # Calculate total exported data size
        total_size = sum(e.get("file_size", 0) for e in self._export_history if e["status"] == "completed")

        return {
            "total_exports": total_exports,
            "successful_exports": successful_exports,
            "failed_exports": failed_exports,
            "active_exports": active_exports,
            "success_rate": (successful_exports / total_exports * 100) if total_exports > 0 else 0,
            "total_exported_size": total_size,
            "total_exported_size_mb": total_size / (1024**2) if total_size > 0 else 0
        }

# Global database exporter instance
database_exporter = DatabaseExporter()

# Convenience functions
async def create_database_export(
    user_id: int,
    username: str,
    export_format: str = "bson_archive",
    collections: Optional[List[str]] = None,
    date_filter: Optional[Dict[str, datetime]] = None
) -> Dict[str, Any]:
    """Create a database export."""
    return await database_exporter.create_export(user_id, username, export_format, collections, date_filter)

def get_export_formats() -> Dict[str, Dict[str, Any]]:
    """Get available export formats."""
    return EXPORT_FORMATS.copy()

def get_database_collections() -> List[str]:
    """Get list of database collections."""
    return database_exporter._get_collection_list()

def cleanup_expired_exports() -> int:
    """Clean up expired export files."""
    return database_exporter.cleanup_expired_exports()
