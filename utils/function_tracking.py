"""
Utility for tracking function usage and preventing false positives in static code analysis.
"""

import functools
import logging

logger = logging.getLogger(__name__)

# Registry of functions we know are used
USED_FUNCTIONS = set()


def mark_as_used(func):
    """
    Decorator that marks a function as used to prevent static analysis tools
    from flagging it as unused.
    """

    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        return func(*args, **kwargs)

    # Add to the registry of used functions
    USED_FUNCTIONS.add(func.__name__)
    return wrapper


def register_usage(*funcs):
    """
    Register multiple functions as used without decorating them.
    Useful for registering keyboard builders that might appear unused.
    """
    for func in funcs:
        if callable(func):
            USED_FUNCTIONS.add(func.__name__)
        elif isinstance(func, str):
            USED_FUNCTIONS.add(func)
