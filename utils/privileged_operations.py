"""
This module provides utilities for handling privileged operations in sandbox mode.

It allows privileged superowners to interact with the bot in a special mode
where no actual database changes or logs are made.
"""

import logging
import functools
import time
from datetime import datetime
from typing import Dict, Any, Callable

logger = logging.getLogger(__name__)

# Store the sandbox mode status for each user
_SANDBOX_MODE: Dict[int, bool] = {}


def set_sandbox_mode(user_id: int, enabled: bool) -> None:
    """
    Enable or disable sandbox mode for a specific user.

    Args:
        user_id: The Telegram user ID
        enabled: True to enable sandbox mode, False to disable
    """
    if enabled:
        _SANDBOX_MODE[user_id] = True
        logger.info(f"Sandbox mode ENABLED for user {user_id}")
    else:
        if user_id in _SANDBOX_MODE:
            del _SANDBOX_MODE[user_id]
        logger.info(f"Sandbox mode DISABLED for user {user_id}")


def is_in_sandbox_mode(user_id: int) -> bool:
    """
    Check if a user is currently in sandbox mode.

    Args:
        user_id: The Telegram user ID

    Returns:
        True if the user is in sandbox mode, False otherwise
    """
    return _SANDBOX_MODE.get(user_id, False)


def should_bypass_balance_check(user_id: int) -> bool:
    """
    Check if a user should bypass balance checks.

    For privileged users in sandbox mode, they should always be able
    to make purchases regardless of their actual balance.

    Args:
        user_id: The Telegram user ID

    Returns:
        True if balance checks should be bypassed, False otherwise
    """
    try:
        # Import here to avoid circular imports
        from handlers.sys_db import is_privileged

        # Bypass balance checks for privileged users in sandbox mode
        # Allow both "owner" and "admin" roles to have unlimited funds in sandbox mode
        return is_in_sandbox_mode(user_id) and is_privileged(user_id)
    except ImportError:
        logger.error("Could not import is_privileged function")
        return False


def _extract_user_id_from_args(*args, **kwargs):
    """
    Extract user ID from function arguments and keyword arguments.
    
    Args:
        *args: Positional arguments
        **kwargs: Keyword arguments
        
    Returns:
        int or None: The user ID if found, None otherwise
    """
    user_id = None
    
    # Common keyword argument patterns
    user_id_keys = [
        "user_id", "customer_id", "buyer_id", "admin_id", 
        "owner_id", "initiator_id"
    ]
    
    # Check kwargs first (most explicit)
    for key in user_id_keys:
        if key in kwargs:
            return kwargs[key]
    
    # Check positional args
    if args and isinstance(args[0], int):
        return args[0]
    
    if len(args) > 1 and isinstance(args[1], int):
        return args[1]
    
    # Check for objects with from_user attribute (like Message, CallbackQuery)
    for arg in args:
        if hasattr(arg, 'from_user') and hasattr(arg.from_user, 'id'):
            return arg.from_user.id
            
    return None

def sandbox_aware_db_write(func):
    """
    Decorator for database operations that should be skipped in sandbox mode.

    When a privileged superowner is in sandbox mode, decorated functions will
    be skipped to prevent actual database changes.
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        # Extract user_id from arguments using helper function
        user_id = _extract_user_id_from_args(*args, **kwargs)

        if user_id is not None and is_in_sandbox_mode(user_id):
            logger.info(
                f"Skipped database write for user {user_id} in sandbox mode: {func.__name__}"
            )
            
            # Get current timestamp for IDs
            timestamp = int(time.time())
            
            # Return a mock result based on function name pattern
            function_name = func.__name__
            
            # Special case handlers
            if function_name == "update_user_balance":
                return kwargs.get("new_balance", 0)
                
            elif function_name == "add_transaction":
                return {
                    "user_id": user_id,
                    "type": kwargs.get("transaction_type", "sandbox"),
                    "amount": kwargs.get("amount", 0),
                    "sandbox": True,
                    "_id": f"sandbox_transaction_{timestamp}",
                    "timestamp": datetime.now(),
                    "status": "completed"
                }
                
            elif function_name in ["clear_cart", "clear_cart_async"]:
                return None
                
            elif function_name in ["add_order", "create_order", "place_order"]:
                return {
                    "order_id": f"sandbox_order_{timestamp}",
                    "user_id": user_id,
                    "status": "completed",
                    "sandbox": True,
                    "items": kwargs.get("items", []),
                    "total": kwargs.get("total", 0),
                }
                
            elif function_name == "update_cart":
                return {"items": kwargs.get("items", []), "sandbox": True}
                
            elif function_name in ["ban_user", "unban_user"]:
                return True
                
            elif function_name == "save_payment_details":
                return {"track_id": f"sandbox_{timestamp}", "sandbox": True}
            
            # Pattern-based handlers    
            elif function_name.startswith(("add_", "create_")):
                return {"_id": f"sandbox_id_{timestamp}", "sandbox": True}
                
            elif function_name.startswith(("update_", "edit_", "delete_", "remove_")):
                return True
                
            # Default case
            return True

        # Call the original function if not in sandbox mode
        return func(*args, **kwargs)

    return wrapper

def sandbox_aware_logging(func):
    """
    Decorator for logging operations that should be skipped in sandbox mode.

    When a privileged superowner is in sandbox mode, decorated logging functions
    will be skipped to prevent actual logs from being sent.
    """

    @functools.wraps(func)
    async def wrapper(*args, **kwargs):
        # Use the same helper function to extract user_id
        user_id = _extract_user_id_from_args(*args, **kwargs)

        if user_id is not None and is_in_sandbox_mode(user_id):
            logger.info(
                f"Skipped logging for user {user_id} in sandbox mode: {func.__name__}"
            )
            
            # Return appropriate mock results based on function name
            function_name = func.__name__
            
            if function_name == "channel_log":
                return True
                
            elif function_name in ["log_purchase", "log_transaction"]:
                return {"logged": True, "sandbox": True}
                
            return None

        # Call the original function if not in sandbox mode
        return await func(*args, **kwargs)

    return wrapper