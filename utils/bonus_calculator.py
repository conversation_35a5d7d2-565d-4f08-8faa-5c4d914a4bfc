"""
Bonus Calculation Engine for the Telegram Bot.
Handles bonus tier evaluation and wallet credit application.
"""

import logging
from typing import Dict, Any, Optional, Tuple
from datetime import datetime

from database.operations import (
    get_applicable_bonus_tier,
    get_applicable_bonus_tier_async,
    calculate_bonus_amount,
    calculate_bonus_amount_async,
    update_user_balance,
    update_user_balance_async,
    add_transaction,
    add_transaction_async,
    get_user_balance,
    get_user_balance_async,
)

logger = logging.getLogger(__name__)


class BonusCalculationError(Exception):
    """Exception raised when bonus calculation fails."""
    pass


class BonusCalculator:
    """
    Handles bonus calculations and wallet credit application.
    """
    
    @staticmethod
    def calculate_deposit_bonus(deposit_amount: float) -> Dict[str, Any]:
        """
        Calculate bonus for a deposit amount.

        Args:
            deposit_amount: The original deposit amount

        Returns:
            Dict containing:
            - bonus_amount: Amount of bonus to be awarded
            - total_amount: Original amount + bonus
            - tier_used: The bonus tier that was applied (if any)
            - original_amount: The original deposit amount
            - has_bonus: Whether any bonus was applied
        """
        try:
            if deposit_amount <= 0:
                raise BonusCalculationError("Deposit amount must be positive")

            result = calculate_bonus_amount(deposit_amount)
            result["has_bonus"] = result["bonus_amount"] > 0

            logger.info(
                f"Bonus calculation: ${deposit_amount:.2f} -> "
                f"${result['total_amount']:.2f} "
                f"(+${result['bonus_amount']:.2f} bonus)"
            )

            return result

        except Exception as e:
            logger.error(f"Error calculating deposit bonus: {e}")
            return {
                "bonus_amount": 0.0,
                "total_amount": deposit_amount,
                "tier_used": None,
                "original_amount": deposit_amount,
                "has_bonus": False,
                "error": str(e)
            }
    
    @staticmethod
    async def calculate_deposit_bonus_async(deposit_amount: float) -> Dict[str, Any]:
        """
        Calculate bonus for a deposit amount (async).

        Args:
            deposit_amount: The original deposit amount

        Returns:
            Dict containing bonus calculation results
        """
        try:
            if deposit_amount <= 0:
                raise BonusCalculationError("Deposit amount must be positive")

            result = await calculate_bonus_amount_async(deposit_amount)
            result["has_bonus"] = result["bonus_amount"] > 0

            logger.info(
                f"Bonus calculation (async): ${deposit_amount:.2f} -> "
                f"${result['total_amount']:.2f} "
                f"(+${result['bonus_amount']:.2f} bonus)"
            )

            return result

        except Exception as e:
            logger.error(f"Error calculating deposit bonus (async): {e}")
            return {
                "bonus_amount": 0.0,
                "total_amount": deposit_amount,
                "tier_used": None,
                "original_amount": deposit_amount,
                "has_bonus": False,
                "error": str(e)
            }
    
    @staticmethod
    def apply_bonus_to_wallet(
        user_id: int,
        deposit_amount: float,
        track_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Calculate and apply bonus to user's wallet based on deposit amount.

        Args:
            user_id: User ID to credit bonus to
            deposit_amount: Original deposit amount
            track_id: Optional deposit track ID for transaction tracking

        Returns:
            Dict containing:
            - success: Whether the operation was successful
            - bonus_amount: Amount of bonus credited
            - new_balance: User's new balance after bonus
            - tier_used: The bonus tier that was applied
            - transaction_id: ID of the bonus transaction record
        """
        try:
            logger.info(f"Starting bonus application for user {user_id}, deposit amount: ${deposit_amount:.2f}")

            # Calculate bonus
            bonus_calc = BonusCalculator.calculate_deposit_bonus(deposit_amount)
            logger.info(f"Bonus calculation result: {bonus_calc}")

            if not bonus_calc["has_bonus"]:
                logger.info(f"No bonus applicable for deposit amount ${deposit_amount:.2f}")
                return {
                    "success": True,
                    "bonus_amount": 0.0,
                    "new_balance": get_user_balance(user_id),
                    "tier_used": None,
                    "transaction_id": None,
                    "message": "No bonus applicable for this deposit amount"
                }
            
            bonus_amount = bonus_calc["bonus_amount"]
            tier_used = bonus_calc["tier_used"]

            logger.info(f"Applying bonus: ${bonus_amount:.2f} from tier {tier_used.get('threshold', 'unknown')}")

            # Get current balance
            current_balance = get_user_balance(user_id)
            logger.info(f"Current user balance: ${current_balance:.2f}")
            new_balance = current_balance + bonus_amount
            logger.info(f"New balance after bonus: ${new_balance:.2f}")

            # Update user balance
            balance_updated = update_user_balance(user_id, new_balance)
            logger.info(f"Balance update result: {balance_updated}")
            if not balance_updated:
                raise BonusCalculationError("Failed to update user balance")
            
            # Record bonus transaction
            if tier_used.get("bonus_type") == "fixed":
                note = f"Deposit bonus: ${tier_used.get('bonus_fixed_amount', 0):.2f} fixed bonus on ${deposit_amount:.2f}"
            else:
                note = f"Deposit bonus: {tier_used.get('bonus_percentage', 0)*100:.1f}% on ${deposit_amount:.2f}"

            transaction_kwargs = {
                "note": note,
                "bonus_tier_id": str(tier_used["_id"]),
                "original_deposit_amount": deposit_amount,
            }

            if track_id:
                transaction_kwargs["track_id"] = track_id

            logger.info(f"Creating bonus transaction with kwargs: {transaction_kwargs}")
            transaction = add_transaction(
                user_id=user_id,
                transaction_type="bonus",
                amount=bonus_amount,
                **transaction_kwargs
            )
            logger.info(f"Transaction creation result: {transaction}")
            
            logger.info(
                f"Applied bonus to user {user_id}: +${bonus_amount:.2f} "
                f"(balance: ${current_balance:.2f} -> ${new_balance:.2f})"
            )
            
            return {
                "success": True,
                "bonus_amount": bonus_amount,
                "new_balance": new_balance,
                "tier_used": tier_used,
                "transaction_id": str(transaction["_id"]) if transaction else None,
                "message": f"Bonus applied: +${bonus_amount:.2f}"
            }
            
        except Exception as e:
            logger.error(f"Error applying bonus to wallet for user {user_id}: {e}")
            return {
                "success": False,
                "bonus_amount": 0.0,
                "new_balance": get_user_balance(user_id),
                "tier_used": None,
                "transaction_id": None,
                "error": str(e)
            }
    
    @staticmethod
    async def apply_bonus_to_wallet_async(
        user_id: int,
        deposit_amount: float,
        track_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Calculate and apply bonus to user's wallet based on deposit amount (async).

        Args:
            user_id: User ID to credit bonus to
            deposit_amount: Original deposit amount
            track_id: Optional deposit track ID for transaction tracking

        Returns:
            Dict containing operation results
        """
        try:
            # Calculate bonus
            bonus_calc = await BonusCalculator.calculate_deposit_bonus_async(deposit_amount)

            if not bonus_calc["has_bonus"]:
                return {
                    "success": True,
                    "bonus_amount": 0.0,
                    "new_balance": await get_user_balance_async(user_id),
                    "tier_used": None,
                    "transaction_id": None,
                    "message": "No bonus applicable for this deposit amount"
                }
            
            bonus_amount = bonus_calc["bonus_amount"]
            tier_used = bonus_calc["tier_used"]
            
            # Get current balance
            current_balance = await get_user_balance_async(user_id)
            new_balance = current_balance + bonus_amount
            
            # Update user balance
            if not await update_user_balance_async(user_id, new_balance):
                raise BonusCalculationError("Failed to update user balance")
            
            # Record bonus transaction
            if tier_used.get("bonus_type") == "fixed":
                note = f"Deposit bonus: ${tier_used.get('bonus_fixed_amount', 0):.2f} fixed bonus on ${deposit_amount:.2f}"
            else:
                note = f"Deposit bonus: {tier_used.get('bonus_percentage', 0)*100:.1f}% on ${deposit_amount:.2f}"

            transaction_kwargs = {
                "note": note,
                "bonus_tier_id": str(tier_used["_id"]),
                "original_deposit_amount": deposit_amount,
            }

            if track_id:
                transaction_kwargs["track_id"] = track_id

            transaction = await add_transaction_async(
                user_id=user_id,
                transaction_type="bonus",
                amount=bonus_amount,
                **transaction_kwargs
            )
            
            logger.info(
                f"Applied bonus to user {user_id} (async): +${bonus_amount:.2f} "
                f"(balance: ${current_balance:.2f} -> ${new_balance:.2f})"
            )
            
            return {
                "success": True,
                "bonus_amount": bonus_amount,
                "new_balance": new_balance,
                "tier_used": tier_used,
                "transaction_id": str(transaction["_id"]) if transaction else None,
                "message": f"Bonus applied: +${bonus_amount:.2f}"
            }
            
        except Exception as e:
            logger.error(f"Error applying bonus to wallet for user {user_id} (async): {e}")
            return {
                "success": False,
                "bonus_amount": 0.0,
                "new_balance": await get_user_balance_async(user_id),
                "tier_used": None,
                "transaction_id": None,
                "error": str(e)
            }
    
    @staticmethod
    def get_bonus_preview(deposit_amount: float) -> Dict[str, Any]:
        """
        Get a preview of what bonus would be applied for a deposit amount.
        This doesn't actually apply the bonus, just calculates it.

        Args:
            deposit_amount: The deposit amount to preview bonus for

        Returns:
            Dict containing preview information
        """
        try:
            bonus_calc = BonusCalculator.calculate_deposit_bonus(deposit_amount)

            if bonus_calc["has_bonus"]:
                tier = bonus_calc["tier_used"]
                return {
                    "has_bonus": True,
                    "bonus_amount": bonus_calc["bonus_amount"],
                    "total_amount": bonus_calc["total_amount"],
                    "bonus_percentage": tier["bonus_percentage"] * 100,
                    "tier_threshold": tier["threshold"],
                    "tier_description": tier.get("description", ""),
                    "preview_text": f"🎉 Bonus: +${bonus_calc['bonus_amount']:.2f} ({tier['bonus_percentage']*100:.1f}%)"
                }
            else:
                return {
                    "has_bonus": False,
                    "bonus_amount": 0.0,
                    "total_amount": deposit_amount,
                    "preview_text": "No bonus available for this amount"
                }
                
        except Exception as e:
            logger.error(f"Error getting bonus preview: {e}")
            return {
                "has_bonus": False,
                "bonus_amount": 0.0,
                "total_amount": deposit_amount,
                "preview_text": "Unable to calculate bonus",
                "error": str(e)
            }


