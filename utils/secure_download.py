"""
Secure Download System for Database Exports
Provides secure file download with token-based authentication, expiration handling,
and proper streaming for large files.
"""

import os
import uuid
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pathlib import Path
import json

from aiogram.types import FSInputFile
from utils.monitoring_system import record_security_event
from utils.database_export_security import export_security

logger = logging.getLogger(__name__)

# Download configuration
DOWNLOAD_TOKENS_PATH = Path("__pycache__/download_tokens.json")
TOKEN_EXPIRY_HOURS = 2
MAX_DOWNLOAD_ATTEMPTS = 3
DOWNLOAD_RATE_LIMIT = 5  # Max 5 downloads per hour per user

class SecureDownloadManager:
    """Manages secure downloads for database export files."""
    
    def __init__(self):
        """Initialize the download manager."""
        self.download_tokens = {}
        self.download_attempts = {}  # user_id -> [timestamps]
        self.download_history = []
        self._load_tokens()
    
    def _load_tokens(self):
        """Load existing download tokens from file."""
        try:
            if DOWNLOAD_TOKENS_PATH.exists():
                with open(DOWNLOAD_TOKENS_PATH, 'r') as f:
                    data = json.load(f)
                    self.download_tokens = data.get("tokens", {})
                    self.download_history = data.get("history", [])
                    
                # Clean expired tokens
                self._cleanup_expired_tokens()
                
        except Exception as e:
            logger.error(f"Error loading download tokens: {e}")
            self.download_tokens = {}
            self.download_history = []
    
    def _save_tokens(self):
        """Save download tokens to file."""
        try:
            DOWNLOAD_TOKENS_PATH.parent.mkdir(parents=True, exist_ok=True)
            data = {
                "tokens": self.download_tokens,
                "history": self.download_history[-1000:]  # Keep last 1000 entries
            }
            with open(DOWNLOAD_TOKENS_PATH, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Error saving download tokens: {e}")
    
    def _cleanup_expired_tokens(self):
        """Remove expired download tokens."""
        current_time = datetime.now()
        expired_tokens = []
        
        for token_id, token_data in self.download_tokens.items():
            if datetime.fromisoformat(token_data["expires_at"]) < current_time:
                expired_tokens.append(token_id)
        
        for token_id in expired_tokens:
            del self.download_tokens[token_id]
        
        if expired_tokens:
            self._save_tokens()
            logger.info(f"Cleaned up {len(expired_tokens)} expired download tokens")
    
    def create_download_token(
        self, 
        user_id: int, 
        username: str, 
        export_id: str, 
        file_path: str,
        filename: str,
        file_size: int
    ) -> Dict[str, Any]:
        """
        Create a secure download token for an export file.
        
        Args:
            user_id: User ID requesting download
            username: Username requesting download
            export_id: Export ID
            file_path: Path to the export file
            filename: Original filename
            file_size: File size in bytes
            
        Returns:
            Dict with token information
        """
        try:
            # Check rate limiting
            if not self._check_download_rate_limit(user_id):
                raise ValueError("Download rate limit exceeded")
            
            # Verify file exists
            if not Path(file_path).exists():
                raise ValueError("Export file not found")
            
            # Generate secure token
            token_id = str(uuid.uuid4())
            token_hash = hashlib.sha256(f"{token_id}{user_id}{export_id}".encode()).hexdigest()
            
            # Create token data
            token_data = {
                "token_id": token_id,
                "token_hash": token_hash,
                "user_id": user_id,
                "username": username,
                "export_id": export_id,
                "file_path": file_path,
                "filename": filename,
                "file_size": file_size,
                "created_at": datetime.now().isoformat(),
                "expires_at": (datetime.now() + timedelta(hours=TOKEN_EXPIRY_HOURS)).isoformat(),
                "download_attempts": 0,
                "max_attempts": MAX_DOWNLOAD_ATTEMPTS,
                "downloaded": False
            }
            
            # Store token
            self.download_tokens[token_id] = token_data
            self._save_tokens()
            
            # Record token creation
            record_security_event("download_token_created", {
                "user_id": user_id,
                "username": username,
                "export_id": export_id,
                "token_id": token_id,
                "expires_at": token_data["expires_at"]
            })
            
            logger.info(f"Download token created for user {user_id}, export {export_id}")
            
            return {
                "success": True,
                "token_id": token_id,
                "expires_at": token_data["expires_at"],
                "max_attempts": MAX_DOWNLOAD_ATTEMPTS
            }
            
        except Exception as e:
            logger.error(f"Error creating download token: {e}")
            
            record_security_event("download_token_creation_failed", {
                "user_id": user_id,
                "username": username,
                "export_id": export_id,
                "error": str(e)
            })
            
            return {
                "success": False,
                "error": str(e)
            }
    
    def _check_download_rate_limit(self, user_id: int) -> bool:
        """Check if user has exceeded download rate limit."""
        current_time = datetime.now()
        hour_ago = current_time - timedelta(hours=1)
        
        # Clean old attempts
        if user_id in self.download_attempts:
            self.download_attempts[user_id] = [
                timestamp for timestamp in self.download_attempts[user_id]
                if timestamp > hour_ago
            ]
        
        # Check current attempts
        recent_attempts = len(self.download_attempts.get(user_id, []))
        return recent_attempts < DOWNLOAD_RATE_LIMIT
    
    def validate_download_token(self, token_id: str, user_id: int) -> Dict[str, Any]:
        """
        Validate a download token for security and expiration.
        
        Args:
            token_id: Download token ID
            user_id: User ID attempting download
            
        Returns:
            Dict with validation results
        """
        try:
            # Clean expired tokens first
            self._cleanup_expired_tokens()
            
            # Check if token exists
            if token_id not in self.download_tokens:
                return {
                    "valid": False,
                    "reason": "Invalid or expired download token"
                }
            
            token_data = self.download_tokens[token_id]
            
            # Check user ownership
            if token_data["user_id"] != user_id:
                record_security_event("unauthorized_download_attempt", {
                    "token_id": token_id,
                    "requesting_user_id": user_id,
                    "token_owner_id": token_data["user_id"]
                })
                
                return {
                    "valid": False,
                    "reason": "Unauthorized access to download token"
                }
            
            # Check expiration
            if datetime.now() > datetime.fromisoformat(token_data["expires_at"]):
                return {
                    "valid": False,
                    "reason": "Download token has expired"
                }
            
            # Check download attempts
            if token_data["download_attempts"] >= token_data["max_attempts"]:
                return {
                    "valid": False,
                    "reason": "Maximum download attempts exceeded"
                }
            
            # Check if file still exists
            if not Path(token_data["file_path"]).exists():
                return {
                    "valid": False,
                    "reason": "Export file no longer available"
                }
            
            return {
                "valid": True,
                "token_data": token_data
            }
            
        except Exception as e:
            logger.error(f"Error validating download token: {e}")
            return {
                "valid": False,
                "reason": f"Token validation error: {str(e)}"
            }
    
    async def process_secure_download(self, token_id: str, user_id: int, bot_message) -> Dict[str, Any]:
        """
        Process a secure download using a valid token.
        
        Args:
            token_id: Download token ID
            user_id: User ID requesting download
            bot_message: Bot message object for sending file
            
        Returns:
            Dict with download results
        """
        try:
            # Validate token
            validation = self.validate_download_token(token_id, user_id)
            if not validation["valid"]:
                return {
                    "success": False,
                    "error": validation["reason"]
                }
            
            token_data = validation["token_data"]
            
            # Record download attempt
            self.download_tokens[token_id]["download_attempts"] += 1
            
            # Record download attempt in rate limiting
            if user_id not in self.download_attempts:
                self.download_attempts[user_id] = []
            self.download_attempts[user_id].append(datetime.now())
            
            # Prepare file for download
            file_path = Path(token_data["file_path"])
            document = FSInputFile(file_path, filename=token_data["filename"])
            
            # Send file
            await bot_message.answer_document(
                document=document,
                caption=(
                    f"📥 <b>Secure Database Export Download</b>\n\n"
                    f"📄 <b>Filename:</b> {token_data['filename']}\n"
                    f"💽 <b>Size:</b> {token_data['file_size'] / (1024*1024):.2f} MB\n"
                    f"🔐 <b>Token:</b> {token_id[:8]}...\n"
                    f"⏰ <b>Expires:</b> {datetime.fromisoformat(token_data['expires_at']).strftime('%Y-%m-%d %H:%M')}\n\n"
                    f"⚠️ <i>This file will be automatically deleted after expiration.</i>\n"
                    f"🔒 <i>Download secured with token-based authentication.</i>"
                ),
                parse_mode="HTML"
            )
            
            # Mark as downloaded
            self.download_tokens[token_id]["downloaded"] = True
            self.download_tokens[token_id]["downloaded_at"] = datetime.now().isoformat()
            
            # Add to download history
            download_record = {
                "token_id": token_id,
                "user_id": user_id,
                "username": token_data["username"],
                "export_id": token_data["export_id"],
                "filename": token_data["filename"],
                "file_size": token_data["file_size"],
                "downloaded_at": datetime.now().isoformat()
            }
            self.download_history.append(download_record)
            
            # Save updated data
            self._save_tokens()
            
            # Record successful download
            record_security_event("secure_download_completed", {
                "user_id": user_id,
                "username": token_data["username"],
                "export_id": token_data["export_id"],
                "token_id": token_id,
                "file_size": token_data["file_size"],
                "download_attempts": token_data["download_attempts"]
            })
            
            logger.info(f"Secure download completed: user {user_id}, token {token_id}")
            
            return {
                "success": True,
                "filename": token_data["filename"],
                "file_size": token_data["file_size"]
            }
            
        except Exception as e:
            logger.error(f"Error in secure download: {e}")
            
            # Record download failure
            record_security_event("secure_download_failed", {
                "user_id": user_id,
                "token_id": token_id,
                "error": str(e)
            })
            
            return {
                "success": False,
                "error": f"Download failed: {str(e)}"
            }
    
    def get_user_download_tokens(self, user_id: int) -> List[Dict[str, Any]]:
        """Get active download tokens for a user."""
        self._cleanup_expired_tokens()
        
        user_tokens = []
        for token_id, token_data in self.download_tokens.items():
            if token_data["user_id"] == user_id:
                user_tokens.append({
                    "token_id": token_id,
                    "export_id": token_data["export_id"],
                    "filename": token_data["filename"],
                    "created_at": token_data["created_at"],
                    "expires_at": token_data["expires_at"],
                    "download_attempts": token_data["download_attempts"],
                    "max_attempts": token_data["max_attempts"],
                    "downloaded": token_data["downloaded"]
                })
        
        return sorted(user_tokens, key=lambda x: x["created_at"], reverse=True)
    
    def revoke_download_token(self, token_id: str, user_id: int) -> bool:
        """Revoke a download token."""
        try:
            if token_id in self.download_tokens:
                token_data = self.download_tokens[token_id]
                
                # Check ownership
                if token_data["user_id"] != user_id:
                    return False
                
                # Remove token
                del self.download_tokens[token_id]
                self._save_tokens()
                
                # Record revocation
                record_security_event("download_token_revoked", {
                    "user_id": user_id,
                    "token_id": token_id,
                    "export_id": token_data["export_id"]
                })
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error revoking download token: {e}")
            return False
    
    def get_download_statistics(self) -> Dict[str, Any]:
        """Get download statistics."""
        try:
            current_time = datetime.now()
            day_ago = current_time - timedelta(days=1)
            week_ago = current_time - timedelta(days=7)
            
            # Count recent downloads
            recent_downloads = [
                record for record in self.download_history
                if datetime.fromisoformat(record["downloaded_at"]) > week_ago
            ]
            
            daily_downloads = [
                record for record in recent_downloads
                if datetime.fromisoformat(record["downloaded_at"]) > day_ago
            ]
            
            return {
                "total_tokens": len(self.download_tokens),
                "active_tokens": len([t for t in self.download_tokens.values() if not t["downloaded"]]),
                "total_downloads": len(self.download_history),
                "downloads_last_24h": len(daily_downloads),
                "downloads_last_7d": len(recent_downloads),
                "total_data_downloaded": sum(r["file_size"] for r in self.download_history),
                "unique_users": len(set(r["user_id"] for r in self.download_history)),
                "report_time": current_time.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error generating download statistics: {e}")
            return {"error": str(e)}

# Global download manager instance
download_manager = SecureDownloadManager()

# Convenience functions
def create_download_token(user_id: int, username: str, export_id: str, file_path: str, filename: str, file_size: int) -> Dict[str, Any]:
    """Create a secure download token."""
    return download_manager.create_download_token(user_id, username, export_id, file_path, filename, file_size)

def validate_download_token(token_id: str, user_id: int) -> Dict[str, Any]:
    """Validate a download token."""
    return download_manager.validate_download_token(token_id, user_id)

async def process_secure_download(token_id: str, user_id: int, bot_message) -> Dict[str, Any]:
    """Process a secure download."""
    return await download_manager.process_secure_download(token_id, user_id, bot_message)

def get_user_download_tokens(user_id: int) -> List[Dict[str, Any]]:
    """Get user's download tokens."""
    return download_manager.get_user_download_tokens(user_id)

def get_download_statistics() -> Dict[str, Any]:
    """Get download statistics."""
    return download_manager.get_download_statistics()
