"""
Utility functions for router management to prevent duplicate registrations.
"""

import logging
from aiogram import Router, Dispatcher
from typing import Optional, Dict, Callable

logger = logging.getLogger(__name__)

# Track which routers have been registered to avoid duplicate attempts
registered_routers = set()
# Track router references by name to prevent garbage collection
router_registry: Dict[str, Router] = {}


def safe_include_router(dispatcher_or_router, router, router_name=None):
    """
    Safely include a router in a dispatcher or another router, avoiding duplicates.

    Args:
        dispatcher_or_router: The dispatcher or router to include the router in
        router: The router to include
        router_name: Optional name for the router (defaults to router.name)

    Returns:
        bool: True if router was included, False if it was already included
    """
    name = router_name or getattr(router, "name", str(id(router)))

    # If router was already registered, don't register it again
    if name in registered_routers:
        logger.debug(f"Router {name} already registered, skipping")
        return False

    try:
        # Store reference to prevent garbage collection
        router_registry[name] = router

        # Include the router
        dispatcher_or_router.include_router(router)
        registered_routers.add(name)
        logger.debug(f"Router {name} registered successfully")
        return True
    except RuntimeError as e:
        # Router was already included
        registered_routers.add(name)
        logger.debug(f"Router {name} already attached: {e}")
        return False
    except Exception as e:
        logger.error(f"Error registering router {name}: {e}")
        raise


def register_router_once(dispatcher, router):
    """
    Register a router with a dispatcher exactly once.

    Args:
        dispatcher: The dispatcher to register the router with
        router: The router to register

    Returns:
        bool: True if router was registered, False if it was already registered
    """
    router_id = getattr(router, "name", str(id(router)))

    if router_id in registered_routers:
        logger.debug(f"Router {router_id} already registered, skipping")
        return False

    try:
        dispatcher.include_router(router)
        registered_routers.add(router_id)
        # Store reference to prevent garbage collection
        router_registry[router_id] = router
        logger.debug(f"Router {router_id} registered successfully")
        return True
    except RuntimeError as e:
        registered_routers.add(router_id)
        logger.debug(f"Router {router_id} already attached: {e}")
        return False
    except Exception as e:
        logger.error(f"Error registering router {router_id}: {e}")
        raise


def get_registered_routers(dispatcher):  # noqa
    """Get list of registered router names."""
    return list(registered_routers)
