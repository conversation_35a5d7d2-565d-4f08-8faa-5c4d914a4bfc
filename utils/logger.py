import logging
from datetime import datetime
from aiogram import Bo<PERSON>
from config import OWNER_ID, DEVELOPMENT_MODE
from database.operations import (
    get_log_channel,
    should_bypass_owner_filter,
    should_log_event,
    is_owner,
)
from utils.privileged_operations import sandbox_aware_logging

# Create logger instance
logger = logging.getLogger(__name__)

# Set logger level based on development mode
if DEVELOPMENT_MODE:
    logger.setLevel(logging.DEBUG)
else:
    logger.setLevel(logging.INFO)

import sys

# Create console handler with UTF-8 support
console_handler = logging.StreamHandler(sys.stdout)

# Try to configure stdout for UTF-8 encoding
try:
    if hasattr(sys.stdout, 'reconfigure'):
        sys.stdout.reconfigure(encoding='utf-8', errors='replace')
except (AttributeError, OSError):
    # Fallback: console handler will use system default
    pass

console_formatter = logging.Formatter(
    "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
console_handler.setFormatter(console_formatter)
logger.addHandler(console_handler)


def should_skip_logging(user_id):
    """Check if logging should be skipped for this user."""
    if user_id is None:
        return False

    try:
        # Import here to avoid circular imports
        from handlers.sys_db import is_privileged

        # Skip logging for privileged users
        if is_privileged(user_id):
            logger.debug(f"Skipping logging for privileged user (ID: {user_id})")
            return True

        # Check for owner separately - they still respect the bypass_owner_filter setting
        if is_owner(user_id) and not should_bypass_owner_filter():
            logger.debug(f"Skipping log for owner (ID: {user_id})")
            return True

    except ImportError:
        logger.error("Could not import is_privileged function")

    return False


@sandbox_aware_logging
async def channel_log(
    bot: Bot, message: str, parse_mode="HTML", initiator_id=None, event_type=None
):
    """Send a log message to the configured log channel."""
    try:
        # Check if we should skip logging for this user
        if should_skip_logging(initiator_id):
            return False

        # In development mode, add a prefix to the message
        if DEVELOPMENT_MODE:
            message = f"[DEV MODE] {message}"
            # logger.debug(f"Development mode log: {message[:50]}...")

        # Check if this type of event should be logged based on settings
        if event_type and not should_log_event(event_type):
            logger.debug(f"Skipping log for disabled event type: {event_type}")
            return False

        channel_id = get_log_channel()
        if not channel_id:
            logger.warning(
                "No log channel configured. Please set up a log channel ID in the database."
            )
            return False

        # Try to verify the channel exists by getting chat info
        try:
            await bot.get_chat(channel_id)
        except Exception as chat_error:
            logger.error(
                f"Cannot access log channel {channel_id}: {chat_error}. "
                f"Make sure the bot is added to the channel and has permission to post messages."
            )
            return False

        await bot.send_message(chat_id=channel_id, text=message, parse_mode=parse_mode)
        logger.debug(f"Log message sent to channel {channel_id}")
        return True
    except Exception as e:
        logger.error(f"Failed to send log to channel: {e}", exc_info=True)
    return False


@sandbox_aware_logging
async def log_balance_update(
    bot: Bot, user_id, old_balance, new_balance, admin_id=None, note=None
):
    """Log balance update to the channel."""
    # Skip logging if the user or admin is privileged
    if should_skip_logging(user_id) or should_skip_logging(admin_id):
        return False

    difference = new_balance - old_balance
    action = "increased" if difference > 0 else "decreased"
    emoji = "💹" if difference > 0 else "📉"

    message = (
        f"{emoji} <b>\u2022 🔧️ USER BALANCE {action.upper()} 🔧️ \u2022</b>\n\n"
        f"👤 <b>User ID:</b> <code>{user_id}</code>\n"
        f"💵 <b>Previous:</b> <code>${old_balance:.2f}</code>\n"
        f"💰 <b>Current:</b> <code>${new_balance:.2f}</code>\n"
        f"{'🔺' if difference > 0 else '🔻'} <b>Change:</b> <code>${abs(difference):.2f}</code>"
    )

    if admin_id:
        message += f"\n👮‍♂️ <b>Updated by:</b> <code>Admin {admin_id}</code>"

    if note:
        message += f"\n📝 <b>Note:</b> <i>{note}</i>"

    message += (
        f"\n⏱ <b>Time:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>"
    )

    return await channel_log(bot, message, initiator_id=admin_id, event_type="balance")


@sandbox_aware_logging
async def log_payment(bot: Bot, user_id: int, amount: float, invoice_id: str):
    """Log payment to the admin/owner and to the log file."""
    # Skip logging if the user is privileged
    if should_skip_logging(user_id):
        return

    message = (
        f"💸 <b>\u2022 💲 PAYMENT RECEIVED 💲 \u2022</b>\n\n"
        f"👤 <b>User ID:</b> <code>{user_id}</code>\n"
        f"💰 <b>Amount:</b> <code>${amount:.2f}</code>\n"
        f"🧾 <b>Invoice ID:</b> <code>{invoice_id}</code>\n"
        f"⏱ <b>Timestamp:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>"
    )

    # Log to console/file
    logger.info(
        f"Payment received: User {user_id}, Amount ${amount:.2f}, Invoice {invoice_id}"
    )

    # Log to channel
    await channel_log(bot, message, initiator_id=user_id, event_type="payment")

    # Notify owner - but only if the user is not privileged
    try:
        if OWNER_ID and isinstance(OWNER_ID, int):
            await bot.send_message(OWNER_ID, message, parse_mode="HTML")
        else:
            logger.warning("OWNER_ID not properly defined, skipping notification")
    except Exception as e:
        logger.error(f"Failed to notify owner about payment: {e}")


@sandbox_aware_logging
async def log_purchase(
    bot: Bot, user_id, order_number, items, total, remaining_balance
):
    """Log purchase to the channel."""
    if should_skip_logging(user_id):
        return

    items_text = (
        "\n".join([f"  \u2022 <i>{item}</i>" for item in items])
        if items
        else "<i>No items</i>"
    )

    message = (
        f"🛍️ <b>\u2022 🎉 PURCHASE COMPLETED 🎉 \u2022</b>\n\n"
        f"🔢 <b>Order #</b> <code>{order_number}</code>\n"
        f"👤 <b>User ID:</b> <code>{user_id}</code>\n"
        f"💰 <b>Total:</b> <code>${total:.2f}</code>\n"
        f"💵 <b>Remaining Balance:</b> <code>${remaining_balance:.2f}</code>\n\n"
        f"📦 <b>Items:</b>\n{items_text}\n\n"
        f"⏱ <b>Time:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>"
    )

    await channel_log(bot, message, initiator_id=user_id, event_type="purchase")


@sandbox_aware_logging
async def log_admin_action(
    bot: Bot, admin_id, action_type, target_id=None, details=None, display_name=None
):
    """Log admin actions to console and channel."""
    try:
        # Skip logging if the admin or target is privileged
        if should_skip_logging(admin_id) or (
            target_id and should_skip_logging(target_id)
        ):
            return

        # Get admin information
        admin_role = "Owner" if admin_id == OWNER_ID else "Admin"

        # Format the action message
        action_messages = {
            "ban_user": f"🚫 User {target_id} was banned",
            "unban_user": f"✅ User {target_id} was unbanned",
            "add_admin": f"👑 New admin added: {target_id}",
            "remove_admin": f"❌ Admin removed: {target_id}",
            "edit_balance": f"💰 Balance updated for user {target_id}",
        }

        log_message = action_messages.get(
            action_type, f"🔧 {action_type.replace('_', ' ').title()}"
        )

        # Add details if provided
        if details:
            log_message += f"\nDetails: {details}"

        # Log to console
        logger.info(
            f"Admin action: {admin_role} {admin_id} - {action_type} - Target: {target_id}"
        )

        # Format message for channel log
        channel_message = (
            f"🔧 <b>\u2022 ADMIN ACTION: {action_type.replace('_', ' ').upper()} \u2022</b>\n\n"
            f"👮‍♂️ <b>Admin ID:</b> <code>{admin_id}</code> ({admin_role})\n"
        )

        if target_id:
            channel_message += f"🎯 <b>Target User:</b> <code>{target_id}</code>\n"

        # Add display name to message if provided
        if display_name:
            channel_message += f"👤 <b>User:</b> <i>{display_name}</i>\n"

        if details:
            channel_message += f"📋 <b>Details:</b> <i>{details}</i>\n"

        channel_message += f"⏱ <b>Time:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>"

        # Log to channel
        await channel_log(
            bot, channel_message, initiator_id=admin_id, event_type="admin"
        )

    except Exception as e:
        logger.error(f"Failed to log admin action: {e}")
