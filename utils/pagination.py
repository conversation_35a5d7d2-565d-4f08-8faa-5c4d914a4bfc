"""Utility functions for pagination in the UI."""

from typing import List, Dict, Any, Tuple, Optional
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton
from utils.template_helpers import format_text


def paginate_items(
    items: List[Any], page: int, items_per_page: int
) -> Tuple[List[Any], int]:
    """
    Paginate a list of items.

    Args:
        items: The list of items to paginate
        page: The current page number (1-based)
        items_per_page: Number of items to show per page

    Returns:
        Tuple containing:
        - List of items for the current page
        - Total number of pages
    """
    # Calculate total pages
    total_pages = (len(items) + items_per_page - 1) // items_per_page

    # Ensure page is within valid range
    if page < 1:
        page = 1
    elif page > total_pages and total_pages > 0:
        page = total_pages

    # Calculate slice indices for current page
    start_idx = (page - 1) * items_per_page
    end_idx = min(start_idx + items_per_page, len(items))

    # Get items for current page
    current_page_items = items[start_idx:end_idx] if items else []

    return current_page_items, total_pages


def create_pagination_buttons(
    current_page: int,
    total_pages: int,
    callback_prefix: str,
    include_back_button: bool = True,
    back_callback: str = "browse_products",
    back_button_text: Optional[str] = None,
    style: str = "default",
) -> List[List[InlineKeyboardButton]]:
    """
    Create pagination buttons for navigating between pages.

    Args:
        current_page: The current page number (1-based)
        total_pages: Total number of pages
        callback_prefix: Prefix for the callback data (e.g., "products_page:")
        include_back_button: Whether to include a back button
        back_callback: Callback data for the back button
        back_button_text: Optional custom text for back button
        style: Style of pagination buttons ("default", "compact", "stylish")

    Returns:
        List of rows of InlineKeyboardButton for pagination
    """
    pagination_buttons = []

    # Only add pagination if there are multiple pages
    if total_pages > 1:
        pagination_row = []

        # Previous page button (if not on first page)
        if current_page > 1:
            prev_text = format_text("buttons", "product_pagination_prev")
            pagination_row.append(
                InlineKeyboardButton(
                    text=prev_text, callback_data=f"{callback_prefix}{current_page - 1}"
                )
            )

        # Page indicator with different styles
        if style == "compact":
            # Compact style just shows numbers
            page_text = f"{current_page}/{total_pages}"
        elif style == "stylish":
            # Stylish version with decorative elements
            page_text = f"\u2022 {current_page}/{total_pages} \u2022"
        else:
            # Default style using template
            page_text = format_text("buttons", "product_pagination_page").format(
                current=current_page, total=total_pages
            )

        pagination_row.append(
            InlineKeyboardButton(
                text=page_text,
                callback_data=f"product_page_info:{current_page}:{total_pages}",
            )
        )

        # Next page button (if not on last page)
        if current_page < total_pages:
            next_text = format_text("buttons", "product_pagination_next")
            pagination_row.append(
                InlineKeyboardButton(
                    text=next_text, callback_data=f"{callback_prefix}{current_page + 1}"
                )
            )

        pagination_buttons.append(pagination_row)

    # Add back button if requested
    if include_back_button:
        # Use custom text if provided, otherwise use template
        back_text = (
            back_button_text
            if back_button_text
            else format_text("buttons", "product_pagination_back")
        )
        pagination_buttons.append(
            [InlineKeyboardButton(text=back_text, callback_data=back_callback)]
        )

    return pagination_buttons


def create_numbered_pagination(
    current_page: int, total_pages: int, callback_prefix: str, max_buttons: int = 5
) -> List[List[InlineKeyboardButton]]:
    """
    Create a numbered pagination row with page numbers.

    Args:
        current_page: The current page number (1-based)
        total_pages: Total number of pages
        callback_prefix: Prefix for the callback data
        max_buttons: Maximum number of page buttons to show

    Returns:
        List containing a single row of pagination buttons
    """
    if total_pages <= 1:
        return []

    pagination_row = []

    # Always include Previous button if not on first page
    if current_page > 1:
        pagination_row.append(
            InlineKeyboardButton(
                text="«", callback_data=f"{callback_prefix}{current_page - 1}"
            )
        )

    # Calculate range of page numbers to display
    if total_pages <= max_buttons:
        # If we have fewer pages than max_buttons, show all pages
        page_range = range(1, total_pages + 1)
    else:
        # Calculate how many buttons to show on each side of current page
        buttons_per_side = (max_buttons - 1) // 2

        # Start with a centered window
        start_page = max(1, current_page - buttons_per_side)
        end_page = min(total_pages, start_page + max_buttons - 1)

        # Adjust if window is too close to the end
        if end_page == total_pages:
            start_page = max(1, end_page - max_buttons + 1)

        page_range = range(start_page, end_page + 1)

    # Add numbered page buttons
    for page_num in page_range:
        # Highlight current page
        if page_num == current_page:
            text = f"\u2022 {page_num} \u2022"
        else:
            text = str(page_num)

        pagination_row.append(
            InlineKeyboardButton(
                text=text, callback_data=f"{callback_prefix}{page_num}"
            )
        )

    # Always include Next button if not on last page
    if current_page < total_pages:
        pagination_row.append(
            InlineKeyboardButton(
                text="»", callback_data=f"{callback_prefix}{current_page + 1}"
            )
        )

    return [pagination_row]
