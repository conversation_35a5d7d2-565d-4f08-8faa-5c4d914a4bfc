"""
Exclusive Single-Use Product Manager
Handles all operations for exclusive single-use digital products.
"""

import logging
import os
from datetime import datetime
from typing import Dict, Any
from pathlib import Path

from database.operations import (
    get_product,
    update_product
)
from utils.monitoring_system import record_security_event, emit_alert, AlertLevel

logger = logging.getLogger(__name__)

# File type configurations
SUPPORTED_IMAGE_TYPES = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}
SUPPORTED_DOCUMENT_TYPES = {'.pdf', '.txt', '.doc', '.docx', '.zip', '.rar', '.7z', '.tmp'}
MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB limit
EXCLUSIVE_FILES_DIR = Path("uploads/exclusive_files")

class ExclusiveProductTheme:
    """Consistent theming for exclusive single-use product interfaces."""
    
    # Header and separator styling
    HEADER_DIVIDER = "<code>━━━━━━━━━━━━━━━━━━━━━━━━━</code>"
    SECTION_DIVIDER = "▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰▰"
    
    # Emoji patterns for exclusive products
    EMOJIS = {
        "exclusive_product": "📄",
        "available": "🟢",
        "sold": "🔴",
        "price": "💰",
        "file": "📁",
        "image": "🖼️",
        "document": "📋",
        "size": "📏",
        "date": "📅",
        "user": "👤",
        "success": "✅",
        "error": "❌",
        "warning": "⚠️",
        "info": "ℹ️",
        "delivery": "📤",
        "preview": "👁️",
        "exclusive": "⭐",
        "single_use": "🔒"
    }
    
    # Status indicators
    STATUS_INDICATORS = {
        "available": "🟢 Available",
        "sold": "🔴 Sold",
        "expired": "⏰ Expired",
        "reserved": "🟡 Reserved"
    }
    
    @staticmethod
    def create_header(title: str, subtitle: str = "") -> str:
        """Create consistent header formatting."""
        header = f"📄 <b>• {title.upper()} •</b>\n\n"
        header += f"{ExclusiveProductTheme.HEADER_DIVIDER}\n"
        if subtitle:
            header += f"<b>{subtitle.upper()}</b>\n"
            header += f"{ExclusiveProductTheme.HEADER_DIVIDER}\n"
        return header
    
    @staticmethod
    def create_section_break() -> str:
        """Create section break with divider."""
        return f"\n{ExclusiveProductTheme.SECTION_DIVIDER}\n"
    
    @staticmethod
    def format_price(price: float, label: str = "Price") -> str:
        """Format price with consistent styling."""
        return f"{ExclusiveProductTheme.EMOJIS['price']} <b>{label}:</b> <code>${price:.2f}</code>"
    
    @staticmethod
    def format_availability_status(is_purchased: bool, purchased_by_user_id: int = None, expiration_date: datetime = None) -> str:
        """Format availability status with appropriate indicators."""
        if expiration_date and datetime.now() > expiration_date:
            return f"{ExclusiveProductTheme.EMOJIS['warning']} <b>Status:</b> {ExclusiveProductTheme.STATUS_INDICATORS['expired']}"
        elif is_purchased:
            return f"{ExclusiveProductTheme.EMOJIS['sold']} <b>Status:</b> {ExclusiveProductTheme.STATUS_INDICATORS['sold']}"
        else:
            return f"{ExclusiveProductTheme.EMOJIS['available']} <b>Status:</b> {ExclusiveProductTheme.STATUS_INDICATORS['available']}"
    
    @staticmethod
    def format_file_info(file_type: str, file_size: int, mime_type: str = None) -> str:
        """Format file information with appropriate icons."""
        # Determine icon based on file type
        if file_type in SUPPORTED_IMAGE_TYPES:
            icon = ExclusiveProductTheme.EMOJIS['image']
            type_label = "Image"
        else:
            icon = ExclusiveProductTheme.EMOJIS['document']
            type_label = "Document"
        
        # Format file size
        if file_size < 1024:
            size_str = f"{file_size} B"
        elif file_size < 1024 * 1024:
            size_str = f"{file_size / 1024:.1f} KB"
        else:
            size_str = f"{file_size / (1024 * 1024):.1f} MB"
        
        return (
            f"{icon} <b>File Type:</b> <code>{type_label} ({file_type.upper()})</code>\n"
            f"{ExclusiveProductTheme.EMOJIS['size']} <b>File Size:</b> <code>{size_str}</code>"
        )
    
    @staticmethod
    def truncate_description(description: str, max_length: int = 200) -> str:
        """Truncate description with ellipsis if too long."""
        if len(description) <= max_length:
            return description
        return description[:max_length].rstrip() + "..."

    @staticmethod
    def create_success_message(title: str, message: str, details: Dict[str, Any] = None) -> str:
        """Create a success message with consistent formatting."""
        text = f"{ExclusiveProductTheme.EMOJIS['success']} <b>• {title.upper()} •</b>\n\n"
        text += f"{ExclusiveProductTheme.HEADER_DIVIDER}\n"
        text += f"{ExclusiveProductTheme.EMOJIS['info']} <b>SUCCESS</b>\n"
        text += f"{ExclusiveProductTheme.HEADER_DIVIDER}\n\n"
        text += f"{message}\n"

        if details:
            text += "\n"
            for key, value in details.items():
                text += f"<b>{key}:</b> {value}\n"

        return text


class ExclusiveProductManager:
    """Consolidated manager for all exclusive single-use product operations."""
    
    def __init__(self):
        """Initialize the exclusive product manager."""
        # Ensure exclusive files directory exists
        EXCLUSIVE_FILES_DIR.mkdir(parents=True, exist_ok=True)
    
    def _validate_file_path_security(self, file_path: str) -> Dict[str, Any]:
        """
        Validate file path for security issues including directory traversal.

        Args:
            file_path: Path to validate

        Returns:
            Dict with validation results
        """
        try:
            # Convert to Path object and resolve to absolute path
            path_obj = Path(file_path).resolve()

            # Check if the resolved path is within allowed directories
            allowed_bases = [
                EXCLUSIVE_FILES_DIR.resolve(),
                Path("uploads").resolve(),
                Path("uploads/exclusive_files").resolve(),
                Path("uploads/temp").resolve()
            ]

            # Ensure the file path is within one of the allowed directories
            is_safe = any(
                str(path_obj).startswith(str(base))
                for base in allowed_bases
            )

            if not is_safe:
                # Record security event
                record_security_event("path_traversal_attempt", {
                    "attempted_path": file_path,
                    "resolved_path": str(path_obj),
                    "allowed_bases": [str(base) for base in allowed_bases]
                })

                return {
                    "valid": False,
                    "error": "File path is outside allowed directories",
                    "security_issue": "path_traversal"
                }

            # Additional check for suspicious path components
            path_parts = path_obj.parts
            suspicious_parts = {"..", "~", "$", "|", "&", ";", "`"}
            found_suspicious = [part for part in path_parts if part in suspicious_parts]

            if found_suspicious:
                # Record security event
                record_security_event("suspicious_path_attempt", {
                    "attempted_path": file_path,
                    "suspicious_components": found_suspicious,
                    "full_path_parts": list(path_parts)
                })

                return {
                    "valid": False,
                    "error": "File path contains suspicious components",
                    "security_issue": "suspicious_path"
                }

            return {
                "valid": True,
                "resolved_path": str(path_obj)
            }

        except Exception as e:
            logger.error(f"Error validating file path security for {file_path}: {e}")
            return {
                "valid": False,
                "error": f"Path validation error: {str(e)}",
                "security_issue": "validation_error"
            }

    def validate_file_type(self, file_path: str, mime_type: str = None) -> Dict[str, Any]:
        """
        Validate if file type is supported for exclusive products with security checks.

        Args:
            file_path: Path to the file
            mime_type: MIME type of the file

        Returns:
            Dict with validation results
        """
        try:
            # First, validate path security
            path_security = self._validate_file_path_security(file_path)
            if not path_security["valid"]:
                return {
                    "valid": False,
                    "error": path_security["error"],
                    "security_issue": path_security.get("security_issue"),
                    "file_type": None
                }

            # Use the resolved secure path
            secure_path = path_security["resolved_path"]
            file_ext = Path(secure_path).suffix.lower()

            # Check if file extension is supported
            if file_ext not in (SUPPORTED_IMAGE_TYPES | SUPPORTED_DOCUMENT_TYPES):
                return {
                    "valid": False,
                    "error": f"Unsupported file type: {file_ext}",
                    "file_type": file_ext
                }

            # Check file exists and get size
            if os.path.exists(secure_path):
                file_size = os.path.getsize(secure_path)
                if file_size > MAX_FILE_SIZE:
                    return {
                        "valid": False,
                        "error": f"File too large: {file_size / (1024*1024):.1f}MB (max: {MAX_FILE_SIZE / (1024*1024):.0f}MB)",
                        "file_type": file_ext,
                        "file_size": file_size
                    }
            else:
                return {
                    "valid": False,
                    "error": "File not found",
                    "file_type": file_ext
                }

            # Determine file category
            if file_ext in SUPPORTED_IMAGE_TYPES:
                file_category = "image"
            else:
                file_category = "document"

            return {
                "valid": True,
                "file_type": file_ext,
                "file_category": file_category,
                "file_size": file_size,
                "mime_type": mime_type,
                "secure_path": secure_path
            }

        except Exception as e:
            logger.error(f"Error validating file type for {file_path}: {e}")
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}",
                "file_type": None
            }
    
    async def validate_exclusive_product_purchase(self, product_id: Any, user_id: int) -> Dict[str, Any]:
        """
        Validate exclusive product purchase with comprehensive checks.
        
        Args:
            product_id: Product ID
            user_id: User ID attempting to purchase
            
        Returns:
            Dict with validation results
        """
        try:
            # Get product data
            product = get_product(product_id)
            if not product:
                return {
                    "valid": False,
                    "error": "Product not found"
                }
            
            # Check if it's an exclusive product
            if not product.get("is_exclusive_single_use", False):
                return {
                    "valid": False,
                    "error": "Product is not an exclusive single-use product"
                }
            
            # Check if already purchased
            if product.get("is_purchased", False):
                purchased_by = product.get("purchased_by_user_id")
                if purchased_by == user_id:
                    return {
                        "valid": False,
                        "error": "You have already purchased this exclusive product"
                    }
                else:
                    return {
                        "valid": False,
                        "error": "This exclusive product has already been sold"
                    }
            
            # Check expiration date
            expiration_date = product.get("expiration_date")
            if expiration_date:
                # Handle string dates
                if isinstance(expiration_date, str):
                    try:
                        expiration_date = datetime.fromisoformat(expiration_date)
                    except ValueError:
                        logger.error(f"Invalid expiration date format for product {product_id}: {expiration_date}")
                        return {
                            "valid": False,
                            "error": "Invalid expiration date format"
                        }

                if datetime.now() > expiration_date:
                    return {
                        "valid": False,
                        "error": "This exclusive product has expired"
                    }
            
            # Check if file exists
            file_path = product.get("exclusive_file_path")
            if not file_path or not os.path.exists(file_path):
                return {
                    "valid": False,
                    "error": "Product file not found"
                }
            
            return {
                "valid": True,
                "product": product,
                "file_path": file_path
            }
            
        except Exception as e:
            logger.error(f"Error validating exclusive product purchase for {product_id}: {e}")
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}"
            }


    def create_exclusive_product_display(self, product: Dict[str, Any], context: str = "shop") -> str:
        """
        Create comprehensive exclusive product display for different contexts.

        Args:
            product: Product dictionary
            context: Display context (shop, cart, admin, etc.)

        Returns:
            Formatted HTML string
        """
        name = product.get("name", "Unnamed Product")
        description = product.get("description", "")
        price = product.get("price", 0.0)
        is_purchased = product.get("is_purchased", False)
        purchased_by_user_id = product.get("purchased_by_user_id")
        purchase_date = product.get("purchase_date")
        expiration_date = product.get("expiration_date")
        file_type = product.get("exclusive_file_type", "")
        file_size = product.get("exclusive_file_size", 0)
        mime_type = product.get("exclusive_file_mime_type", "")

        if context == "shop":
            # Shop display with full details
            text = ExclusiveProductTheme.create_header("EXCLUSIVE PRODUCT", "SINGLE-USE DIGITAL ITEM")
            text += f"{ExclusiveProductTheme.EMOJIS['exclusive_product']} <b>{name}</b>\n\n"
            text += f"{ExclusiveProductTheme.EMOJIS['info']} <b>Description:</b>\n<i>{ExclusiveProductTheme.truncate_description(description)}</i>\n\n"
            text += f"{ExclusiveProductTheme.format_price(price)}\n"
            text += f"{ExclusiveProductTheme.format_availability_status(is_purchased, purchased_by_user_id, expiration_date)}\n"

            if file_type and file_size:
                text += f"{ExclusiveProductTheme.format_file_info(file_type, file_size, mime_type)}\n"

            if expiration_date and not is_purchased:
                text += f"{ExclusiveProductTheme.EMOJIS['date']} <b>Available until:</b> <code>{expiration_date.strftime('%Y-%m-%d %H:%M')}</code>\n"

            text += ExclusiveProductTheme.create_section_break()
            text += f"{ExclusiveProductTheme.EMOJIS['single_use']} <i>This is an exclusive single-use product - once purchased, it becomes unavailable to all other users</i>"

        elif context == "cart":
            # Cart display with purchase info
            text = f"{ExclusiveProductTheme.EMOJIS['exclusive_product']} <b>{name}</b>\n"
            text += f"{ExclusiveProductTheme.format_price(price)}\n"
            text += f"{ExclusiveProductTheme.EMOJIS['exclusive']} <b>Type:</b> <code>Exclusive Single-Use</code>"

        elif context == "admin":
            # Admin display with management info
            text = f"{ExclusiveProductTheme.EMOJIS['exclusive_product']} <b>{name}</b>\n"
            text += f"{ExclusiveProductTheme.format_price(price)}\n"
            text += f"{ExclusiveProductTheme.format_availability_status(is_purchased, purchased_by_user_id, expiration_date)}\n"

            if file_type and file_size:
                text += f"{ExclusiveProductTheme.format_file_info(file_type, file_size, mime_type)}\n"

            if is_purchased and purchase_date:
                text += f"{ExclusiveProductTheme.EMOJIS['date']} <b>Purchased:</b> <code>{purchase_date.strftime('%Y-%m-%d %H:%M')}</code>\n"
                if purchased_by_user_id:
                    text += f"{ExclusiveProductTheme.EMOJIS['user']} <b>Buyer ID:</b> <code>{purchased_by_user_id}</code>\n"

        else:
            # Default minimal display
            text = f"{ExclusiveProductTheme.EMOJIS['exclusive_product']} <b>{name}</b>\n"
            text += f"{ExclusiveProductTheme.format_price(price)}\n"
            text += f"{ExclusiveProductTheme.format_availability_status(is_purchased, purchased_by_user_id, expiration_date)}"

        return text

    async def mark_as_purchased(self, product_id: Any, user_id: int) -> Dict[str, Any]:
        """
        Mark exclusive product as purchased by a specific user.

        Args:
            product_id: Product ID
            user_id: User ID who purchased the product

        Returns:
            Dict with operation results
        """
        try:
            # Validate the purchase first
            validation = await self.validate_exclusive_product_purchase(product_id, user_id)
            if not validation["valid"]:
                return {
                    "success": False,
                    "error": validation["error"]
                }

            # Update product as purchased
            update_data = {
                "is_purchased": True,
                "purchased_by_user_id": user_id,
                "purchase_date": datetime.now()
            }

            success = update_product(product_id, update_data)
            if success:
                logger.info(f"Marked exclusive product {product_id} as purchased by user {user_id}")
                return {
                    "success": True,
                    "product_id": product_id,
                    "user_id": user_id,
                    "purchase_date": update_data["purchase_date"]
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to update product purchase status"
                }

        except Exception as e:
            logger.error(f"Error marking exclusive product {product_id} as purchased: {e}")
            return {
                "success": False,
                "error": f"Purchase marking error: {str(e)}"
            }

    def create_exclusive_product_cart_item(self, product: Dict[str, Any], product_id: Any) -> Dict[str, Any]:
        """
        Create cart item data for exclusive products using consolidated factory.

        Args:
            product: Product dictionary
            product_id: Product ID

        Returns:
            Cart item dictionary
        """
        # Use the consolidated cart item factory
        from utils.cart_item_factory import cart_item_factory

        return cart_item_factory.create_exclusive_cart_item(
            product=product,
            product_id=product_id  # Let cart factory handle type conversion
        )


# Create global instance
exclusive_product_manager = ExclusiveProductManager()
