"""
Database indexing utilities for exclusive single-use products.
Ensures optimal query performance for exclusive product operations.
"""

import logging
from typing import Dict, Any, List
from database.operations import products_collection

logger = logging.getLogger(__name__)

class ExclusiveProductIndexManager:
    """Manages database indexes for exclusive products."""
    
    @staticmethod
    def create_exclusive_product_indexes() -> Dict[str, Any]:
        """
        Create optimized indexes for exclusive product queries.
        
        Returns:
            Dict with index creation results
        """
        try:
            results = {}
            
            # Index for exclusive product filtering
            try:
                index_result = products_collection.create_index([
                    ("is_exclusive_single_use", 1),
                    ("is_purchased", 1)
                ], name="exclusive_availability_idx")
                results["exclusive_availability"] = index_result
                logger.info("Created exclusive product availability index")
            except Exception as e:
                logger.warning(f"Failed to create exclusive availability index: {e}")
                results["exclusive_availability"] = f"Error: {e}"
            
            # Index for user purchase history
            try:
                index_result = products_collection.create_index([
                    ("purchased_by_user_id", 1),
                    ("is_exclusive_single_use", 1),
                    ("purchase_date", -1)
                ], name="user_exclusive_purchases_idx")
                results["user_purchases"] = index_result
                logger.info("Created user exclusive purchases index")
            except Exception as e:
                logger.warning(f"Failed to create user purchases index: {e}")
                results["user_purchases"] = f"Error: {e}"
            
            # Index for expiration date queries
            try:
                index_result = products_collection.create_index([
                    ("is_exclusive_single_use", 1),
                    ("expiration_date", 1),
                    ("is_purchased", 1)
                ], name="exclusive_expiration_idx")
                results["expiration"] = index_result
                logger.info("Created exclusive product expiration index")
            except Exception as e:
                logger.warning(f"Failed to create expiration index: {e}")
                results["expiration"] = f"Error: {e}"
            
            # Index for category filtering with exclusive status
            try:
                index_result = products_collection.create_index([
                    ("category_id", 1),
                    ("is_exclusive_single_use", 1),
                    ("is_purchased", 1)
                ], name="category_exclusive_idx")
                results["category_exclusive"] = index_result
                logger.info("Created category exclusive products index")
            except Exception as e:
                logger.warning(f"Failed to create category exclusive index: {e}")
                results["category_exclusive"] = f"Error: {e}"
            
            # Compound index for admin statistics
            try:
                index_result = products_collection.create_index([
                    ("is_exclusive_single_use", 1),
                    ("is_purchased", 1),
                    ("price", 1),
                    ("purchase_date", -1)
                ], name="exclusive_stats_idx")
                results["statistics"] = index_result
                logger.info("Created exclusive product statistics index")
            except Exception as e:
                logger.warning(f"Failed to create statistics index: {e}")
                results["statistics"] = f"Error: {e}"
            
            return {
                "success": True,
                "indexes_created": results,
                "total_indexes": len(results)
            }
            
        except Exception as e:
            logger.error(f"Error creating exclusive product indexes: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    @staticmethod
    def list_exclusive_product_indexes() -> List[Dict[str, Any]]:
        """
        List all indexes related to exclusive products.
        
        Returns:
            List of index information
        """
        try:
            all_indexes = products_collection.list_indexes()
            exclusive_indexes = []
            
            for index in all_indexes:
                index_name = index.get("name", "")
                if "exclusive" in index_name.lower():
                    exclusive_indexes.append({
                        "name": index_name,
                        "key": index.get("key", {}),
                        "unique": index.get("unique", False),
                        "sparse": index.get("sparse", False)
                    })
            
            return exclusive_indexes
            
        except Exception as e:
            logger.error(f"Error listing exclusive product indexes: {e}")
            return []
    
    @staticmethod
    def drop_exclusive_product_indexes() -> Dict[str, Any]:
        """
        Drop all exclusive product indexes (for maintenance).
        
        Returns:
            Dict with drop results
        """
        try:
            exclusive_indexes = ExclusiveProductIndexManager.list_exclusive_product_indexes()
            results = {}
            
            for index in exclusive_indexes:
                index_name = index["name"]
                if index_name != "_id_":  # Don't drop the default _id index
                    try:
                        products_collection.drop_index(index_name)
                        results[index_name] = "Dropped successfully"
                        logger.info(f"Dropped exclusive product index: {index_name}")
                    except Exception as e:
                        results[index_name] = f"Error: {e}"
                        logger.warning(f"Failed to drop index {index_name}: {e}")
            
            return {
                "success": True,
                "dropped_indexes": results
            }
            
        except Exception as e:
            logger.error(f"Error dropping exclusive product indexes: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    @staticmethod
    def optimize_exclusive_product_queries() -> Dict[str, Any]:
        """
        Analyze and optimize exclusive product query performance.
        
        Returns:
            Dict with optimization results
        """
        try:
            # Create indexes if they don't exist
            index_results = ExclusiveProductIndexManager.create_exclusive_product_indexes()
            
            # Get current index statistics
            current_indexes = ExclusiveProductIndexManager.list_exclusive_product_indexes()
            
            return {
                "success": True,
                "index_creation": index_results,
                "current_indexes": len(current_indexes),
                "optimization_complete": True
            }
            
        except Exception as e:
            logger.error(f"Error optimizing exclusive product queries: {e}")
            return {
                "success": False,
                "error": str(e)
            }

# Initialize indexes on module import
def initialize_exclusive_product_indexes():
    """Initialize exclusive product indexes if they don't exist."""
    try:
        manager = ExclusiveProductIndexManager()
        result = manager.create_exclusive_product_indexes()
        if result.get("success"):
            logger.info("Exclusive product indexes initialized successfully")
        else:
            logger.warning(f"Failed to initialize some exclusive product indexes: {result}")
    except Exception as e:
        logger.error(f"Error initializing exclusive product indexes: {e}")

# Create a global instance
exclusive_index_manager = ExclusiveProductIndexManager()
