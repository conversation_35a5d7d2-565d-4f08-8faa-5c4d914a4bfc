"""
Payment metrics tracking module.

This module provides functionality for tracking and analyzing payment-related
metrics and statistics for business intelligence and monitoring.
"""

import logging
import asyncio
import json
from datetime import datetime
from typing import Dict, Any, Optional, Union, List

# Optional: import your database connection if needed
# from database.db import get_db_connection

# Configure logging
logger = logging.getLogger(__name__)

# In-memory metrics store for real-time stats (can be replaced with Redis or similar)
_payment_metrics = {
    "daily_totals": {},
    "success_count": 0,
    "failure_count": 0,
    "total_volume": 0.0,
    "last_update": None,
}


async def record_payment_completion(
    user_id: int, amount: float, status: str, metadata: Optional[Dict[str, Any]] = None
) -> bool:
    """
    Record payment completion metrics.

    Args:
        user_id: The user ID associated with the payment
        amount: The payment amount
        status: Status of the payment (success, failed, etc.)
        metadata: Additional metadata about the transaction

    Returns:
        bool: True if metrics were recorded successfully
    """
    try:
        # Get current date for daily aggregation
        today = datetime.now().strftime("%Y-%m-%d")

        # Update in-memory metrics
        _payment_metrics["last_update"] = datetime.now().isoformat()

        if status == "success":
            _payment_metrics["success_count"] += 1
            _payment_metrics["total_volume"] += amount

            # Update daily totals
            if today not in _payment_metrics["daily_totals"]:
                _payment_metrics["daily_totals"][today] = {
                    "count": 0,
                    "volume": 0.0,
                    "users": set(),
                }

            _payment_metrics["daily_totals"][today]["count"] += 1
            _payment_metrics["daily_totals"][today]["volume"] += amount
            _payment_metrics["daily_totals"][today]["users"].add(user_id)

        else:
            _payment_metrics["failure_count"] += 1

        # Prepare data for persistent storage
        metrics_data = {
            "user_id": user_id,
            "amount": amount,
            "status": status,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {},
        }

        # Schedule async write to database or analytics service
        asyncio.create_task(_store_metrics(metrics_data))

        logger.info(
            f"Payment metrics recorded: {status} payment of {amount} for user {user_id}",
            extra={"user_id": user_id, "amount": amount, "status": status},
        )

        return True

    except Exception as e:
        logger.error(f"Failed to record payment metrics: {str(e)}", exc_info=True)
        return False


async def _store_metrics(metrics_data: Dict[str, Any]) -> None:
    """
    Store metrics data in persistent storage.

    This function runs asynchronously to avoid blocking the main thread.
    Implement your storage logic here (database, file, external API, etc.)
    """
    try:
        # Example: Write to a log file
        with open("payment_metrics.jsonl", "a") as f:
            f.write(json.dumps(metrics_data, ensure_ascii=True) + "\n")

        # Example: Store in database (commented out)
        # async with get_db_connection() as conn:
        #     await conn.execute(
        #         "INSERT INTO payment_metrics (user_id, amount, status, timestamp, metadata) "
        #         "VALUES (?, ?, ?, ?, ?)",
        #         metrics_data["user_id"],
        #         metrics_data["amount"],
        #         metrics_data["status"],
        #         metrics_data["timestamp"],
        #         json.dumps(metrics_data["metadata"], ensure_ascii=True)
        #     )
        #     await conn.commit()

    except Exception as e:
        logger.error(f"Failed to store metrics data: {str(e)}", exc_info=True)


# API functions to retrieve metrics


async def get_payment_stats(
    start_date: Optional[str] = None, end_date: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get payment statistics for the specified date range.

    Args:
        start_date: Start date in YYYY-MM-DD format (inclusive)
        end_date: End date in YYYY-MM-DD format (inclusive)

    Returns:
        Dict containing payment statistics
    """
    # Default to today's metrics if no dates provided
    if not start_date and not end_date:
        today = datetime.now().strftime("%Y-%m-%d")
        daily_stats = _payment_metrics["daily_totals"].get(
            today, {"count": 0, "volume": 0.0, "users": set()}
        )

        return {
            "period": "today",
            "success_count": daily_stats["count"],
            "total_volume": daily_stats["volume"],
            "unique_users": len(daily_stats["users"]),
            "average_payment": (
                daily_stats["volume"] / daily_stats["count"]
                if daily_stats["count"] > 0
                else 0
            ),
        }

    # Implementation for date range queries would go here
    # This would typically involve querying the database

    return {
        "period": f"{start_date} to {end_date}",
        "success_count": _payment_metrics["success_count"],
        "total_volume": _payment_metrics["total_volume"],
        "failure_count": _payment_metrics["failure_count"],
    }


# Utility function to reset metrics (for testing or maintenance)
async def reset_metrics() -> None:
    """Reset all in-memory metrics."""
    global _payment_metrics
    _payment_metrics = {
        "daily_totals": {},
        "success_count": 0,
        "failure_count": 0,
        "total_volume": 0.0,
        "last_update": datetime.now().isoformat(),
    }
    logger.info("Payment metrics have been reset")
