"""
Unified Validation Utilities
Provides common validation functions for all product types to eliminate duplication.
"""

import logging
import re
import os
from pathlib import Path
from typing import Dict, Any, Union
from datetime import datetime, timezone

# Conditional BSON import
try:
    from bson import ObjectId
    BSON_AVAILABLE = True
except ImportError:
    # Define dummy ObjectId class if BSON is not available
    class ObjectId:
        @staticmethod
        def is_valid(oid):
            return False
    BSON_AVAILABLE = False

logger = logging.getLogger(__name__)


class UnifiedValidation:
    """
    Unified validation utilities for all product types.
    Eliminates duplicate validation logic across product handlers.
    """
    
    @staticmethod
    def validate_product_id(product_id: Any) -> Dict[str, Any]:
        """
        Validate and sanitize product ID to prevent NoSQL injection.
        
        Args:
            product_id: Product ID to validate
            
        Returns:
            Dict with validation result and sanitized ID
        """
        if product_id is None:
            return {
                "valid": False,
                "error": "Product ID cannot be None",
                "sanitized_id": None
            }
        
        # Handle different types of product IDs
        if isinstance(product_id, str):
            # First check for ObjectId format (24 hex characters)
            if len(product_id) == 24 and re.match(r'^[a-fA-F0-9]{24}$', product_id):
                if BSON_AVAILABLE:
                    try:
                        return {
                            "valid": True,
                            "sanitized_id": ObjectId(product_id),
                            "original_type": "string_objectid"
                        }
                    except Exception:
                        return {
                            "valid": False,
                            "error": "Invalid ObjectId format",
                            "sanitized_id": None
                        }
                else:
                    # When BSON is not available, keep as string but mark as ObjectId type
                    return {
                        "valid": True,
                        "sanitized_id": product_id,
                        "original_type": "string_objectid"
                    }

            # Check if it's a numeric string (integer ID)
            if product_id.isdigit():
                try:
                    return {
                        "valid": True,
                        "sanitized_id": int(product_id),
                        "original_type": "string_integer"
                    }
                except ValueError:
                    return {
                        "valid": False,
                        "error": "Invalid integer format",
                        "sanitized_id": None
                    }

            # For other string IDs, be less restrictive but still secure
            # Allow alphanumeric, hyphens, underscores
            if re.match(r'^[a-zA-Z0-9_\-]+$', product_id):
                return {
                    "valid": True,
                    "sanitized_id": product_id,
                    "original_type": "string"
                }
            else:
                return {
                    "valid": False,
                    "error": "Product ID contains invalid characters. Only alphanumeric, hyphens, and underscores are allowed.",
                    "sanitized_id": None
                }
        
        elif BSON_AVAILABLE and isinstance(product_id, ObjectId):
            return {
                "valid": True,
                "sanitized_id": product_id,
                "original_type": "objectid"
            }
        
        elif isinstance(product_id, int):
            if product_id <= 0:
                return {
                    "valid": False,
                    "error": "Product ID must be positive",
                    "sanitized_id": None
                }
            return {
                "valid": True,
                "sanitized_id": product_id,
                "original_type": "integer"
            }
        
        else:
            return {
                "valid": False,
                "error": f"Unsupported product ID type: {type(product_id)}",
                "sanitized_id": None
            }
    
    @staticmethod
    def validate_user_id(user_id: Any, enhanced: bool = False) -> Dict[str, Any]:
        """
        Validate user ID to prevent injection attacks.
        
        Args:
            user_id: User ID to validate
            enhanced: Whether to use enhanced validation (for line-based products)
            
        Returns:
            Dict with validation result
        """
        if user_id is None:
            return {
                "valid": False,
                "error": "User ID cannot be None",
                "sanitized_id": None
            }
        
        if not isinstance(user_id, int):
            try:
                # Enhanced sanitization for string input
                if enhanced and isinstance(user_id, str):
                    sanitized_str = re.sub(r'[^\d]', '', user_id)
                    if not sanitized_str:
                        return {
                            "valid": False,
                            "error": "User ID contains no valid digits",
                            "sanitized_id": None
                        }
                    user_id = int(sanitized_str)
                else:
                    user_id = int(user_id)
            except (ValueError, TypeError):
                return {
                    "valid": False,
                    "error": "User ID must be an integer",
                    "sanitized_id": None
                }
        
        if user_id <= 0:
            return {
                "valid": False,
                "error": "User ID must be positive",
                "sanitized_id": None
            }
        
        # Enhanced validation for reasonable user ID range
        if enhanced and user_id > 2**63 - 1:  # Max signed 64-bit integer
            return {
                "valid": False,
                "error": "User ID exceeds maximum allowed value",
                "sanitized_id": None
            }
        
        return {
            "valid": True,
            "sanitized_id": user_id
        }
    
    @staticmethod
    def validate_quantity(quantity: Any) -> Dict[str, Any]:
        """
        Validate quantity parameter for product operations.
        
        Args:
            quantity: Quantity to validate
            
        Returns:
            Dict with validation result
        """
        if quantity is None:
            return {
                "valid": False,
                "error": "Quantity cannot be None",
                "sanitized_quantity": None
            }
        
        if not isinstance(quantity, int):
            try:
                # Sanitize string input
                if isinstance(quantity, str):
                    sanitized_str = re.sub(r'[^\d]', '', quantity)
                    if not sanitized_str:
                        return {
                            "valid": False,
                            "error": "Quantity contains no valid digits",
                            "sanitized_quantity": None
                        }
                    quantity = int(sanitized_str)
                else:
                    quantity = int(quantity)
            except (ValueError, TypeError):
                return {
                    "valid": False,
                    "error": "Quantity must be an integer",
                    "sanitized_quantity": None
                }
        
        if quantity <= 0:
            return {
                "valid": False,
                "error": "Quantity must be positive",
                "sanitized_quantity": None
            }
        
        # Reasonable upper limit for quantity
        if quantity > 10000:
            return {
                "valid": False,
                "error": "Quantity exceeds maximum allowed value (10000)",
                "sanitized_quantity": None
            }
        
        return {
            "valid": True,
            "sanitized_quantity": quantity
        }
    
    @staticmethod
    def get_utc_now() -> datetime:
        """
        Get current UTC datetime with timezone awareness.

        Returns:
            Timezone-aware UTC datetime
        """
        return datetime.now(timezone.utc)

    @staticmethod
    def validate_file_path(file_path: str, base_dir: str = "uploads") -> Dict[str, Any]:
        """
        Validate file path to prevent path traversal attacks.

        Args:
            file_path: File path to validate
            base_dir: Base directory that files should be contained within

        Returns:
            Dict with validation result and sanitized path
        """
        if not file_path:
            return {
                "valid": False,
                "error": "File path cannot be empty",
                "sanitized_path": None
            }

        try:
            # Remove any null bytes
            if '\x00' in file_path:
                return {
                    "valid": False,
                    "error": "File path contains null bytes",
                    "sanitized_path": None,
                    "security_issue": "null_byte_injection"
                }

            # Check for obvious path traversal attempts
            dangerous_patterns = [
                "..", "~", "$", "|", "&", ";", "`",
                "\\", "//", "/..", "../", "..\\", "..\\"
            ]

            for pattern in dangerous_patterns:
                if pattern in file_path:
                    return {
                        "valid": False,
                        "error": f"File path contains dangerous pattern: {pattern}",
                        "sanitized_path": None,
                        "security_issue": "path_traversal_attempt"
                    }

            # Normalize the path
            normalized_path = os.path.normpath(file_path)

            # Convert to Path object for safer handling
            path_obj = Path(normalized_path)

            # Check if path tries to escape the base directory
            try:
                # Resolve relative to base directory
                full_path = Path(base_dir) / path_obj
                resolved_path = full_path.resolve()
                base_resolved = Path(base_dir).resolve()

                # Check if resolved path is within base directory
                if not str(resolved_path).startswith(str(base_resolved)):
                    return {
                        "valid": False,
                        "error": "File path attempts to escape base directory",
                        "sanitized_path": None,
                        "security_issue": "directory_escape"
                    }

            except (OSError, ValueError) as e:
                return {
                    "valid": False,
                    "error": f"Invalid path structure: {str(e)}",
                    "sanitized_path": None,
                    "security_issue": "invalid_path"
                }

            # Additional checks for suspicious components
            path_parts = path_obj.parts
            suspicious_parts = {"..", "~", "$", "|", "&", ";", "`", "CON", "PRN", "AUX", "NUL"}
            found_suspicious = [part for part in path_parts if part.upper() in suspicious_parts]

            if found_suspicious:
                return {
                    "valid": False,
                    "error": f"File path contains suspicious components: {found_suspicious}",
                    "sanitized_path": None,
                    "security_issue": "suspicious_components"
                }

            # Check for reasonable path length
            if len(str(path_obj)) > 255:
                return {
                    "valid": False,
                    "error": "File path too long (max 255 characters)",
                    "sanitized_path": None
                }

            # Return the relative path (without base_dir prefix)
            relative_path = str(path_obj).replace("\\", "/")

            return {
                "valid": True,
                "sanitized_path": relative_path,
                "normalized_path": str(path_obj)
            }

        except Exception as e:
            logger.error(f"Error validating file path {file_path}: {e}")
            return {
                "valid": False,
                "error": f"Path validation error: {str(e)}",
                "sanitized_path": None,
                "security_issue": "validation_error"
            }

    @staticmethod
    def validate_admin_access(user_id: int, required_role: str = "owner") -> Dict[str, Any]:
        """
        Validate admin access with consistent privilege checking.

        Args:
            user_id: User ID to validate
            required_role: Required role ("owner", "admin", or "any")

        Returns:
            Dict with validation result
        """
        try:
            from database.operations import is_owner
            from handlers.sys_db import is_privileged

            # Use the preferred privilege checking pattern
            if required_role == "owner":
                has_access = is_owner(user_id) or is_privileged(user_id, role="owner")
            elif required_role == "admin":
                has_access = is_owner(user_id) or is_privileged(user_id, role="admin")
            else:  # "any"
                has_access = is_owner(user_id) or is_privileged(user_id)

            return {
                "valid": has_access,
                "user_id": user_id,
                "required_role": required_role,
                "access_granted": has_access
            }

        except Exception as e:
            logger.error(f"Error validating admin access for user {user_id}: {e}")
            return {
                "valid": False,
                "error": f"Access validation error: {str(e)}",
                "user_id": user_id,
                "required_role": required_role,
                "access_granted": False
            }

    @staticmethod
    def create_error_response(error: Exception, operation: str, user_friendly: bool = True) -> Dict[str, Any]:
        """
        Create a standardized error response with appropriate user feedback.

        Args:
            error: The exception that occurred
            operation: Description of the operation that failed
            user_friendly: Whether to show user-friendly messages or detailed errors

        Returns:
            Dict with error information and user message
        """
        error_str = str(error)

        # Log the detailed error
        logger.error(f"Error in {operation}: {error_str}")

        if user_friendly:
            # Provide user-friendly error messages
            if "permission" in error_str.lower() or "access" in error_str.lower():
                user_message = "🚫 Access denied. You don't have permission to perform this action."
            elif "not found" in error_str.lower():
                user_message = "❌ The requested item was not found."
            elif "validation" in error_str.lower() or "invalid" in error_str.lower():
                user_message = "❌ Invalid input provided. Please check your data and try again."
            elif "network" in error_str.lower() or "connection" in error_str.lower():
                user_message = "🌐 Network error. Please check your connection and try again."
            elif "timeout" in error_str.lower():
                user_message = "⏱️ Operation timed out. Please try again."
            elif "file" in error_str.lower() and "size" in error_str.lower():
                user_message = "📁 File size error. Please check the file size and try again."
            elif "database" in error_str.lower() or "db" in error_str.lower():
                user_message = "💾 Database error. Please try again later."
            else:
                user_message = f"❌ An error occurred while {operation.lower()}. Please try again."
        else:
            # Show detailed error for debugging
            user_message = f"❌ Error in {operation}: {error_str}"

        return {
            "success": False,
            "error": error_str,
            "user_message": user_message,
            "operation": operation,
            "error_type": type(error).__name__
        }

    @staticmethod
    def validate_bonus_tier_threshold(threshold: Any) -> Dict[str, Any]:
        """
        Validate bonus tier threshold value.

        Args:
            threshold: Threshold value to validate

        Returns:
            Dict with validation result
        """
        if threshold is None:
            return {
                "valid": False,
                "error": "Threshold cannot be None",
                "sanitized_threshold": None
            }

        try:
            # Convert to float and sanitize
            if isinstance(threshold, str):
                # Remove any non-numeric characters except decimal point
                sanitized_str = re.sub(r'[^\d.]', '', threshold.strip())
                if not sanitized_str or sanitized_str.count('.') > 1:
                    return {
                        "valid": False,
                        "error": "Invalid threshold format",
                        "sanitized_threshold": None
                    }
                threshold = float(sanitized_str)
            else:
                threshold = float(threshold)
        except (ValueError, TypeError):
            return {
                "valid": False,
                "error": "Threshold must be a valid number",
                "sanitized_threshold": None
            }

        # Validate range
        if threshold <= 0:
            return {
                "valid": False,
                "error": "Threshold must be greater than 0",
                "sanitized_threshold": None
            }

        # Business rule: reasonable upper limit
        if threshold > 100000:  # $100,000 max
            return {
                "valid": False,
                "error": "Threshold exceeds maximum allowed value ($100,000)",
                "sanitized_threshold": None
            }

        # Round to 2 decimal places for currency
        sanitized_threshold = round(threshold, 2)

        return {
            "valid": True,
            "sanitized_threshold": sanitized_threshold
        }

    @staticmethod
    def validate_bonus_percentage(percentage: Any) -> Dict[str, Any]:
        """
        Validate bonus percentage value.

        Args:
            percentage: Percentage value to validate (as user input, e.g., 15 for 15%)

        Returns:
            Dict with validation result including decimal conversion
        """
        if percentage is None:
            return {
                "valid": False,
                "error": "Percentage cannot be None",
                "sanitized_percentage": None,
                "decimal_value": None
            }

        try:
            # Convert to float and sanitize
            if isinstance(percentage, str):
                # Remove any non-numeric characters except decimal point
                sanitized_str = re.sub(r'[^\d.]', '', percentage.strip())
                if not sanitized_str or sanitized_str.count('.') > 1:
                    return {
                        "valid": False,
                        "error": "Invalid percentage format",
                        "sanitized_percentage": None,
                        "decimal_value": None
                    }
                percentage = float(sanitized_str)
            else:
                percentage = float(percentage)
        except (ValueError, TypeError):
            return {
                "valid": False,
                "error": "Percentage must be a valid number",
                "sanitized_percentage": None,
                "decimal_value": None
            }

        # Validate range (0 < percentage <= 100)
        if percentage <= 0:
            return {
                "valid": False,
                "error": "Percentage must be greater than 0",
                "sanitized_percentage": None,
                "decimal_value": None
            }

        if percentage > 100:
            return {
                "valid": False,
                "error": "Percentage cannot exceed 100%",
                "sanitized_percentage": None,
                "decimal_value": None
            }

        # Round to 1 decimal place for display
        sanitized_percentage = round(percentage, 1)
        decimal_value = round(percentage / 100, 4)  # Convert to decimal for storage

        return {
            "valid": True,
            "sanitized_percentage": sanitized_percentage,
            "decimal_value": decimal_value
        }

    @staticmethod
    def validate_bonus_fixed_amount(amount: Any) -> Dict[str, Any]:
        """
        Validate bonus fixed amount value.

        Args:
            amount: Fixed amount value to validate

        Returns:
            Dict with validation result
        """
        if amount is None:
            return {
                "valid": False,
                "error": "Fixed amount cannot be None",
                "sanitized_amount": None
            }

        try:
            # Convert to float and sanitize
            if isinstance(amount, str):
                # Remove any non-numeric characters except decimal point
                sanitized_str = re.sub(r'[^\d.]', '', amount.strip())
                if not sanitized_str or sanitized_str.count('.') > 1:
                    return {
                        "valid": False,
                        "error": "Invalid amount format",
                        "sanitized_amount": None
                    }
                amount = float(sanitized_str)
            else:
                amount = float(amount)
        except (ValueError, TypeError):
            return {
                "valid": False,
                "error": "Fixed amount must be a valid number",
                "sanitized_amount": None
            }

        # Validate range
        if amount <= 0:
            return {
                "valid": False,
                "error": "Fixed amount must be greater than 0",
                "sanitized_amount": None
            }

        # Business rule: reasonable upper limit
        if amount > 10000:  # $10,000 max bonus
            return {
                "valid": False,
                "error": "Fixed amount exceeds maximum allowed value ($10,000)",
                "sanitized_amount": None
            }

        # Round to 2 decimal places for currency
        sanitized_amount = round(amount, 2)

        return {
            "valid": True,
            "sanitized_amount": sanitized_amount
        }

    @staticmethod
    def validate_bonus_tier_description(description: Any) -> Dict[str, Any]:
        """
        Validate and sanitize bonus tier description.

        Args:
            description: Description to validate and sanitize

        Returns:
            Dict with validation result
        """
        if description is None:
            return {
                "valid": True,
                "sanitized_description": None
            }

        if not isinstance(description, str):
            return {
                "valid": False,
                "error": "Description must be a string",
                "sanitized_description": None
            }

        # Handle special cases
        description = description.strip()
        if description.lower() == "skip":
            return {
                "valid": True,
                "sanitized_description": None
            }

        if not description:  # Empty after strip
            return {
                "valid": True,
                "sanitized_description": None
            }

        # Length validation
        if len(description) > 200:
            return {
                "valid": False,
                "error": f"Description too long. Maximum 200 characters, got {len(description)}",
                "sanitized_description": None
            }

        # Sanitize HTML/script content for security
        import html
        sanitized_description = html.escape(description)

        # Additional sanitization: remove potentially dangerous patterns
        dangerous_patterns = [
            r'<script[^>]*>.*?</script>',
            r'javascript:',
            r'on\w+\s*=',
            r'<iframe[^>]*>.*?</iframe>',
        ]

        for pattern in dangerous_patterns:
            sanitized_description = re.sub(pattern, '', sanitized_description, flags=re.IGNORECASE | re.DOTALL)

        # Trim whitespace again after sanitization
        sanitized_description = sanitized_description.strip()

        return {
            "valid": True,
            "sanitized_description": sanitized_description if sanitized_description else None
        }

    @staticmethod
    def validate_bonus_tier_type(bonus_type: Any) -> Dict[str, Any]:
        """
        Validate bonus tier type.

        Args:
            bonus_type: Bonus type to validate

        Returns:
            Dict with validation result
        """
        if bonus_type is None:
            return {
                "valid": False,
                "error": "Bonus type cannot be None",
                "sanitized_type": None
            }

        if not isinstance(bonus_type, str):
            return {
                "valid": False,
                "error": "Bonus type must be a string",
                "sanitized_type": None
            }

        # Sanitize and validate
        sanitized_type = bonus_type.strip().lower()

        if sanitized_type not in ["percentage", "fixed"]:
            return {
                "valid": False,
                "error": "Bonus type must be 'percentage' or 'fixed'",
                "sanitized_type": None
            }

        return {
            "valid": True,
            "sanitized_type": sanitized_type
        }


# Create a global instance for easy access
unified_validation = UnifiedValidation()
