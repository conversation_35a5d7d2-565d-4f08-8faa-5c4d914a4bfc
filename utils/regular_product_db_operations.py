"""
Consolidated Database Operations for Regular Products
Provides optimized database operations with unified monitoring and validation.
"""

import logging
import time
import re
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone
from bson import ObjectId

from database.operations import (
    get_product,
    update_product,
    add_transaction
)
from utils.unified_product_performance import (
    unified_product_cache,
    unified_product_performance_monitor,
    ProductType,
    get_product_with_monitoring,
    invalidate_product_cache
)
from utils.unified_validation import unified_validation

logger = logging.getLogger(__name__)


class RegularProductDBOperations:
    """Consolidated database operations for regular products with unified monitoring."""
    
    @staticmethod
    def _validate_product_id(product_id: Any) -> Dict[str, Any]:
        """
        Validate and sanitize product ID to prevent NoSQL injection.
        Uses unified validation to eliminate duplication.

        Args:
            product_id: Product ID to validate

        Returns:
            Dict with validation result and sanitized ID
        """
        return unified_validation.validate_product_id(product_id)
    
    @staticmethod
    def _validate_user_id(user_id: Any) -> Dict[str, Any]:
        """
        Validate user ID to prevent injection attacks.
        Uses unified validation to eliminate duplication.

        Args:
            user_id: User ID to validate

        Returns:
            Dict with validation result
        """
        return unified_validation.validate_user_id(user_id)
    
    @staticmethod
    def _get_utc_now() -> datetime:
        """
        Get current UTC datetime with timezone awareness.
        Uses unified validation to eliminate duplication.

        Returns:
            Timezone-aware UTC datetime
        """
        return unified_validation.get_utc_now()
    
    @staticmethod
    def get_regular_product_with_validation(product_id: Any) -> Optional[Dict[str, Any]]:
        """
        Get regular product with validation and monitoring.
        
        Args:
            product_id: Product ID
            
        Returns:
            Product dict if valid regular product, None otherwise
        """
        start_time = time.time()
        operation_name = "get_regular_product_with_validation"
        
        try:
            # Validate and sanitize product ID
            validation = RegularProductDBOperations._validate_product_id(product_id)
            if not validation["valid"]:
                logger.warning(f"Invalid product ID: {validation['error']}")
                unified_product_performance_monitor.record_error(operation_name, ProductType.REGULAR)
                return None
            
            sanitized_product_id = validation["sanitized_id"]
            
            # Use unified monitoring for product retrieval (preserve type)
            product = get_product_with_monitoring(sanitized_product_id, ProductType.REGULAR)
            if not product:
                logger.warning(f"Product {sanitized_product_id} not found")
                duration = time.time() - start_time
                unified_product_performance_monitor.record_query_time(operation_name, duration, ProductType.REGULAR)
                return None
                
            # Verify it's a regular product (not exclusive or line-based)
            if product.get("is_exclusive_single_use", False) or product.get("is_line_based", False):
                logger.warning(f"Product {sanitized_product_id} is not a regular product")
                unified_product_performance_monitor.record_error(f"{operation_name}_not_regular", ProductType.REGULAR)
                return None
                
            # Record successful operation
            duration = time.time() - start_time
            unified_product_performance_monitor.record_query_time(operation_name, duration, ProductType.REGULAR)
            return product
            
        except Exception as e:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_error(operation_name, ProductType.REGULAR)
            logger.error(f"Error getting regular product {product_id}: {e}")
            return None
    
    @staticmethod
    def process_regular_purchase_with_monitoring(product_id: Any, quantity: int, user_id: int, order_number: Optional[int] = None) -> Dict[str, Any]:
        """
        Process regular product purchase with comprehensive validation and monitoring.
        
        Args:
            product_id: Product ID
            quantity: Quantity to purchase
            user_id: User ID making the purchase
            order_number: Optional order number for tracking
            
        Returns:
            Dict with operation result and details
        """
        start_time = time.time()
        operation_name = "process_regular_purchase_with_monitoring"
        
        try:
            # Validate inputs
            product_validation = RegularProductDBOperations._validate_product_id(product_id)
            if not product_validation["valid"]:
                logger.error(f"Invalid product ID in process_regular_purchase: {product_validation['error']}")
                return {
                    "success": False,
                    "error": f"Invalid product ID: {product_validation['error']}",
                    "error_code": "INVALID_PRODUCT_ID"
                }
            
            user_validation = RegularProductDBOperations._validate_user_id(user_id)
            if not user_validation["valid"]:
                logger.error(f"Invalid user ID in process_regular_purchase: {user_validation['error']}")
                return {
                    "success": False,
                    "error": f"Invalid user ID: {user_validation['error']}",
                    "error_code": "INVALID_USER_ID"
                }
            
            if not isinstance(quantity, int) or quantity <= 0:
                return {
                    "success": False,
                    "error": "Quantity must be a positive integer",
                    "error_code": "INVALID_QUANTITY"
                }
            
            sanitized_product_id = product_validation["sanitized_id"]
            sanitized_user_id = user_validation["sanitized_id"]
            
            # Get product details
            product = RegularProductDBOperations.get_regular_product_with_validation(sanitized_product_id)
            if not product:
                return {
                    "success": False,
                    "error": "Product not found or not a regular product",
                    "error_code": "PRODUCT_NOT_FOUND"
                }
            
            # Calculate total price
            unit_price = product.get("price", 0.0)
            total_price = unit_price * quantity
            
            # Record transaction
            try:
                add_transaction(
                    sanitized_user_id,
                    "regular_purchase",
                    total_price,
                    product_id=sanitized_product_id,
                    product_name=product.get("name", "Regular Product"),
                    quantity=quantity,
                    status="completed",
                    timestamp=RegularProductDBOperations._get_utc_now(),
                    product_type="regular",
                    order_number=order_number
                )
            except Exception as e:
                logger.warning(f"Failed to record transaction for regular product {sanitized_product_id}: {e}")
            
            duration = time.time() - start_time
            unified_product_performance_monitor.record_query_time(operation_name, duration, ProductType.REGULAR)
            
            return {
                "success": True,
                "product_id": sanitized_product_id,
                "product_name": product.get("name", "Regular Product"),
                "quantity_purchased": quantity,
                "unit_price": unit_price,
                "total_price": total_price,
                "user_id": sanitized_user_id,
                "order_number": order_number,
                "operation_duration": duration,
                "timestamp": RegularProductDBOperations._get_utc_now()
            }
                
        except Exception as e:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_error(operation_name, ProductType.REGULAR)
            logger.error(f"Error processing regular purchase for product {product_id}: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "error_code": "EXCEPTION",
                "operation_duration": duration
            }
    
    @staticmethod
    def check_regular_product_availability(product_id: Any) -> Dict[str, Any]:
        """
        Check availability status for regular product.
        
        Args:
            product_id: Product ID
            
        Returns:
            Dict with availability status and details
        """
        start_time = time.time()
        operation_name = "check_regular_product_availability"
        
        try:
            # Validate product ID
            validation = RegularProductDBOperations._validate_product_id(product_id)
            if not validation["valid"]:
                logger.warning(f"Invalid product ID: {validation['error']}")
                return {
                    "success": False,
                    "error": f"Invalid product ID: {validation['error']}",
                    "error_code": "INVALID_PRODUCT_ID"
                }
            
            sanitized_product_id = validation["sanitized_id"]
            
            # Get product with monitoring (preserve type)
            product = get_product_with_monitoring(sanitized_product_id, ProductType.REGULAR)
            if not product:
                duration = time.time() - start_time
                unified_product_performance_monitor.record_query_time(operation_name, duration, ProductType.REGULAR)
                return {
                    "success": False,
                    "error": "Product not found",
                    "error_code": "PRODUCT_NOT_FOUND"
                }
            
            # Verify it's a regular product
            if product.get("is_exclusive_single_use", False) or product.get("is_line_based", False):
                unified_product_performance_monitor.record_error(f"{operation_name}_not_regular", ProductType.REGULAR)
                return {
                    "success": False,
                    "error": "Product is not a regular product",
                    "error_code": "NOT_REGULAR_PRODUCT"
                }
            
            # Regular products are generally always available unless explicitly disabled
            is_available = product.get("is_active", True) and not product.get("is_disabled", False)
            
            duration = time.time() - start_time
            unified_product_performance_monitor.record_query_time(operation_name, duration, ProductType.REGULAR)
            
            return {
                "success": True,
                "product_id": sanitized_product_id,
                "product_name": product.get("name") or "Unknown",
                "availability": {
                    "is_available": is_available,
                    "is_active": product.get("is_active", True),
                    "is_disabled": product.get("is_disabled", False),
                    "unlimited_stock": True,  # Regular products typically have unlimited stock
                    "price": product.get("price", 0.0)
                },
                "operation_duration": duration,
                "timestamp": RegularProductDBOperations._get_utc_now()
            }
            
        except Exception as e:
            duration = time.time() - start_time
            unified_product_performance_monitor.record_error(operation_name, ProductType.REGULAR)
            logger.error(f"Error checking availability for product {product_id}: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "error_code": "EXCEPTION",
                "operation_duration": duration
            }
