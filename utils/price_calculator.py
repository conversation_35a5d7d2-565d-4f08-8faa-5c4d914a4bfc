"""
Price Calculator Module
Handles all monetary calculations with proper precision using Decimal arithmetic.
Prevents floating-point precision errors in financial calculations.
"""

import logging
from decimal import Decimal, ROUND_HALF_UP, InvalidOperation
from typing import Union, Dict, Any, Optional

logger = logging.getLogger(__name__)

# Price calculation constants
DECIMAL_PLACES = 2  # Standard currency precision
ROUNDING_MODE = ROUND_HALF_UP  # Standard rounding for currency


class PriceCalculationError(Exception):
    """Custom exception for price calculation errors."""
    pass


class PriceCalculator:
    """
    Handles all price calculations with proper decimal precision.
    Ensures financial accuracy and prevents floating-point errors.
    """
    
    @staticmethod
    def to_decimal(value: Union[str, int, float, Decimal]) -> Decimal:
        """
        Convert various numeric types to Decimal with proper error handling.
        
        Args:
            value: Numeric value to convert
            
        Returns:
            Decimal representation of the value
            
        Raises:
            PriceCalculationError: If conversion fails
        """
        try:
            if isinstance(value, Decimal):
                return value
            elif isinstance(value, (int, float)):
                return Decimal(str(value))  # Convert to string first to avoid float precision issues
            elif isinstance(value, str):
                return Decimal(value)
            else:
                raise PriceCalculationError(f"Cannot convert {type(value)} to Decimal: {value}")
        except (InvalidOperation, ValueError) as e:
            raise PriceCalculationError(f"Invalid numeric value: {value} - {str(e)}")
    
    @staticmethod
    def round_currency(amount: Union[str, int, float, Decimal]) -> Decimal:
        """
        Round amount to standard currency precision.
        
        Args:
            amount: Amount to round
            
        Returns:
            Rounded Decimal amount
        """
        decimal_amount = PriceCalculator.to_decimal(amount)
        return decimal_amount.quantize(Decimal('0.01'), rounding=ROUNDING_MODE)
    
    @staticmethod
    def calculate_line_price(product: Dict[str, Any]) -> Decimal:
        """
        Calculate line price with fallback to base price using Decimal arithmetic.
        
        Args:
            product: Product dictionary
            
        Returns:
            Line price as Decimal
        """
        try:
            line_price = product.get("line_price")
            if line_price is not None:
                return PriceCalculator.round_currency(line_price)
            
            base_price = product.get("price") or 0
            return PriceCalculator.round_currency(base_price)
            
        except Exception as e:
            logger.error(f"Error calculating line price for product: {e}")
            return Decimal('0.00')
    
    @staticmethod
    def calculate_total_price(unit_price: Union[str, int, float, Decimal], quantity: int) -> Decimal:
        """
        Calculate total price for a given quantity with proper precision.
        
        Args:
            unit_price: Price per unit
            quantity: Number of units
            
        Returns:
            Total price as Decimal
        """
        try:
            if quantity <= 0:
                raise PriceCalculationError("Quantity must be greater than zero")
            
            decimal_price = PriceCalculator.to_decimal(unit_price)
            decimal_quantity = Decimal(str(quantity))
            
            total = decimal_price * decimal_quantity
            return PriceCalculator.round_currency(total)
            
        except Exception as e:
            logger.error(f"Error calculating total price: {e}")
            raise PriceCalculationError(f"Failed to calculate total price: {str(e)}")
    
    @staticmethod
    def calculate_cart_total(cart_items: list) -> Dict[str, Any]:
        """
        Calculate cart total with detailed breakdown.
        
        Args:
            cart_items: List of cart items
            
        Returns:
            Dict with total and breakdown information
        """
        try:
            total = Decimal('0.00')
            item_count = 0
            calculation_details = []
            
            for item in cart_items:
                try:
                    item_price = PriceCalculator.to_decimal(item.get("price", 0))
                    item_quantity = int(item.get("quantity", 1))
                    item_total = item_price * Decimal(str(item_quantity))
                    
                    total += item_total
                    item_count += item_quantity
                    
                    calculation_details.append({
                        "name": item.get("name", "Unknown Item"),
                        "unit_price": str(item_price),
                        "quantity": item_quantity,
                        "item_total": str(PriceCalculator.round_currency(item_total)),
                        "is_line_based": item.get("is_line_based", False),
                        "is_exclusive": item.get("is_exclusive_single_use", False)
                    })
                    
                except Exception as item_error:
                    logger.error(f"Error calculating price for cart item: {item_error}")
                    # Continue with other items, but log the error
                    continue
            
            return {
                "total": PriceCalculator.round_currency(total),
                "total_str": str(PriceCalculator.round_currency(total)),
                "item_count": item_count,
                "calculation_details": calculation_details,
                "currency": "USD"  # Could be made configurable
            }
            
        except Exception as e:
            logger.error(f"Error calculating cart total: {e}")
            return {
                "total": Decimal('0.00'),
                "total_str": "0.00",
                "item_count": 0,
                "calculation_details": [],
                "error": str(e)
            }
    
    @staticmethod
    def validate_price(price: Union[str, int, float, Decimal]) -> Dict[str, Any]:
        """
        Validate a price value for business rules.
        
        Args:
            price: Price to validate
            
        Returns:
            Dict with validation results
        """
        try:
            decimal_price = PriceCalculator.to_decimal(price)
            
            # Business rule validations
            if decimal_price < 0:
                return {
                    "valid": False,
                    "error": "Price cannot be negative",
                    "price": str(decimal_price)
                }
            
            if decimal_price == 0:
                return {
                    "valid": False,
                    "error": "Price cannot be zero",
                    "price": str(decimal_price)
                }
            
            # Check for reasonable maximum (configurable)
            max_price = Decimal('99999.99')  # Could be made configurable
            if decimal_price > max_price:
                return {
                    "valid": False,
                    "error": f"Price exceeds maximum allowed ({max_price})",
                    "price": str(decimal_price)
                }
            
            return {
                "valid": True,
                "price": str(PriceCalculator.round_currency(decimal_price)),
                "decimal_price": PriceCalculator.round_currency(decimal_price)
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"Invalid price format: {str(e)}",
                "price": str(price)
            }
    
    @staticmethod
    def format_price_display(price: Union[str, int, float, Decimal], currency: str = "USD") -> str:
        """
        Format price for display with proper currency formatting.
        
        Args:
            price: Price to format
            currency: Currency code
            
        Returns:
            Formatted price string
        """
        try:
            decimal_price = PriceCalculator.round_currency(price)
            
            if currency.upper() == "USD":
                return f"${decimal_price:.2f}"
            else:
                return f"{decimal_price:.2f} {currency}"
                
        except Exception as e:
            logger.error(f"Error formatting price display: {e}")
            return "$0.00"


# Global instance for easy access
price_calculator = PriceCalculator()
