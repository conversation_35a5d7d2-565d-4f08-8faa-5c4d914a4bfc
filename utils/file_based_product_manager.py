"""
File-Based Product Management Utilities
Handles operations for products that require file uploads and management.
"""

import logging
import os
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from database.operations import get_all_products_for_admin
from utils.file_metadata import normalize_file_path

logger = logging.getLogger(__name__)

# Base uploads directory
UPLOADS_DIR = Path("uploads")

class FileBasedProductManager:
    """Manages file-based products and their upload status."""
    
    @staticmethod
    def get_file_based_products() -> List[Dict[str, Any]]:
        """
        Get all products that have file-based content (non-text content types).
        
        Returns:
            List of products that require file uploads
        """
        try:
            # Get all products for admin (including purchased/removed exclusive products)
            all_products = get_all_products_for_admin()
            
            file_based_products = []
            
            for product in all_products:
                product_id = product.get("_id") or product.get("id")
                product_name = product.get("name", "Unknown Product")
                product_type = FileBasedProductManager._determine_product_type(product)

                # Check if product has file-based content
                file_info = FileBasedProductManager._get_product_file_info(product)

                if file_info["has_files"]:
                    # Check file status
                    file_status = FileBasedProductManager._check_file_status(file_info["file_paths"])
                    
                    file_based_products.append({
                        "product_id": product_id,
                        "name": product_name,
                        "type": product_type,
                        "price": product.get("price", 0),
                        "file_info": file_info,
                        "file_status": file_status,
                        "created_at": product.get("created_at"),
                        "is_exclusive": product.get("is_exclusive_single_use", False),
                        "is_line_based": product.get("is_line_based", False),
                        "is_purchased": product.get("is_purchased", False),
                        "removed_from_listings": product.get("removed_from_listings", False)
                    })
            
            # Sort by creation date (newest first)
            file_based_products.sort(
                key=lambda x: x.get("created_at", datetime.min), 
                reverse=True
            )
            
            return file_based_products
            
        except Exception as e:
            logger.error(f"Error getting file-based products: {e}")
            return []
    
    @staticmethod
    def _determine_product_type(product: Dict[str, Any]) -> str:
        """Determine the product type based on its properties."""
        if product.get("is_exclusive_single_use", False):
            return "exclusive"
        elif product.get("is_line_based", False):
            return "line-based"
        else:
            return "regular"
    
    @staticmethod
    def _get_product_file_info(product: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract file information from a product.
        
        Returns:
            Dict with file information including paths and types
        """
        file_paths = []
        file_types = []
        has_files = False
        
        # Check regular product file
        if product.get("file_path"):
            file_paths.append({
                "path": product["file_path"],
                "type": "product_file",
                "description": "Product File"
            })
            file_types.append("product_file")
            has_files = True
        
        # Check line-based inventory file
        if product.get("inventory_file_path"):
            file_paths.append({
                "path": product["inventory_file_path"],
                "type": "inventory_file",
                "description": "Inventory File"
            })
            file_types.append("inventory_file")
            has_files = True
        
        # Check exclusive product file
        if product.get("exclusive_file_path"):
            file_paths.append({
                "path": product["exclusive_file_path"],
                "type": "exclusive_file",
                "description": "Exclusive File"
            })
            file_types.append("exclusive_file")
            has_files = True
        
        # Check if product has image files
        if product.get("image_url") and not product["image_url"].startswith(("http://", "https://")):
            # Local image file
            file_paths.append({
                "path": product["image_url"],
                "type": "image_file",
                "description": "Product Image"
            })
            file_types.append("image_file")
            has_files = True
        
        return {
            "has_files": has_files,
            "file_paths": file_paths,
            "file_types": file_types,
            "total_files": len(file_paths)
        }
    
    @staticmethod
    def _check_file_status(file_paths: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Check the status of files for a product.
        
        Args:
            file_paths: List of file path dictionaries
            
        Returns:
            Dict with file status information
        """
        total_files = len(file_paths)
        existing_files = 0
        missing_files = 0
        file_details = []
        
        # Ensure uploads directory exists
        uploads_exists = UPLOADS_DIR.exists()
        
        for file_info in file_paths:
            file_path = file_info["path"]
            file_type = file_info["type"]
            description = file_info["description"]
            
            # Normalize the file path
            normalized_path = normalize_file_path(file_path)
            full_path = UPLOADS_DIR / normalized_path if normalized_path else None
            
            file_exists = False
            file_size = 0
            
            if full_path and full_path.exists() and full_path.is_file():
                file_exists = True
                file_size = full_path.stat().st_size
                existing_files += 1
            else:
                missing_files += 1
            
            file_details.append({
                "path": file_path,
                "normalized_path": normalized_path,
                "full_path": str(full_path) if full_path else None,
                "type": file_type,
                "description": description,
                "exists": file_exists,
                "size": file_size,
                "size_mb": round(file_size / (1024 * 1024), 2) if file_size > 0 else 0
            })
        
        return {
            "total_files": total_files,
            "existing_files": existing_files,
            "missing_files": missing_files,
            "all_files_present": missing_files == 0,
            "uploads_directory_exists": uploads_exists,
            "file_details": file_details,
            "status": "complete" if missing_files == 0 else "incomplete"
        }
    
    @staticmethod
    def get_upload_statistics() -> Dict[str, Any]:
        """
        Get overall statistics about file uploads across all products.
        
        Returns:
            Dict with upload statistics
        """
        try:
            file_based_products = FileBasedProductManager.get_file_based_products()
            
            total_products = len(file_based_products)
            products_with_all_files = 0
            products_with_missing_files = 0
            total_files = 0
            total_existing_files = 0
            total_missing_files = 0
            
            product_type_stats = {
                "regular": {"total": 0, "complete": 0, "incomplete": 0},
                "line-based": {"total": 0, "complete": 0, "incomplete": 0},
                "exclusive": {"total": 0, "complete": 0, "incomplete": 0}
            }
            
            for product in file_based_products:
                product_type = product["type"]
                file_status = product["file_status"]
                
                # Update product type stats
                product_type_stats[product_type]["total"] += 1
                
                if file_status["all_files_present"]:
                    products_with_all_files += 1
                    product_type_stats[product_type]["complete"] += 1
                else:
                    products_with_missing_files += 1
                    product_type_stats[product_type]["incomplete"] += 1
                
                # Update file stats
                total_files += file_status["total_files"]
                total_existing_files += file_status["existing_files"]
                total_missing_files += file_status["missing_files"]
            
            return {
                "total_products": total_products,
                "products_with_all_files": products_with_all_files,
                "products_with_missing_files": products_with_missing_files,
                "completion_rate": round((products_with_all_files / max(total_products, 1)) * 100, 1),
                "total_files": total_files,
                "total_existing_files": total_existing_files,
                "total_missing_files": total_missing_files,
                "file_completion_rate": round((total_existing_files / max(total_files, 1)) * 100, 1),
                "product_type_stats": product_type_stats,
                "uploads_directory_exists": UPLOADS_DIR.exists()
            }
            
        except Exception as e:
            logger.error(f"Error getting upload statistics: {e}")
            return {
                "total_products": 0,
                "products_with_all_files": 0,
                "products_with_missing_files": 0,
                "completion_rate": 0,
                "total_files": 0,
                "total_existing_files": 0,
                "total_missing_files": 0,
                "file_completion_rate": 0,
                "product_type_stats": {
                    "regular": {"total": 0, "complete": 0, "incomplete": 0},
                    "line-based": {"total": 0, "complete": 0, "incomplete": 0},
                    "exclusive": {"total": 0, "complete": 0, "incomplete": 0}
                },
                "uploads_directory_exists": False
            }

    @staticmethod
    def ensure_uploads_directory() -> bool:
        """
        Ensure the uploads directory and its subdirectories exist.

        Returns:
            True if directories were created/exist, False if there was an error
        """
        try:
            # Create main uploads directory
            UPLOADS_DIR.mkdir(exist_ok=True)

            # Create common subdirectories
            subdirs = [
                "product_images",
                "category_images",
                "files",
                "user_uploads",
                "temp",
                "exclusive_files",
                "inventory",
                "deliveries",
                "exclusive_previews",
                "welcome_images",
                ".metadata"
            ]

            for subdir in subdirs:
                (UPLOADS_DIR / subdir).mkdir(exist_ok=True)

            logger.info("Uploads directory structure ensured")
            return True

        except Exception as e:
            logger.error(f"Error ensuring uploads directory: {e}")
            return False

    @staticmethod
    def check_product_file_integrity(product_id: Any) -> Dict[str, Any]:
        """
        Check file integrity for a specific product.

        Args:
            product_id: Product ID to check

        Returns:
            Dict with detailed file integrity information
        """
        try:
            from database.operations import get_product

            product = get_product(product_id)
            if not product:
                return {
                    "success": False,
                    "error": "Product not found",
                    "product_id": product_id
                }

            file_info = FileBasedProductManager._get_product_file_info(product)

            if not file_info["has_files"]:
                return {
                    "success": True,
                    "product_id": product_id,
                    "product_name": product.get("name", "Unknown"),
                    "has_files": False,
                    "message": "Product does not require file uploads"
                }

            file_status = FileBasedProductManager._check_file_status(file_info["file_paths"])

            return {
                "success": True,
                "product_id": product_id,
                "product_name": product.get("name", "Unknown"),
                "has_files": True,
                "file_info": file_info,
                "file_status": file_status,
                "integrity_check": {
                    "all_files_present": file_status["all_files_present"],
                    "missing_files_count": file_status["missing_files"],
                    "corrupted_files": [],  # Could be extended to check file corruption
                    "total_size_mb": sum(detail["size_mb"] for detail in file_status["file_details"]),
                    "check_timestamp": datetime.now().isoformat()
                }
            }

        except Exception as e:
            logger.error(f"Error checking file integrity for product {product_id}: {e}")
            return {
                "success": False,
                "error": str(e),
                "product_id": product_id
            }

    @staticmethod
    def get_missing_files_report() -> Dict[str, Any]:
        """
        Generate a comprehensive report of all missing files across products.

        Returns:
            Dict with missing files report
        """
        try:
            file_based_products = FileBasedProductManager.get_file_based_products()

            missing_files_by_product = []
            missing_files_by_type = {
                "product_file": [],
                "inventory_file": [],
                "exclusive_file": [],
                "image_file": []
            }

            total_missing_files = 0

            for product in file_based_products:
                if not product["file_status"]["all_files_present"]:
                    product_missing = {
                        "product_id": product["product_id"],
                        "product_name": product["name"],
                        "product_type": product["type"],
                        "missing_files": []
                    }

                    for file_detail in product["file_status"]["file_details"]:
                        if not file_detail["exists"]:
                            missing_file_info = {
                                "path": file_detail["path"],
                                "type": file_detail["type"],
                                "description": file_detail["description"],
                                "expected_location": file_detail["full_path"]
                            }

                            product_missing["missing_files"].append(missing_file_info)
                            missing_files_by_type[file_detail["type"]].append({
                                "product_id": product["product_id"],
                                "product_name": product["name"],
                                **missing_file_info
                            })
                            total_missing_files += 1

                    missing_files_by_product.append(product_missing)

            return {
                "total_missing_files": total_missing_files,
                "products_with_missing_files": len(missing_files_by_product),
                "missing_files_by_product": missing_files_by_product,
                "missing_files_by_type": missing_files_by_type,
                "report_timestamp": datetime.now().isoformat(),
                "uploads_directory_exists": UPLOADS_DIR.exists()
            }

        except Exception as e:
            logger.error(f"Error generating missing files report: {e}")
            return {
                "total_missing_files": 0,
                "products_with_missing_files": 0,
                "missing_files_by_product": [],
                "missing_files_by_type": {
                    "product_file": [],
                    "inventory_file": [],
                    "exclusive_file": [],
                    "image_file": []
                },
                "error": str(e),
                "report_timestamp": datetime.now().isoformat(),
                "uploads_directory_exists": False
            }
