"""
Monitoring and Alerting System
Monitors critical operations and provides alerting for issues.
Tracks performance metrics and security events.
"""

import logging
import time
import threading
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
from collections import defaultdict, deque
from enum import Enum
import json

logger = logging.getLogger(__name__)


class AlertLevel(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MetricType(Enum):
    """Types of metrics to track."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


class Alert:
    """Represents a system alert."""
    
    def __init__(self, level: AlertLevel, message: str, details: Optional[Dict] = None):
        self.level = level
        self.message = message
        self.details = details or {}
        self.timestamp = datetime.now()
        self.alert_id = f"{level.value}_{int(time.time() * 1000)}"
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert alert to dictionary."""
        return {
            "alert_id": self.alert_id,
            "level": self.level.value,
            "message": self.message,
            "details": self.details,
            "timestamp": self.timestamp.isoformat()
        }


class Metric:
    """Represents a system metric."""
    
    def __init__(self, name: str, metric_type: MetricType, value: float, tags: Optional[Dict] = None):
        self.name = name
        self.type = metric_type
        self.value = value
        self.tags = tags or {}
        self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metric to dictionary."""
        return {
            "name": self.name,
            "type": self.type.value,
            "value": self.value,
            "tags": self.tags,
            "timestamp": self.timestamp.isoformat()
        }


class MonitoringSystem:
    """
    Comprehensive monitoring system for tracking performance and security events.
    Provides alerting, metrics collection, and performance analysis.
    """
    
    def __init__(self, max_alerts: int = 1000, max_metrics: int = 10000):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
        
        # Storage
        self.alerts: deque = deque(maxlen=max_alerts)
        self.metrics: deque = deque(maxlen=max_metrics)
        self.alert_handlers: List[Callable] = []
        
        # Counters and gauges
        self.counters: Dict[str, float] = defaultdict(float)
        self.gauges: Dict[str, float] = {}
        self.timers: Dict[str, List[float]] = defaultdict(list)
        
        # Security monitoring
        self.security_events: deque = deque(maxlen=1000)
        self.failed_operations: Dict[str, int] = defaultdict(int)
        
        # Performance tracking
        self.operation_times: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        self.error_rates: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Start background monitoring
        self._start_background_monitoring()
    
    def add_alert_handler(self, handler: Callable[[Alert], None]) -> None:
        """Add an alert handler function."""
        self.alert_handlers.append(handler)
    
    def emit_alert(self, level: AlertLevel, message: str, details: Optional[Dict] = None) -> None:
        """Emit an alert."""
        alert = Alert(level, message, details)
        
        with self._lock:
            self.alerts.append(alert)
        
        # Call alert handlers
        for handler in self.alert_handlers:
            try:
                handler(alert)
            except Exception as e:
                self.logger.error(f"Error in alert handler: {e}")
        
        # Log the alert
        log_level = {
            AlertLevel.INFO: logging.INFO,
            AlertLevel.WARNING: logging.WARNING,
            AlertLevel.ERROR: logging.ERROR,
            AlertLevel.CRITICAL: logging.CRITICAL
        }.get(level, logging.INFO)
        
        self.logger.log(log_level, f"ALERT [{level.value.upper()}]: {message}")
    
    def record_metric(self, name: str, metric_type: MetricType, value: float, tags: Optional[Dict] = None) -> None:
        """Record a metric."""
        metric = Metric(name, metric_type, value, tags)
        
        with self._lock:
            self.metrics.append(metric)
            
            # Update internal counters/gauges
            if metric_type == MetricType.COUNTER:
                self.counters[name] += value
            elif metric_type == MetricType.GAUGE:
                self.gauges[name] = value
            elif metric_type == MetricType.TIMER:
                self.timers[name].append(value)
                # Keep only last 1000 timer values
                if len(self.timers[name]) > 1000:
                    self.timers[name] = self.timers[name][-1000:]
    
    def increment_counter(self, name: str, value: float = 1.0, tags: Optional[Dict] = None) -> None:
        """Increment a counter metric."""
        self.record_metric(name, MetricType.COUNTER, value, tags)
    
    def set_gauge(self, name: str, value: float, tags: Optional[Dict] = None) -> None:
        """Set a gauge metric."""
        self.record_metric(name, MetricType.GAUGE, value, tags)
    
    def record_timer(self, name: str, duration: float, tags: Optional[Dict] = None) -> None:
        """Record a timer metric."""
        self.record_metric(name, MetricType.TIMER, duration, tags)
    
    def record_security_event(self, event_type: str, details: Dict[str, Any]) -> None:
        """Record a security event."""
        event = {
            "type": event_type,
            "details": details,
            "timestamp": datetime.now().isoformat()
        }
        
        with self._lock:
            self.security_events.append(event)
        
        # Emit alert for critical security events
        if event_type in ["path_traversal_attempt", "injection_attempt", "unauthorized_access"]:
            self.emit_alert(
                AlertLevel.CRITICAL,
                f"Security event detected: {event_type}",
                details
            )
    
    def record_operation_result(self, operation: str, success: bool, duration: float) -> None:
        """Record the result of an operation."""
        with self._lock:
            self.operation_times[operation].append(duration)
            self.error_rates[operation].append(0 if success else 1)
        
        # Check for performance issues
        if duration > 5.0:  # 5 second threshold
            self.emit_alert(
                AlertLevel.WARNING,
                f"Slow operation detected: {operation}",
                {"duration": duration, "operation": operation}
            )
        
        # Check error rates
        if not success:
            self.failed_operations[operation] += 1
            
            # Alert on high error rates
            recent_errors = list(self.error_rates[operation])[-10:]  # Last 10 operations
            if len(recent_errors) >= 10 and sum(recent_errors) >= 5:  # 50% error rate
                self.emit_alert(
                    AlertLevel.ERROR,
                    f"High error rate detected: {operation}",
                    {"error_rate": sum(recent_errors) / len(recent_errors), "operation": operation}
                )
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of current metrics."""
        with self._lock:
            # Calculate timer statistics
            timer_stats = {}
            for name, values in self.timers.items():
                if values:
                    timer_stats[name] = {
                        "count": len(values),
                        "avg": sum(values) / len(values),
                        "min": min(values),
                        "max": max(values),
                        "p95": sorted(values)[int(len(values) * 0.95)] if len(values) > 0 else 0
                    }
            
            # Calculate error rates
            error_rate_stats = {}
            for operation, errors in self.error_rates.items():
                if errors:
                    error_rate_stats[operation] = {
                        "total_operations": len(errors),
                        "error_count": sum(errors),
                        "error_rate": sum(errors) / len(errors) if errors else 0
                    }
            
            return {
                "timestamp": datetime.now().isoformat(),
                "counters": dict(self.counters),
                "gauges": dict(self.gauges),
                "timers": timer_stats,
                "error_rates": error_rate_stats,
                "total_alerts": len(self.alerts),
                "total_metrics": len(self.metrics),
                "security_events": len(self.security_events)
            }
    
    def get_recent_alerts(self, level: Optional[AlertLevel] = None, limit: int = 50) -> List[Dict[str, Any]]:
        """Get recent alerts, optionally filtered by level."""
        with self._lock:
            alerts = list(self.alerts)
        
        if level:
            alerts = [alert for alert in alerts if alert.level == level]
        
        # Sort by timestamp (most recent first)
        alerts.sort(key=lambda x: x.timestamp, reverse=True)
        
        return [alert.to_dict() for alert in alerts[:limit]]
    
    def get_security_events(self, event_type: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get recent security events."""
        with self._lock:
            events = list(self.security_events)
        
        if event_type:
            events = [event for event in events if event.get("type") == event_type]
        
        return events[-limit:]
    
    def check_system_health(self) -> Dict[str, Any]:
        """Perform system health check."""
        health_status = {
            "status": "healthy",
            "timestamp": datetime.now().isoformat(),
            "checks": {}
        }
        
        # Check error rates
        high_error_operations = []
        for operation, errors in self.error_rates.items():
            if errors:
                recent_errors = list(errors)[-20:]  # Last 20 operations
                if len(recent_errors) >= 10:
                    error_rate = sum(recent_errors) / len(recent_errors)
                    if error_rate > 0.2:  # 20% error rate threshold
                        high_error_operations.append({
                            "operation": operation,
                            "error_rate": error_rate
                        })
        
        health_status["checks"]["error_rates"] = {
            "status": "warning" if high_error_operations else "ok",
            "high_error_operations": high_error_operations
        }
        
        # Check performance
        slow_operations = []
        for operation, times in self.operation_times.items():
            if times:
                recent_times = list(times)[-10:]  # Last 10 operations
                avg_time = sum(recent_times) / len(recent_times)
                if avg_time > 2.0:  # 2 second threshold
                    slow_operations.append({
                        "operation": operation,
                        "avg_time": avg_time
                    })
        
        health_status["checks"]["performance"] = {
            "status": "warning" if slow_operations else "ok",
            "slow_operations": slow_operations
        }
        
        # Check recent critical alerts
        recent_critical = [
            alert for alert in self.alerts 
            if alert.level == AlertLevel.CRITICAL and 
            alert.timestamp > datetime.now() - timedelta(minutes=10)
        ]
        
        health_status["checks"]["critical_alerts"] = {
            "status": "critical" if recent_critical else "ok",
            "recent_critical_count": len(recent_critical)
        }
        
        # Overall status
        if recent_critical:
            health_status["status"] = "critical"
        elif high_error_operations or slow_operations:
            health_status["status"] = "warning"
        
        return health_status
    
    def _start_background_monitoring(self) -> None:
        """Start background monitoring thread."""
        def monitor():
            while True:
                try:
                    # Perform periodic health checks
                    health = self.check_system_health()
                    
                    if health["status"] != "healthy":
                        self.emit_alert(
                            AlertLevel.WARNING if health["status"] == "warning" else AlertLevel.CRITICAL,
                            f"System health check failed: {health['status']}",
                            health["checks"]
                        )
                    
                    # Clean up old data
                    self._cleanup_old_data()
                    
                    time.sleep(60)  # Check every minute
                    
                except Exception as e:
                    self.logger.error(f"Error in background monitoring: {e}")
                    time.sleep(60)
        
        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()
    
    def _cleanup_old_data(self) -> None:
        """Clean up old monitoring data."""
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        with self._lock:
            # Clean up old timer data
            for name in list(self.timers.keys()):
                if not self.timers[name]:
                    del self.timers[name]
            
            # Reset failed operation counters periodically
            if datetime.now().minute == 0:  # Once per hour
                self.failed_operations.clear()


# Global monitoring instance
monitoring_system = MonitoringSystem()

# Convenience functions
def emit_alert(level: AlertLevel, message: str, details: Optional[Dict] = None) -> None:
    """Emit an alert to the monitoring system."""
    monitoring_system.emit_alert(level, message, details)

def record_operation(operation: str, success: bool, duration: float) -> None:
    """Record an operation result."""
    monitoring_system.record_operation_result(operation, success, duration)

def record_security_event(event_type: str, details: Dict[str, Any]) -> None:
    """Record a security event."""
    monitoring_system.record_security_event(event_type, details)

def increment_counter(name: str, value: float = 1.0, tags: Optional[Dict] = None) -> None:
    """Increment a counter."""
    monitoring_system.increment_counter(name, value, tags)
