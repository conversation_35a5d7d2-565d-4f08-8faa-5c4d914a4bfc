"""
Product Validation Module
Comprehensive validation for product data and operations.
Ensures data integrity and prevents invalid product states.
"""

import logging
import re
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from pathlib import Path

from utils.price_calculator import price_calculator, PriceCalculationError

logger = logging.getLogger(__name__)


class ProductValidationError(Exception):
    """Custom exception for product validation errors."""
    pass


class ProductValidator:
    """
    Comprehensive validator for product data and operations.
    Ensures all product data meets business rules and security requirements.
    """
    
    # Validation constants
    MAX_NAME_LENGTH = 200
    MAX_DESCRIPTION_LENGTH = 2000
    MIN_NAME_LENGTH = 1
    MAX_QUANTITY_PER_ORDER = 1000
    ALLOWED_FILE_EXTENSIONS = {'.txt', '.pdf', '.doc', '.docx', '.zip', '.rar', '.7z', '.jpg', '.jpeg', '.png', '.gif', '.webp', '.tmp'}
    
    @staticmethod
    def validate_product_name(name: str) -> Dict[str, Any]:
        """
        Validate product name.
        
        Args:
            name: Product name to validate
            
        Returns:
            Dict with validation results
        """
        try:
            if not name or not isinstance(name, str):
                return {
                    "valid": False,
                    "error": "Product name is required and must be a string"
                }
            
            name = name.strip()
            
            if len(name) < ProductValidator.MIN_NAME_LENGTH:
                return {
                    "valid": False,
                    "error": f"Product name must be at least {ProductValidator.MIN_NAME_LENGTH} character long"
                }
            
            if len(name) > ProductValidator.MAX_NAME_LENGTH:
                return {
                    "valid": False,
                    "error": f"Product name cannot exceed {ProductValidator.MAX_NAME_LENGTH} characters"
                }
            
            # Check for suspicious characters
            if re.search(r'[<>"\'\&\$\;]', name):
                return {
                    "valid": False,
                    "error": "Product name contains invalid characters"
                }
            
            return {
                "valid": True,
                "cleaned_name": name
            }
            
        except Exception as e:
            logger.error(f"Error validating product name: {e}")
            return {
                "valid": False,
                "error": f"Name validation error: {str(e)}"
            }
    
    @staticmethod
    def validate_product_description(description: str) -> Dict[str, Any]:
        """
        Validate product description.
        
        Args:
            description: Product description to validate
            
        Returns:
            Dict with validation results
        """
        try:
            if description is None:
                description = ""
            
            if not isinstance(description, str):
                return {
                    "valid": False,
                    "error": "Product description must be a string"
                }
            
            description = description.strip()
            
            if len(description) > ProductValidator.MAX_DESCRIPTION_LENGTH:
                return {
                    "valid": False,
                    "error": f"Product description cannot exceed {ProductValidator.MAX_DESCRIPTION_LENGTH} characters"
                }
            
            # Basic HTML/script injection prevention
            if re.search(r'<script|javascript:|data:|vbscript:', description, re.IGNORECASE):
                return {
                    "valid": False,
                    "error": "Product description contains potentially dangerous content"
                }
            
            return {
                "valid": True,
                "cleaned_description": description
            }
            
        except Exception as e:
            logger.error(f"Error validating product description: {e}")
            return {
                "valid": False,
                "error": f"Description validation error: {str(e)}"
            }
    
    @staticmethod
    def validate_product_price(price: Union[str, int, float]) -> Dict[str, Any]:
        """
        Validate product price using the price calculator.
        
        Args:
            price: Price to validate
            
        Returns:
            Dict with validation results
        """
        return price_calculator.validate_price(price)
    
    @staticmethod
    def validate_file_link(file_link: Optional[str]) -> Dict[str, Any]:
        """
        Validate product file link.

        Args:
            file_link: File link to validate

        Returns:
            Dict with validation results
        """
        try:
            if file_link is None:
                return {
                    "valid": True,
                    "cleaned_file_link": ""
                }

            if not isinstance(file_link, str):
                return {
                    "valid": False,
                    "error": "File link must be a string"
                }

            file_link = file_link.strip()

            # Allow empty file links
            if not file_link:
                return {
                    "valid": True,
                    "cleaned_file_link": ""
                }

            # More flexible validation for file links
            # Accept URLs
            if file_link.startswith(('http://', 'https://', 'ftp://')):
                return {
                    "valid": True,
                    "cleaned_file_link": file_link
                }

            # Accept absolute paths
            if file_link.startswith('/'):
                return {
                    "valid": True,
                    "cleaned_file_link": file_link
                }

            # Accept file_id references (for Telegram files)
            if file_link.startswith('file_id:'):
                return {
                    "valid": True,
                    "cleaned_file_link": file_link
                }

            # Accept text content references
            if file_link.startswith('text:'):
                return {
                    "valid": True,
                    "cleaned_file_link": file_link
                }

            # Accept relative paths (don't check existence as files might be uploaded later)
            # This includes paths like "uploads/files/filename.ext" or just "filename.ext"
            if len(file_link) > 0:
                return {
                    "valid": True,
                    "cleaned_file_link": file_link
                }

            return {
                "valid": False,
                "error": "File link cannot be empty"
            }

        except Exception as e:
            logger.error(f"Error validating file link: {e}")
            return {
                "valid": False,
                "error": f"File link validation error: {str(e)}"
            }

    @staticmethod
    def validate_category_id(category_id: Optional[Union[int, str, Any]]) -> Dict[str, Any]:
        """
        Validate category ID. Supports both integer IDs and ObjectIds.

        Args:
            category_id: Category ID to validate

        Returns:
            Dict with validation results
        """
        try:
            if category_id is None:
                return {
                    "valid": True,
                    "category_id": None
                }

            # Handle string inputs
            if isinstance(category_id, str):
                if category_id.strip() == "":
                    return {
                        "valid": True,
                        "category_id": None
                    }

                # Try to convert to integer first
                if category_id.isdigit():
                    try:
                        category_id = int(category_id)
                    except ValueError:
                        return {
                            "valid": False,
                            "error": "Category ID must be a valid integer"
                        }
                else:
                    # Check if it's a valid ObjectId string
                    try:
                        from bson import ObjectId
                        if ObjectId.is_valid(category_id):
                            # Return the ObjectId string as-is for database operations
                            return {
                                "valid": True,
                                "category_id": category_id
                            }
                        else:
                            return {
                                "valid": False,
                                "error": "Category ID must be a valid integer or ObjectId"
                            }
                    except ImportError:
                        # BSON not available, only accept integers
                        return {
                            "valid": False,
                            "error": "Category ID must be a valid integer"
                        }

            # Handle ObjectId instances
            try:
                from bson import ObjectId
                if isinstance(category_id, ObjectId):
                    return {
                        "valid": True,
                        "category_id": str(category_id)  # Convert to string for consistency
                    }
            except ImportError:
                pass  # BSON not available

            # Handle integer IDs
            if isinstance(category_id, int):
                if category_id <= 0:
                    return {
                        "valid": False,
                        "error": "Category ID must be a positive integer"
                    }
                return {
                    "valid": True,
                    "category_id": category_id
                }

            # If we get here, it's an unsupported type
            return {
                "valid": False,
                "error": "Category ID must be an integer or valid ObjectId"
            }
            
        except Exception as e:
            logger.error(f"Error validating category ID: {e}")
            return {
                "valid": False,
                "error": f"Category ID validation error: {str(e)}"
            }
    
    @staticmethod
    def validate_line_based_fields(product_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate line-based product specific fields.
        
        Args:
            product_data: Product data dictionary
            
        Returns:
            Dict with validation results
        """
        try:
            if not product_data.get("is_line_based", False):
                return {"valid": True}  # Not a line-based product
            
            errors = []
            
            # Validate max_quantity_per_order
            max_quantity = product_data.get("max_quantity_per_order", 1)
            try:
                max_quantity = int(max_quantity)
                if max_quantity <= 0:
                    errors.append("Max quantity per order must be positive")
                elif max_quantity > ProductValidator.MAX_QUANTITY_PER_ORDER:
                    errors.append(f"Max quantity per order cannot exceed {ProductValidator.MAX_QUANTITY_PER_ORDER}")
            except (ValueError, TypeError):
                errors.append("Max quantity per order must be a valid integer")
            
            # Validate line_price if provided
            line_price = product_data.get("line_price")
            if line_price is not None:
                price_validation = ProductValidator.validate_product_price(line_price)
                if not price_validation["valid"]:
                    errors.append(f"Line price validation failed: {price_validation['error']}")
            
            # Validate preview_format
            preview_format = product_data.get("preview_format", "")
            if preview_format and len(preview_format) > 100:
                errors.append("Preview format cannot exceed 100 characters")
            
            if errors:
                return {
                    "valid": False,
                    "errors": errors
                }
            
            return {"valid": True}
            
        except Exception as e:
            logger.error(f"Error validating line-based fields: {e}")
            return {
                "valid": False,
                "errors": [f"Line-based validation error: {str(e)}"]
            }
    
    @staticmethod
    def validate_exclusive_fields(product_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate exclusive single-use product specific fields.
        
        Args:
            product_data: Product data dictionary
            
        Returns:
            Dict with validation results
        """
        try:
            if not product_data.get("is_exclusive_single_use", False):
                return {"valid": True}  # Not an exclusive product
            
            errors = []
            
            # Validate expiration_date if provided
            expiration_date = product_data.get("expiration_date")
            if expiration_date:
                if isinstance(expiration_date, str):
                    try:
                        expiration_date = datetime.fromisoformat(expiration_date)
                    except ValueError:
                        errors.append("Invalid expiration date format")
                
                if isinstance(expiration_date, datetime):
                    if expiration_date <= datetime.now():
                        errors.append("Expiration date must be in the future")
            
            # Validate exclusive file fields if provided
            exclusive_file_path = product_data.get("exclusive_file_path")
            if exclusive_file_path:
                file_ext = Path(exclusive_file_path).suffix.lower()
                if file_ext not in ProductValidator.ALLOWED_FILE_EXTENSIONS:
                    errors.append(f"Unsupported file extension: {file_ext}")
            
            if errors:
                return {
                    "valid": False,
                    "errors": errors
                }
            
            return {"valid": True}
            
        except Exception as e:
            logger.error(f"Error validating exclusive fields: {e}")
            return {
                "valid": False,
                "errors": [f"Exclusive validation error: {str(e)}"]
            }
    
    @staticmethod
    def validate_complete_product(product_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive validation of product data.
        
        Args:
            product_data: Complete product data dictionary
            
        Returns:
            Dict with validation results and cleaned data
        """
        try:
            errors = []
            cleaned_data = {}
            
            # Validate required fields
            name_validation = ProductValidator.validate_product_name(product_data.get("name", ""))
            if not name_validation["valid"]:
                errors.append(name_validation["error"])
            else:
                cleaned_data["name"] = name_validation["cleaned_name"]
            
            desc_validation = ProductValidator.validate_product_description(product_data.get("description", ""))
            if not desc_validation["valid"]:
                errors.append(desc_validation["error"])
            else:
                cleaned_data["description"] = desc_validation["cleaned_description"]
            
            price_validation = ProductValidator.validate_product_price(product_data.get("price", 0))
            if not price_validation["valid"]:
                errors.append(price_validation["error"])
            else:
                cleaned_data["price"] = float(price_validation["decimal_price"])
            
            # Validate optional fields
            category_validation = ProductValidator.validate_category_id(product_data.get("category_id"))
            if not category_validation["valid"]:
                errors.append(category_validation["error"])
            else:
                cleaned_data["category_id"] = category_validation["category_id"]

            file_link_validation = ProductValidator.validate_file_link(product_data.get("file_link"))
            if not file_link_validation["valid"]:
                errors.append(file_link_validation["error"])
            else:
                cleaned_data["file_link"] = file_link_validation["cleaned_file_link"]
            
            # Validate product type specific fields
            line_validation = ProductValidator.validate_line_based_fields(product_data)
            if not line_validation["valid"]:
                errors.extend(line_validation["errors"])
            
            exclusive_validation = ProductValidator.validate_exclusive_fields(product_data)
            if not exclusive_validation["valid"]:
                errors.extend(exclusive_validation["errors"])
            
            # Business rule: Product cannot be both line-based and exclusive
            if product_data.get("is_line_based", False) and product_data.get("is_exclusive_single_use", False):
                errors.append("Product cannot be both line-based and exclusive single-use")
            
            if errors:
                return {
                    "valid": False,
                    "errors": errors,
                    "cleaned_data": cleaned_data
                }
            
            # Copy remaining valid fields including type-specific fields
            basic_fields = ["file_link", "image_url", "is_line_based", "is_exclusive_single_use"]
            # Regular product file metadata fields
            file_metadata_fields = [
                "file_name", "file_size", "file_type", "file_path", "file_mime_type"
            ]
            line_based_fields = [
                "inventory_file_path", "total_lines", "available_lines", "reserved_lines",
                "line_price", "max_quantity_per_order", "preview_format", "allow_shared_inventory"
            ]
            exclusive_fields = [
                "is_purchased", "purchased_by_user_id", "purchase_date",
                "exclusive_file_path", "exclusive_file_type", "exclusive_file_size",
                "exclusive_file_mime_type", "expiration_date"
            ]

            all_optional_fields = basic_fields + file_metadata_fields + line_based_fields + exclusive_fields

            for field in all_optional_fields:
                if field in product_data and product_data[field] is not None:
                    cleaned_data[field] = product_data[field]

            return {
                "valid": True,
                "cleaned_data": cleaned_data
            }
            
        except Exception as e:
            logger.error(f"Error in complete product validation: {e}")
            return {
                "valid": False,
                "errors": [f"Validation error: {str(e)}"],
                "cleaned_data": {}
            }


# Global instance for easy access
product_validator = ProductValidator()
