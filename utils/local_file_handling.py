import os
import uuid
import aiohttp
import logging
from datetime import datetime
from pathlib import Path
import aiofiles
from aiogram import Bo<PERSON>
from aiogram.types import Message
import re
from typing import Dict, Any, Optional
from urllib.parse import urlparse
from utils.helpers import ensure_upload_directories
from utils.file_metadata import (
    store_file_metadata,
    get_original_filename,
    get_file_metadata,
    normalize_file_path,
    resolve_file_path,
)

# Base directory for file storage
BASE_DIR = Path(__file__).parent.parent / "uploads"
# Logger setup
logger = logging.getLogger(__name__)

# Define folder constants
PRODUCT_IMAGES_FOLDER = "product_images"
CATEGORY_IMAGES_FOLDER = "category_images"
USER_UPLOADS_FOLDER = "user_uploads"
FILE_UPLOADS_FOLDER = "files"
TEMP_FOLDER = "temp"


def generate_file_path(folder, original_filename):
    """
    Generate a unique file path for uploaded files.

    Args:
        folder (str): The subdirectory to save the file in
        original_filename (str): The original filename

    Returns:
        tuple: (absolute_path, relative_path) where:
            - absolute_path: The full path where the file will be saved
            - relative_path: The path relative to uploads directory
    """
    # Create folder if it doesn't exist
    folder_path = BASE_DIR / folder
    os.makedirs(folder_path, exist_ok=True)

    # Get extension from original filename
    _, ext = os.path.splitext(original_filename)
    if not ext:
        ext = ".jpg"  # Default extension if none provided

    # Sanitize original filename part (remove special chars)
    base_name = os.path.basename(original_filename)
    sanitized_name = "".join(c for c in base_name if c.isalnum() or c in "._- ")

    # Check if file with this sanitized name already exists
    potential_path = folder_path / sanitized_name
    if not potential_path.exists():
        # If file doesn't exist yet, use the sanitized original name
        filename = sanitized_name
    else:
        # If file already exists, use the unique name approach
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        unique_id = str(uuid.uuid4())[:8]  # First 8 chars of UUID
        filename = f"file_{unique_id}_{sanitized_name}"

    # Create both absolute and relative paths
    absolute_path = folder_path / filename
    relative_path = f"{folder}/{filename}"

    # Normalize the relative path for consistent storage
    from utils.file_metadata import normalize_file_path
    normalized_relative_path = normalize_file_path(relative_path)

    return absolute_path, normalized_relative_path


async def process_file_upload(
    source_path: str,
    original_filename: str,
    folder: str,
    product_id: str = None,
    file_size: int = None,
    mime_type: str = None
) -> Dict[str, Any]:
    """
    Unified file processing function that handles all file uploads consistently.

    This function ensures consistent behavior across all upload systems including:
    - Filename preservation with sanitization
    - Unique path generation
    - Security validation
    - Metadata storage
    - Database-ready relative path formatting

    Args:
        source_path (str): Path to the source file to process
        original_filename (str): Original filename to preserve
        folder (str): Target subfolder within uploads directory
        product_id (str, optional): Product ID for metadata tracking
        file_size (int, optional): File size in bytes
        mime_type (str, optional): MIME type of the file

    Returns:
        Dict[str, Any]: File processing result containing:
            - success (bool): Whether processing succeeded
            - absolute_path (str): Full path to the processed file
            - relative_path (str): Path relative to uploads directory
            - original_filename (str): Original filename
            - sanitized_filename (str): Final sanitized filename used
            - file_size (int): File size in bytes
            - mime_type (str): MIME type
            - error (str): Error message if processing failed
    """
    try:
        # Validate inputs
        if not source_path or not os.path.exists(source_path):
            return {
                "success": False,
                "error": f"Source file does not exist: {source_path}"
            }

        if not original_filename:
            original_filename = os.path.basename(source_path)

        # Generate consistent file path using existing function
        absolute_path, relative_path = generate_file_path(folder, original_filename)

        # Get file metadata if not provided
        if file_size is None:
            file_size = os.path.getsize(source_path)

        if mime_type is None:
            import mimetypes
            mime_type, _ = mimetypes.guess_type(source_path)
            if not mime_type:
                mime_type = "application/octet-stream"

        # Copy file to destination (use copy2 to preserve metadata)
        import shutil
        shutil.copy2(source_path, absolute_path)

        # Store file metadata using existing system
        store_file_metadata(
            relative_path,
            original_filename,
            file_size,
            mime_type
        )

        # Get the final sanitized filename
        sanitized_filename = os.path.basename(absolute_path)

        logger.info(f"Successfully processed file upload: {original_filename} -> {relative_path}")

        return {
            "success": True,
            "absolute_path": str(absolute_path),
            "relative_path": relative_path,
            "original_filename": original_filename,
            "sanitized_filename": sanitized_filename,
            "file_size": file_size,
            "mime_type": mime_type
        }

    except Exception as e:
        logger.error(f"Error processing file upload: {e}", exc_info=True)
        return {
            "success": False,
            "error": str(e)
        }


async def save_file(file_content, file_path):
    """
    Save a file to the specified path asynchronously.

    Args:
        file_content: The file content to save (file-like object with read() or bytes)
        file_path (Path): The path to save the file to

    Returns:
        str: The relative path to the saved file
    """
    # Make sure directory exists
    os.makedirs(os.path.dirname(file_path), exist_ok=True)

    # Write file
    async with aiofiles.open(file_path, "wb") as f:
        # Handle both bytes and file-like objects
        if isinstance(file_content, bytes):
            await f.write(file_content)
        else:
            # Assume it's a file-like object with read() method
            await f.write(file_content.read())

    # Return the path relative to the project root
    relative_path = os.path.relpath(file_path, Path(__file__).parent.parent)
    return relative_path


def normalize_image_path(image_path):
    """
    Normalize image paths for consistent handling across the application.
    Works with URLs, file paths, and file IDs.

    Args:
        image_path: String containing a URL, file path, or file ID

    Returns:
        Normalized image path or None if invalid
    """
    import os
    from aiogram.types import FSInputFile

    if not image_path or not isinstance(image_path, str):
        return None

    # Handle URLs
    if image_path.startswith(("http://", "https://")):
        return image_path

    # Check if it's a data URI (for embedded images)
    if image_path.startswith("data:image"):
        return None  # Telegram doesn't support data URIs directly

    # Check common locations for image files
    if os.path.isabs(image_path) and os.path.isfile(image_path):
        return FSInputFile(image_path)

    # Try relative paths from various base directories
    possible_paths = [
        image_path,
        os.path.join(os.getcwd(), image_path),
        os.path.join(os.getcwd(), "uploads", image_path),
        os.path.join(
            os.getcwd(), "uploads", "product_images", os.path.basename(image_path)
        ),
        os.path.join(
            os.getcwd(), "uploads", "category_images", os.path.basename(image_path)
        ),
    ]

    for path in possible_paths:
        if os.path.isfile(path):
            return FSInputFile(path)

    # If we get here and it's not a URL or file path, assume it's a file_id
    # Check if it looks like a file_id (base64-like string)
    if re.match(r"^[A-Za-z0-9_\-]{20,}$", image_path):
        return image_path

    # We couldn't determine what this is
    return None


async def download_file_from_telegram(
    bot: Bot, file_id: str, folder: str, original_filename=None
) -> Dict[str, Any]:
    """
    Download a file from Telegram and save it locally.

    Args:
        bot: Telegram Bot instance
        file_id: Telegram file_id
        folder: Destination subfolder
        original_filename: Original filename (optional)

    Returns:
        dict: File data including url_path, local_path, original_file_id
    """
    try:
        # Ensure directories exist
        ensure_upload_directories()

        # Get file info from Telegram
        file_info = await bot.get_file(file_id)

        # Extract extension from Telegram file path if original_filename doesn't have one
        telegram_ext = os.path.splitext(file_info.file_path)[1]

        # Clean up and preserve original filename
        if original_filename:
            # Keep original filename but sanitize it
            # Replace spaces with underscores and remove problematic characters
            safe_filename = "".join(
                c
                for c in original_filename.replace(" ", "_")
                if c.isalnum() or c in "._-"
            )

            # Add extension from Telegram if original doesn't have one
            if not os.path.splitext(safe_filename)[1] and telegram_ext:
                safe_filename = f"{safe_filename}{telegram_ext}"
        else:
            # Create a filename based on file_id if no original name
            safe_filename = f"file_{file_id[-8:]}{telegram_ext}"

        # Use our improved generate_file_path function to get both absolute and relative paths
        file_path, relative_path = generate_file_path(folder, safe_filename)

        # Download the file
        downloaded_file = await bot.download_file(file_info.file_path, file_path)

        # Determine mime type
        mime_type = None
        if telegram_ext.lower() in [".jpg", ".jpeg", ".png", ".gif", ".webp"]:
            mime_type = f"image/{telegram_ext[1:].lower()}"
        elif telegram_ext.lower() == ".pdf":
            mime_type = "application/pdf"
        elif telegram_ext.lower() == ".doc" or telegram_ext.lower() == ".docx":
            mime_type = "application/msword"
        elif telegram_ext.lower() == ".xls" or telegram_ext.lower() == ".xlsx":
            mime_type = "application/vnd.ms-excel"
        elif telegram_ext.lower() == ".zip":
            mime_type = "application/zip"
        elif telegram_ext.lower() == ".txt":
            mime_type = "text/plain"

        # Store metadata for the file including the file_id for backward compatibility
        store_file_metadata(
            relative_path, original_filename or safe_filename, mime_type, file_id
        )

        return {
            "local_path": str(file_path),
            "url_path": relative_path,  # Store only the path relative to uploads/
            "original_file_id": file_id,
            "original_filename": original_filename or safe_filename,
            "file_size": file_info.file_size,
        }
    except Exception as e:
        logger.error(f"Error downloading file from Telegram: {e}", exc_info=True)
        return None


async def process_image_message(message: Message, folder: str) -> dict:
    """
    Process an image message and save the image to local storage.
    Handles both photo and document uploads.

    Args:
        message: Message object containing a photo or document
        folder: Target folder (category_images, product_images, or files)

    Returns:
        dict: Dictionary containing file info including local path
    """
    try:
        # Handle photo messages
        if message.photo:
            photo = message.photo[-1]
            file_id = photo.file_id
            original_filename = None

        # Handle document messages (often used for sending images with better quality)
        elif message.document:
            # Check if it's an image by MIME type
            is_image = False

            # Check MIME type
            if message.document.mime_type and message.document.mime_type.startswith(
                "image/"
            ):
                is_image = True

            # Check file extension for GIFs specifically
            if (
                message.document.file_name
                and message.document.file_name.lower().endswith(".gif")
            ):
                is_image = True
                logger.info(
                    f"GIF file detected in process_image_message: {message.document.file_name}"
                )

            if is_image:
                file_id = message.document.file_id
                original_filename = message.document.file_name
            else:
                logger.error("No valid image found in message")
                return None
        else:
            logger.error("No valid image found in message")
            return None

        # Download the file
        file_data = await download_file_from_telegram(
            bot=message.bot,
            file_id=file_id,
            folder=folder,
            original_filename=original_filename,
        )

        return file_data

    except Exception as e:
        logger.error(f"Error processing image message: {e}", exc_info=True)
        return None


async def process_document_file(
    message: Message, folder: str = FILE_UPLOADS_FOLDER
) -> dict:
    """
    Process a document message and save the file to local storage.

    Args:
        message: Message object containing a document
        folder: Target folder (default: files)

    Returns:
        dict: Dictionary containing file info including local path
    """
    try:
        if not message.document:
            logger.error("No document found in message")
            return None

        file_id = message.document.file_id
        original_filename = (
            message.document.file_name
            or f"file_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        )
        file_size = message.document.file_size
        mime_type = message.document.mime_type

        # Download the file
        file_data = await download_file_from_telegram(
            bot=message.bot,
            file_id=file_id,
            folder=folder,
            original_filename=original_filename,
        )

        if file_data:
            # Add additional document-specific metadata
            file_data["file_size"] = file_size
            file_data["mime_type"] = mime_type
            file_data["document_id"] = (
                file_id  # Store document_id separately from file_id
            )

        return file_data

    except Exception as e:
        logger.error(f"Error processing document file: {e}", exc_info=True)
        return None


async def save_external_image(url: str, folder: str) -> dict:
    """
    Download an image from an external URL and save it locally.

    Args:
        url: External image URL
        folder: Target folder (category_images, product_images, or files)

    Returns:
        dict: Dictionary containing file info including local path
    """
    try:
        # Extract extension and original filename from URL
        parsed_url = urlparse(url)
        url_path = parsed_url.path.split("?")[0]  # Remove query parameters
        original_filename = os.path.basename(url_path)
        _, ext = os.path.splitext(original_filename)

        # Use default extension and filename if not available
        if not ext:
            ext = ".jpg"  # Default extension
        if not original_filename or original_filename == "":
            original_filename = f"image{ext}"

        # Clean up original filename
        safe_filename = "".join(
            c for c in original_filename.replace(" ", "_") if c.isalnum() or c in "._-"
        )

        # Use our improved generate_file_path function to get both absolute and relative paths
        file_path, relative_path = generate_file_path(folder, safe_filename)

        # Download the image
        async with aiohttp.ClientSession() as session:
            async with session.get(url) as response:
                if response.status != 200:
                    logger.error(
                        f"Failed to download image from URL: {response.status}"
                    )
                    return None

                # Save the file
                async with aiofiles.open(file_path, "wb") as f:
                    await f.write(await response.read())

        # Determine mime type
        mime_type = None
        if ext.lower() in [".jpg", ".jpeg", ".png", ".gif", ".webp"]:
            mime_type = f"image/{ext[1:].lower()}"
        elif ext.lower() == ".pdf":
            mime_type = "application/pdf"
        elif ext.lower() == ".doc" or ext.lower() == ".docx":
            mime_type = "application/msword"
        elif ext.lower() == ".xls" or ext.lower() == ".xlsx":
            mime_type = "application/vnd.ms-excel"
        elif ext.lower() == ".zip":
            mime_type = "application/zip"
        elif ext.lower() == ".txt":
            mime_type = "text/plain"

        # Store metadata
        store_file_metadata(relative_path, original_filename, mime_type)

        # Return file information
        file_data = {
            "original_url": url,
            "folder": folder,
            "filename": os.path.basename(file_path),
            "local_path": str(file_path),
            "url_path": relative_path,  # Store only the path relative to uploads/
            "file_size": os.path.getsize(file_path),
            "original_filename": original_filename,
        }

        logger.info(f"External image downloaded successfully: {relative_path}")
        return file_data

    except Exception as e:
        logger.error(f"Error saving external image: {e}", exc_info=True)
        return None


def get_file_url_path(relative_path):
    """
    Convert a relative file path to a URL path for accessing via web server.

    Args:
        relative_path: Relative path of file (e.g. 'uploads/product_images/file.jpg')

    Returns:
        str: URL path to access the file
    """
    # Handle cases where the path might already be formatted
    if relative_path.startswith(("http://", "https://")):
        return relative_path

    # For paths with uploads/, ensure we only keep the part starting from uploads/
    if "uploads/" in relative_path:
        # Find the index of 'uploads/'
        uploads_index = relative_path.find("uploads/")
        if uploads_index != -1:
            # Return only the part starting from 'uploads/'
            return relative_path[uploads_index:]

    # If it's already a path starting with / (for web server), return as is
    if relative_path.startswith("/"):
        return relative_path

    return relative_path


def validate_telegram_url(url_or_path):
    """
    Validates and properly formats a URL for Telegram API compatibility.
    Less strict than before but still ensures security.

    Args:
        url_or_path: URL or path to validate

    Returns:
        tuple: (is_valid, processed_url)
            - is_valid: Boolean indicating if URL is valid for Telegram
            - processed_url: Properly formatted URL or None if invalid
    """
    # Handle local paths - these can't be sent directly to Telegram
    if os.path.exists(url_or_path) or url_or_path.startswith(("/", "\\", "./")):
        return False, None

    # Check if it's a valid URL
    if not url_or_path.startswith(("http://", "https://")):
        return False, None

    try:
        # Parse the URL
        parsed = urlparse(url_or_path)

        # Ensure URL has a valid hostname
        if not parsed.netloc:
            return False, None

        # Simpler validation - just make sure it's a well-formed URL
        # Don't over-encode parts as this can break some image URLs
        return True, url_or_path
    except Exception as e:
        logger.error(f"URL validation error: {e}")
        # If any parsing error occurs, URL is invalid
        return False, None


def extract_image_from_object(obj, object_type=None):
    """
    Extract image source from an object (product or category).

    Args:
        obj: The object (product or category) to extract image from
        object_type: Optional type specifier ('product' or 'category') to prioritize specific fields

    Returns:
        Image source string or None if not found
    """
    if not obj:
        return None

    # Base directory for resolving relative paths
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # Check for structured image object first
    image_data = obj.get("image")
    if image_data and isinstance(image_data, dict):
        # Handle URL-type images
        if image_data.get("type") == "url":
            return image_data.get("url")  # Return the direct URL string

        # Handle file-type images
        if image_data.get("type") == "file":
            return image_data.get("path")

        # Legacy handling for path/url directly in the image object
        if image_data.get("path") or image_data.get("url"):
            return image_data.get("path") or image_data.get("url")

    # Helper function to check if a file exists and return relative path
    def resolve_local_path(path):
        if not path:
            return None

        logger.debug(f"Resolving local path: {path}")

        # If it's a URL, return as is
        if path.startswith(("http://", "https://")):
            return path

        # If it's a file_id reference, return as is
        if path.startswith("file_id:"):
            return path

        # First try to normalize the path
        normalized_path = normalize_file_path(path)
        if normalized_path:
            # Check if the normalized path exists
            full_path = os.path.join(base_dir, "uploads", normalized_path)
            if os.path.isfile(full_path):
                logger.debug(f"Found file with normalized path: {normalized_path}")
                return normalized_path

        # Check if it's already an absolute path
        if os.path.isabs(path) and os.path.isfile(path):
            # Convert absolute path to relative path
            try:
                rel_path = os.path.relpath(path, os.path.join(base_dir, "uploads"))
                rel_path = rel_path.replace("\\", "/")  # Normalize slashes
                logger.debug(f"Converted absolute path to relative: {rel_path}")
                return rel_path
            except ValueError:
                # If the paths are on different drives, just use the filename
                filename = os.path.basename(path)
                logger.debug(f"Using filename from absolute path: {filename}")
                return filename

        # Check if it's a relative path from the project root
        full_path = os.path.join(base_dir, path)
        if os.path.isfile(full_path):
            # Convert to relative path from uploads directory
            if "uploads" in full_path:
                # Extract the part after uploads/
                uploads_index = full_path.replace("\\", "/").find("uploads/")
                if uploads_index != -1:
                    rel_path = full_path.replace("\\", "/")[
                        uploads_index + len("uploads/") :
                    ]
                    logger.debug(
                        f"Found file at project root, relative path: {rel_path}"
                    )
                    return rel_path
            # If uploads not in path, use the filename
            filename = os.path.basename(full_path)
            logger.debug(f"Using filename from project root path: {filename}")
            return filename

        # For product and category images, check specific folders based on object_type
        filename = os.path.basename(path)
        if object_type == "product":
            product_path = os.path.join(base_dir, "uploads", "product_images", filename)
            if os.path.isfile(product_path):
                logger.debug(f"Found product image by object type")
                return f"product_images/{filename}"
        elif object_type == "category":
            category_path = os.path.join(
                base_dir, "uploads", "category_images", filename
            )
            if os.path.isfile(category_path):
                logger.debug(f"Found category image by object type")
                return f"category_images/{filename}"

        # If we couldn't find the file but have a path that looks like a relative path, return it
        if "/" in path or "\\" in path:
            # Try to normalize it one more time
            clean_path = path.replace("\\", "/")
            if any(
                subdir in clean_path
                for subdir in [
                    "product_images/",
                    "category_images/",
                    "files/",
                    "user_uploads/",
                ]
            ):
                logger.debug(
                    f"Returning path that looks like a relative path: {clean_path}"
                )
                return clean_path

        # Just return the filename as a last resort
        filename = os.path.basename(path)
        logger.debug(f"Returning just the filename: {filename}")
        return filename

    # If object_type is specified, prioritize accordingly
    if object_type == "category":
        # For categories, check in this priority order
        if (
            obj.get("image")
            and isinstance(obj.get("image"), dict)
            and obj["image"].get("path")
        ):
            path = obj["image"]["path"]
            resolved_path = resolve_local_path(path)
            if resolved_path:
                return resolved_path

        elif obj.get("category_image"):
            path = obj.get("category_image")
            resolved_path = resolve_local_path(path)
            if resolved_path:
                return resolved_path

        elif obj.get("image_url"):
            # For URLs, return as is
            if obj.get("image_url").startswith(("http://", "https://")):
                return obj.get("image_url")
            else:
                return resolve_local_path(obj.get("image_url"))

        elif obj.get("photo"):
            return obj.get("photo")

    elif object_type == "product":
        # For products, check in this priority order
        # First check for product-specific image fields to avoid using category images
        if obj.get("product_image"):
            path = obj.get("product_image")
            resolved_path = resolve_local_path(path)
            if resolved_path:
                return resolved_path

        if (
            obj.get("image")
            and isinstance(obj.get("image"), dict)
            and obj["image"].get("path")
        ):
            path = obj["image"]["path"]
            resolved_path = resolve_local_path(path)
            if resolved_path:
                return resolved_path

        elif obj.get("image"):
            path = obj.get("image")
            resolved_path = resolve_local_path(path)
            if resolved_path:
                return resolved_path

        elif obj.get("image_url"):
            # For URLs, return as is
            if obj.get("image_url").startswith(("http://", "https://")):
                return obj.get("image_url")
            else:
                # This is likely a local path - make sure to resolve it properly
                local_path = obj.get("image_url")
                # Try to resolve the path
                resolved_path = resolve_local_path(local_path)
                if resolved_path:
                    return resolved_path

                # If that fails, try with the base directory
                base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                full_path = os.path.join(base_dir, local_path)
                if os.path.isfile(full_path):
                    return full_path

                # Return the original path as a last resort
                return local_path

        elif obj.get("photo"):
            return obj.get("photo")

        elif obj.get("file_id"):
            return f"file_id:{obj.get('file_id')}"

    else:
        # Generic extraction (no specific type)
        # Check all possible image fields in priority order
        if (
            obj.get("image")
            and isinstance(obj.get("image"), dict)
            and obj["image"].get("path")
        ):
            path = obj["image"]["path"]
            resolved_path = resolve_local_path(path)
            if resolved_path:
                return resolved_path

        elif obj.get("image"):
            path = obj.get("image")
            resolved_path = resolve_local_path(path)
            if resolved_path:
                return resolved_path

        elif obj.get("image_url"):
            # For URLs, return as is
            if obj.get("image_url").startswith(("http://", "https://")):
                return obj.get("image_url")
            else:
                return resolve_local_path(obj.get("image_url"))

        elif obj.get("category_image"):
            path = obj.get("category_image")
            resolved_path = resolve_local_path(path)
            if resolved_path:
                return resolved_path

        elif obj.get("photo"):
            return obj.get("photo")

        elif obj.get("file_id"):
            return f"file_id:{obj.get('file_id')}"

    return None


async def get_file_path(file_source, folder: str = FILE_UPLOADS_FOLDER) -> tuple:
    """
    Gets a usable file path from various file sources (path, url, file_id).

    Args:
        file_source: File source (file_id, URL, or local path)
        folder: Target folder if downloading is needed

    Returns:
        tuple: (absolute_path, relative_path) where:
            - absolute_path: The full path to the file
            - relative_path: The path relative to uploads directory
        Returns (None, None) if file cannot be found/downloaded
    """
    if not file_source:
        return None, None

    # Get the base directory for resolving paths
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # If it's a URL, return as is
    if file_source.startswith(("http://", "https://")):
        logger.warning(f"URL source can't be used as direct file path: {file_source}")
        return None, file_source

    # If it's already a local file path that exists
    if os.path.isfile(file_source):
        # Convert absolute path to relative path if possible
        try:
            rel_path = os.path.relpath(file_source, os.path.join(base_dir, "uploads"))
            rel_path = rel_path.replace("\\", "/")  # Normalize slashes
            return file_source, rel_path
        except ValueError:
            # If paths are on different drives, use the filename
            filename = os.path.basename(file_source)
            return file_source, filename

    # If it's a file_id reference, try to resolve it using metadata
    if file_source.startswith("file_id:"):
        # Use the resolve_file_path function to find the corresponding file
        relative_path = resolve_file_path(file_source)
        if relative_path:
            # Convert relative path to absolute path
            full_path = os.path.join(base_dir, "uploads", relative_path)
            if os.path.isfile(full_path):
                logger.info(f"Resolved file_id reference to: {relative_path}")
                return full_path, relative_path

        logger.warning(f"Could not resolve file_id reference: {file_source}")
        return None, None

    # Normalize the path to be relative to uploads directory
    normalized_path = normalize_file_path(file_source)
    if normalized_path:
        # Try the normalized path
        full_path = os.path.join(base_dir, "uploads", normalized_path)
        if os.path.isfile(full_path):
            return full_path, normalized_path

    # Try multiple path resolution strategies to find the local file
    possible_paths = [
        (os.path.join(base_dir, file_source), file_source),
        (
            os.path.join(base_dir, "uploads", folder, os.path.basename(file_source)),
            f"{folder}/{os.path.basename(file_source)}",
        ),
        (os.path.join(base_dir, "uploads", file_source), file_source),
    ]

    # Add more thorough search for files with unique IDs
    if "-" in file_source:
        # Extract the original part of the filename (before unique ID)
        original_part = file_source.split("-")[0]
        uploads_dir = os.path.join(base_dir, "uploads")

        # Search in all upload folders
        for root, _, files in os.walk(uploads_dir):
            for file in files:
                # Check if file starts with the original part or contains the file_source
                if file.startswith(original_part) or file_source in file:
                    abs_path = os.path.join(root, file)
                    # Get the relative path from uploads directory
                    rel_path = os.path.relpath(abs_path, uploads_dir)
                    rel_path = rel_path.replace("\\", "/")  # Normalize slashes
                    possible_paths.append((abs_path, rel_path))

    # Try all possible paths
    for abs_path, rel_path in possible_paths:
        if os.path.isfile(abs_path):
            return abs_path, rel_path

    # If we couldn't find the file, log a warning
    logger.warning(f"Unable to resolve file path for: {file_source}")
    return None, None


def extract_file_from_product(product_data):
    """
    Extract file information from a product.

    Args:
        product_data: The product data dictionary

    Returns:
        str: Relative file path or None if not found
    """
    if not product_data:
        return None

    # Check various possible field names for file data
    if product_data.get("file_path"):
        file_path = product_data.get("file_path")
        # Handle URLs and file_id references as is
        if file_path.startswith(("http://", "https://", "file_id:")):
            return file_path
        # Normalize the path to be relative to uploads directory
        return normalize_file_path(file_path)

    if product_data.get("file_link"):
        file_link = product_data.get("file_link")
        # If it's a file_id reference, try to resolve it
        if file_link.startswith("file_id:"):
            resolved_path = resolve_file_path(file_link)
            if resolved_path:
                return resolved_path
            # If we couldn't resolve it, return the original file_id reference
            return file_link
        # If it's not a file_id reference, normalize it
        if not file_link.startswith(("http://", "https://")):
            return normalize_file_path(file_link)
        return file_link

    if product_data.get("file_url"):
        file_url = product_data.get("file_url")
        # If it's not a URL, normalize it
        if not file_url.startswith(("http://", "https://")):
            return normalize_file_path(file_url)
        return file_url

    if product_data.get("file") and isinstance(product_data.get("file"), dict):
        file_data = product_data.get("file")
        if file_data.get("path"):
            file_path = file_data.get("path")
            # Handle URLs and file_id references as is
            if file_path.startswith(("http://", "https://", "file_id:")):
                return file_path
            # Normalize the path to be relative to uploads directory
            return normalize_file_path(file_path)

        if file_data.get("url"):
            file_url = file_data.get("url")
            # If it's not a URL, normalize it
            if not file_url.startswith(("http://", "https://")):
                return normalize_file_path(file_url)
            return file_url

    return None


from typing import Union
from aiogram.types import FSInputFile


def validate_and_prepare_photo_arg(photo_source: str) -> Union[str, FSInputFile, None]:
    """
    Validates and prepares a photo source for sending to Telegram.

    Args:
        photo_source: URL, file_id, or local path

    Returns:
        Valid photo argument for Telegram or None if invalid
    """
    if (
        not photo_source
        or not isinstance(photo_source, str)
        or not photo_source.strip()
    ):
        logger.debug("Photo source is empty or not a string.")
        return None

    # If it's a URL, return as is
    if photo_source.startswith(("http://", "https://")):
        return photo_source

    # Get the base directory for resolving paths
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))

    # If it's a file_id reference, try to resolve it
    if photo_source.startswith("file_id:"):
        # Use the resolve_file_path function to find the corresponding file
        relative_path = resolve_file_path(photo_source)
        if relative_path:
            # Convert relative path to absolute path
            full_path = os.path.join(base_dir, "uploads", relative_path)
            if os.path.isfile(full_path):
                logger.info(f"Resolved file_id reference to: {relative_path}")
                return FSInputFile(full_path)

        # If we couldn't resolve it, extract the file_id and return it
        file_id = photo_source[len("file_id:") :]
        logger.warning(
            f"Could not resolve file_id reference, using raw file_id: {file_id}"
        )
        return file_id

    # If it's a local path, normalize it and check if file exists
    normalized_path = normalize_file_path(photo_source)
    if normalized_path:
        # Try with the normalized path
        full_path = os.path.join(base_dir, "uploads", normalized_path)
        if os.path.isfile(full_path):
            logger.debug(f"Using normalized path: {normalized_path}")
            return FSInputFile(full_path)

    # If it's already an absolute path and exists, use it
    if os.path.isabs(photo_source) and os.path.isfile(photo_source):
        # Convert to relative path for logging
        try:
            rel_path = os.path.relpath(photo_source, os.path.join(base_dir, "uploads"))
            rel_path = rel_path.replace("\\", "/")  # Normalize slashes
            logger.debug(f"Using absolute path: {photo_source} (relative: {rel_path})")
        except ValueError:
            # If paths are on different drives, just log the absolute path
            logger.debug(f"Using absolute path: {photo_source}")
        return FSInputFile(photo_source)

    # Try with the original path as a fallback for common relative paths
    if photo_source.startswith(
        ("uploads/", "category_images/", "product_images/", "user_uploads/", "files/")
    ):
        # Try with base directory first
        abs_path = os.path.join(base_dir, photo_source)
        if os.path.isfile(abs_path):
            logger.debug(f"Found file using base directory: {photo_source}")
            return FSInputFile(abs_path)

        # Try with current working directory
        abs_path = os.path.join(os.getcwd(), photo_source)
        if os.path.isfile(abs_path):
            logger.debug(f"Found file using current directory: {photo_source}")
            return FSInputFile(abs_path)
        else:
            logger.warning(f"Local image file not found: {abs_path}")
            # Don't return None here, fall through to the file_id assumption

    # Otherwise assume it's a file_id and return as is
    logger.debug(f"Treating as raw file_id: {photo_source}")
    return photo_source
