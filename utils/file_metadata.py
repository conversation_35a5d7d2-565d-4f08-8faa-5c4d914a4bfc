"""
File metadata storage and retrieval utilities.
This module provides functions to store and retrieve metadata about files,
such as original filenames, to ensure files maintain their integrity throughout
the upload-to-download process.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional
from utils.unified_validation import unified_validation

# Logger setup
logger = logging.getLogger(__name__)

# Base directory for metadata storage
BASE_DIR = Path(__file__).parent.parent / "uploads" / ".metadata"

# Ensure the metadata directory exists
os.makedirs(BASE_DIR, exist_ok=True)

# Metadata file path
METADATA_FILE = BASE_DIR / "file_metadata.json"

# Initialize metadata storage
if not os.path.exists(METADATA_FILE):
    with open(METADATA_FILE, "w") as f:
        json.dump({}, f)


def load_metadata() -> Dict[str, Any]:
    """
    Load file metadata from storage.

    Returns:
        Dict: Metadata dictionary
    """
    try:
        with open(METADATA_FILE, "r") as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError) as e:
        logger.error(f"Error loading metadata: {e}")
        return {}


def save_metadata(metadata: Dict[str, Any]) -> bool:
    """
    Save file metadata to storage.

    Args:
        metadata: Metadata dictionary to save

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        with open(METADATA_FILE, "w") as f:
            json.dump(metadata, f, indent=2)
        return True
    except Exception as e:
        logger.error(f"Error saving metadata: {e}")
        return False


def normalize_file_path(file_path: str) -> str:
    """
    Normalize a file path to be relative to uploads directory with security validation.

    Args:
        file_path: Any file path (absolute or relative)

    Returns:
        str: Normalized path relative to uploads directory, or empty string if invalid
    """
    # Handle None or empty strings
    if not file_path:
        return ""

    # Remove file_id: prefix if present
    if file_path.startswith("file_id:"):
        logger.warning(f"Attempted to normalize a file_id reference: {file_path}")
        return ""

    # Use secure path validation
    validation_result = unified_validation.validate_file_path(file_path, "uploads")
    if not validation_result["valid"]:
        logger.warning(f"Path validation failed for {file_path}: {validation_result['error']}")
        if validation_result.get("security_issue"):
            logger.error(f"Security issue detected in path {file_path}: {validation_result['security_issue']}")
        return ""

    # Return the sanitized path
    sanitized_path = validation_result["sanitized_path"]

    # Extract the part after uploads/ if it's still there
    if "uploads/" in sanitized_path:
        uploads_index = sanitized_path.find("uploads/")
        if uploads_index != -1:
            return sanitized_path[uploads_index + len("uploads/") :]

    # If path doesn't contain 'uploads/' but contains a subdirectory we recognize,
    # assume it's already relative to uploads
    known_subdirs = [
        "files/",
        "product_images/",
        "category_images/",
        "user_uploads/",
        "temp/",
        "exclusive_files/",
        "inventory/",
        "deliveries/",
        "exclusive_previews/",
        "welcome_images/",
    ]
    for subdir in known_subdirs:
        if sanitized_path.startswith(subdir):
            return sanitized_path

    # If it's just a filename, assume it's in the root of uploads
    if "/" not in sanitized_path and "\\" not in sanitized_path:
        return sanitized_path

    # Return the sanitized path as-is if it's already relative
    return sanitized_path


def store_file_metadata(
    file_path: str,
    original_filename: str,
    mime_type: Optional[str] = None,
    file_id: Optional[str] = None,
) -> bool:
    """
    Store metadata for a file.

    Args:
        file_path: Path to the file (relative to uploads directory)
        original_filename: Original filename before any modifications
        mime_type: MIME type of the file (optional)
        file_id: Telegram file_id if available (optional)

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Normalize the file path to be relative to uploads directory
        normalized_path = normalize_file_path(file_path)
        if not normalized_path:
            logger.error(f"Could not normalize path for metadata storage: {file_path}")
            return False

        # Load existing metadata
        metadata = load_metadata()

        # Prepare metadata object
        file_metadata = {
            "original_filename": original_filename,
            "mime_type": mime_type,
        }

        # Add file_id if provided (for backward compatibility)
        if file_id:
            file_metadata["file_id"] = file_id

        # Store metadata for this file
        metadata[normalized_path] = file_metadata

        # Save updated metadata
        return save_metadata(metadata)
    except Exception as e:
        logger.error(f"Error storing file metadata: {e}")
        return False


def get_file_metadata(file_path: str) -> Dict[str, Any]:
    """
    Get metadata for a file.

    Args:
        file_path: Path to the file (relative to uploads directory)

    Returns:
        Dict: Metadata for the file or empty dict if not found
    """
    try:
        # Normalize the file path to be relative to uploads directory
        normalized_path = normalize_file_path(file_path)
        if not normalized_path:
            logger.warning(
                f"Could not normalize path for metadata retrieval: {file_path}"
            )
            return {}

        # Load metadata
        metadata = load_metadata()

        # Return metadata for this file or empty dict if not found
        return metadata.get(normalized_path, {})
    except Exception as e:
        logger.error(f"Error getting file metadata: {e}")
        return {}


def get_original_filename(file_path: str) -> Optional[str]:
    """
    Get the original filename for a file.

    Args:
        file_path: Path to the file (relative to uploads directory)

    Returns:
        str: Original filename or None if not found
    """
    metadata = get_file_metadata(file_path)
    return metadata.get("original_filename")


def get_mime_type(file_path: str) -> Optional[str]:
    """
    Get the MIME type for a file.

    Args:
        file_path: Path to the file (relative to uploads directory)

    Returns:
        str: MIME type or None if not found
    """
    metadata = get_file_metadata(file_path)
    return metadata.get("mime_type")


def resolve_file_path(file_reference: str) -> Optional[str]:
    """
    Resolve a file reference to a normalized path relative to uploads directory.
    This function handles various file reference formats including file_id references.

    Args:
        file_reference: File reference (path, URL, or file_id reference)

    Returns:
        str: Normalized path relative to uploads directory or None if not resolvable
    """
    if not file_reference:
        return None

    # If it's a file_id reference, we need to find the corresponding file
    if file_reference.startswith("file_id:"):
        file_id = file_reference[len("file_id:") :]

        # Load all metadata to search for files with this file_id
        metadata = load_metadata()

        # Check if we have any files with this file_id in their metadata
        for path, file_meta in metadata.items():
            if file_meta.get("file_id") == file_id:
                logger.info(f"Resolved file_id:{file_id} to path: {path}")
                return path

        logger.warning(f"Could not resolve file_id reference: {file_reference}")
        return None

    # For regular paths, just normalize
    return normalize_file_path(file_reference)
