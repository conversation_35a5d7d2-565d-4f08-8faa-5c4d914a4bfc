"""
Product Price Calculator Module
Consolidated price calculation logic for all product types.
Eliminates duplication and ensures consistent pricing across the system.
"""

import logging
from typing import Dict, Any, Union

logger = logging.getLogger(__name__)


class ProductPriceCalculator:
    """
    Consolidated price calculator for all product types.
    Handles regular, line-based, and exclusive product pricing.
    """
    
    @staticmethod
    def calculate_line_price(product: Dict[str, Any]) -> float:
        """
        Calculate the price per line for line-based products.
        
        Args:
            product: Product data dictionary
            
        Returns:
            Price per line as float
        """
        try:
            # Use line_price if specified, otherwise fall back to base price
            line_price = product.get("line_price")
            if line_price is not None:
                return float(line_price)
            
            base_price = product.get("price") or 0
            return float(base_price)
            
        except (ValueError, TypeError) as e:
            logger.error(f"Error calculating line price: {e}")
            return 0.0
    
    @staticmethod
    def calculate_total_price(
        product: Dict[str, Any], 
        quantity: int = 1
    ) -> Dict[str, Any]:
        """
        Calculate total price for any product type.
        
        Args:
            product: Product data dictionary
            quantity: Quantity to calculate for
            
        Returns:
            Dictionary with price breakdown
        """
        try:
            if quantity <= 0:
                raise ValueError("Quantity must be positive")
            
            is_line_based = product.get("is_line_based", False)
            is_exclusive = product.get("is_exclusive_single_use", False)
            
            # Exclusive products always have quantity 1
            if is_exclusive:
                quantity = 1
            
            if is_line_based:
                unit_price = ProductPriceCalculator.calculate_line_price(product)
                total_price = unit_price * quantity
                
                return {
                    "unit_price": unit_price,
                    "quantity": quantity,
                    "total_price": total_price,
                    "product_type": "line_based",
                    "price_breakdown": f"${unit_price:.2f} × {quantity} = ${total_price:.2f}"
                }
            else:
                # Regular or exclusive product
                unit_price = float(product.get("price") or 0)
                total_price = unit_price * quantity
                
                product_type = "exclusive" if is_exclusive else "regular"
                
                return {
                    "unit_price": unit_price,
                    "quantity": quantity,
                    "total_price": total_price,
                    "product_type": product_type,
                    "price_breakdown": f"${unit_price:.2f}" if quantity == 1 else f"${unit_price:.2f} × {quantity} = ${total_price:.2f}"
                }
                
        except Exception as e:
            logger.error(f"Error calculating total price: {e}")
            return {
                "unit_price": 0.0,
                "quantity": quantity,
                "total_price": 0.0,
                "product_type": "unknown",
                "price_breakdown": "Error calculating price",
                "error": str(e)
            }
    
    @staticmethod
    def validate_price_constraints(
        product: Dict[str, Any], 
        quantity: int
    ) -> Dict[str, Any]:
        """
        Validate price and quantity constraints for a product.
        
        Args:
            product: Product data dictionary
            quantity: Requested quantity
            
        Returns:
            Validation result dictionary
        """
        try:
            errors = []
            
            # Basic quantity validation
            if quantity <= 0:
                errors.append("Quantity must be positive")
            
            # Line-based product constraints
            if product.get("is_line_based", False):
                max_quantity = product.get("max_quantity_per_order", 1)
                if quantity > max_quantity:
                    errors.append(f"Maximum {max_quantity} items per order")
                
                available_lines = product.get("available_lines", 0)
                if quantity > available_lines:
                    errors.append(f"Insufficient stock. Available: {available_lines}")
            
            # Exclusive product constraints
            elif product.get("is_exclusive_single_use", False):
                if quantity != 1:
                    errors.append("Exclusive products can only be purchased in quantity of 1")
                
                if product.get("is_purchased", False):
                    errors.append("This exclusive product has already been purchased")
            
            # Price validation
            price_calc = ProductPriceCalculator.calculate_total_price(product, quantity)
            if price_calc.get("error"):
                errors.append(f"Price calculation error: {price_calc['error']}")
            
            if errors:
                return {
                    "valid": False,
                    "errors": errors,
                    "price_calculation": price_calc
                }
            
            return {
                "valid": True,
                "price_calculation": price_calc
            }
            
        except Exception as e:
            logger.error(f"Error validating price constraints: {e}")
            return {
                "valid": False,
                "errors": [f"Validation error: {str(e)}"],
                "price_calculation": None
            }
    
    @staticmethod
    def format_price_display(price_calculation: Dict[str, Any]) -> str:
        """
        Format price calculation for display.
        
        Args:
            price_calculation: Result from calculate_total_price
            
        Returns:
            Formatted price string
        """
        try:
            if price_calculation.get("error"):
                return "Price calculation error"
            
            return price_calculation.get("price_breakdown", f"${price_calculation.get('total_price', 0):.2f}")
            
        except Exception as e:
            logger.error(f"Error formatting price display: {e}")
            return "Price display error"


# Global instance for easy access
product_price_calculator = ProductPriceCalculator()
