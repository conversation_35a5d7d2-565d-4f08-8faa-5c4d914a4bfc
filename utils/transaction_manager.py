"""
Transaction Manager Module
Handles complex transactions with proper rollback mechanisms.
Ensures data consistency across multiple operations.
"""

import logging
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime
from contextlib import contextmanager

logger = logging.getLogger(__name__)


class TransactionError(Exception):
    """Custom exception for transaction errors."""
    pass


class TransactionStep:
    """Represents a single step in a transaction with rollback capability."""
    
    def __init__(self, name: str, execute_func: Callable, rollback_func: Optional[Callable] = None, **kwargs):
        self.name = name
        self.execute_func = execute_func
        self.rollback_func = rollback_func
        self.kwargs = kwargs
        self.executed = False
        self.result = None
        self.error = None
    
    def execute(self) -> Any:
        """Execute the transaction step."""
        try:
            logger.info(f"Executing transaction step: {self.name}")
            self.result = self.execute_func(**self.kwargs)
            self.executed = True
            logger.info(f"Successfully executed transaction step: {self.name}")
            return self.result
        except Exception as e:
            self.error = e
            logger.error(f"Failed to execute transaction step {self.name}: {e}")
            raise TransactionError(f"Step '{self.name}' failed: {str(e)}")
    
    def rollback(self) -> bool:
        """Rollback the transaction step if possible."""
        if not self.executed or not self.rollback_func:
            return True
        
        try:
            logger.warning(f"Rolling back transaction step: {self.name}")
            self.rollback_func(self.result, **self.kwargs)
            logger.info(f"Successfully rolled back transaction step: {self.name}")
            return True
        except Exception as e:
            logger.error(f"Failed to rollback transaction step {self.name}: {e}")
            return False


class TransactionManager:
    """
    Manages complex transactions with automatic rollback on failure.
    Ensures data consistency across multiple operations.
    """
    
    def __init__(self, transaction_name: str):
        self.transaction_name = transaction_name
        self.steps: List[TransactionStep] = []
        self.executed_steps: List[TransactionStep] = []
        self.transaction_id = f"{transaction_name}_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
        self.start_time = None
        self.end_time = None
        self.success = False
    
    def add_step(self, name: str, execute_func: Callable, rollback_func: Optional[Callable] = None, **kwargs) -> 'TransactionManager':
        """Add a step to the transaction."""
        step = TransactionStep(name, execute_func, rollback_func, **kwargs)
        self.steps.append(step)
        return self
    
    def execute(self) -> Dict[str, Any]:
        """Execute all transaction steps with automatic rollback on failure."""
        self.start_time = datetime.now()
        logger.info(f"Starting transaction: {self.transaction_name} (ID: {self.transaction_id})")
        current_step = None

        try:
            results = {}

            # Execute all steps
            for step in self.steps:
                current_step = step
                try:
                    result = step.execute()
                    self.executed_steps.append(step)
                    results[step.name] = result
                except TransactionError:
                    # Step failed, initiate rollback
                    logger.error(f"Transaction {self.transaction_name} failed at step: {step.name}")
                    self._rollback()
                    raise

            # All steps succeeded
            self.success = True
            self.end_time = datetime.now()
            duration = (self.end_time - self.start_time).total_seconds()

            logger.info(f"Transaction {self.transaction_name} completed successfully in {duration:.2f}s")

            return {
                "success": True,
                "transaction_id": self.transaction_id,
                "results": results,
                "duration": duration,
                "steps_executed": len(self.executed_steps)
            }

        except Exception as e:
            self.end_time = datetime.now()
            duration = (self.end_time - self.start_time).total_seconds()

            logger.error(f"Transaction {self.transaction_name} failed after {duration:.2f}s: {e}")

            return {
                "success": False,
                "transaction_id": self.transaction_id,
                "error": str(e),
                "duration": duration,
                "steps_executed": len(self.executed_steps),
                "failed_step": current_step.name if current_step else "unknown"
            }
    
    def _rollback(self) -> None:
        """Rollback all executed steps in reverse order."""
        logger.warning(f"Initiating rollback for transaction: {self.transaction_name}")
        
        rollback_failures = []
        
        # Rollback in reverse order
        for step in reversed(self.executed_steps):
            try:
                if not step.rollback():
                    rollback_failures.append(step.name)
            except Exception as e:
                logger.error(f"Critical: Failed to rollback step {step.name}: {e}")
                rollback_failures.append(step.name)
        
        if rollback_failures:
            logger.critical(f"CRITICAL: Some rollback operations failed for transaction {self.transaction_name}: {rollback_failures}")
        else:
            logger.info(f"Successfully rolled back all steps for transaction: {self.transaction_name}")


@contextmanager
def transaction_context(transaction_name: str):
    """Context manager for transaction handling."""
    transaction = TransactionManager(transaction_name)
    result = None
    try:
        yield transaction
        # Transaction will be executed when the context exits
        result = transaction.execute()
        if not result["success"]:
            raise TransactionError(f"Transaction failed: {result.get('error', 'Unknown error')}")
        return result
    except Exception as e:
        logger.error(f"Transaction context error: {e}")
        raise
    finally:
        # Ensure result is available even if exception occurs
        if hasattr(transaction, '_context_result'):
            transaction._context_result = result


# Specific transaction functions for common operations

def create_purchase_transaction(user_id: int, cart_items: List[Dict], order_total: float) -> TransactionManager:
    """
    Create a transaction for processing a purchase.
    Includes balance deduction, inventory updates, and transaction recording.
    """
    from database.operations import (
        update_user_balance, add_transaction, get_user_balance,
        reserve_inventory_lines, reserve_inventory_lines_for_user,
        confirm_line_purchase, confirm_line_purchase_with_history
    )
    from utils.exclusive_product_db_operations import ExclusiveProductDBOperations
    
    transaction = TransactionManager(f"purchase_user_{user_id}")
    
    # Step 1: Validate user balance
    def validate_balance(user_id: int, required_amount: float) -> Dict[str, Any]:
        current_balance = get_user_balance(user_id)
        if current_balance is None:
            raise TransactionError(f"Failed to retrieve user balance for user {user_id}")
        if current_balance < required_amount:
            raise TransactionError(f"Insufficient balance: {current_balance} < {required_amount}")
        return {"current_balance": current_balance, "required_amount": required_amount}
    
    transaction.add_step(
        "validate_balance",
        validate_balance,
        user_id=user_id,
        required_amount=order_total
    )
    
    # Step 2: Reserve inventory for line-based products
    def reserve_all_inventory(cart_items: List[Dict], user_id: int) -> Dict[str, Any]:
        reservations = []
        for item in cart_items:
            if item.get("is_line_based", False):
                product_id = item.get("product_id")
                quantity = item.get("quantity", 1)

                # Use user-aware reservation for line-based products
                reservation_success = reserve_inventory_lines_for_user(product_id, quantity, user_id)
                if not reservation_success:
                    raise TransactionError(f"Failed to reserve {quantity} lines for product {product_id} - insufficient inventory or user already purchased lines")

                reservations.append({"product_id": product_id, "quantity": quantity})

        return {"reservations": reservations}
    
    def rollback_inventory_reservations(result: Dict[str, Any], user_id: int, **kwargs) -> None:
        from database.operations import release_reserved_lines
        for reservation in result.get("reservations", []):
            try:
                release_success = release_reserved_lines(reservation["product_id"], reservation["quantity"], user_id)
                if not release_success:
                    logger.error(f"Failed to release reserved lines for product {reservation['product_id']}")
            except Exception as e:
                logger.error(f"Exception during inventory rollback for product {reservation['product_id']}: {e}")
    
    transaction.add_step(
        "reserve_inventory",
        reserve_all_inventory,
        rollback_inventory_reservations,
        cart_items=cart_items,
        user_id=user_id
    )
    
    # Step 3: Mark exclusive products as purchased
    def mark_exclusive_products(cart_items: List[Dict], user_id: int) -> Dict[str, Any]:
        exclusive_purchases = []
        for item in cart_items:
            if item.get("is_exclusive_single_use", False):
                product_id = item.get("product_id")

                result = ExclusiveProductDBOperations.mark_exclusive_product_as_purchased(product_id, user_id)
                if result is None:
                    raise TransactionError(f"Failed to mark exclusive product {product_id} as purchased - operation returned None")

                if not isinstance(result, dict):
                    raise TransactionError(f"Invalid result type from exclusive product operation: {type(result)}")

                if not result.get("success", False):
                    error_msg = result.get("error", "Unknown error")
                    raise TransactionError(f"Failed to mark exclusive product {product_id} as purchased: {error_msg}")

                exclusive_purchases.append({"product_id": product_id})

        return {"exclusive_purchases": exclusive_purchases}
    
    def rollback_exclusive_purchases(result: Dict[str, Any], user_id: int, **kwargs) -> None:
        for purchase in result.get("exclusive_purchases", []):
            try:
                rollback_result = ExclusiveProductDBOperations.rollback_product_purchase(
                    purchase["product_id"], user_id, "Transaction rollback"
                )
                if rollback_result and not rollback_result.get("success", False):
                    logger.error(f"Failed to rollback exclusive product {purchase['product_id']}: {rollback_result.get('error', 'Unknown error')}")
            except Exception as e:
                logger.error(f"Exception during exclusive product rollback for {purchase['product_id']}: {e}")
    
    transaction.add_step(
        "mark_exclusive_products",
        mark_exclusive_products,
        rollback_exclusive_purchases,
        cart_items=cart_items,
        user_id=user_id
    )
    
    # Step 4: Deduct balance
    def deduct_user_balance(user_id: int, amount: float) -> Dict[str, Any]:
        current_balance = get_user_balance(user_id)
        if current_balance is None:
            raise TransactionError(f"Failed to retrieve user balance for user {user_id}")

        new_balance = current_balance - amount

        balance_update_success = update_user_balance(user_id, new_balance)
        if not balance_update_success:
            raise TransactionError(f"Failed to update user balance from {current_balance} to {new_balance}")

        return {"old_balance": current_balance, "new_balance": new_balance, "amount_deducted": amount}

    def rollback_balance_deduction(result: Dict[str, Any], user_id: int, **kwargs) -> None:
        rollback_success = update_user_balance(user_id, result["old_balance"])
        if not rollback_success:
            logger.error(f"CRITICAL: Failed to rollback balance for user {user_id} to {result['old_balance']}")
    
    transaction.add_step(
        "deduct_balance",
        deduct_user_balance,
        rollback_balance_deduction,
        user_id=user_id,
        amount=order_total
    )
    
    # Step 5: Record transaction
    def record_transaction(user_id: int, cart_items: List[Dict], order_total: float) -> Dict[str, Any]:
        item_names = [item.get("name", "Unknown") for item in cart_items]

        transaction_record = add_transaction(
            user_id=user_id,
            transaction_type="purchase",
            amount=order_total,
            items=item_names,
            note=f"Purchase of {len(cart_items)} items"
        )

        if transaction_record is None:
            raise TransactionError("Failed to record transaction - add_transaction returned None")

        if not isinstance(transaction_record, dict):
            raise TransactionError(f"Invalid transaction record type: {type(transaction_record)}")

        return {"transaction_record": transaction_record}
    
    transaction.add_step(
        "record_transaction",
        record_transaction,
        user_id=user_id,
        cart_items=cart_items,
        order_total=order_total
    )
    
    return transaction
