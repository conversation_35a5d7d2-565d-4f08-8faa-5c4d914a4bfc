"""
Consolidated Database Operations for Exclusive Single-Use Products
Provides optimized database operations for exclusive products.
"""

import logging
import time
import re
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone
from bson import ObjectId

from database.operations import (
    get_product,
    get_products,
    add_transaction
)
from utils.unified_validation import unified_validation

logger = logging.getLogger(__name__)


class ExclusiveProductDBOperations:
    """Consolidated database operations for exclusive single-use products."""

    @staticmethod
    def _validate_product_id(product_id: Any) -> Dict[str, Any]:
        """
        Validate and sanitize product ID to prevent NoSQL injection.
        Uses unified validation to eliminate duplication.

        Args:
            product_id: Product ID to validate

        Returns:
            Dict with validation result and sanitized ID
        """
        return unified_validation.validate_product_id(product_id)

    @staticmethod
    def _validate_user_id(user_id: Any) -> Dict[str, Any]:
        """
        Validate user ID to prevent injection attacks.
        Uses unified validation to eliminate duplication.

        Args:
            user_id: User ID to validate

        Returns:
            Dict with validation result
        """
        return unified_validation.validate_user_id(user_id)

    @staticmethod
    def _get_utc_now() -> datetime:
        """
        Get current UTC datetime with timezone awareness.
        Uses unified validation to eliminate duplication.

        Returns:
            Timezone-aware UTC datetime
        """
        return unified_validation.get_utc_now()

    @staticmethod
    def _ensure_timezone_aware(dt: datetime) -> datetime:
        """
        Ensure datetime is timezone-aware, defaulting to UTC if naive.

        Args:
            dt: Datetime to check

        Returns:
            Timezone-aware datetime
        """
        if dt.tzinfo is None:
            # Assume naive datetime is UTC
            return dt.replace(tzinfo=timezone.utc)
        return dt

    @staticmethod
    def get_exclusive_product_with_validation(product_id: Any) -> Optional[Dict[str, Any]]:
        """
        Get exclusive single-use product with validation.

        Args:
            product_id: Product ID

        Returns:
            Product dict if valid exclusive product, None otherwise
        """
        try:
            # Validate and sanitize product ID
            validation = ExclusiveProductDBOperations._validate_product_id(product_id)
            if not validation["valid"]:
                logger.warning(f"Invalid product ID: {validation['error']}")
                return None

            sanitized_product_id = validation["sanitized_id"]

            product = get_product(sanitized_product_id)
            if not product:
                logger.warning(f"Product {sanitized_product_id} not found")
                return None

            if not product.get("is_exclusive_single_use", False):
                logger.warning(f"Product {sanitized_product_id} is not an exclusive single-use product")
                return None

            return product

        except Exception as e:
            logger.error(f"Error getting exclusive product {product_id}: {e}")
            return None
    
    @staticmethod
    def get_available_exclusive_products(category_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get all available (unpurchased) exclusive products with optimized query and performance monitoring.

        Args:
            category_id: Optional category filter

        Returns:
            List of available exclusive products
        """
        start_time = time.time()
        operation_name = "get_available_exclusive_products"

        try:
            from database.operations import products_collection
            from utils.exclusive_product_performance import (
                exclusive_product_cache,
                exclusive_product_performance_monitor,
                ExclusiveProductQueryOptimizer
            )

            # Try cache first for category-specific queries
            cache_key = f"available_category_{category_id}" if category_id else "available_all"
            cached_result = exclusive_product_cache.get_product(cache_key)
            if cached_result is not None:
                duration = time.time() - start_time
                exclusive_product_performance_monitor.record_query_time(f"{operation_name}_cached", duration)
                logger.debug(f"Returned {len(cached_result)} available exclusive products from cache")
                return cached_result

            # Build optimized query using query optimizer
            query = ExclusiveProductQueryOptimizer.get_optimized_availability_query(category_id)
            projection = ExclusiveProductQueryOptimizer.get_optimized_projection()

            # Execute optimized query with projection for better performance
            cursor = products_collection.find(query, projection).sort("created_at", -1)  # Sort by newest first

            available_exclusive = list(cursor)

            # Convert ObjectId to string for consistency
            for product in available_exclusive:
                if "_id" in product:
                    product["id"] = product["_id"]

            # Cache the result
            exclusive_product_cache.cache_product(cache_key, available_exclusive)

            # Record performance metrics
            duration = time.time() - start_time
            exclusive_product_performance_monitor.record_query_time(operation_name, duration)

            logger.debug(f"Found {len(available_exclusive)} available exclusive products in {duration:.3f}s")
            return available_exclusive

        except Exception as e:
            # Record error
            duration = time.time() - start_time
            exclusive_product_performance_monitor.record_error(operation_name)
            logger.error(f"Error getting available exclusive products: {e}")
            return []
    
    @staticmethod
    def get_purchased_exclusive_products(user_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Get all purchased exclusive products with optimized query, optionally filtered by user.

        Args:
            user_id: Optional user ID filter

        Returns:
            List of purchased exclusive products
        """
        try:
            from database.operations import products_collection

            # Build optimized query
            query = {
                "is_exclusive_single_use": True,
                "is_purchased": True
            }

            # Add user filter if specified
            if user_id is not None:
                query["purchased_by_user_id"] = user_id

            # Execute optimized query with projection
            cursor = products_collection.find(
                query,
                {
                    "_id": 1,
                    "name": 1,
                    "price": 1,
                    "purchased_by_user_id": 1,
                    "purchase_date": 1,
                    "exclusive_file_type": 1,
                    "exclusive_file_size": 1,
                    "created_at": 1
                }
            ).sort("purchase_date", -1)  # Sort by most recent purchases first

            purchased_exclusive = list(cursor)

            # Convert ObjectId to string for consistency
            for product in purchased_exclusive:
                if "_id" in product:
                    product["id"] = product["_id"]

            logger.debug(f"Found {len(purchased_exclusive)} purchased exclusive products")
            return purchased_exclusive

        except Exception as e:
            logger.error(f"Error getting purchased exclusive products: {e}")
            return []
    
    @staticmethod
    def mark_exclusive_product_as_purchased(product_id: Any, user_id: int) -> Dict[str, Any]:
        """
        Mark an exclusive product as purchased by a specific user with atomic operation and cache management.

        Args:
            product_id: Product ID
            user_id: User ID who purchased the product

        Returns:
            Dict with 'success' key and additional details
        """
        start_time = time.time()
        operation_name = "mark_exclusive_product_as_purchased"

        try:
            # Validate and sanitize inputs
            product_validation = ExclusiveProductDBOperations._validate_product_id(product_id)
            if not product_validation["valid"]:
                logger.error(f"Invalid product ID in mark_exclusive_product_as_purchased: {product_validation['error']}")
                return {
                    "success": False,
                    "error": f"Invalid product ID: {product_validation['error']}",
                    "error_code": "INVALID_PRODUCT_ID"
                }

            user_validation = ExclusiveProductDBOperations._validate_user_id(user_id)
            if not user_validation["valid"]:
                logger.error(f"Invalid user ID in mark_exclusive_product_as_purchased: {user_validation['error']}")
                return {
                    "success": False,
                    "error": f"Invalid user ID: {user_validation['error']}",
                    "error_code": "INVALID_USER_ID"
                }

            sanitized_product_id = product_validation["sanitized_id"]
            sanitized_user_id = user_validation["sanitized_id"]

            # Use atomic update to prevent race conditions
            from database.operations import products_collection
            from utils.unified_product_performance import (
                exclusive_product_cache,
                exclusive_product_performance_monitor,
                ProductType
            )

            # CRITICAL: Invalidate cache BEFORE database update to prevent race conditions
            # This ensures that any concurrent reads will not get stale cached data
            exclusive_product_cache.invalidate_product(str(sanitized_product_id))
            exclusive_product_cache.clear_all()  # More aggressive but ensures consistency

            # Atomic update: only update if product is not already purchased
            update_result = products_collection.update_one(
                {
                    "_id": sanitized_product_id,
                    "is_exclusive_single_use": True,
                    "is_purchased": {"$ne": True}  # Only update if not already purchased
                },
                {
                    "$set": {
                        "is_purchased": True,
                        "purchased_by_user_id": sanitized_user_id,
                        "purchase_date": ExclusiveProductDBOperations._get_utc_now()
                    }
                }
            )

            if update_result.modified_count == 0:
                # Product was already purchased or doesn't exist
                product = ExclusiveProductDBOperations.get_exclusive_product_with_validation(sanitized_product_id)
                if not product:
                    logger.error(f"Invalid exclusive product {sanitized_product_id}")
                    exclusive_product_performance_monitor.record_error(operation_name)
                    return {
                        "success": False,
                        "error": f"Invalid exclusive product {sanitized_product_id}",
                        "error_code": "PRODUCT_NOT_FOUND"
                    }
                elif product.get("is_purchased", False):
                    logger.warning(f"Exclusive product {sanitized_product_id} already purchased")
                    exclusive_product_performance_monitor.record_error(f"{operation_name}_already_purchased")
                    return {
                        "success": False,
                        "error": f"Exclusive product {sanitized_product_id} already purchased",
                        "error_code": "ALREADY_PURCHASED"
                    }
                else:
                    logger.error(f"Failed to update exclusive product {sanitized_product_id} - unknown error")
                    exclusive_product_performance_monitor.record_error(operation_name)
                    return {
                        "success": False,
                        "error": f"Failed to update exclusive product {sanitized_product_id}",
                        "error_code": "UPDATE_FAILED"
                    }

            logger.info(f"Marked exclusive product {sanitized_product_id} as purchased by user {sanitized_user_id}")

            # Get updated product for transaction recording
            product = ExclusiveProductDBOperations.get_exclusive_product_with_validation(sanitized_product_id)
            if product:
                # Record transaction with correct function signature
                try:
                    add_transaction(
                        sanitized_user_id,
                        "exclusive_purchase",
                        product.get("price", 0.0),
                        product_id=sanitized_product_id,
                        product_name=product.get("name", "Exclusive Product"),
                        status="completed",
                        timestamp=ExclusiveProductDBOperations._get_utc_now(),
                        product_type="exclusive_single_use",
                        file_type=product.get("exclusive_file_type"),
                        file_size=product.get("exclusive_file_size")
                    )
                except Exception as e:
                    logger.warning(f"Failed to record transaction for exclusive product {sanitized_product_id}: {e}")

            # Record performance metrics
            duration = time.time() - start_time
            exclusive_product_performance_monitor.record_query_time(operation_name, duration)

            return {
                "success": True,
                "product_id": sanitized_product_id,
                "user_id": sanitized_user_id,
                "purchase_date": ExclusiveProductDBOperations._get_utc_now(),
                "operation_duration": duration
            }

        except Exception as e:
            # Record error and performance
            duration = time.time() - start_time
            exclusive_product_performance_monitor.record_error(operation_name)
            logger.error(f"Error marking exclusive product {product_id} as purchased: {e}")
            return {
                "success": False,
                "error": f"Exception occurred: {str(e)}",
                "error_code": "EXCEPTION",
                "operation_duration": duration
            }
    
    @staticmethod
    def get_user_exclusive_purchases(user_id: int) -> List[Dict[str, Any]]:
        """
        Get all exclusive products purchased by a specific user.
        
        Args:
            user_id: User ID
            
        Returns:
            List of exclusive products purchased by the user
        """
        try:
            return ExclusiveProductDBOperations.get_purchased_exclusive_products(user_id)
            
        except Exception as e:
            logger.error(f"Error getting user {user_id} exclusive purchases: {e}")
            return []
    
    @staticmethod
    def check_user_can_purchase_exclusive(product_id: Any, user_id: int) -> Dict[str, Any]:
        """
        Check if a user can purchase a specific exclusive product.
        
        Args:
            product_id: Product ID
            user_id: User ID
            
        Returns:
            Dict with check results
        """
        try:
            # Get product
            product = ExclusiveProductDBOperations.get_exclusive_product_with_validation(product_id)
            if not product:
                return {
                    "can_purchase": False,
                    "reason": "Product not found or not exclusive"
                }
            
            # Check if already purchased
            if product.get("is_purchased", False):
                purchased_by = product.get("purchased_by_user_id")
                if purchased_by == user_id:
                    return {
                        "can_purchase": False,
                        "reason": "You have already purchased this exclusive product"
                    }
                else:
                    return {
                        "can_purchase": False,
                        "reason": "This exclusive product has already been sold"
                    }
            
            # Check expiration date
            expiration_date = product.get("expiration_date")
            if expiration_date and datetime.now() > expiration_date:
                return {
                    "can_purchase": False,
                    "reason": "This exclusive product has expired"
                }
            
            return {
                "can_purchase": True,
                "product": product
            }
            
        except Exception as e:
            logger.error(f"Error checking if user {user_id} can purchase exclusive product {product_id}: {e}")
            return {
                "can_purchase": False,
                "reason": f"Check error: {str(e)}"
            }
    
    @staticmethod
    def get_exclusive_product_statistics(product_id: Optional[Any] = None) -> Dict[str, Any]:
        """
        Get statistics for exclusive products.
        
        Args:
            product_id: Optional specific product ID
            
        Returns:
            Dict with statistics
        """
        try:
            if product_id:
                # Statistics for specific product
                product = ExclusiveProductDBOperations.get_exclusive_product_with_validation(product_id)
                if not product:
                    return {"error": "Product not found or not exclusive"}
                
                return {
                    "product_id": product_id,
                    "product_name": product.get("name", "Unknown"),
                    "is_purchased": product.get("is_purchased", False),
                    "purchased_by_user_id": product.get("purchased_by_user_id"),
                    "purchase_date": product.get("purchase_date"),
                    "price": product.get("price", 0.0),
                    "file_type": product.get("exclusive_file_type"),
                    "file_size": product.get("exclusive_file_size"),
                    "expiration_date": product.get("expiration_date"),
                    "created_at": product.get("created_at"),
                    "last_updated": datetime.now().isoformat()
                }
            else:
                # Overall statistics
                all_products = get_products()
                exclusive_products = [p for p in all_products if p.get("is_exclusive_single_use", False)]
                
                total_exclusive = len(exclusive_products)
                purchased_count = len([p for p in exclusive_products if p.get("is_purchased", False)])
                available_count = total_exclusive - purchased_count
                
                # Calculate total revenue from exclusive products
                total_revenue = sum(p.get("price", 0.0) for p in exclusive_products if p.get("is_purchased", False))
                
                # Get expired products count
                expired_count = 0
                for product in exclusive_products:
                    expiration_date = product.get("expiration_date")
                    if expiration_date and datetime.now() > expiration_date and not product.get("is_purchased", False):
                        expired_count += 1
                
                return {
                    "total_exclusive_products": total_exclusive,
                    "available_products": available_count,
                    "purchased_products": purchased_count,
                    "expired_products": expired_count,
                    "total_revenue": total_revenue,
                    "average_price": total_revenue / purchased_count if purchased_count > 0 else 0,
                    "purchase_rate": (purchased_count / total_exclusive * 100) if total_exclusive > 0 else 0,
                    "last_updated": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"Error getting exclusive product statistics: {e}")
            return {"error": f"Failed to get statistics: {str(e)}"}

    @staticmethod
    def mark_product_delivered_and_remove_from_listings(
        product_id: Any, user_id: int, order_number: int
    ) -> Dict[str, Any]:
        """
        Mark exclusive product as delivered and automatically remove from customer listings.
        This is the main function called after successful delivery.

        Args:
            product_id: Product ID
            user_id: User who purchased the product
            order_number: Order number for tracking

        Returns:
            Dict with operation results
        """
        start_time = time.time()
        operation_name = "mark_product_delivered_and_remove_from_listings"

        try:
            from database.operations import mark_exclusive_product_delivered_and_remove
            from utils.exclusive_product_performance import (
                exclusive_product_cache,
                exclusive_product_performance_monitor
            )

            # Use the database function for atomic operation
            result = mark_exclusive_product_delivered_and_remove(product_id, user_id, order_number)

            if result["success"]:
                # Update cache to reflect the removal
                exclusive_product_cache.mark_product_delivered(str(product_id))
                exclusive_product_cache.mark_product_removed(str(product_id))

                # Clear all cache to ensure consistency across category listings
                exclusive_product_cache.clear_all()

                # Record performance metrics
                duration = time.time() - start_time
                exclusive_product_performance_monitor.record_query_time(operation_name, duration)

                logger.info(f"Successfully removed exclusive product {product_id} from listings after delivery")
            else:
                # Record error
                exclusive_product_performance_monitor.record_error(operation_name)
                logger.error(f"Failed to remove exclusive product {product_id} from listings: {result.get('error')}")

            return result

        except Exception as e:
            # Record error and performance
            duration = time.time() - start_time
            exclusive_product_performance_monitor.record_error(operation_name)
            logger.error(f"Error in mark_product_delivered_and_remove_from_listings for {product_id}: {e}")
            return {
                "success": False,
                "error": f"Operation failed: {str(e)}"
            }

    @staticmethod
    def rollback_product_purchase(
        product_id: Any, user_id: int, reason: str = "Delivery failed"
    ) -> Dict[str, Any]:
        """
        Rollback exclusive product purchase and restore to available status.
        Used when delivery fails after purchase.

        Args:
            product_id: Product ID
            user_id: User who attempted to purchase
            reason: Reason for rollback

        Returns:
            Dict with rollback results
        """
        try:
            from database.operations import rollback_exclusive_product_removal

            # Use the database function for atomic rollback
            result = rollback_exclusive_product_removal(product_id, user_id, reason)

            if result["success"]:
                logger.warning(f"Successfully rolled back exclusive product {product_id} purchase for user {user_id}")
            else:
                logger.error(f"Failed to rollback exclusive product {product_id} purchase: {result.get('error')}")

            return result

        except Exception as e:
            logger.error(f"Error in rollback_product_purchase for {product_id}: {e}")
            return {
                "success": False,
                "error": f"Rollback failed: {str(e)}"
            }

    @staticmethod
    def get_all_exclusive_products_for_admin(
        include_removed: bool = True, category_id: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Get all exclusive products for admin view, including removed/sold products.

        Args:
            include_removed: Whether to include removed products
            category_id: Optional category filter

        Returns:
            List of all exclusive products
        """
        try:
            from database.operations import products_collection

            # Build admin query
            query = {"is_exclusive_single_use": True}

            # Add category filter if specified
            if category_id is not None:
                query["category_id"] = category_id

            # If not including removed, filter them out
            if not include_removed:
                query["removed_from_listings"] = {"$ne": True}

            # Execute query with full projection for admin
            cursor = products_collection.find(query).sort([
                ("removed_from_listings", 1),  # Available first
                ("purchase_date", -1),         # Then by purchase date
                ("created_at", -1)             # Then by creation date
            ])

            admin_exclusive = list(cursor)

            # Convert ObjectId to string for consistency
            for product in admin_exclusive:
                if "_id" in product:
                    product["id"] = product["_id"]

            logger.debug(f"Found {len(admin_exclusive)} exclusive products for admin view")
            return admin_exclusive

        except Exception as e:
            logger.error(f"Error getting exclusive products for admin: {e}")
            return []

    @staticmethod
    def get_product_lifecycle_status(product_id: Any) -> Dict[str, Any]:
        """
        Get detailed lifecycle status of an exclusive product.

        Args:
            product_id: Product ID

        Returns:
            Dict with lifecycle information
        """
        try:
            from database.operations import get_exclusive_product_lifecycle_status

            return get_exclusive_product_lifecycle_status(product_id)

        except Exception as e:
            logger.error(f"Error getting product lifecycle status for {product_id}: {e}")
            return {"error": f"Status check failed: {str(e)}"}

    # ============================================================================
    # ADMIN MANAGEMENT WRAPPER METHODS
    # ============================================================================

    @staticmethod
    def get_exclusive_product_by_id(product_id: Any) -> Optional[Dict[str, Any]]:
        """Get exclusive product by ID - wrapper for admin management."""
        return ExclusiveProductDBOperations.get_exclusive_product_with_validation(product_id)

    @staticmethod
    def mark_as_purchased(product_id: Any, user_id: int, admin_action: bool = False) -> Dict[str, Any]:
        """Mark product as purchased - wrapper for admin management."""
        return ExclusiveProductDBOperations.mark_exclusive_product_as_purchased(product_id, user_id)

    @staticmethod
    def mark_as_delivered(product_id: Any, admin_action: bool = False) -> Dict[str, Any]:
        """Mark product as delivered - wrapper for admin management."""
        # For admin actions, we'll use a dummy order number and user ID
        try:
            # Get the product to find the purchaser
            product = ExclusiveProductDBOperations.get_exclusive_product_with_validation(product_id)
            if not product:
                return {"success": False, "error": "Product not found"}

            user_id = product.get("purchased_by_user_id", 0)
            order_number = 999999  # Admin override order number

            return ExclusiveProductDBOperations.mark_product_delivered_and_remove_from_listings(
                product_id, user_id, order_number
            )
        except Exception as e:
            logger.error(f"Error marking product as delivered: {e}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def remove_from_listings(product_id: Any, admin_action: bool = False) -> Dict[str, Any]:
        """Remove product from listings - admin management method."""
        try:
            from database.operations import update_product

            # Update the product to mark as removed from listings
            update_data = {
                "removed_from_listings": True,
                "admin_removed": admin_action
            }

            result = update_product(product_id, update_data)
            if result:
                return {"success": True, "message": "Product removed from listings"}
            else:
                return {"success": False, "error": "Failed to update product"}

        except Exception as e:
            logger.error(f"Error removing product from listings: {e}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def restore_to_available(product_id: Any, admin_action: bool = False) -> Dict[str, Any]:
        """Restore product to available status - admin management method."""
        try:
            from database.operations import update_product

            # Reset product to available status
            update_data = {
                "is_purchased": False,
                "removed_from_listings": False,
                "purchased_by_user_id": None,
                "purchase_date": None,
                "delivery_date": None,
                "admin_restored": admin_action
            }

            result = update_product(product_id, update_data)
            if result:
                return {"success": True, "message": "Product restored to available status"}
            else:
                return {"success": False, "error": "Failed to update product"}

        except Exception as e:
            logger.error(f"Error restoring product: {e}")
            return {"success": False, "error": str(e)}

    @staticmethod
    def delete_exclusive_product(product_id: Any, admin_action: bool = False) -> Dict[str, Any]:
        """Delete exclusive product permanently - admin management method."""
        try:
            from database.operations import delete_product
            import os

            # Get product details before deletion to clean up files
            product = ExclusiveProductDBOperations.get_exclusive_product_with_validation(product_id)

            if product:
                # Clean up associated file if it exists
                file_path = product.get("file_path")
                if file_path and os.path.exists(file_path):
                    try:
                        os.remove(file_path)
                        logger.info(f"Deleted file: {file_path}")
                    except Exception as e:
                        logger.warning(f"Could not delete file {file_path}: {e}")

            # Delete the product from database
            result = delete_product(product_id)
            if result:
                return {"success": True, "message": "Product deleted permanently"}
            else:
                return {"success": False, "error": "Failed to delete product"}

        except Exception as e:
            logger.error(f"Error deleting product: {e}")
            return {"success": False, "error": str(e)}
