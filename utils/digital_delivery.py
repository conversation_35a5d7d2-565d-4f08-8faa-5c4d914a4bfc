"""
Enhanced Digital Product Delivery System for All Product Types.
Consolidated delivery system that handles regular, line-based, and exclusive products
with consistent UI theming and improved performance.
"""

import os
import logging
from typing import Optional, Dict, Any, Set, List
from pathlib import Path
from datetime import datetime

from aiogram import Bot
from aiogram.types import FSInputFile, InlineKeyboardMarkup, InlineKeyboardButton

from utils.line_inventory import line_inventory_manager
from utils.exclusive_file_handler import exclusive_file_handler
from utils.exclusive_product_db_operations import ExclusiveProductDBOperations
from database.operations import (
    get_product,
    confirm_line_purchase,
    confirm_line_purchase_with_history,
    release_reserved_lines,
    add_transaction,
    update_product_inventory
)

logger = logging.getLogger(__name__)

class DeliveryError(Exception):
    """Custom exception for delivery-related errors."""
    pass

class InventoryError(Exception):
    """Custom exception for inventory-related errors."""
    pass




class DigitalDeliveryManager:
    """
    Enhanced digital product delivery manager for all product types.
    Supports regular, line-based, and exclusive products with consistent delivery flow.
    """

    def __init__(self, bot: Bot):
        self.bot = bot
        self.delivery_dir = Path("uploads/deliveries")
        self.delivery_dir.mkdir(parents=True, exist_ok=True)
        self._delivery_cache = {}  # Cache for recent deliveries
        # Track delivered orders to prevent duplicates
        self._delivered_orders: Set[str] = set()
        self._exclusive_deliveries: Set[str] = set()  # Track exclusive product deliveries

    @staticmethod
    def create_optimized_delivery_message(
        product_name: str,
        product_type: str,
        order_number: str,
        delivery_method: str,
        quantity: int = 1,
        additional_info: dict = None
    ) -> str:
        """
        Create an optimized delivery confirmation message with minimal redundancy.

        Args:
            product_name: Name of the delivered product
            product_type: Type of product (line_based, exclusive, regular)
            order_number: Order number for tracking
            delivery_method: How the product was delivered
            quantity: Number of items delivered
            additional_info: Optional additional information to include

        Returns:
            Formatted delivery confirmation message
        """
        from utils.line_product_manager import LineProductTheme
        from utils.exclusive_product_manager import ExclusiveProductTheme

        # Choose appropriate theme based on product type
        if product_type == "exclusive":
            theme = ExclusiveProductTheme
            header_title = "EXCLUSIVE PRODUCT DELIVERED"
            success_msg = "Your exclusive product has been delivered!"
            type_display = "Exclusive Single-Use"
        elif product_type == "line_based":
            theme = LineProductTheme
            header_title = "DIGITAL PRODUCT DELIVERED"
            success_msg = "Your line-based product has been delivered!"
            type_display = f"{quantity} items"
        else:
            theme = LineProductTheme
            header_title = "PRODUCT DELIVERED"
            success_msg = "Your digital product has been delivered!"
            type_display = f"Quantity: {quantity}"

        # Create compact header and basic info
        message_lines = [
            f"📋 <b>• {header_title} •</b>",
            "",
            f"{theme.HEADER_DIVIDER}",
            f"{theme.EMOJIS.get('success', '✅')} <b>{success_msg}</b>",
            "",
            f"{theme.EMOJIS.get('product', '📦')} <b>Product:</b> {product_name}",
        ]

        # Add type-specific information
        if product_type == "exclusive":
            message_lines.append(f"{theme.EMOJIS.get('single_use', '🔒')} <b>Type:</b> {type_display}")
        elif quantity > 1:
            message_lines.append(f"{theme.EMOJIS.get('quantity', '🔢')} <b>Quantity:</b> {type_display}")

        # Add order and delivery info
        message_lines.extend([
            f"🆔 <b>Order #:</b> <code>{order_number}</code>",
            f"{theme.EMOJIS.get('date', '📅')} <b>Delivered:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>",
            f"{theme.EMOJIS.get('delivery', '🚚')} <b>Delivery Method:</b> {delivery_method}",
        ])

        # Add additional info if provided
        if additional_info:
            message_lines.append("")
            for key, value in additional_info.items():
                message_lines.append(f"{value}")

        # Add footer with essential instructions
        message_lines.extend([
            "",
            f"{theme.SECTION_DIVIDER}",
            f"{theme.EMOJIS.get('save', '💾')} <i>Please save this content immediately</i>",
            f"{theme.EMOJIS.get('security', '🔒')} <i>Keep this content secure and do not share it</i>",
            "",
            f"📞 <i>Contact support if you have any issues with your delivery</i>"
        ])

        return "\n".join(message_lines)

    def _create_cc_shop_keyboard(self) -> InlineKeyboardMarkup:
        """
        Create inline keyboard with CC shop link button for delivery messages.

        Returns:
            InlineKeyboardMarkup with CC shop link button
        """
        return InlineKeyboardMarkup(
            inline_keyboard=[
                [
                    InlineKeyboardButton(
                        text="🛒 CC SHOP LINK",
                        url="https://telegra.ph/Useful-links-07-15"
                    )
                ]
            ]
        )

    def _resolve_secure_file_path(self, file_path: str, product_id: Any) -> Optional[str]:
        """
        Securely resolve file path to prevent path traversal attacks.

        Args:
            file_path: The file path from the product configuration
            product_id: Product ID for logging purposes

        Returns:
            Resolved secure file path or None if invalid
        """
        try:
            # Normalize the input path to remove any path traversal attempts
            normalized_path = os.path.normpath(file_path)

            # Check for path traversal attempts
            if '..' in normalized_path:
                logger.warning(f"Path traversal attempt detected for product {product_id}: {file_path}")
                return None

            # Define allowed base directories
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            allowed_base_dirs = [
                os.path.join(project_root, "uploads"),
                os.path.join(project_root, "exclusive_files"),
                os.path.join(project_root, "digital_products")
            ]

            # If path is relative, resolve it against allowed directories
            if not os.path.isabs(normalized_path):
                # Try each allowed directory until we find the file
                for base_dir in allowed_base_dirs:
                    candidate_path = os.path.join(base_dir, normalized_path)
                    real_candidate_path = os.path.realpath(candidate_path)

                    # Ensure the resolved path is still within the allowed directory
                    real_base_dir = os.path.realpath(base_dir)
                    if real_candidate_path.startswith(real_base_dir + os.sep) or real_candidate_path == real_base_dir:
                        if os.path.exists(real_candidate_path) and os.path.isfile(real_candidate_path):
                            return real_candidate_path

                # If file not found in any allowed directory, return None
                logger.error(f"File not found in allowed directories for product {product_id}: {file_path}")
                return None
            else:
                # For absolute paths, ensure they're within allowed directories
                real_path = os.path.realpath(normalized_path)
                for base_dir in allowed_base_dirs:
                    real_base_dir = os.path.realpath(base_dir)
                    if real_path.startswith(real_base_dir + os.sep) or real_path == real_base_dir:
                        if os.path.exists(real_path) and os.path.isfile(real_path):
                            return real_path

                logger.warning(f"Absolute path outside allowed directories for product {product_id}: {file_path}")
                return None

        except Exception as e:
            logger.error(f"Error resolving secure file path for product {product_id}: {e}")
            return None

    def _validate_delivery_request(self, user_id: int, product_id: int, quantity: int) -> Dict[str, Any]:
        """Validate delivery request parameters."""
        if not user_id or user_id <= 0:
            return {"valid": False, "error": "Invalid user ID"}

        if not product_id:
            return {"valid": False, "error": "Invalid product ID"}

        if quantity <= 0:
            return {"valid": False, "error": "Quantity must be greater than zero"}

        return {"valid": True}

    def _get_cache_key(self, user_id: int, product_id: int, quantity: int) -> str:
        """Generate cache key for delivery requests."""
        return f"{user_id}_{product_id}_{quantity}_{datetime.now().strftime('%Y%m%d%H')}"

    def _get_exclusive_delivery_key(self, user_id: int, product_id: int, order_number: int) -> str:
        """Generate unique key for exclusive product delivery tracking."""
        return f"exclusive_{user_id}_{product_id}_{order_number}"

    def _is_exclusive_already_delivered(self, user_id: int, product_id: int, order_number: int) -> bool:
        """Check if exclusive product has already been delivered for this order."""
        delivery_key = self._get_exclusive_delivery_key(user_id, product_id, order_number)
        return delivery_key in self._exclusive_deliveries

    def _mark_exclusive_delivered(self, user_id: int, product_id: int, order_number: int):
        """Mark exclusive product as delivered to prevent duplicates."""
        delivery_key = self._get_exclusive_delivery_key(user_id, product_id, order_number)
        self._exclusive_deliveries.add(delivery_key)
        logger.info(f"Marked exclusive product {product_id} as delivered for user {user_id}, order {order_number}")


    
    async def process_line_purchase(
        self,
        user_id: int,
        product_id: Any,
        quantity: int,
        order_number: int,
        payment_track_id: str = None
    ) -> Dict[str, Any]:
        """
        Enhanced line-based product purchase processing with improved error handling.

        Args:
            user_id: Telegram user ID
            product_id: Product ID
            quantity: Number of lines to deliver
            order_number: Order number for tracking
            payment_track_id: Payment tracking ID

        Returns:
            Dict with delivery status and details
        """
        # Validate input parameters
        logger.info(f"Processing line purchase: user_id={user_id}, product_id={product_id}, quantity={quantity}, order_number={order_number}")
        validation = self._validate_delivery_request(user_id, product_id, quantity)
        if not validation["valid"]:
            logger.error(f"Validation failed for line purchase: {validation['error']}")
            return {"success": False, "error": validation["error"]}

        try:
            # Get product details with enhanced error handling
            product = get_product(product_id)
            if not product:
                logger.error(f"Product {product_id} not found for user {user_id}")
                return {"success": False, "error": "Product not found"}

            if not product.get("is_line_based"):
                logger.error(f"Product {product_id} is not line-based")
                return {"success": False, "error": "Product is not line-based"}

            # Validate inventory file existence and accessibility
            inventory_file = product.get("inventory_file_path")
            if not inventory_file:
                logger.error(f"No inventory file path for product {product_id}")
                return {"success": False, "error": "Inventory file not configured"}

            if not os.path.exists(inventory_file):
                logger.error(f"Inventory file not found: {inventory_file}")
                return {"success": False, "error": "Inventory file not found"}

            # Check file permissions
            if not os.access(inventory_file, os.R_OK | os.W_OK):
                logger.error(f"Insufficient permissions for inventory file: {inventory_file}")
                return {"success": False, "error": "Inventory file access denied"}

            # Check if this is a structured inventory file
            is_structured = line_inventory_manager.is_structured_inventory(inventory_file)

            # Extract lines or records from inventory with shared inventory support
            allow_shared_inventory = product.get("allow_shared_inventory", False)
            line_indices = []

            logger.info(f"Starting inventory extraction: structured={is_structured}, shared_inventory={allow_shared_inventory}")

            try:
                if is_structured:
                    # For structured records, use shared inventory-aware extraction
                    logger.info(f"Extracting {quantity} structured records for user {user_id} from {inventory_file}")
                    extracted_data, extraction_success, line_indices = line_inventory_manager.extract_lines_for_user(
                        inventory_file, quantity, user_id, product_id, allow_shared_inventory
                    )
                    data_type = "records"
                else:
                    # Use shared inventory-aware extraction for regular lines
                    logger.info(f"Extracting {quantity} lines for user {user_id} from {inventory_file}")
                    extracted_data, extraction_success, line_indices = line_inventory_manager.extract_lines_for_user(
                        inventory_file, quantity, user_id, product_id, allow_shared_inventory
                    )
                    data_type = "lines"

                logger.info(f"Extraction result: success={extraction_success}, data_count={len(extracted_data) if extracted_data else 0}")
            except Exception as e:
                logger.error(f"Error extracting {data_type} from {inventory_file}: {e}")
                logger.exception(f"Full extraction error details:")
                release_reserved_lines(product_id, quantity)
                return {"success": False, "error": f"Failed to extract inventory {data_type}"}

            if not extraction_success or not extracted_data:
                logger.error(f"{data_type.capitalize()} extraction failed for product {product_id}, quantity {quantity}")
                logger.error(f"Extraction details: success={extraction_success}, data_length={len(extracted_data) if extracted_data else 0}")
                release_reserved_lines(product_id, quantity)
                return {"success": False, "error": f"Failed to extract inventory {data_type}"}

            # Update database inventory counts and get remaining lines
            if not allow_shared_inventory:
                # For exclusive inventory, update the database with actual file count
                try:
                    remaining_lines = line_inventory_manager.get_available_lines_count(inventory_file)
                    update_product_inventory(product_id, remaining_lines)
                    logger.info(f"Updated inventory for product {product_id}: {remaining_lines} remaining")

                    # Immediately invalidate all caches for exclusive inventory
                    try:
                        from utils.line_product_manager import line_product_manager
                        line_product_manager.invalidate_all_caches_for_product(product_id)
                        logger.debug(f"Immediate cache invalidation for exclusive inventory product {product_id} after inventory update")
                    except Exception as cache_e:
                        logger.warning(f"Failed immediate cache invalidation for exclusive product {product_id}: {cache_e}")

                except Exception as e:
                    logger.error(f"Error updating inventory count: {e}")
                    # Continue with delivery even if count update fails
                    remaining_lines = 0  # Fallback value
            else:
                # For shared inventory, update the database with actual file count after marking lines as used
                try:
                    remaining_lines = line_inventory_manager.get_available_lines_count(inventory_file)
                    update_product_inventory(product_id, remaining_lines)
                    logger.info(f"Updated shared inventory for product {product_id}: {remaining_lines} remaining")

                    # Immediately invalidate user-specific cache for shared inventory
                    try:
                        from utils.line_product_manager import line_product_manager
                        line_product_manager.clear_validation_cache(product_id, user_id)
                        logger.debug(f"Immediate cache invalidation for shared inventory product {product_id} after purchase by user {user_id}")
                    except Exception as cache_e:
                        logger.warning(f"Failed immediate cache invalidation for shared product {product_id}, user {user_id}: {cache_e}")

                except Exception as e:
                    logger.error(f"Error updating shared inventory count: {e}")
                    # Continue with delivery even if count update fails
                    remaining_lines = 0  # Fallback value

            # Confirm the purchase in database with shared inventory support
            try:
                if allow_shared_inventory and line_indices:
                    confirm_line_purchase_with_history(product_id, quantity, user_id, line_indices)
                    logger.info(f"Confirmed shared inventory purchase for user {user_id}, product {product_id}, lines {line_indices}")
                else:
                    confirm_line_purchase(product_id, quantity)
                    logger.info(f"Confirmed exclusive inventory purchase for product {product_id}")

                # Invalidate caches after successful purchase confirmation to ensure fresh data
                try:
                    from utils.line_product_manager import line_product_manager

                    if allow_shared_inventory:
                        # For shared inventory, clear user-specific cache
                        line_product_manager.clear_validation_cache(product_id, user_id)
                        logger.debug(f"Invalidated user-specific cache for shared inventory product {product_id} after purchase confirmation by user {user_id}")
                    else:
                        # For exclusive inventory, clear all caches
                        line_product_manager.invalidate_all_caches_for_product(product_id)
                        logger.debug(f"Invalidated all caches for exclusive inventory product {product_id} after purchase confirmation")
                except Exception as cache_e:
                    logger.warning(f"Failed to invalidate caches for product {product_id}: {cache_e}")
                    # Don't fail the delivery for cache invalidation issues

            except Exception as e:
                logger.error(f"Error confirming purchase: {e}")
                # Continue with delivery

            # Determine delivery method based on content size and quantity
            delivery_method = product.get("delivery_method", "auto")  # auto, file, text

            # For small quantities and short content, offer text delivery with <pre> tags
            should_use_text_delivery = (
                delivery_method == "text" or
                (delivery_method == "auto" and quantity <= 3 and
                 all(len(str(item)) < 500 for item in extracted_data))
            )

            # Initialize delivery_file to None for all cases
            delivery_file = None

            if should_use_text_delivery and not is_structured:
                # Deliver as formatted text message with <pre> tags
                logger.info(f"Using text delivery for order {order_number}, user {user_id}")
                delivery_success = await self._deliver_line_content_as_text(
                    user_id, extracted_data, product["name"], quantity, order_number
                )
                logger.info(f"Text delivery result for order {order_number}: {delivery_success}")
                # For text delivery, set delivery_file to indicate text delivery
                delivery_file = "text_delivery"
            else:
                # Create delivery file with enhanced error handling
                logger.info(f"Using file delivery for order {order_number}, user {user_id}, structured: {is_structured}")
                try:
                    if is_structured:
                        delivery_file = line_inventory_manager.create_structured_delivery_file(
                            extracted_data, product["name"], order_number
                        )
                    else:
                        delivery_file = line_inventory_manager.create_delivery_file(
                            extracted_data, product["name"], order_number
                        )
                    logger.info(f"Created delivery file for order {order_number}: {delivery_file}")
                except Exception as e:
                    logger.error(f"Error creating delivery file: {e}")
                    logger.exception(f"Full delivery file creation error:")
                    return {"success": False, "error": "Failed to create delivery file"}

                if not delivery_file:
                    logger.error(f"Delivery file creation returned None for order {order_number}")
                    return {"success": False, "error": "Failed to create delivery file"}

                # Send delivery file to user
                logger.info(f"Sending delivery file {delivery_file} to user {user_id} for order {order_number}")
                delivery_success = await self.send_delivery_file(
                    user_id, delivery_file, product["name"], quantity, order_number
                )
                logger.info(f"File delivery result for order {order_number}: {delivery_success}")
            
            logger.info(f"Final delivery success status for order {order_number}: {delivery_success}")
            if delivery_success:
                # Log successful transaction
                transaction_data = {
                    "product_id": product_id,
                    "product_name": product["name"],
                    "quantity": quantity,
                    "order_number": order_number,
                    "delivery_file": delivery_file,
                    "payment_track_id": payment_track_id,
                    "data_type": data_type
                }

                # Add sample items for reference based on data type
                if is_structured:
                    # For structured records, store summary info
                    sample_records = []
                    for record in extracted_data[:3]:  # First 3 records
                        summary = {
                            "personal_name": record.get('personal_info', {}).get('name', 'N/A'),
                            "company_name": record.get('company_info', {}).get('name', 'N/A')
                        }
                        sample_records.append(summary)
                    transaction_data["sample_records"] = sample_records
                else:
                    # For simple lines, store first 3 items
                    transaction_data["items"] = extracted_data[:3]
                
                add_transaction(
                    user_id, 
                    "line_purchase", 
                    product.get("line_price", product["price"]) * quantity,
                    **transaction_data
                )
                
                # Clean up delivery file after sending (only for actual files, not text delivery)
                if delivery_file and delivery_file != "text_delivery" and os.path.exists(delivery_file):
                    try:
                        os.remove(delivery_file)
                    except Exception as e:
                        logger.warning(f"Failed to clean up delivery file {delivery_file}: {e}")
                
                return {
                    "success": True,
                    "delivered_items": len(extracted_data),
                    "remaining_inventory": remaining_lines,
                    "order_number": order_number,
                    "data_type": data_type
                }
            else:
                return {"success": False, "error": "Failed to deliver file to user"}
                
        except Exception as e:
            logger.error(f"Error processing line purchase for user {user_id}: {e}")
            # Try to release reserved lines on error
            try:
                release_reserved_lines(product_id, quantity, user_id)
            except:
                pass
            return {"success": False, "error": f"Processing error: {str(e)}"}

    async def _deliver_line_content_as_text(
        self,
        user_id: int,
        extracted_lines: List[str],
        product_name: str,
        quantity: int,
        order_number: int
    ) -> bool:
        """
        Deliver line-based content as formatted text message with <pre> tags.

        Args:
            user_id: Telegram user ID
            extracted_lines: Lines to deliver
            product_name: Product name
            quantity: Quantity delivered
            order_number: Order number

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Starting text delivery for order {order_number}, user {user_id}, quantity {quantity}")
            # Import theming for consistent formatting
            from utils.line_product_manager import LineProductTheme

            # Create delivery message header
            delivery_message = LineProductTheme.create_header("DIGITAL PRODUCT DELIVERED", "LINE-BASED CONTENT")
            delivery_message += f"{LineProductTheme.EMOJIS['success']} <b>Your line-based product has been delivered!</b>\n\n"
            delivery_message += f"{LineProductTheme.EMOJIS['product']} <b>Product:</b> {product_name}\n"
            delivery_message += f"{LineProductTheme.EMOJIS['quantity']} <b>Quantity:</b> {quantity} items\n"
            delivery_message += f"{LineProductTheme.EMOJIS['order']} <b>Order #:</b> <code>{order_number}</code>\n"
            delivery_message += f"{LineProductTheme.EMOJIS['date']} <b>Delivered:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>\n"
            delivery_message += f"{LineProductTheme.EMOJIS['delivery_method']} <b>Delivery Method:</b> Direct Message\n\n"

            # Format content with proper separators and <pre> tags
            delivery_message += LineProductTheme.create_section_break()
            delivery_message += f"📝 <b>Your Content:</b>\n\n"

            # Format each line with proper separators
            content_lines = []
            for i, line in enumerate(extracted_lines, 1):
                content_lines.append(f"Item {i}:")
                content_lines.append("-" * 35)  # Dashed separator
                content_lines.append(line.strip())
                content_lines.append("-" * 35)  # Dashed separator
                if i < len(extracted_lines):  # Add spacing between items
                    content_lines.append("")

            # Use <pre> tag for better visibility and formatting
            formatted_content = "\n".join(content_lines)
            delivery_message += f"<pre>{formatted_content}</pre>\n\n"

            delivery_message += LineProductTheme.create_section_break()
            delivery_message += f"{LineProductTheme.EMOJIS['save']} <i>Please save this content immediately as it contains your purchased items</i>\n"
            delivery_message += f"{LineProductTheme.EMOJIS['security']} <i>Keep this content secure and do not share it with others</i>\n\n"
            delivery_message += f"📞 <i>Contact support if you have any issues with your delivery</i>"

            # Send the message with CC shop link keyboard
            logger.info(f"Sending text delivery message to user {user_id} for order {order_number}")
            await self.bot.send_message(
                chat_id=user_id,
                text=delivery_message,
                parse_mode="HTML",
                reply_markup=self._create_cc_shop_keyboard()
            )
            logger.info(f"Text delivery message sent successfully to user {user_id} for order {order_number}")

            # Log successful delivery
            self._log_delivery_success(
                user_id=user_id,
                product_name=product_name,
                product_type="line_based",
                quantity=quantity,
                order_number=order_number,
                delivery_method="direct_message",
                file_path="text_delivery"
            )

            logger.info(f"Successfully delivered {quantity} line-based items as text to user {user_id} for order {order_number}")
            return True

        except Exception as e:
            logger.error(f"Error delivering line content as text to user {user_id}: {e}")

            # Log delivery failure
            self._log_delivery_failure(
                user_id=user_id,
                product_name=product_name,
                product_type="line_based",
                order_number=order_number,
                error_type="text_delivery_failed",
                error_message=str(e),
                additional_info={"quantity": quantity}
            )

            return False

    async def send_delivery_file(
        self,
        user_id: int,
        file_path: str,
        product_name: str,
        quantity: int,
        order_number: int
    ) -> bool:
        """
        Send delivery file to user via Telegram with consistent theming.

        Args:
            user_id: Telegram user ID
            file_path: Path to delivery file
            product_name: Name of the product
            quantity: Number of items delivered
            order_number: Order number

        Returns:
            True if successful, False otherwise
        """
        try:
            logger.info(f"Starting file delivery for order {order_number}, user {user_id}, file: {file_path}")
            if not os.path.exists(file_path):
                logger.error(f"Delivery file not found: {file_path}")

                # Log delivery failure
                self._log_delivery_failure(
                    user_id=user_id,
                    product_name=product_name,
                    product_type="line_based",
                    order_number=order_number,
                    error_type="file_not_found",
                    error_message=f"Delivery file not found: {file_path}",
                    additional_info={"file_path": file_path}
                )

                # Send user-friendly error notification
                await self._send_delivery_error_notification(
                    user_id, product_name, order_number, "file_not_found", f"File not found: {file_path}"
                )

                return False

            # Create input file for Telegram
            input_file = FSInputFile(file_path)

            # Import theming for consistent formatting
            from utils.line_product_manager import LineProductTheme

            # Prepare delivery message with consistent theming
            delivery_message = LineProductTheme.create_header("DIGITAL PRODUCT DELIVERED", "LINE-BASED CONTENT")
            delivery_message += f"{LineProductTheme.EMOJIS['success']} <b>Your line-based product has been delivered!</b>\n\n"
            delivery_message += f"{LineProductTheme.EMOJIS['product']} <b>Product:</b> {product_name}\n"
            delivery_message += f"{LineProductTheme.EMOJIS['quantity']} <b>Quantity:</b> {quantity} items\n"
            delivery_message += f"{LineProductTheme.EMOJIS['order']} <b>Order #:</b> <code>{order_number}</code>\n"
            delivery_message += f"{LineProductTheme.EMOJIS['date']} <b>Delivered:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>\n"
            delivery_message += f"{LineProductTheme.EMOJIS['delivery_method']} <b>Delivery Method:</b> File Attachment\n\n"

            delivery_message += LineProductTheme.create_section_break()
            delivery_message += f"{LineProductTheme.EMOJIS['file']} <b>Your items are attached in the file below</b>\n"
            delivery_message += f"{LineProductTheme.EMOJIS['save']} <i>Please save this file immediately as it contains your purchased items</i>\n"
            delivery_message += f"{LineProductTheme.EMOJIS['security']} <i>Keep this file secure and do not share it with others</i>\n\n"
            delivery_message += f"📞 <i>Contact support if you have any issues with your delivery</i>"

            # Send the file with enhanced error handling
            try:
                logger.info(f"Sending delivery file to user {user_id} for order {order_number}")
                await self.bot.send_document(
                    chat_id=user_id,
                    document=input_file,
                    caption=delivery_message,
                    parse_mode="HTML",
                    reply_markup=self._create_cc_shop_keyboard()
                )
                logger.info(f"Delivery file sent successfully to user {user_id} for order {order_number}")

                # Log successful delivery with detailed information
                self._log_delivery_success(
                    user_id=user_id,
                    product_name=product_name,
                    product_type="line_based",
                    quantity=quantity,
                    order_number=order_number,
                    delivery_method="file_attachment",
                    file_path=file_path
                )

                logger.info(f"Successfully delivered {quantity} line-based items to user {user_id} for order {order_number}")
                return True

            except Exception as send_error:
                logger.error(f"Failed to send delivery file to user {user_id}: {send_error}")

                # Log delivery failure
            self._log_delivery_failure(
                user_id=user_id,
                product_name=product_name,
                product_type="line_based",
                order_number=order_number,
                error_type="file_delivery_failed",
                error_message=str(send_error),
                additional_info={"file_path": file_path}
            )

            # Try to send user-friendly error notification
            await self._send_delivery_error_notification(
                user_id, product_name, order_number, "file_delivery_failed", str(send_error)
            )

            return False

        except Exception as e:
            logger.error(f"Error in send_delivery_file for user {user_id}: {e}")
            # Try to send error notification for general failures
            await self._send_delivery_error_notification(
                user_id, product_name, order_number, "general_error", str(e)
            )
            return False

    async def _send_delivery_error_notification(
        self,
        user_id: int,
        product_name: str,
        order_number: int,
        error_type: str,
        error_details: str = None
    ) -> bool:
        """
        Send user-friendly error notification for delivery failures.

        Args:
            user_id: Telegram user ID
            product_name: Name of the product
            order_number: Order number
            error_type: Type of error (file_delivery_failed, general_error, etc.)
            error_details: Technical error details for logging

        Returns:
            True if notification sent successfully, False otherwise
        """
        try:
            # Import theming for consistent error messages
            from utils.line_product_manager import LineProductTheme

            # Create user-friendly error messages based on error type
            if error_type == "file_delivery_failed":
                title = "FILE DELIVERY FAILED"
                subtitle = "TEMPORARY ISSUE"
                issue_description = "We encountered an issue while delivering your file"
                next_steps = "Your purchase is recorded. Support will deliver your file manually"

            elif error_type == "file_not_found":
                title = "FILE NOT AVAILABLE"
                subtitle = "CONTENT ISSUE"
                issue_description = "The product file is temporarily unavailable"
                next_steps = "Support will resolve this issue and deliver your content"

            elif error_type == "telegram_error":
                title = "DELIVERY SERVICE ISSUE"
                subtitle = "TEMPORARY PROBLEM"
                issue_description = "Telegram delivery service is experiencing issues"
                next_steps = "Please try again in a few minutes or contact support"

            else:  # general_error
                title = "DELIVERY ERROR"
                subtitle = "TECHNICAL ISSUE"
                issue_description = "An unexpected error occurred during delivery"
                next_steps = "Support will investigate and resolve this issue"

            # Create themed error message
            error_message = LineProductTheme.create_header(title, subtitle)
            error_message += f"{LineProductTheme.EMOJIS['error']} <b>Delivery Issue Detected</b>\n\n"
            error_message += f"{LineProductTheme.EMOJIS['regular_product']} <b>Product:</b> {product_name}\n"
            error_message += f"🆔 <b>Order #:</b> <code>{order_number}</code>\n"
            error_message += f"{LineProductTheme.EMOJIS['date']} <b>Time:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>\n\n"

            error_message += LineProductTheme.create_section_break()
            error_message += f"{LineProductTheme.EMOJIS['warning']} <b>Issue:</b> {issue_description}\n"
            error_message += f"🔧 <b>Next Steps:</b> {next_steps}\n\n"
            error_message += f"📞 <b>Support:</b> Contact us with order #{order_number} for immediate assistance\n"
            error_message += f"💡 <i>Your payment is secure and your purchase is recorded</i>"

            await self.bot.send_message(
                chat_id=user_id,
                text=error_message,
                parse_mode="HTML",
                reply_markup=self._create_cc_shop_keyboard()
            )

            logger.info(f"Sent delivery error notification to user {user_id} for order {order_number}")
            return True

        except Exception as e:
            logger.error(f"Failed to send delivery error notification to user {user_id}: {e}")

            # Last resort: try to send basic error message
            try:
                basic_message = (
                    f"❌ <b>Delivery Issue</b>\n\n"
                    f"Order #{order_number}: {product_name}\n\n"
                    f"We're experiencing a delivery issue. "
                    f"Please contact support with your order number.\n\n"
                    f"Your purchase is recorded and will be resolved."
                )

                await self.bot.send_message(
                    chat_id=user_id,
                    text=basic_message,
                    parse_mode="HTML",
                    reply_markup=self._create_cc_shop_keyboard()
                )
                return True

            except Exception as basic_error:
                logger.error(f"Failed to send basic error notification to user {user_id}: {basic_error}")
                return False

    def _log_delivery_success(
        self,
        user_id: int,
        product_name: str,
        product_type: str,
        quantity: int,
        order_number: int,
        delivery_method: str,
        file_path: str = None,
        additional_info: dict = None
    ) -> None:
        """
        Log successful delivery with comprehensive details for tracking and analytics.

        Args:
            user_id: Telegram user ID
            product_name: Name of the delivered product
            product_type: Type of product (line_based, exclusive, regular)
            quantity: Quantity delivered
            order_number: Order number
            delivery_method: How the product was delivered (file_attachment, message, link)
            file_path: Path to delivered file (if applicable)
            additional_info: Additional delivery information
        """
        try:
            delivery_info = {
                "timestamp": datetime.now().isoformat(),
                "user_id": user_id,
                "product_name": product_name,
                "product_type": product_type,
                "quantity": quantity,
                "order_number": order_number,
                "delivery_method": delivery_method,
                "status": "success"
            }

            if file_path:
                delivery_info["file_path"] = file_path
                try:
                    delivery_info["file_size"] = os.path.getsize(file_path)
                except:
                    delivery_info["file_size"] = "unknown"

            if additional_info:
                delivery_info.update(additional_info)

            # Log structured delivery information
            logger.info(f"DELIVERY_SUCCESS: {delivery_info}")

            # Also log to transaction system if available
            try:
                from database.operations import add_transaction

                # Create a copy of delivery_info without user_id to avoid duplicate keyword argument
                transaction_data = delivery_info.copy()
                transaction_data.pop('user_id', None)  # Remove user_id if it exists

                add_transaction(
                    user_id=user_id,
                    transaction_type="delivery_confirmation",
                    amount=0,  # No monetary value for delivery confirmation
                    note=f"Delivered {product_type} product: {product_name} (Order #{order_number})",
                    **transaction_data
                )
            except Exception as tx_error:
                logger.warning(f"Failed to log delivery to transaction system: {tx_error}")

        except Exception as e:
            logger.error(f"Failed to log delivery success: {e}")

    def _log_delivery_failure(
        self,
        user_id: int,
        product_name: str,
        product_type: str,
        order_number: int,
        error_type: str,
        error_message: str,
        additional_info: dict = None
    ) -> None:
        """
        Log delivery failure with comprehensive details for troubleshooting.

        Args:
            user_id: Telegram user ID
            product_name: Name of the product that failed to deliver
            product_type: Type of product (line_based, exclusive, regular)
            order_number: Order number
            error_type: Type of error that occurred
            error_message: Detailed error message
            additional_info: Additional error information
        """
        try:
            failure_info = {
                "timestamp": datetime.now().isoformat(),
                "user_id": user_id,
                "product_name": product_name,
                "product_type": product_type,
                "order_number": order_number,
                "error_type": error_type,
                "error_message": error_message,
                "status": "failed"
            }

            if additional_info:
                failure_info.update(additional_info)

            # Log structured failure information
            logger.error(f"DELIVERY_FAILURE: {failure_info}")

            # Also log to transaction system if available
            try:
                from database.operations import add_transaction

                # Create a copy of failure_info without user_id to avoid duplicate keyword argument
                transaction_data = failure_info.copy()
                transaction_data.pop('user_id', None)  # Remove user_id if it exists

                add_transaction(
                    user_id=user_id,
                    transaction_type="delivery_failure",
                    amount=0,  # No monetary value for delivery failure
                    note=f"Failed to deliver {product_type} product: {product_name} (Order #{order_number}) - {error_type}",
                    **transaction_data
                )
            except Exception as tx_error:
                logger.warning(f"Failed to log delivery failure to transaction system: {tx_error}")

        except Exception as e:
            logger.error(f"Failed to log delivery failure: {e}")
    
    async def send_delivery_preview(
        self, 
        user_id: int, 
        product: Dict[str, Any], 
        quantity: int
    ) -> bool:
        """
        Send a preview of what the user will receive without showing actual content.
        
        Args:
            user_id: Telegram user ID
            product: Product details
            quantity: Requested quantity
            
        Returns:
            True if successful, False otherwise
        """
        try:
            inventory_file = product.get("inventory_file_path")
            if not inventory_file:
                return False
                
            # Get preview lines
            preview_lines = line_inventory_manager.get_preview_lines(inventory_file, 3)
            
            if not preview_lines:
                return False
                
            preview_message = (
                f"📋 **Purchase Preview**\n\n"
                f"📦 **Product:** {product['name']}\n"
                f"📊 **Quantity:** {quantity} items\n"
                f"💰 **Total Price:** ${(product.get('line_price', product['price']) * quantity):.2f}\n\n"
                f"📄 **Sample Format:**\n\n"
            )

            # Use code block for better visibility of preview content
            preview_content = ""
            for i, line in enumerate(preview_lines, 1):
                preview_content += f"{i}. {line}\n"

            preview_message += f"```\n{preview_content.strip()}\n```\n"
                
            if len(preview_lines) < quantity:
                preview_message += f"... and {quantity - len(preview_lines)} more items\n"
                
            preview_message += (
                f"\n🔒 **Note:** Actual content will be different from the preview.\n"
                f"📁 Items will be delivered as a downloadable .txt file upon payment confirmation."
            )
            
            await self.bot.send_message(
                chat_id=user_id,
                text=preview_message,
                parse_mode="Markdown"
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending delivery preview to user {user_id}: {e}")
            return False
    
    def validate_purchase_request(
        self,
        product: Dict[str, Any],
        quantity: int,
        user_id: int = None
    ) -> Dict[str, Any]:
        """
        Validate a purchase request for a line-based product with shared inventory support.

        Args:
            product: Product details
            quantity: Requested quantity
            user_id: Optional user ID for shared inventory validation

        Returns:
            Dict with validation results
        """
        try:
            if not product.get("is_line_based"):
                return {"valid": False, "error": "Product is not line-based"}

            max_quantity = product.get("max_quantity_per_order", 1)
            allow_shared_inventory = product.get("allow_shared_inventory", False)

            if quantity <= 0:
                return {"valid": False, "error": "Quantity must be greater than 0"}

            if quantity > max_quantity:
                return {
                    "valid": False,
                    "error": f"Quantity exceeds maximum allowed per order ({max_quantity})"
                }

            # For shared inventory, check user-specific availability
            if allow_shared_inventory and user_id:
                from database.operations import get_available_lines_for_user
                product_id = product.get("id") or product.get("_id")
                total_lines = product.get("total_lines", 0)
                user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)

                if len(user_available_lines) < quantity:
                    return {
                        "valid": False,
                        "error": f"You have already purchased most available content. New lines available: {len(user_available_lines)}"
                    }
            else:
                # For exclusive inventory, use standard availability check
                available_lines = product.get("available_lines", 0)
                if quantity > available_lines:
                    return {
                        "valid": False,
                        "error": f"Not enough stock. Available: {available_lines}, Requested: {quantity}"
                    }
                
            # Check if inventory file exists
            inventory_file = product.get("inventory_file_path")
            if not inventory_file or not os.path.exists(inventory_file):
                return {"valid": False, "error": "Inventory file not available"}
                
            # Verify actual file has enough lines (for exclusive inventory only)
            if not allow_shared_inventory:
                actual_lines = line_inventory_manager.get_available_lines_count(inventory_file)
                if actual_lines < quantity:
                    return {
                        "valid": False,
                        "error": f"Inventory file has insufficient lines. Available: {actual_lines}"
                    }
                
            total_price = (product.get("line_price", product["price"]) * quantity)

            # Prepare return data based on inventory mode
            result = {
                "valid": True,
                "quantity": quantity,
                "total_price": total_price
            }

            if allow_shared_inventory:
                # For shared inventory, show user-specific availability
                if user_id:
                    from database.operations import get_available_lines_for_user
                    product_id = product.get("id") or product.get("_id")
                    total_lines = product.get("total_lines", 0)
                    user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
                    result["available_stock"] = len(user_available_lines)
                else:
                    result["available_stock"] = product.get("total_lines", 0)
                result["actual_file_lines"] = product.get("total_lines", 0)
            else:
                # For exclusive inventory, show product availability
                result["available_stock"] = product.get("available_lines", 0)
                actual_lines = line_inventory_manager.get_available_lines_count(inventory_file)
                result["actual_file_lines"] = actual_lines

            return result
            
        except Exception as e:
            logger.error(f"Error validating purchase request: {e}")
            return {"valid": False, "error": f"Validation error: {str(e)}"}

    async def process_exclusive_purchase(
        self,
        user_id: int,
        product_id: Any,
        order_number: int,
        payment_track_id: str = None
    ) -> Dict[str, Any]:
        """
        Process exclusive single-use product purchase and delivery with enhanced error handling.

        Args:
            user_id: Telegram user ID
            product_id: Product ID
            order_number: Order number for tracking
            payment_track_id: Payment tracking ID

        Returns:
            Dict with delivery status and details
        """
        try:
            # Validate the purchase
            validation = ExclusiveProductDBOperations.check_user_can_purchase_exclusive(product_id, user_id)
            if not validation["can_purchase"]:
                logger.warning(f"Exclusive purchase validation failed for product {product_id}, user {user_id}: {validation['reason']}")
                return {"success": False, "error": validation["reason"]}

            product = validation["product"]

            # Enhanced file path validation with secure path resolution
            file_path = product.get("exclusive_file_path")
            if not file_path:
                logger.error(f"No file path found for exclusive product {product_id}")
                return {"success": False, "error": "Product file configuration missing"}

            # Secure path resolution to prevent path traversal attacks
            full_file_path = self._resolve_secure_file_path(file_path, product_id)
            if not full_file_path:
                logger.error(f"Security violation: Invalid file path for product {product_id}: {file_path}")
                return {"success": False, "error": "File access denied for security reasons"}

            if not os.path.exists(full_file_path):
                logger.error(f"Exclusive product file not found: {full_file_path}")
                return {"success": False, "error": "Product file not found on server"}

            # Validate file integrity
            try:
                file_size = os.path.getsize(full_file_path)
                if file_size == 0:
                    logger.error(f"Exclusive product file is empty: {full_file_path}")
                    return {"success": False, "error": "Product file is corrupted (empty)"}

                # Check if file is readable
                with open(full_file_path, 'rb') as f:
                    f.read(1)  # Try to read first byte

            except (OSError, IOError) as e:
                logger.error(f"Cannot read exclusive product file {full_file_path}: {e}")
                return {"success": False, "error": "Product file is corrupted or inaccessible"}

            delivery_info = exclusive_file_handler.get_file_delivery_info(
                full_file_path, product["name"], order_number
            )

            if not delivery_info["success"]:
                logger.error(f"Failed to get delivery info for exclusive product {product_id}: {delivery_info.get('error')}")
                return {"success": False, "error": delivery_info["error"]}

            # NOTE: Product purchase marking is now handled in the main order processing
            # to ensure proper transaction atomicity and error handling

            # Store purchase info for logging
            purchase_info = {
                "product_id": product_id,
                "user_id": user_id,
                "order_number": order_number,
                "purchase_timestamp": datetime.now()
            }

            # Return delivery info WITHOUT sending file here
            # The file will be sent by the main order processing system to avoid duplicates

            # Log successful transaction preparation
            transaction_data = {
                "product_id": product_id,
                "product_name": product["name"],
                "order_number": order_number,
                "purchase_info": purchase_info,
                "file_path": full_file_path,
                "file_type": product.get("exclusive_file_type"),
                "file_size": product.get("exclusive_file_size"),
                "payment_track_id": payment_track_id,
                "delivery_status": "pending"  # Will be updated after actual delivery
            }

            add_transaction(
                user_id,
                "exclusive_purchase",
                product["price"],
                **transaction_data
            )

            # Return success with delivery info for the main system to handle
            return {
                "success": True,
                "product_name": product["name"],
                "file_info": delivery_info,
                "order_number": order_number,
                "delivery_handled": False  # Indicates delivery still needs to be done
            }

        except Exception as e:
            logger.error(f"Error processing exclusive purchase for user {user_id}: {e}")
            return {"success": False, "error": f"Processing error: {str(e)}"}

    async def send_exclusive_file(
        self,
        user_id: int,
        delivery_info: Dict[str, Any],
        product_name: str,
        order_number: int,
        product_id: int = None
    ) -> Dict[str, Any]:
        """
        Send exclusive product file to user via Telegram with comprehensive error handling and rollback.

        Args:
            user_id: Telegram user ID
            delivery_info: File delivery information
            product_name: Name of the product
            order_number: Order number
            product_id: Product ID for duplicate tracking

        Returns:
            Dict with success status, error details, and rollback information
        """
        rollback_actions = []
        delivery_attempt_id = f"{user_id}_{product_id}_{order_number}_{int(datetime.now().timestamp())}"

        try:
            logger.info(f"Starting exclusive file delivery - ID: {delivery_attempt_id}")

            # Check for duplicate delivery if product_id is provided
            if product_id and self._is_exclusive_already_delivered(user_id, product_id, order_number):
                logger.warning(f"Exclusive product {product_id} already delivered for user {user_id}, order {order_number}")
                return {
                    "success": True,
                    "message": "Already delivered",
                    "delivery_attempt_id": delivery_attempt_id,
                    "duplicate_delivery": True
                }

            # Validate file path and existence
            file_path = delivery_info.get("file_path")
            if not file_path:
                logger.error(f"No file path provided in delivery_info for delivery {delivery_attempt_id}")
                return {
                    "success": False,
                    "error": "No file path provided",
                    "error_code": "MISSING_FILE_PATH",
                    "delivery_attempt_id": delivery_attempt_id
                }

            if not os.path.exists(file_path):
                logger.error(f"Exclusive file not found: {file_path} for delivery {delivery_attempt_id}")
                return {
                    "success": False,
                    "error": f"Product file not found: {file_path}",
                    "error_code": "FILE_NOT_FOUND",
                    "delivery_attempt_id": delivery_attempt_id
                }

            # Validate file accessibility and integrity
            try:
                file_size = os.path.getsize(file_path)
                if file_size == 0:
                    logger.error(f"Exclusive file is empty: {file_path} for delivery {delivery_attempt_id}")

                    # Send user-friendly error notification
                    await self._send_delivery_error_notification(
                        user_id, product_name, order_number, "file_not_found", "Product file is corrupted"
                    )

                    return {
                        "success": False,
                        "error": "Product file is empty or corrupted",
                        "error_code": "FILE_CORRUPTED",
                        "delivery_attempt_id": delivery_attempt_id
                    }

                # Test file readability
                with open(file_path, 'rb') as f:
                    f.read(1024)  # Read first 1KB to test accessibility

            except (OSError, IOError, PermissionError) as e:
                logger.error(f"Cannot access exclusive file {file_path} for delivery {delivery_attempt_id}: {e}")

                # Send user-friendly error notification
                await self._send_delivery_error_notification(
                    user_id, product_name, order_number, "file_not_found", str(e)
                )

                return {
                    "success": False,
                    "error": f"Cannot access product file: {str(e)}",
                    "error_code": "FILE_ACCESS_ERROR",
                    "delivery_attempt_id": delivery_attempt_id
                }

            # Prepare file for delivery
            try:
                input_file = FSInputFile(file_path, filename=delivery_info.get("delivery_filename", "exclusive_product"))
                rollback_actions.append(("file_prepared", {"file_path": file_path}))
            except Exception as e:
                logger.error(f"Failed to prepare file for delivery {delivery_attempt_id}: {e}")
                return {
                    "success": False,
                    "error": f"Failed to prepare file for delivery: {str(e)}",
                    "error_code": "FILE_PREPARATION_ERROR",
                    "delivery_attempt_id": delivery_attempt_id
                }

            # Import exclusive theming
            try:
                from utils.exclusive_product_manager import ExclusiveProductTheme
            except ImportError as e:
                logger.error(f"Failed to import exclusive theming for delivery {delivery_attempt_id}: {e}")
                return {
                    "success": False,
                    "error": "Failed to load delivery theming",
                    "error_code": "THEMING_IMPORT_ERROR",
                    "delivery_attempt_id": delivery_attempt_id
                }

            # Prepare delivery message with exclusive theming
            try:
                delivery_message = ExclusiveProductTheme.create_header("EXCLUSIVE PRODUCT DELIVERED", "SINGLE-USE DIGITAL ITEM")
                delivery_message += f"{ExclusiveProductTheme.EMOJIS['success']} <b>Your exclusive product has been delivered!</b>\n\n"
                delivery_message += f"{ExclusiveProductTheme.EMOJIS['exclusive_product']} <b>Product:</b> {product_name}\n"
                delivery_message += f"{ExclusiveProductTheme.EMOJIS['single_use']} <b>Type:</b> Exclusive Single-Use\n"
                delivery_message += f"🆔 <b>Order #:</b> <code>{order_number}</code>\n"
                delivery_message += f"{ExclusiveProductTheme.EMOJIS['date']} <b>Delivered:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>\n"
                delivery_message += f"{ExclusiveProductTheme.EMOJIS['delivery']} <b>Delivery Method:</b> File Attachment\n\n"

                # File information
                preview_info = delivery_info.get("preview_info", {})
                if preview_info:
                    delivery_message += f"{preview_info.get('icon', '📁')} <b>File Type:</b> {preview_info.get('type_display', 'Unknown')}\n"
                    delivery_message += f"{ExclusiveProductTheme.EMOJIS['size']} <b>File Size:</b> {preview_info.get('size_display', 'Unknown')}\n\n"

                delivery_message += ExclusiveProductTheme.create_section_break()
                delivery_message += f"{ExclusiveProductTheme.EMOJIS['exclusive']} <i>This is an exclusive product - you are the only owner!</i>\n"
                delivery_message += f"{ExclusiveProductTheme.EMOJIS['info']} <i>Please save this file immediately as it contains your exclusive content</i>\n"
                delivery_message += f"{ExclusiveProductTheme.EMOJIS['warning']} <i>Keep this file secure and do not share it with others</i>\n\n"
                delivery_message += f"📞 <i>Contact support if you have any issues with your delivery</i>"
            except Exception as e:
                logger.error(f"Failed to prepare delivery message for delivery {delivery_attempt_id}: {e}")
                return {
                    "success": False,
                    "error": "Failed to prepare delivery message",
                    "error_code": "MESSAGE_PREPARATION_ERROR",
                    "delivery_attempt_id": delivery_attempt_id
                }

            # Send the file with comprehensive error handling
            try:
                await self.bot.send_document(
                    chat_id=user_id,
                    document=input_file,
                    caption=delivery_message,
                    parse_mode="HTML",
                    reply_markup=self._create_cc_shop_keyboard()
                )
                rollback_actions.append(("file_sent", {"user_id": user_id, "product_id": product_id, "order_number": order_number}))
                logger.info(f"File successfully sent for delivery {delivery_attempt_id}")

            except Exception as e:
                logger.error(f"Failed to send file for delivery {delivery_attempt_id}: {e}")

                # Send user-friendly error notification
                await self._send_delivery_error_notification(
                    user_id, product_name, order_number, "telegram_error", str(e)
                )

                # Attempt rollback before returning error
                await self._perform_delivery_rollback(rollback_actions, delivery_attempt_id)
                return {
                    "success": False,
                    "error": f"Failed to send file via Telegram: {str(e)}",
                    "error_code": "TELEGRAM_SEND_ERROR",
                    "delivery_attempt_id": delivery_attempt_id,
                    "rollback_performed": True
                }

            # Mark as delivered to prevent duplicates
            if product_id:
                try:
                    self._mark_exclusive_delivered(user_id, product_id, order_number)
                    rollback_actions.append(("marked_delivered", {"user_id": user_id, "product_id": product_id, "order_number": order_number}))

                    # Trigger automatic product removal after successful delivery
                    removal_success = await self._handle_exclusive_product_removal(product_id, user_id, order_number)

                    # Log removal status
                    if removal_success:
                        logger.info(f"Exclusive product {product_id} automatically removed from listings after delivery {delivery_attempt_id}")
                        rollback_actions.append(("product_removed", {"product_id": product_id}))
                    else:
                        logger.warning(f"Failed to automatically remove exclusive product {product_id} from listings for delivery {delivery_attempt_id}")

                except Exception as e:
                    logger.error(f"Error in post-delivery processing for delivery {delivery_attempt_id}: {e}")
                    # Attempt rollback
                    await self._perform_delivery_rollback(rollback_actions, delivery_attempt_id)
                    return {
                        "success": False,
                        "error": f"Post-delivery processing failed: {str(e)}",
                        "error_code": "POST_DELIVERY_ERROR",
                        "delivery_attempt_id": delivery_attempt_id,
                        "rollback_performed": True
                    }

            # Log successful exclusive delivery
            self._log_delivery_success(
                user_id=user_id,
                product_name=product_name,
                product_type="exclusive",
                quantity=1,  # Exclusive products are always quantity 1
                order_number=order_number,
                delivery_method="file_attachment",
                file_path=file_path,
                additional_info={
                    "delivery_attempt_id": delivery_attempt_id,
                    "product_id": product_id,
                    "actions_performed": len(rollback_actions)
                }
            )

            logger.info(f"Successfully completed exclusive product delivery {delivery_attempt_id}")
            return {
                "success": True,
                "message": "Exclusive product delivered successfully",
                "delivery_attempt_id": delivery_attempt_id,
                "user_id": user_id,
                "product_id": product_id,
                "order_number": order_number,
                "actions_performed": len(rollback_actions)
            }

        except Exception as e:
            logger.error(f"Unexpected error in exclusive file delivery {delivery_attempt_id}: {e}")

            # Perform comprehensive rollback
            await self._perform_delivery_rollback(rollback_actions, delivery_attempt_id)

            return {
                "success": False,
                "error": f"Unexpected delivery error: {str(e)}",
                "error_code": "UNEXPECTED_ERROR",
                "delivery_attempt_id": delivery_attempt_id,
                "rollback_performed": True
            }

    async def _perform_delivery_rollback(self, rollback_actions: List[tuple], delivery_attempt_id: str) -> bool:
        """
        Perform comprehensive rollback of delivery actions.

        Args:
            rollback_actions: List of actions to rollback
            delivery_attempt_id: Unique delivery attempt identifier

        Returns:
            True if rollback successful, False otherwise
        """
        try:
            logger.warning(f"Starting delivery rollback for {delivery_attempt_id} with {len(rollback_actions)} actions")
            rollback_success = True

            # Rollback actions in reverse order
            for action_type, action_data in reversed(rollback_actions):
                try:
                    if action_type == "product_removed":
                        # Cannot easily rollback product removal, log for manual intervention
                        logger.error(f"Product {action_data['product_id']} was removed - manual restoration may be required")

                    elif action_type == "marked_delivered":
                        # Remove delivery tracking
                        user_id = action_data["user_id"]
                        product_id = action_data["product_id"]
                        order_number = action_data["order_number"]

                        delivery_key = f"{user_id}_{product_id}_{order_number}"
                        if delivery_key in self._exclusive_deliveries:
                            self._exclusive_deliveries.remove(delivery_key)
                            logger.info(f"Removed delivery tracking for {delivery_key}")

                    elif action_type == "file_sent":
                        # Cannot unsend Telegram message, but attempt product purchase rollback
                        user_id = action_data["user_id"]
                        product_id = action_data["product_id"]

                        if product_id:
                            from utils.exclusive_product_db_operations import ExclusiveProductDBOperations
                            rollback_result = ExclusiveProductDBOperations.rollback_product_purchase(
                                product_id, user_id, f"Delivery rollback for {delivery_attempt_id}"
                            )
                            if rollback_result.get("success"):
                                logger.info(f"Successfully rolled back product purchase for {product_id}")
                            else:
                                logger.error(f"Failed to rollback product purchase for {product_id}: {rollback_result.get('error')}")
                                rollback_success = False

                    elif action_type == "file_prepared":
                        # File preparation rollback (cleanup if needed)
                        logger.debug(f"File preparation rollback for {action_data['file_path']}")

                except Exception as e:
                    logger.error(f"Error during rollback action {action_type} for {delivery_attempt_id}: {e}")
                    rollback_success = False

            if rollback_success:
                logger.info(f"Delivery rollback completed successfully for {delivery_attempt_id}")
            else:
                logger.error(f"Delivery rollback completed with errors for {delivery_attempt_id} - manual intervention may be required")

            return rollback_success

        except Exception as e:
            logger.error(f"Critical error during delivery rollback for {delivery_attempt_id}: {e}")
            return False

    async def _handle_exclusive_product_removal(self, product_id: int, user_id: int, order_number: int) -> bool:
        """
        Handle automatic removal of exclusive product from listings after successful delivery.
        Includes retry logic for timing issues.

        Args:
            product_id: Product ID that was delivered
            user_id: User who purchased the product
            order_number: Order number for logging

        Returns:
            True if removal was successful, False otherwise
        """
        import asyncio

        try:
            # Use the enhanced database operations for atomic removal
            from utils.exclusive_product_db_operations import ExclusiveProductDBOperations

            # Retry logic for timing issues
            max_retries = 3
            retry_delay = 0.5  # seconds

            for attempt in range(max_retries):
                # Perform automatic removal with delivery confirmation
                removal_result = ExclusiveProductDBOperations.mark_product_delivered_and_remove_from_listings(
                    product_id=product_id,
                    user_id=user_id,
                    order_number=order_number
                )

                if removal_result["success"]:
                    if attempt > 0:
                        logger.info(f"Successfully removed exclusive product {product_id} from listings after {attempt + 1} attempts")
                    break

                # Check if this is a timing issue (product not marked as purchased yet)
                error_msg = removal_result.get('error', '')
                if "not purchased" in error_msg.lower() and attempt < max_retries - 1:
                    logger.info(f"Retrying product removal for {product_id} (attempt {attempt + 1}/{max_retries}) due to timing issue")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # Exponential backoff
                    continue
                else:
                    break

            if removal_result["success"]:
                logger.info(f"Successfully removed exclusive product {product_id} from listings after delivery to user {user_id}")
                return True
            else:
                error_msg = removal_result.get('error', 'Unknown error')
                logger.warning(f"Failed to automatically remove exclusive product {product_id} from listings for delivery {user_id}_{product_id}_{order_number}: {error_msg}")

                # If the error is about product not being purchased, this might be a timing issue
                # Log it as a warning rather than error since delivery was successful
                if "not purchased" in error_msg.lower():
                    logger.warning(f"Product removal failed due to purchase status check - this may be a timing issue. Product {product_id} was delivered successfully to user {user_id}")

                return False

        except Exception as e:
            logger.error(f"Error handling exclusive product removal for product {product_id}: {e}")
            # Don't raise exception - delivery was successful, removal is secondary
            return False

    async def _rollback_exclusive_product_purchase(self, product_id: int, user_id: int, reason: str = "Delivery failed") -> bool:
        """
        Rollback exclusive product purchase if delivery fails.

        Args:
            product_id: Product ID to rollback
            user_id: User who attempted to purchase
            reason: Reason for rollback

        Returns:
            True if rollback was successful, False otherwise
        """
        try:
            from utils.exclusive_product_db_operations import ExclusiveProductDBOperations

            # Perform rollback operation
            rollback_result = ExclusiveProductDBOperations.rollback_product_purchase(
                product_id=product_id,
                user_id=user_id,
                reason=reason
            )

            if rollback_result["success"]:
                logger.warning(f"Successfully rolled back exclusive product {product_id} purchase for user {user_id}: {reason}")
                return True
            else:
                logger.error(f"Failed to rollback exclusive product {product_id} purchase: {rollback_result.get('error')}")
                return False

        except Exception as e:
            logger.error(f"Error rolling back exclusive product {product_id} purchase: {e}")
            return False

    async def deliver_line_based_product(
        self,
        user_id: int,
        product_id: Any,
        product_name: str,
        quantity: int,
        order_number: int
    ) -> Dict[str, Any]:
        """
        Deliver line-based product to user.

        Args:
            user_id: Telegram user ID
            product_id: Product ID (can be int, str, or ObjectId)
            product_name: Product name
            quantity: Quantity to deliver
            order_number: Order number for tracking

        Returns:
            Delivery result dictionary
        """
        try:
            logger.info(f"Delivering line-based product {product_id} to user {user_id}, quantity {quantity}")

            # Use the existing process_line_purchase method
            result = await self.process_line_purchase(
                user_id=user_id,
                product_id=product_id,
                quantity=quantity,
                order_number=order_number
            )

            if result.get("success"):
                return {
                    "success": True,
                    "product_name": product_name,
                    "quantity": quantity,
                    "delivery_type": "file"
                }
            else:
                return {
                    "success": False,
                    "error": result.get("error", "Unknown error"),
                    "product_name": product_name
                }

        except Exception as e:
            logger.error(f"Error delivering line-based product {product_id}: {e}")
            return {
                "success": False,
                "error": f"Line-based delivery failed: {str(e)}",
                "product_name": product_name
            }

    async def deliver_exclusive_product(
        self,
        user_id: int,
        delivery_info: Dict[str, Any],
        product_name: str,
        order_number: int,
        product_id: Any = None
    ) -> Dict[str, Any]:
        """
        Deliver exclusive single-use product to user.

        Args:
            user_id: Telegram user ID
            delivery_info: Delivery information dictionary
            product_name: Product name
            order_number: Order number for tracking
            product_id: Product ID (optional)

        Returns:
            Delivery result dictionary
        """
        try:
            logger.info(f"Delivering exclusive product {product_id} to user {user_id}")

            # Use the existing send_exclusive_file method
            success = await self.send_exclusive_file(
                user_id=user_id,
                delivery_info=delivery_info,
                product_name=product_name,
                order_number=order_number,
                product_id=product_id
            )

            if success:
                return {
                    "success": True,
                    "product_name": product_name,
                    "delivery_type": "exclusive"
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to deliver exclusive product",
                    "product_name": product_name
                }

        except Exception as e:
            logger.error(f"Error delivering exclusive product {product_id}: {e}")
            return {
                "success": False,
                "error": f"Exclusive delivery failed: {str(e)}",
                "product_name": product_name
            }

    async def deliver_file(
        self,
        user_id: int,
        file_path: str,
        product_name: str,
        quantity: int,
        order_number: int
    ) -> Dict[str, Any]:
        """
        Deliver regular file to user.

        Args:
            user_id: Telegram user ID
            file_path: Path to the file
            product_name: Product name
            quantity: Quantity (for regular products, usually 1)
            order_number: Order number for tracking

        Returns:
            Delivery result dictionary
        """
        try:
            logger.info(f"Delivering file {file_path} to user {user_id}")

            # Use the existing send_delivery_file method
            success = await self.send_delivery_file(
                user_id=user_id,
                file_path=file_path,
                product_name=product_name,
                quantity=quantity,
                order_number=order_number
            )

            if success:
                return {
                    "success": True,
                    "product_name": product_name,
                    "delivery_type": "file"
                }
            else:
                return {
                    "success": False,
                    "error": "Failed to deliver file",
                    "product_name": product_name
                }

        except Exception as e:
            logger.error(f"Error delivering file {file_path}: {e}")
            return {
                "success": False,
                "error": f"File delivery failed: {str(e)}",
                "product_name": product_name
            }

    async def deliver_product_consolidated(
        self,
        user_id: int,
        cart_item: Dict[str, Any],
        order_number: int
    ) -> Dict[str, Any]:
        """
        Consolidated delivery method for all product types.

        Args:
            user_id: Telegram user ID
            cart_item: Cart item with product information
            order_number: Order number

        Returns:
            Delivery result dictionary
        """
        try:
            product_id = cart_item.get("product_id")
            product_name = cart_item.get("name", "Unknown Product")
            quantity = cart_item.get("quantity", 1)

            logger.info(f"Starting consolidated delivery for user {user_id}, product {product_id}, order {order_number}")

            # Determine product type and route to appropriate delivery method
            if cart_item.get("is_line_based", False):
                return await self.deliver_line_based_product(
                    user_id, product_id, product_name, quantity, order_number
                )

            elif cart_item.get("is_exclusive_single_use", False):
                # For exclusive products, we need the delivery info
                delivery_info = {
                    "file_path": cart_item.get("exclusive_file_path"),
                    "file_type": cart_item.get("exclusive_file_type", "file"),
                    "delivery_filename": f"{product_name}.{cart_item.get('exclusive_file_type', 'file')}"
                }

                return await self.deliver_exclusive_product(
                    user_id, delivery_info, product_name, order_number, product_id
                )

            else:
                # Regular product delivery
                file_link = cart_item.get("file_link", "")
                file_path = cart_item.get("file_path", "")

                # Check if there's a file path that exists on disk (uploaded file)
                if file_path:
                    # Resolve the file path properly - it might be relative to uploads directory
                    resolved_file_path = self._resolve_secure_file_path(file_path, cart_item.get("product_id", "unknown"))
                    if resolved_file_path and os.path.exists(resolved_file_path):
                        logger.info(f"Delivering uploaded file for product {product_name}: {resolved_file_path}")
                        return await self.deliver_file(user_id, resolved_file_path, product_name, quantity, order_number)
                    else:
                        logger.warning(f"Could not resolve file path for product {product_name}: {file_path}")

                # Check if file_link is a direct file path that exists
                if file_link:
                    # First try to resolve as a secure file path
                    resolved_link_path = self._resolve_secure_file_path(file_link, cart_item.get("product_id", "unknown"))
                    if resolved_link_path and os.path.exists(resolved_link_path):
                        logger.info(f"Delivering file from file_link for product {product_name}: {resolved_link_path}")
                        return await self.deliver_file(user_id, resolved_link_path, product_name, quantity, order_number)
                    # If not a file path, check if it's a direct path that exists
                    elif os.path.exists(file_link):
                        logger.info(f"Delivering direct file path for product {product_name}: {file_link}")
                        return await self.deliver_file(user_id, file_link, product_name, quantity, order_number)
                    else:
                        # Handle different types of content delivery (URLs, text, etc.)
                        return await self._deliver_regular_product_content(
                            user_id, file_link, product_name, quantity, order_number
                        )
                else:
                    # No file path or file_link available
                    logger.warning(f"No file path or file_link available for product {product_name}")
                    return {
                        "success": False,
                        "error": "No file available for delivery",
                        "product_name": product_name
                    }

        except Exception as e:
            logger.error(f"Error in consolidated delivery for user {user_id}: {e}")
            return {
                "success": False,
                "error": f"Delivery failed: {str(e)}",
                "product_name": cart_item.get("name", "Unknown Product")
            }

    async def _deliver_regular_product_content(
        self,
        user_id: int,
        file_link: str,
        product_name: str,
        quantity: int,
        order_number: int
    ) -> Dict[str, Any]:
        """
        Handle delivery of regular product content with proper content type detection.

        Args:
            user_id: Telegram user ID
            file_link: File link or content reference
            product_name: Name of the product
            quantity: Quantity ordered
            order_number: Order number

        Returns:
            Delivery result dictionary
        """
        try:
            # Import theming for consistent formatting
            from utils.line_product_manager import LineProductTheme

            # Determine content type and delivery method
            delivery_method_display = ""
            delivery_type = ""
            content_info = ""

            if not file_link:
                # No content - direct message delivery
                delivery_method_display = "Direct Message"
                delivery_type = "message"
                content_info = ""

            elif file_link.startswith("text:"):
                # Text content delivery
                delivery_method_display = "Direct Message"
                delivery_type = "text"
                text_content = file_link[5:]  # Remove "text:" prefix

                # Use <pre> tag for better visibility and formatting of content
                content_info = f"\n📝 <b>Your Content:</b>\n\n<pre>{text_content}</pre>\n"

            elif file_link.startswith("file_id:"):
                # Telegram file ID delivery - attempt to send the file directly
                file_id = file_link[8:]  # Remove "file_id:" prefix
                try:
                    await self.bot.send_document(
                        chat_id=user_id,
                        document=file_id,
                        caption=f"📦 <b>{product_name}</b>\n\n🎉 Your digital product has been delivered!",
                        parse_mode="HTML",
                        reply_markup=self._create_cc_shop_keyboard()
                    )
                    delivery_method_display = "File Attachment"
                    delivery_type = "file"
                    content_info = f"\n📎 <b>File delivered successfully!</b>\n"

                    # Log successful delivery
                    self._log_delivery_success(
                        user_id=user_id,
                        product_name=product_name,
                        product_type="regular",
                        quantity=quantity,
                        order_number=order_number,
                        delivery_method="file_attachment",
                        file_path=f"telegram_file_id:{file_id}"
                    )

                    return {
                        "success": True,
                        "product_name": product_name,
                        "delivery_type": "file"
                    }

                except Exception as e:
                    logger.error(f"Failed to deliver file via Telegram file_id {file_id}: {e}")
                    delivery_method_display = "Direct Message"
                    delivery_type = "message"
                    content_info = f"\n⚠️ <i>File delivery failed, but your purchase is confirmed</i>\n"

            elif file_link.startswith(("http://", "https://")):
                # URL download link
                delivery_method_display = "Download Link"
                delivery_type = "link"

                # Use <pre> tag for better visibility of the download link
                content_info = f"\n🔗 <b>Download Link:</b>\n\n<pre>{file_link}</pre>\n"

            else:
                # Unknown or invalid file reference - don't show technical details to user
                delivery_method_display = "Direct Message"
                delivery_type = "message"
                content_info = ""  # Don't show technical file reference to users

            # Create themed delivery message for regular products
            message = LineProductTheme.create_header("PRODUCT DELIVERED", "DIGITAL CONTENT")
            message += f"{LineProductTheme.EMOJIS['success']} <b>Your digital product has been delivered!</b>\n\n"
            message += f"{LineProductTheme.EMOJIS['regular_product']} <b>Product:</b> {product_name}\n"
            message += f"{LineProductTheme.EMOJIS['quantity']} <b>Quantity:</b> {quantity}\n"
            message += f"🆔 <b>Order #:</b> <code>{order_number}</code>\n"
            message += f"{LineProductTheme.EMOJIS['date']} <b>Delivered:</b> <code>{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</code>\n"
            message += f"{LineProductTheme.EMOJIS['delivery']} <b>Delivery Method:</b> {delivery_method_display}\n"

            # Add content-specific information
            if content_info:
                message += content_info

            message += LineProductTheme.create_section_break()
            message += f"{LineProductTheme.EMOJIS['success']} <i>Your digital product has been delivered successfully!</i>\n"

            # Add content-specific instructions
            if delivery_type == "link":
                message += f"{LineProductTheme.EMOJIS['info']} <i>Please download your content using the link above</i>\n"
            elif delivery_type == "text":
                message += f"{LineProductTheme.EMOJIS['info']} <i>Your content is displayed above</i>\n"
            elif delivery_type == "file":
                message += f"{LineProductTheme.EMOJIS['info']} <i>Your file will be delivered in a separate message</i>\n"

            message += f"📞 <i>Contact support if you have any issues with your delivery</i>"

            await self.bot.send_message(
                chat_id=user_id,
                text=message,
                parse_mode="HTML",
                reply_markup=self._create_cc_shop_keyboard()
            )

            # Log successful regular product delivery with proper delivery method
            self._log_delivery_success(
                user_id=user_id,
                product_name=product_name,
                product_type="regular",
                quantity=quantity,
                order_number=order_number,
                delivery_method=delivery_method_display.lower().replace(" ", "_"),
                additional_info={
                    "file_link": file_link,
                    "content_type": delivery_type
                } if file_link else None
            )

            return {
                "success": True,
                "delivery_type": delivery_type,
                "product_name": product_name
            }

        except Exception as e:
            logger.error(f"Error delivering regular product content to user {user_id}: {e}")

            # Log delivery failure
            self._log_delivery_failure(
                user_id=user_id,
                product_name=product_name,
                product_type="regular",
                order_number=order_number,
                error_type="content_delivery_failed",
                error_message=str(e),
                additional_info={"file_link": file_link}
            )

            # Send error notification
            await self._send_delivery_error_notification(
                user_id, product_name, order_number, "general_error", str(e)
            )

            return {
                "success": False,
                "error": f"Content delivery failed: {str(e)}",
                "product_name": product_name
            }


# Global instance - will be initialized with bot instance
digital_delivery_manager: Optional[DigitalDeliveryManager] = None


def initialize_delivery_manager(bot: Bot):
    """Initialize the global delivery manager with bot instance."""
    global digital_delivery_manager
    try:
        digital_delivery_manager = DigitalDeliveryManager(bot)
        logger.info("Digital delivery manager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize digital delivery manager: {e}")
        logger.exception("Full initialization error details:")
        raise


def get_delivery_manager() -> Optional[DigitalDeliveryManager]:
    """Get the global delivery manager instance."""
    return digital_delivery_manager
