"""
Helper functions for working with templates.
This module exposes the template manager functionality as standalone functions.
"""

import logging
from typing import Dict, Any
from .template_manager import TemplateManager

logger = logging.getLogger(__name__)

# Create a global template manager instance
_template_manager = TemplateManager()


def format_text(template_file: str, key: str, default: str = "", **kwargs) -> str:
    """
    Get template text and optionally format it with provided variables.
    This is the main function for all template operations - both retrieving and formatting text.

    Args:
        template_file: The template file name (with or without .json extension)
        key: The key to retrieve from the template
        default: Default text to return if key is not found
        **kwargs: Format parameters to apply to the template text (optional)

    Returns:
        Template text, either as-is or formatted with provided parameters
    """
    return _template_manager.format_text(template_file, key, default, **kwargs)


def update_text(template_file: str, key: str, value: str) -> bool:
    """
    Update a specific text value in a template file

    Args:
        template_file: The template file name (with or without .json extension)
        key: The key to update
        value: The new value to set

    Returns:
        True if update successful, False otherwise
    """
    return _template_manager.update_text(template_file, key, value)


def clear_template_cache():
    """Clear the template cache"""
    _template_manager.clear_cache()
