"""
State management helper functions to prevent duplicate key issues with FSMContext.update_data()
"""

import logging
from typing import Dict, Any, Optional
from aiogram.fsm.context import FSMContext

logger = logging.getLogger(__name__)

async def safe_update_data(state: FSMContext, **kwargs) -> None:
    """
    Safely update state data by first checking if keys already exist.
    This prevents the "got multiple values for keyword argument" error.
    
    Args:
        state: The FSM context to update
        **kwargs: Key-value pairs to update in the state data
    """
    # Get current state data
    current_data = await state.get_data()
    
    # Create a new dictionary with updated values
    new_data = {**current_data, **kwargs}
    
    # Clear state data and set it with the new values
    await state.set_data(new_data)
    
    # Log the update for debugging
    logger.debug(f"Updated state data: {kwargs.keys()}")

async def clear_state_data(state: FSMContext, keys: Optional[list] = None) -> None:
    """
    Clear specific keys from state data or all data if no keys provided.
    
    Args:
        state: The FSM context to update
        keys: Optional list of keys to clear. If None, clears all data.
    """
    if keys is None:
        # Clear all state data
        await state.clear()
        logger.debug("Cleared all state data")
        return
    
    # Get current state data
    current_data = await state.get_data()
    
    # Remove specified keys
    for key in keys:
        if key in current_data:
            del current_data[key]
    
    # Update state with the modified data
    await state.set_data(current_data)
    logger.debug(f"Cleared keys from state data: {keys}")

async def get_state_data(state: FSMContext, key: str, default: Any = None) -> Any:
    """
    Safely get a value from state data with a default fallback.
    
    Args:
        state: The FSM context to get data from
        key: The key to retrieve
        default: Default value if key doesn't exist
        
    Returns:
        The value for the key or the default value
    """
    data = await state.get_data()
    return data.get(key, default)

async def merge_state_data(state: FSMContext, data: Dict[str, Any]) -> None:
    """
    Merge a dictionary of data with existing state data.
    
    Args:
        state: The FSM context to update
        data: Dictionary of data to merge with existing state data
    """
    current_data = await state.get_data()
    await state.set_data({**current_data, **data})
    logger.debug(f"Merged state data with keys: {data.keys()}")
