"""
Performance monitoring utilities for tracking callback response times and bottlenecks.
"""

import time
import logging
import asyncio
import aiohttp
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta
from collections import defaultdict, deque

logger = logging.getLogger(__name__)

# Performance metrics storage
_performance_metrics = {
    "api_calls": deque(maxlen=100),  # Last 100 API calls
    "db_operations": deque(maxlen=100),  # Last 100 DB operations
    "callback_responses": deque(maxlen=100),  # Last 100 callback responses
}

# Response time thresholds (in seconds)
THRESHOLDS = {
    "api_call_warning": 3.0,
    "api_call_critical": 8.0,
    "db_operation_warning": 1.0,
    "db_operation_critical": 3.0,
    "callback_response_warning": 5.0,
    "callback_response_critical": 15.0,
}


class PerformanceTimer:
    """Context manager for timing operations."""
    
    def __init__(self, operation_name: str, operation_type: str = "general"):
        self.operation_name = operation_name
        self.operation_type = operation_type
        self.start_time = None
        self.end_time = None
        self.duration = None
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        
        # Log the performance metric
        log_performance_metric(
            operation_type=self.operation_type,
            operation_name=self.operation_name,
            duration=self.duration,
            success=exc_type is None
        )


def log_performance_metric(
    operation_type: str,
    operation_name: str,
    duration: float,
    success: bool = True,
    additional_data: Optional[Dict] = None
):
    """
    Log a performance metric.
    
    Args:
        operation_type: Type of operation (api_call, db_operation, callback_response)
        operation_name: Name/description of the operation
        duration: Duration in seconds
        success: Whether the operation was successful
        additional_data: Additional data to log
    """
    metric = {
        "timestamp": datetime.now(),
        "operation_type": operation_type,
        "operation_name": operation_name,
        "duration": duration,
        "success": success,
        "additional_data": additional_data or {}
    }
    
    # Store the metric
    if operation_type in _performance_metrics:
        _performance_metrics[operation_type].append(metric)
    
    # Check thresholds and log warnings
    threshold_key = f"{operation_type}_warning"
    critical_key = f"{operation_type}_critical"
    
    if duration > THRESHOLDS.get(critical_key, float('inf')):
        logger.error(
            f"CRITICAL: {operation_name} took {duration:.2f}s "
            f"(threshold: {THRESHOLDS.get(critical_key)}s)"
        )
    elif duration > THRESHOLDS.get(threshold_key, float('inf')):
        logger.warning(
            f"SLOW: {operation_name} took {duration:.2f}s "
            f"(threshold: {THRESHOLDS.get(threshold_key)}s)"
        )
    else:
        logger.debug(f"{operation_name} completed in {duration:.2f}s")


def get_performance_summary(operation_type: Optional[str] = None, last_n: int = 50) -> Dict:
    """
    Get a summary of recent performance metrics.
    
    Args:
        operation_type: Filter by operation type (optional)
        last_n: Number of recent operations to analyze
    
    Returns:
        Dictionary with performance statistics
    """
    if operation_type and operation_type in _performance_metrics:
        metrics = list(_performance_metrics[operation_type])[-last_n:]
    else:
        # Combine all metrics
        metrics = []
        for op_type, op_metrics in _performance_metrics.items():
            metrics.extend(list(op_metrics)[-last_n:])
        metrics.sort(key=lambda x: x["timestamp"])
        metrics = metrics[-last_n:]
    
    if not metrics:
        return {"error": "No metrics available"}
    
    durations = [m["duration"] for m in metrics]
    successful_ops = [m for m in metrics if m["success"]]
    failed_ops = [m for m in metrics if not m["success"]]
    
    return {
        "total_operations": len(metrics),
        "successful_operations": len(successful_ops),
        "failed_operations": len(failed_ops),
        "success_rate": len(successful_ops) / len(metrics) * 100,
        "avg_duration": sum(durations) / len(durations),
        "min_duration": min(durations),
        "max_duration": max(durations),
        "median_duration": sorted(durations)[len(durations) // 2],
        "slow_operations": len([d for d in durations if d > 3.0]),
        "critical_operations": len([d for d in durations if d > 8.0]),
        "time_range": {
            "start": min(m["timestamp"] for m in metrics),
            "end": max(m["timestamp"] for m in metrics)
        }
    }


async def test_api_connectivity() -> Dict[str, float]:
    """
    Test connectivity to external APIs and measure response times.
    
    Returns:
        Dictionary with response times for each API
    """
    apis_to_test = [
        ("OXA Pay API", "https://api.oxapay.com/v1/common/prices"),
        ("Telegram API", "https://api.telegram.org/bot"),
    ]
    
    results = {}
    
    async with aiohttp.ClientSession() as session:
        for api_name, url in apis_to_test:
            try:
                start_time = time.time()
                async with session.get(url, timeout=5) as response:
                    duration = time.time() - start_time
                    results[api_name] = {
                        "duration": duration,
                        "status": response.status,
                        "success": response.status < 400
                    }
            except asyncio.TimeoutError:
                results[api_name] = {
                    "duration": 5.0,
                    "status": "timeout",
                    "success": False
                }
            except Exception as e:
                results[api_name] = {
                    "duration": 0.0,
                    "status": f"error: {str(e)}",
                    "success": False
                }
    
    return results


def get_slow_operations_report(hours: int = 1) -> List[Dict]:
    """
    Get a report of slow operations in the last N hours.
    
    Args:
        hours: Number of hours to look back
    
    Returns:
        List of slow operations with details
    """
    cutoff_time = datetime.now() - timedelta(hours=hours)
    slow_operations = []
    
    for operation_type, metrics in _performance_metrics.items():
        for metric in metrics:
            if (metric["timestamp"] > cutoff_time and 
                metric["duration"] > THRESHOLDS.get(f"{operation_type}_warning", 3.0)):
                slow_operations.append(metric)
    
    # Sort by duration (slowest first)
    slow_operations.sort(key=lambda x: x["duration"], reverse=True)
    
    return slow_operations


def format_performance_report() -> str:
    """
    Format a human-readable performance report.
    
    Returns:
        Formatted performance report string
    """
    lines = ["📊 <b>Performance Report</b>\n"]
    
    for operation_type in ["api_calls", "db_operations", "callback_responses"]:
        summary = get_performance_summary(operation_type, last_n=20)
        
        if "error" not in summary:
            lines.append(f"🔹 <b>{operation_type.replace('_', ' ').title()}</b>")
            lines.append(f"   • Operations: {summary['total_operations']}")
            lines.append(f"   • Success Rate: {summary['success_rate']:.1f}%")
            lines.append(f"   • Avg Duration: {summary['avg_duration']:.2f}s")
            lines.append(f"   • Max Duration: {summary['max_duration']:.2f}s")
            lines.append(f"   • Slow Ops: {summary['slow_operations']}")
            lines.append("")
    
    # Add slow operations from last hour
    slow_ops = get_slow_operations_report(hours=1)
    if slow_ops:
        lines.append("⚠️ <b>Recent Slow Operations</b>")
        for op in slow_ops[:5]:  # Show top 5
            lines.append(
                f"   • {op['operation_name']}: {op['duration']:.2f}s "
                f"({op['timestamp'].strftime('%H:%M:%S')})"
            )
    
    return "\n".join(lines)
