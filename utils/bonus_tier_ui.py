"""
Unified UI components and styling for bonus tier management.
Ensures consistent design patterns, messaging, and user experience across all tier operations.
"""

from typing import Dict, Any, List, Optional
from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton


class TierUIComponents:
    """Unified UI components for bonus tier management."""
    
    # Consistent emojis and styling
    EMOJIS = {
        "tier": "🎁",
        "threshold": "💰",
        "percentage": "📈",
        "fixed": "💵",
        "active": "🟢",
        "inactive": "🔴",
        "edit": "✏️",
        "delete": "🗑️",
        "toggle": "🔄",
        "back": "🔙",
        "add": "➕",
        "view": "📊",
        "success": "✅",
        "error": "❌",
        "warning": "⚠️",
        "info": "ℹ️",
        "security": "🔒",
        "loading": "⏳"
    }
    
    # Consistent styling patterns
    STYLES = {
        "header_separator": "━━━━━━━━━━━━━━━━━━",
        "section_spacing": "\n\n",
        "item_spacing": "\n",
        "indent": "   "
    }
    
    @staticmethod
    def format_header(title: str, subtitle: str = None) -> str:
        """
        Create a consistently formatted header.
        
        Args:
            title: Main title
            subtitle: Optional subtitle
            
        Returns:
            str: Formatted header
        """
        header_parts = [
            f"{TierUIComponents.EMOJIS['tier']} <b>• {title.upper()} •</b>\n\n",
            f"{TierUIComponents.STYLES['header_separator']}\n"
        ]
        
        if subtitle:
            header_parts.extend([
                f"<b>{subtitle.upper()}</b>\n",
                f"{TierUIComponents.STYLES['header_separator']}\n"
            ])
        
        return "".join(header_parts)
    
    @staticmethod
    def format_tier_info(tier: Dict[str, Any], detailed: bool = False) -> str:
        """
        Format tier information consistently.
        
        Args:
            tier: Tier data dictionary
            detailed: Whether to include detailed information
            
        Returns:
            str: Formatted tier information
        """
        threshold = tier.get("threshold", 0)
        bonus_type = tier.get("bonus_type", "percentage")
        status = TierUIComponents.EMOJIS["active"] if tier.get("is_active", True) else TierUIComponents.EMOJIS["inactive"]
        description = tier.get("description", "No description")
        
        # Format bonus information based on type
        if bonus_type == "fixed":
            fixed_amount = tier.get("bonus_fixed_amount", 0)
            bonus_info = f"${fixed_amount:.2f} fixed bonus"
            bonus_emoji = TierUIComponents.EMOJIS["fixed"]
        else:
            percentage = tier.get("bonus_percentage", 0) * 100
            bonus_info = f"{percentage:.1f}% bonus"
            bonus_emoji = TierUIComponents.EMOJIS["percentage"]
        
        info_parts = [
            f"{TierUIComponents.EMOJIS['threshold']} <b>Threshold:</b> ${threshold:.2f}\n",
            f"{bonus_emoji} <b>Bonus:</b> {bonus_info}\n"
        ]
        
        if detailed:
            info_parts.extend([
                f"📊 <b>Type:</b> {bonus_type.title()}\n",
                f"🔄 <b>Status:</b> {status} {'Active' if tier.get('is_active', True) else 'Inactive'}\n",
                f"📝 <b>Description:</b> <i>{description}</i>"
            ])
        else:
            info_parts.append(f"🔄 <b>Status:</b> {status}")
        
        return "".join(info_parts)
    
    @staticmethod
    def format_step_progress(current_step: int, total_steps: int, step_name: str) -> str:
        """
        Format step progress indicator.
        
        Args:
            current_step: Current step number
            total_steps: Total number of steps
            step_name: Name of current step
            
        Returns:
            str: Formatted progress indicator
        """
        progress_bar = "".join([
            "●" if i <= current_step else "○" 
            for i in range(1, total_steps + 1)
        ])
        
        return (
            f"📋 <b>Step {current_step}/{total_steps}: {step_name.upper()}</b>\n"
            f"{progress_bar} ({current_step}/{total_steps})\n"
        )
    
    @staticmethod
    def create_tier_management_keyboard() -> InlineKeyboardMarkup:
        """Create the main tier management keyboard."""
        return InlineKeyboardMarkup(inline_keyboard=[
            [InlineKeyboardButton(
                text=f"{TierUIComponents.EMOJIS['view']} View All Tiers", 
                callback_data="bonus_view_all"
            )],
            [InlineKeyboardButton(
                text=f"{TierUIComponents.EMOJIS['add']} Add New Tier", 
                callback_data="bonus_add_new"
            )],
            [InlineKeyboardButton(
                text=f"{TierUIComponents.EMOJIS['toggle']} Refresh", 
                callback_data="bonus_management"
            )],
            [InlineKeyboardButton(
                text=f"{TierUIComponents.EMOJIS['back']} Back to Admin", 
                callback_data="admin_panel"
            )]
        ])
    
    @staticmethod
    def create_tier_action_keyboard(tier_id: str) -> InlineKeyboardMarkup:
        """Create action keyboard for individual tier."""
        return InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text=f"{TierUIComponents.EMOJIS['edit']} Edit", 
                    callback_data=f"bonus_edit:{tier_id}"
                ),
                InlineKeyboardButton(
                    text=f"{TierUIComponents.EMOJIS['toggle']} Toggle", 
                    callback_data=f"bonus_toggle:{tier_id}"
                )
            ],
            [InlineKeyboardButton(
                text=f"{TierUIComponents.EMOJIS['delete']} Delete", 
                callback_data=f"bonus_delete_confirm:{tier_id}"
            )],
            [InlineKeyboardButton(
                text=f"{TierUIComponents.EMOJIS['back']} Back", 
                callback_data="bonus_view_all"
            )]
        ])
    
    @staticmethod
    def create_edit_options_keyboard(tier_id: str, tier: Dict[str, Any] = None) -> InlineKeyboardMarkup:
        """Create edit options keyboard for a tier."""
        buttons = [
            [InlineKeyboardButton(
                text=f"{TierUIComponents.EMOJIS['threshold']} Edit Threshold", 
                callback_data=f"bonus_edit_threshold:{tier_id}"
            )],
            [InlineKeyboardButton(
                text="🎯 Edit Bonus Type", 
                callback_data=f"bonus_edit_type:{tier_id}"
            )]
        ]
        
        # Add appropriate bonus value edit option based on current type
        if tier:
            bonus_type = tier.get("bonus_type", "percentage")
            if bonus_type == "fixed":
                buttons.append([InlineKeyboardButton(
                    text=f"{TierUIComponents.EMOJIS['fixed']} Edit Fixed Amount", 
                    callback_data=f"bonus_edit_fixed:{tier_id}"
                )])
            else:
                buttons.append([InlineKeyboardButton(
                    text=f"{TierUIComponents.EMOJIS['percentage']} Edit Percentage", 
                    callback_data=f"bonus_edit_percentage:{tier_id}"
                )])
        else:
            # Fallback - show both options
            buttons.extend([
                [InlineKeyboardButton(
                    text=f"{TierUIComponents.EMOJIS['percentage']} Edit Percentage", 
                    callback_data=f"bonus_edit_percentage:{tier_id}"
                )],
                [InlineKeyboardButton(
                    text=f"{TierUIComponents.EMOJIS['fixed']} Edit Fixed Amount", 
                    callback_data=f"bonus_edit_fixed:{tier_id}"
                )]
            ])
        
        buttons.extend([
            [InlineKeyboardButton(
                text="📝 Edit Description", 
                callback_data=f"bonus_edit_description:{tier_id}"
            )],
            [InlineKeyboardButton(
                text=f"{TierUIComponents.EMOJIS['back']} Back", 
                callback_data=f"bonus_view_tier:{tier_id}"
            )]
        ])
        
        return InlineKeyboardMarkup(inline_keyboard=buttons)
    
    @staticmethod
    def create_confirmation_keyboard(confirm_action: str, cancel_action: str = None) -> InlineKeyboardMarkup:
        """Create confirmation keyboard with consistent styling."""
        buttons = [
            [
                InlineKeyboardButton(
                    text=f"{TierUIComponents.EMOJIS['success']} Yes, Confirm", 
                    callback_data=confirm_action
                ),
                InlineKeyboardButton(
                    text=f"{TierUIComponents.EMOJIS['error']} Cancel", 
                    callback_data=cancel_action or "bonus_management"
                )
            ]
        ]
        return InlineKeyboardMarkup(inline_keyboard=buttons)
    
    @staticmethod
    def format_success_message(operation: str, details: str = None) -> str:
        """Format success message consistently."""
        message_parts = [
            f"{TierUIComponents.EMOJIS['success']} <b>• {operation.upper()} SUCCESSFUL •</b>\n\n",
            f"{TierUIComponents.STYLES['header_separator']}\n",
            "<b>SUCCESS</b>\n",
            f"{TierUIComponents.STYLES['header_separator']}\n\n"
        ]
        
        if details:
            message_parts.append(f"{details}\n\n")
        
        message_parts.append(
            f"{TierUIComponents.EMOJIS['security']} <b>Security:</b> Operation logged for audit trail"
        )
        
        return "".join(message_parts)
    
    @staticmethod
    def format_input_prompt(field_name: str, examples: List[str] = None, 
                          constraints: List[str] = None) -> str:
        """Format input prompt consistently."""
        prompt_parts = [
            f"📝 <b>Enter the {field_name.lower()}:</b>\n\n"
        ]
        
        if examples:
            prompt_parts.append("<i>Examples:</i>\n")
            for example in examples:
                prompt_parts.append(f"• <code>{example}</code>\n")
            prompt_parts.append("\n")
        
        if constraints:
            prompt_parts.append("⚠️ <i>Requirements:</i>\n")
            for constraint in constraints:
                prompt_parts.append(f"• {constraint}\n")
        
        return "".join(prompt_parts)


# Create global instance for easy access
tier_ui = TierUIComponents()
