"""
Consolidated Product Operations Module
Unified operations for all product types to eliminate duplication.
Provides consistent interfaces for product management across the system.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class ProductOperations:
    """
    Consolidated operations for all product types.
    Eliminates duplication and provides consistent interfaces.
    """
    
    @staticmethod
    def get_product_with_validation(product_id: Union[int, str]) -> Dict[str, Any]:
        """
        Get product with basic validation.
        
        Args:
            product_id: Product ID
            
        Returns:
            Dict with product data and validation status
        """
        try:
            from database.operations import get_product
            
            product = get_product(product_id)
            if not product:
                return {
                    "success": False,
                    "error": "Product not found",
                    "product": None
                }
            
            return {
                "success": True,
                "product": product
            }
            
        except Exception as e:
            logger.error(f"Error getting product {product_id}: {e}")
            return {
                "success": False,
                "error": f"Database error: {str(e)}",
                "product": None
            }
    
    @staticmethod
    def validate_product_availability(product: Dict[str, Any], quantity: int = 1, user_id: int = None) -> Dict[str, Any]:
        """
        Validate product availability for purchase with shared inventory support.

        Args:
            product: Product data
            quantity: Requested quantity
            user_id: Optional user ID for shared inventory validation

        Returns:
            Validation result
        """
        try:
            if not product:
                return {"valid": False, "error": "Product data is required"}

            # Check if product is line-based
            if product.get("is_line_based", False):
                max_quantity = product.get("max_quantity_per_order", 1)
                allow_shared_inventory = product.get("allow_shared_inventory", False)

                if quantity > max_quantity:
                    return {
                        "valid": False,
                        "error": f"Maximum {max_quantity} items per order"
                    }

                # For shared inventory, check user-specific availability
                if allow_shared_inventory and user_id:
                    from database.operations import get_available_lines_for_user
                    product_id = product.get("id") or product.get("_id")
                    total_lines = product.get("total_lines", 0)
                    user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)

                    if len(user_available_lines) < quantity:
                        return {
                            "valid": False,
                            "error": f"You have already purchased most available content. New lines available: {len(user_available_lines)}"
                        }
                else:
                    # For exclusive inventory, use standard availability check
                    available_lines = product.get("available_lines", 0)
                    if available_lines < quantity:
                        return {
                            "valid": False,
                            "error": f"Insufficient stock. Available: {available_lines}"
                        }
            
            # Check if product is exclusive
            elif product.get("is_exclusive_single_use", False):
                if quantity != 1:
                    return {
                        "valid": False,
                        "error": "Exclusive products can only be purchased in quantity of 1"
                    }
                
                if product.get("is_purchased", False):
                    return {
                        "valid": False,
                        "error": "This exclusive product has already been purchased"
                    }
                
                # Check expiration
                expiration_date = product.get("expiration_date")
                if expiration_date:
                    if isinstance(expiration_date, str):
                        try:
                            expiration_date = datetime.fromisoformat(expiration_date)
                        except ValueError:
                            return {
                                "valid": False,
                                "error": "Invalid expiration date format"
                            }
                    
                    if datetime.now() > expiration_date:
                        return {
                            "valid": False,
                            "error": "This exclusive product has expired"
                        }
            
            return {"valid": True}
            
        except Exception as e:
            logger.error(f"Error validating product availability: {e}")
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}"
            }
    
    @staticmethod
    def get_product_type_info(product: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get product type information and metadata.
        
        Args:
            product: Product data
            
        Returns:
            Product type information
        """
        try:
            if product.get("is_line_based", False):
                return {
                    "type": "line_based",
                    "display_name": "Line-Based Product",
                    "supports_quantity": True,
                    "max_quantity": product.get("max_quantity_per_order", 1),
                    "available_stock": product.get("available_lines", 0),
                    "icon": "📦"
                }
            
            elif product.get("is_exclusive_single_use", False):
                return {
                    "type": "exclusive",
                    "display_name": "Exclusive Single-Use Product",
                    "supports_quantity": False,
                    "max_quantity": 1,
                    "available_stock": 1 if not product.get("is_purchased", False) else 0,
                    "icon": "💎"
                }
            
            else:
                return {
                    "type": "regular",
                    "display_name": "Regular Digital Product",
                    "supports_quantity": True,
                    "max_quantity": 999,  # Arbitrary high limit for regular products
                    "available_stock": 999,  # Regular products don't have stock limits
                    "icon": "📄"
                }
                
        except Exception as e:
            logger.error(f"Error getting product type info: {e}")
            return {
                "type": "unknown",
                "display_name": "Unknown Product Type",
                "supports_quantity": False,
                "max_quantity": 1,
                "available_stock": 0,
                "icon": "❓"
            }
    
    @staticmethod
    def calculate_product_price(product: Dict[str, Any], quantity: int = 1) -> Dict[str, Any]:
        """
        Calculate price for any product type.
        
        Args:
            product: Product data
            quantity: Quantity
            
        Returns:
            Price calculation result
        """
        try:
            from utils.product_price_calculator import product_price_calculator
            
            return product_price_calculator.calculate_total_price(product, quantity)
            
        except Exception as e:
            logger.error(f"Error calculating product price: {e}")
            return {
                "unit_price": 0.0,
                "quantity": quantity,
                "total_price": 0.0,
                "product_type": "unknown",
                "price_breakdown": "Error calculating price",
                "error": str(e)
            }
    
    @staticmethod
    def create_product_summary(product: Dict[str, Any], include_stock: bool = True) -> Dict[str, Any]:
        """
        Create a comprehensive product summary.
        
        Args:
            product: Product data
            include_stock: Whether to include stock information
            
        Returns:
            Product summary dictionary
        """
        try:
            type_info = ProductOperations.get_product_type_info(product)
            price_calc = ProductOperations.calculate_product_price(product, 1)
            
            summary = {
                "id": product.get("id"),
                "name": product.get("name") or "Unnamed Product",
                "description": product.get("description", ""),
                "price": price_calc["unit_price"],
                "type_info": type_info,
                "price_display": price_calc["price_breakdown"],
                "file_link": product.get("file_link", ""),
                "image_url": product.get("image_url"),
                "category_id": product.get("category_id"),
                "created_at": product.get("created_at")
            }
            
            if include_stock:
                summary["stock_info"] = {
                    "available": type_info["available_stock"],
                    "max_per_order": type_info["max_quantity"],
                    "supports_quantity": type_info["supports_quantity"]
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Error creating product summary: {e}")
            return {
                "id": product.get("id") if product else None,
                "name": "Error loading product",
                "error": str(e)
            }
    
    @staticmethod
    def validate_purchase_request(product_id: Union[int, str], quantity: int, user_id: int) -> Dict[str, Any]:
        """
        Comprehensive validation for purchase requests.
        
        Args:
            product_id: Product ID
            quantity: Requested quantity
            user_id: User ID
            
        Returns:
            Validation result with detailed information
        """
        try:
            # Get product
            product_result = ProductOperations.get_product_with_validation(product_id)
            if not product_result["success"]:
                return product_result
            
            product = product_result["product"]
            
            # Validate availability
            availability = ProductOperations.validate_product_availability(product, quantity)
            if not availability["valid"]:
                return availability
            
            # Calculate pricing
            price_calc = ProductOperations.calculate_product_price(product, quantity)
            if price_calc.get("error"):
                return {
                    "valid": False,
                    "error": f"Price calculation failed: {price_calc['error']}"
                }
            
            # Get type info
            type_info = ProductOperations.get_product_type_info(product)
            
            return {
                "valid": True,
                "product": product,
                "price_calculation": price_calc,
                "type_info": type_info,
                "quantity": quantity,
                "user_id": user_id
            }
            
        except Exception as e:
            logger.error(f"Error validating purchase request: {e}")
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}"
            }


# Global instance for easy access
product_operations = ProductOperations()
