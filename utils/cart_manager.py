"""
Enhanced Cart Manager
Provides robust cart operations with comprehensive error handling and validation.
Ensures cart data consistency and provides detailed user feedback.
"""

import logging
from typing import Dict, Any, List, Optional, Union
from datetime import datetime
from enum import Enum

from database.operations import (
    get_or_create_cart, update_cart, clear_cart, get_product
)
from utils.price_calculator import price_calculator
from utils.product_validator import product_validator

logger = logging.getLogger(__name__)


class CartErrorType(Enum):
    """Enumeration of cart error types for specific handling."""
    DATABASE_ERROR = "database_error"
    PRODUCT_NOT_FOUND = "product_not_found"
    INVALID_QUANTITY = "invalid_quantity"
    INSUFFICIENT_STOCK = "insufficient_stock"
    PRODUCT_UNAVAILABLE = "product_unavailable"
    VALIDATION_ERROR = "validation_error"
    CART_LIMIT_EXCEEDED = "cart_limit_exceeded"
    DUPLICATE_EXCLUSIVE = "duplicate_exclusive"
    NETWORK_ERROR = "network_error"


class CartError(Exception):
    """Custom exception for cart operations with detailed error information."""
    
    def __init__(self, error_type: CartErrorType, message: str, details: Optional[Dict] = None):
        self.error_type = error_type
        self.message = message
        self.details = details or {}
        super().__init__(message)


class CartManager:
    """
    Enhanced cart manager with comprehensive error handling and validation.
    Provides detailed feedback and ensures cart data consistency.
    """
    
    # Configuration constants
    MAX_CART_ITEMS = 50
    MAX_QUANTITY_PER_ITEM = 1000
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.{self.__class__.__name__}")
    
    def get_cart_with_validation(self, user_id: int) -> Dict[str, Any]:
        """
        Get user cart with comprehensive validation and error handling.
        
        Args:
            user_id: User ID
            
        Returns:
            Dict with cart data and validation results
            
        Raises:
            CartError: If cart retrieval fails
        """
        try:
            self.logger.debug(f"Retrieving cart for user {user_id}")
            
            # Attempt to get or create cart
            cart = get_or_create_cart(user_id)
            
            if cart is None:
                raise CartError(
                    CartErrorType.DATABASE_ERROR,
                    "Failed to retrieve or create cart",
                    {"user_id": user_id, "operation": "get_or_create"}
                )
            
            # Validate cart structure
            if not isinstance(cart, dict):
                raise CartError(
                    CartErrorType.VALIDATION_ERROR,
                    "Invalid cart data structure",
                    {"user_id": user_id, "cart_type": type(cart)}
                )
            
            # Ensure cart has required fields
            if "items" not in cart:
                cart["items"] = []
                self.logger.warning(f"Cart for user {user_id} missing items field, initialized empty")
            
            if "user_id" not in cart:
                cart["user_id"] = user_id
                self.logger.warning(f"Cart missing user_id field, set to {user_id}")
            
            # Validate and clean cart items
            validated_cart = self._validate_cart_items(cart)
            
            return {
                "success": True,
                "cart": validated_cart,
                "item_count": len(validated_cart.get("items", [])),
                "validation_performed": True
            }
            
        except CartError:
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error retrieving cart for user {user_id}: {e}")
            raise CartError(
                CartErrorType.DATABASE_ERROR,
                f"Unexpected cart retrieval error: {str(e)}",
                {"user_id": user_id, "original_error": str(e)}
            )
    
    def _validate_cart_items(self, cart: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate all items in the cart and remove invalid ones.
        
        Args:
            cart: Cart dictionary
            
        Returns:
            Cart with validated items
        """
        items = cart.get("items", [])
        valid_items = []
        removed_items = []
        
        for item in items:
            try:
                validation_result = self._validate_cart_item(item)
                if validation_result["valid"]:
                    valid_items.append(validation_result["item"])
                else:
                    removed_items.append({
                        "item": item,
                        "reason": validation_result["error"]
                    })
                    self.logger.warning(f"Removed invalid cart item: {validation_result['error']}")
            except Exception as e:
                removed_items.append({
                    "item": item,
                    "reason": f"Validation error: {str(e)}"
                })
                self.logger.error(f"Error validating cart item: {e}")
        
        # Update cart with valid items
        cart["items"] = valid_items
        
        if removed_items:
            cart["validation_info"] = {
                "removed_items_count": len(removed_items),
                "removed_items": removed_items,
                "validation_timestamp": datetime.now().isoformat()
            }
        
        return cart
    
    def _validate_cart_item(self, item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate a single cart item.
        
        Args:
            item: Cart item dictionary
            
        Returns:
            Dict with validation results
        """
        try:
            # Check required fields
            required_fields = ["product_id", "name", "price"]
            missing_fields = [field for field in required_fields if field not in item]
            
            if missing_fields:
                return {
                    "valid": False,
                    "error": f"Missing required fields: {', '.join(missing_fields)}"
                }
            
            # Validate product still exists
            product_id = item.get("product_id")
            product = get_product(product_id)
            
            if not product:
                return {
                    "valid": False,
                    "error": f"Product {product_id} no longer exists"
                }
            
            # Validate quantity
            quantity = item.get("quantity", 1)
            try:
                quantity = int(quantity)
                if quantity <= 0:
                    return {
                        "valid": False,
                        "error": "Quantity must be positive"
                    }
                if quantity > self.MAX_QUANTITY_PER_ITEM:
                    return {
                        "valid": False,
                        "error": f"Quantity exceeds maximum ({self.MAX_QUANTITY_PER_ITEM})"
                    }
            except (ValueError, TypeError):
                return {
                    "valid": False,
                    "error": "Invalid quantity value"
                }
            
            # Validate price
            price_validation = price_calculator.validate_price(item.get("price", 0))
            if not price_validation["valid"]:
                return {
                    "valid": False,
                    "error": f"Invalid price: {price_validation['error']}"
                }
            
            # Check product availability for exclusive items
            if item.get("is_exclusive_single_use", False):
                if product.get("is_purchased", False):
                    return {
                        "valid": False,
                        "error": "Exclusive product no longer available"
                    }
            
            # Check stock for line-based items with shared inventory support
            if item.get("is_line_based", False):
                allow_shared_inventory = product.get("allow_shared_inventory", False)

                if allow_shared_inventory:
                    # For shared inventory, we need user context for validation
                    # Mark this item as needing user-specific validation
                    item["needs_user_validation"] = True
                else:
                    # For exclusive inventory, use standard availability check
                    available_lines = product.get("available_lines", 0)
                    if quantity > available_lines:
                        return {
                            "valid": False,
                            "error": f"Insufficient stock: {available_lines} available, {quantity} requested"
                        }
            
            # Item is valid, ensure it has all required fields
            validated_item = {
                "product_id": product_id,
                "name": item.get("name", product.get("name", "Unknown Product")),
                "price": float(price_validation["decimal_price"]),
                "quantity": quantity,
                "is_line_based": item.get("is_line_based", False),
                "is_exclusive_single_use": item.get("is_exclusive_single_use", False),
                "file_link": item.get("file_link", product.get("file_link", "")),
                "validation_timestamp": datetime.now().isoformat()
            }
            
            return {
                "valid": True,
                "item": validated_item
            }
            
        except Exception as e:
            return {
                "valid": False,
                "error": f"Validation error: {str(e)}"
            }

    def validate_cart_item_with_user_context(self, item: Dict[str, Any], user_id: int) -> Dict[str, Any]:
        """
        Validate cart item with user context for shared inventory support.

        Args:
            item: Cart item to validate
            user_id: User ID for shared inventory validation

        Returns:
            Dict with validation results
        """
        try:
            # First do basic validation
            basic_validation = self._validate_cart_item(item)
            if not basic_validation["valid"]:
                return basic_validation

            # For line-based products with shared inventory, delegate validation to line product manager
            if item.get("is_line_based", False):
                product_id = item.get("product_id")
                product = get_product(product_id)

                if product and product.get("allow_shared_inventory", False):
                    # Use centralized validation from line product manager
                    from utils.line_product_manager import line_product_manager
                    import asyncio

                    try:
                        # Run async validation in sync context
                        loop = asyncio.get_event_loop()
                        validation = loop.run_until_complete(
                            line_product_manager.validate_line_product_purchase(
                                product_id, item.get("quantity", 1), user_id=user_id
                            )
                        )
                        if not validation["valid"]:
                            return {
                                "valid": False,
                                "error": validation["error"]
                            }
                    except Exception as e:
                        self.logger.warning(f"Failed to validate shared inventory for product {product_id}: {e}")
                        # Fall back to basic validation
                        pass

            return basic_validation

        except Exception as e:
            return {
                "valid": False,
                "error": f"User context validation error: {str(e)}"
            }
    
    def add_item_to_cart(self, user_id: int, product_id: int, quantity: int = 1) -> Dict[str, Any]:
        """
        Add item to cart with comprehensive validation and error handling.
        
        Args:
            user_id: User ID
            product_id: Product ID to add
            quantity: Quantity to add
            
        Returns:
            Dict with operation results
            
        Raises:
            CartError: If operation fails
        """
        try:
            self.logger.info(f"Adding product {product_id} (qty: {quantity}) to cart for user {user_id}")
            
            # Get and validate cart
            cart_result = self.get_cart_with_validation(user_id)
            cart = cart_result["cart"]
            
            # Validate product exists
            product = get_product(product_id)
            if not product:
                raise CartError(
                    CartErrorType.PRODUCT_NOT_FOUND,
                    f"Product {product_id} not found",
                    {"product_id": product_id, "user_id": user_id}
                )
            
            # Validate quantity
            if not isinstance(quantity, int) or quantity <= 0:
                raise CartError(
                    CartErrorType.INVALID_QUANTITY,
                    "Quantity must be a positive integer",
                    {"quantity": quantity, "type": type(quantity)}
                )
            
            # Check cart limits
            current_items = len(cart.get("items", []))
            if current_items >= self.MAX_CART_ITEMS:
                raise CartError(
                    CartErrorType.CART_LIMIT_EXCEEDED,
                    f"Cart limit exceeded (max {self.MAX_CART_ITEMS} items)",
                    {"current_items": current_items, "max_items": self.MAX_CART_ITEMS}
                )
            
            # Check for exclusive product conflicts
            if product.get("is_exclusive_single_use", False):
                # Check if already in cart (use string comparison for ObjectId compatibility)
                for item in cart.get("items", []):
                    if str(item.get("product_id")) == str(product_id):
                        raise CartError(
                            CartErrorType.DUPLICATE_EXCLUSIVE,
                            "Exclusive product already in cart",
                            {"product_id": product_id}
                        )
                
                # Check if already purchased
                if product.get("is_purchased", False):
                    raise CartError(
                        CartErrorType.PRODUCT_UNAVAILABLE,
                        "Exclusive product no longer available",
                        {"product_id": product_id}
                    )
            
            # Check stock for line-based products
            if product.get("is_line_based", False):
                allow_shared_inventory = product.get("allow_shared_inventory", False)

                if allow_shared_inventory:
                    # For shared inventory, use user-specific availability
                    from database.operations import get_available_lines_for_user
                    total_lines = product.get("total_lines", 0)
                    user_available_lines = get_available_lines_for_user(user_id, product_id, total_lines)
                    available_lines = len(user_available_lines)

                    if quantity > available_lines:
                        raise CartError(
                            CartErrorType.INSUFFICIENT_STOCK,
                            f"You have already purchased most available content. New lines available: {available_lines}",
                            {"requested": quantity, "available": available_lines, "user_specific": True}
                        )
                else:
                    # For exclusive inventory, use standard availability check
                    available_lines = product.get("available_lines", 0)
                    if quantity > available_lines:
                        raise CartError(
                            CartErrorType.INSUFFICIENT_STOCK,
                            f"Insufficient stock: {available_lines} available",
                            {"requested": quantity, "available": available_lines, "user_specific": False}
                        )
            
            # Create cart item using consolidated factory
            from utils.cart_item_factory import cart_item_factory

            cart_item = cart_item_factory.create_cart_item(
                product=product,
                product_id=product_id,
                quantity=quantity
            )
            
            # Add to cart
            cart["items"].append(cart_item)
            
            # Update cart in database
            if not update_cart(user_id, cart["items"]):
                raise CartError(
                    CartErrorType.DATABASE_ERROR,
                    "Failed to update cart in database",
                    {"user_id": user_id, "operation": "update_cart"}
                )
            
            # Calculate cart totals
            cart_totals = price_calculator.calculate_cart_total(cart["items"])
            
            self.logger.info(f"Successfully added product {product_id} to cart for user {user_id}")
            
            return {
                "success": True,
                "message": f"Added {product.get('name', 'product')} to cart",
                "cart_item": cart_item,
                "cart_totals": cart_totals,
                "item_count": len(cart["items"])
            }
            
        except CartError:
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error adding item to cart: {e}")
            raise CartError(
                CartErrorType.DATABASE_ERROR,
                f"Unexpected error: {str(e)}",
                {"user_id": user_id, "product_id": product_id, "original_error": str(e)}
            )
    
    def remove_item_from_cart(self, user_id: int, product_id: int) -> Dict[str, Any]:
        """
        Remove item from cart with error handling.
        
        Args:
            user_id: User ID
            product_id: Product ID to remove
            
        Returns:
            Dict with operation results
        """
        try:
            self.logger.info(f"Removing product {product_id} from cart for user {user_id}")
            
            # Get cart
            cart_result = self.get_cart_with_validation(user_id)
            cart = cart_result["cart"]
            
            # Find and remove item
            items = cart.get("items", [])
            original_count = len(items)
            
            # Use string comparison for ObjectId compatibility
            cart["items"] = [item for item in items if str(item.get("product_id")) != str(product_id)]
            
            if len(cart["items"]) == original_count:
                raise CartError(
                    CartErrorType.PRODUCT_NOT_FOUND,
                    f"Product {product_id} not found in cart",
                    {"product_id": product_id, "user_id": user_id}
                )
            
            # Update cart in database
            if not update_cart(user_id, cart["items"]):
                raise CartError(
                    CartErrorType.DATABASE_ERROR,
                    "Failed to update cart in database",
                    {"user_id": user_id, "operation": "remove_item"}
                )
            
            # Calculate new totals
            cart_totals = price_calculator.calculate_cart_total(cart["items"])
            
            self.logger.info(f"Successfully removed product {product_id} from cart for user {user_id}")
            
            return {
                "success": True,
                "message": "Item removed from cart",
                "cart_totals": cart_totals,
                "item_count": len(cart["items"])
            }
            
        except CartError:
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error removing item from cart: {e}")
            raise CartError(
                CartErrorType.DATABASE_ERROR,
                f"Unexpected error: {str(e)}",
                {"user_id": user_id, "product_id": product_id, "original_error": str(e)}
            )
    
    def clear_user_cart(self, user_id: int) -> Dict[str, Any]:
        """
        Clear user cart with error handling.
        
        Args:
            user_id: User ID
            
        Returns:
            Dict with operation results
        """
        try:
            self.logger.info(f"Clearing cart for user {user_id}")
            
            if not clear_cart(user_id):
                raise CartError(
                    CartErrorType.DATABASE_ERROR,
                    "Failed to clear cart",
                    {"user_id": user_id, "operation": "clear_cart"}
                )
            
            return {
                "success": True,
                "message": "Cart cleared successfully",
                "item_count": 0
            }
            
        except CartError:
            raise
        except Exception as e:
            self.logger.error(f"Unexpected error clearing cart: {e}")
            raise CartError(
                CartErrorType.DATABASE_ERROR,
                f"Unexpected error: {str(e)}",
                {"user_id": user_id, "original_error": str(e)}
            )

    def validate_cart_with_user_context(self, user_id: int) -> Dict[str, Any]:
        """
        Validate entire cart with user context for shared inventory support.

        Args:
            user_id: User ID for validation

        Returns:
            Dict with validation results and any issues found
        """
        try:
            # Get cart using the correct method
            cart_result = self.get_cart_with_validation(user_id)
            if not cart_result["success"]:
                return {"valid": False, "error": "Failed to retrieve cart"}

            cart = cart_result["cart"]
            items = cart.get("items", [])

            if not items:
                return {"valid": True, "items": [], "issues": []}

            valid_items = []
            issues = []

            for item in items:
                # Validate each item with user context
                validation = self.validate_cart_item_with_user_context(item, user_id)

                if validation["valid"]:
                    valid_items.append(item)
                else:
                    issues.append({
                        "item_name": item.get("name", "Unknown Product"),
                        "error": validation["error"]
                    })
                    self.logger.warning(f"Cart item validation failed for user {user_id}: {validation['error']}")

            # Update cart with only valid items if there were issues
            if issues:
                from database.operations import update_cart
                update_cart(user_id, valid_items)
                self.logger.info(f"Removed {len(issues)} invalid items from cart for user {user_id}")

            return {
                "valid": len(issues) == 0,
                "items": valid_items,
                "issues": issues,
                "removed_count": len(issues)
            }

        except Exception as e:
            self.logger.error(f"Error validating cart with user context for user {user_id}: {e}")
            return {"valid": False, "error": f"Cart validation error: {str(e)}"}


# Global instance for easy access
cart_manager = CartManager()
