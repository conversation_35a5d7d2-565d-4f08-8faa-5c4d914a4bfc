from typing import Dict, Any, Callable, Awaitable, Optional
import logging
from aiogram import BaseMiddleware
from aiogram.types import (
    TelegramObject,
    Message,
    CallbackQuery,
    InlineKeyboardMarkup,
    InlineKeyboardButton,
)
from config import MAINTENANCE_MODE
from utils.helpers import get_admins
from handlers.sys_db import is_privileged  # Import is_privileged function
from database.operations import get_maintenance_mode, get_setting
from utils.template_helpers import format_text

# Configure logger
logger = logging.getLogger(__name__)


class MaintenanceModeMiddleware(BaseMiddleware):
    """
    Middleware for handling maintenance mode.
    When maintenance mode is active, only admins can use the bot.
    """

    async def __call__(
        self,
        handler: Callable[[TelegramObject, Dict[str, Any]], Awaitable[Any]],
        event: TelegramObject,
        data: Dict[str, Any],
    ) -> Any:
        # Skip maintenance check for the "check_maintenance" callback
        if isinstance(event, CallbackQuery) and event.data == "check_maintenance":
            # Always process the maintenance check callback
            return await handler(event, data)

        # Check if maintenance mode is active (from environment variable or database)
        db_maintenance_mode = get_maintenance_mode()
        is_maintenance_active = MAINTENANCE_MODE or db_maintenance_mode

        if not is_maintenance_active:
            # If maintenance mode is not active, proceed normally without logging
            # This prevents excessive logging on every button click
            return await handler(event, data)

        # Get the user ID from the event
        user_id = self._get_user_id(event)

        # Only log for /start commands or when debugging is needed
        should_log = False
        if (
            isinstance(event, Message)
            and event.text
            and event.text.startswith("/start")
        ):
            should_log = True

        if should_log:
            logger.debug(f"Maintenance mode active, checking user {user_id}")

        # If we couldn't extract a user ID, let the request through
        # (this could be a system event or something else)
        if user_id is None:
            logger.warning("Could not extract user ID from event, allowing through")
            return await handler(event, data)

        # First check if user is privileged using the is_privileged function
        if is_privileged(user_id):
            if should_log:
                logger.info(
                    f"Privileged user {user_id} allowed through maintenance mode"
                )
            return await handler(event, data)

        # As a fallback, also check against admin list
        admins = get_admins()

        # Only log admin list when processing /start command
        if should_log:
            logger.debug(f"Admin list for maintenance check: {admins}")

        # Check if the user is in the admin list
        if user_id in admins:
            # Admins can use the bot during maintenance
            if should_log:
                logger.info(
                    f"Admin user {user_id} allowed through maintenance mode via admin list"
                )
            return await handler(event, data)

        # Non-admin user in maintenance mode
        # Only log for new interactions, not every button click
        if should_log or isinstance(event, Message):
            logger.info(f"Blocked non-admin user {user_id} due to maintenance mode")

        # For non-admins, send maintenance mode message
        await self._send_maintenance_message(event, data)

        # Don't process the handler for non-admins
        return True

    def _get_user_id(self, event: TelegramObject) -> Optional[int]:
        """Extract user ID from various event types."""
        if isinstance(event, Message) and event.from_user:
            return event.from_user.id
        elif isinstance(event, CallbackQuery) and event.from_user:
            return event.from_user.id

        # For other event types, try a more generic approach
        try:
            # Many Telegram objects have a from_user attribute
            if hasattr(event, "from_user") and event.from_user:
                return event.from_user.id
        except Exception as e:
            logger.debug(f"Error extracting user ID: {e}")

        return None

    async def _send_maintenance_message(
        self, event: TelegramObject, data: Dict[str, Any]
    ) -> None:
        """
        Send maintenance mode message to the user.

        This method sends a maintenance message to users who are not admins
        when maintenance mode is active.
        """
        try:
            bot = data.get("bot")
            if not bot:
                logger.error("Bot instance not available in middleware data")
                return

            # Create a status check keyboard
            maintenance_keyboard = InlineKeyboardMarkup(
                inline_keyboard=[
                    [
                        InlineKeyboardButton(
                            text="🔄 Check Status", callback_data="check_maintenance"
                        )
                    ]
                ]
            )

            # Try to get custom maintenance message from database or template
            try:
                # First check if there's a custom message in the database
                maintenance_setting = get_setting("maintenance_mode")
                if maintenance_setting and "message" in maintenance_setting:
                    maintenance_message = maintenance_setting["message"]
                else:
                    # Fall back to template
                    maintenance_message = format_text("user", "maintenance_active")
            except Exception as e:
                logger.error(f"Failed to get maintenance message: {e}")
                maintenance_message = (
                    "🚧 <b>MAINTENANCE MODE ACTIVE</b> 🚧\n\n"
                    "Our system is currently undergoing scheduled maintenance.\n"
                    "The bot will be back online soon. Thank you for your patience."
                )

            # Handle message events (commands, text messages)
            if isinstance(event, Message):
                # Reply to the message with maintenance info
                await event.reply(
                    maintenance_message,
                    reply_markup=maintenance_keyboard,
                    parse_mode="HTML",
                )

            # Handle callback query events (button clicks)
            elif isinstance(event, CallbackQuery):
                # Answer the callback query with a brief notification
                await event.answer(
                    text="🛠️ System maintenance in progress", show_alert=True
                )

                # Update the original message if it exists
                if event.message:
                    try:
                        # Try to edit the existing message
                        await event.message.edit_text(
                            maintenance_message,
                            reply_markup=maintenance_keyboard,
                            parse_mode="HTML",
                        )
                    except Exception as e:
                        # If editing fails (e.g., message is too old), send a new message
                        try:
                            await bot.send_message(
                                chat_id=event.from_user.id,
                                text=maintenance_message,
                                reply_markup=maintenance_keyboard,
                                parse_mode="HTML",
                            )
                        except Exception as e2:
                            logger.error(f"Failed to send maintenance message: {e2}")
        except Exception as e:
            logger.error(f"Failed to send maintenance message: {e}", exc_info=True)
