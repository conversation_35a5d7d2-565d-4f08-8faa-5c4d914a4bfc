# Environment variables and secrets
.env
*.env
.env.*

# Python cache files
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
*.so
.Python

# Logs and databases
*.log
*.sqlite
*.db
error_logs/

# Admin secure data
secure_data/

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Distribution / packaging
dist/
build/
*.egg-info/

# Local development settings
.idea/
.vscode/
*.code-workspace
.DS_Store

# Temporary uploads and media files
uploads/*
!uploads/category_images/.gitkeep
!uploads/product_images/.gitkeep

# Testing
.coverage
htmlcov/

# Documentation
_build/
docs/_build/

# Backup files
*.bak
*.tmp
*~
*.swp
