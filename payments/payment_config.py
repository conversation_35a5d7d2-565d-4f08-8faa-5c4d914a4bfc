import os
import socket
import json
import hmac
import hashlib
from typing import Dict, Any, Optional

# API key for OXA Pay - should be loaded from environment or config
OXA_PAY_API_KEY = os.environ.get("OXA_PAY_API_KEY", "test_api_key")


def parse_bool_env(env_value: str, default: bool = False) -> bool:
    """Parse environment variable string to boolean"""
    if not env_value:
        return default
    return env_value.lower() in ("true", "1", "yes")


def is_development_mode():
    """Check if we're in development mode and return 'true' or 'false' as a lowercase string"""
    return os.environ.get("DEVELOPMENT_MODE", "false").lower() in ("true", "1", "yes")


# Function to get the host machine's IP address for external connections
def get_host_ip() -> str:
    """Get the primary IP address of the host machine"""
    try:
        # This creates a socket, connects to an external server (but doesn't send data)
        # and gets the local IP address that would be used for that connection
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        # Using a public DNS server as the target - doesn't actually connect
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except (socket.error, OSError):
        return "127.0.0.1"  # Fallback to localhost


def generate_hmac(data: Dict[str, Any]) -> str:
    """
    Generate HMAC signature for data.

    Args:
        data: Data to sign (dictionary that will be converted to JSON)

    Returns:
        HMAC signature as hex string
    """
    # Convert data to JSON string and encode as bytes
    data_bytes = json.dumps(data, ensure_ascii=True).encode("utf-8", errors='replace')

    # Generate HMAC signature using SHA512
    signature = hmac.new(
        OXA_PAY_API_KEY.encode("utf-8"), data_bytes, hashlib.sha512
    ).hexdigest()

    return signature


# Environment settings
DEVELOPMENT_MODE = is_development_mode()
TESTING_MODE = parse_bool_env(os.environ.get("TESTING_MODE", "false"))
DEBUG_MODE = parse_bool_env(os.environ.get("DEBUG_MODE", "false"))

# Default server settings - in production, use the real IP
DEFAULT_HOST = "0.0.0.0" if DEVELOPMENT_MODE else get_host_ip()
DEFAULT_PORT = 3001

# URL paths
CALLBACK_PATH = "/callback"
HEALTH_PATH = "/health"

# Default server URL
DEFAULT_SERVER_URL = f"http://{DEFAULT_HOST}:{DEFAULT_PORT}"

# Default callback URLs
DEFAULT_CALLBACK_URL = f"{DEFAULT_SERVER_URL}{CALLBACK_PATH}"
DEFAULT_HEALTH_URL = f"{DEFAULT_SERVER_URL}{HEALTH_PATH}"

# Version information
VERSION = "1.0.0"
SERVICE_NAME = "payment_callback"


def get_url_with_path(
    host: Optional[str] = None, port: Optional[int] = None, path: str = "/"
) -> str:
    """Common function to build URLs with proper host handling"""
    if not host:
        host = DEFAULT_HOST

    if not port:
        port = DEFAULT_PORT

    # Adjust localhost for the simulator to work properly
    connect_host = "localhost" if host == "0.0.0.0" else host

    return f"http://{connect_host}:{port}{path}"


def get_callback_url(host: Optional[str] = None, port: Optional[int] = None) -> str:
    """
    Get the callback URL with the given host and port.

    For callback URLs, we always use the actual server IP (not 0.0.0.0)
    because external payment providers need to be able to reach the callback endpoint.
    """
    if not host:
        # Always use the actual server IP for callback URLs, regardless of development mode
        host = get_host_ip()

    if not port:
        port = DEFAULT_PORT

    return f"http://{host}:{port}{CALLBACK_PATH}"


def get_external_callback_url(port: Optional[int] = None) -> str:
    """
    Get a callback URL that's accessible from external payment providers.

    This function always uses the actual server IP (never 0.0.0.0 or localhost)
    regardless of development mode, because external services need to reach the callback.
    """
    host = get_host_ip()
    if not port:
        port = DEFAULT_PORT

    return f"http://{host}:{port}{CALLBACK_PATH}"


def get_health_url(host: Optional[str] = None, port: Optional[int] = None) -> str:
    """Get the health check URL with the given host and port"""
    return get_url_with_path(host, port, HEALTH_PATH)
