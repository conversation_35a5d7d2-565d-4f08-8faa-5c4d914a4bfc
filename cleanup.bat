@echo off
echo Comprehensive Python cleanup...

echo Cleaning Python cache files...
del /s /q *.pyc 2>nul
del /s /q *.pyo 2>nul

echo Removing __pycache__ directories...
FOR /d /r . %%d IN (__pycache__) DO @IF EXIST "%%d" rd /s /q "%%d" 2>nul

echo Cleaning log files...
del /q *.log 2>nul
if exist error_logs rmdir /s /q error_logs 2>nul

echo Cleaning temporary files...
del /q uploads\temp\*.* 2>nul
del /q *.tmp 2>nul
del /q *.bak 2>nul

echo Cleanup complete!
